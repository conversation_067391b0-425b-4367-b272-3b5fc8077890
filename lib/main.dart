import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend_re/router/go_router_provider.dart';
import 'package:frontend_re/theme/theme_provider.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:frontend_re/core/network/http_service.dart';
import 'package:frontend_re/core/services/app_initialization_service.dart';
import 'package:frontend_re/core/utils/ui/window_manager_util.dart';
import 'package:frontend_re/features/workbench/controllers/simple_browser_manager.dart';

// 导入 userInfoControllerProvider
import 'core/relay/core.dart';

/// 应用程序入口函数
void main() async {
  // 确保 Flutter 绑定初始化
  WidgetsFlutterBinding.ensureInitialized();
  
  // 执行应用初始化
  await AppInitializationService.initialize();
  
  // 启动应用
  runApp(const ProviderScope(child: MyApp()));
}

/// 应用程序主组件
/// ConsumerStatefulWidget 是 Riverpod 提供的 Widget，用于监听状态变化并管理生命周期
class MyApp extends ConsumerStatefulWidget {
  const MyApp({super.key});

  @override
  ConsumerState<MyApp> createState() => _MyAppState();
}

class _MyAppState extends ConsumerState<MyApp> with WidgetsBindingObserver {
  bool _browsersClosed = false; // 防止重复关闭浏览器
  
  @override
  void initState() {
    super.initState();
    // 添加应用生命周期监听器
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    // 移除应用生命周期监听器
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Future<void> didChangeAppLifecycleState(AppLifecycleState state) async {
    super.didChangeAppLifecycleState(state);
    
    debugPrint('🔄 App lifecycle state changed to: ${state.name}');

    final processManager = ProcessManager();

    // 当应用进入后台、隐藏或分离状态时，保存窗口状态
    switch (state) {
      case AppLifecycleState.paused:
        // 异步保存窗口状态，避免阻塞主线程
        _saveWindowStateAsync();
        break;
      case AppLifecycleState.hidden:
        // 应用被隐藏，可能是关闭或最小化
        // 由于WindowManagerUtil.onWindowClose()已经被调用，这通常意味着是关闭操作
        debugPrint('🔄 App hidden: 应用可能正在关闭，开始关闭浏览器');
        _closeAllBrowsersAsync();
        _saveWindowStateAsync();
        await processManager.stop();
        break;
      case AppLifecycleState.detached:
        // 应用被分离（真正关闭），关闭所有浏览器
        debugPrint('🔄 App detached: 应用正在关闭，开始关闭浏览器');
        _closeAllBrowsersAsync();
        _saveWindowStateAsync();
        await processManager.stop();
        break;
      case AppLifecycleState.resumed:
        // 应用恢复或非活动状态，暂时不需要特殊处理
        break;
      case AppLifecycleState.inactive:
        // 应用非活动状态，暂时不需要特殊处理
        break;
    }
  }

  /// 异步保存窗口状态
  void _saveWindowStateAsync() {
    Future.microtask(() async {
      try {
        await WindowManagerUtil.saveCurrentWindowState();
        debugPrint('窗口状态已保存');
      } catch (e) {
        debugPrint('保存窗口状态时发生错误: $e');
      }
    });
  }

  /// 异步关闭所有浏览器
  void _closeAllBrowsersAsync() {
    // 防止重复关闭
    if (_browsersClosed) {
      debugPrint('🔄 App lifecycle: 浏览器已经关闭过了，跳过');
      return;
    }
    
    _browsersClosed = true;
    
    Future.microtask(() async {
      try {
        debugPrint('🔄 App lifecycle: 应用正在关闭，开始关闭所有浏览器...');
        await SimpleBrowserManager.instance.stopAllBrowsers();
        debugPrint('✅ App lifecycle: 所有浏览器关闭完成');
      } catch (e) {
        debugPrint('❌ App lifecycle: 关闭浏览器时发生错误: $e');
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    // 监听语言设置状态
    //final locale = ref.watch(languageProvider);

    final goRouter = ref.watch(goRouterProvider);
    final themeMode = ref.watch(themeModeProvider);
    final lightTheme = ref.watch(lightThemeProvider);
    final darkTheme = ref.watch(darkThemeProvider);

    // 初始化HttpService
    ref.watch(httpServiceInitProvider);
    
    return ScreenUtilInit(
      designSize: const Size(1280, 720),
      // 是否根据宽度/高度中的最小值适配文字
      minTextAdapt: true,
      // 支持分屏尺寸
      splitScreenMode: true,
      builder: (context, child) {
        return MediaQuery.withNoTextScaling(
          child: MaterialApp.router(
            // 配置路由
            routerConfig: goRouter,
            // 应用名称
            title: 'PrismBrowser',
            // 移除调试标签
            debugShowCheckedModeBanner: false,
            // 主题配置
            theme: lightTheme,
            darkTheme: darkTheme,
            themeMode: themeMode,
            // 国际化代理配置
            localizationsDelegates: const [
              // Material组件的本地化代理
              GlobalMaterialLocalizations.delegate,
              // 基础Widget的本地化代理
              GlobalWidgetsLocalizations.delegate,
              // Cupertino(iOS风格)组件的本地化代理
              GlobalCupertinoLocalizations.delegate,
            ],
            // 当前语言环境
            // locale: locale,
            // 支持的语言列表
            supportedLocales: const [
              Locale('en'), // 英语
              Locale('zh'), // 中文
            ],
            builder: (context, child) {
              return Scaffold(
                body: child!,
              );
            },
          ),// 返回应用程序主体
        );
      },
    );
  }
}
