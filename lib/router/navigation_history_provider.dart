import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'navigation_history_provider.g.dart';

/// 简化的导航历史 - 只记录上一个路径
@Riverpod(keepAlive: true)
class NavigationHistory extends _$NavigationHistory {
  @override
  String? build() {
    return null; // 初始状态没有历史
  }

  /// 保存当前路径作为历史
  void savePath(String path) {
    // 不保存公共路由路径，避免循环
    if (!_isPublicRoute(path)) {
      state = path;
      debugPrint('Navigation: Saved previous path - $path');
    }
  }

  /// 清空历史
  void clear() {
    state = null;
    debugPrint('Navigation: History cleared');
  }

  /// 检查是否有历史
  bool get hasPrevious => state != null && state!.isNotEmpty;

  /// 检查是否是公共路由
  bool _isPublicRoute(String path) {
    final publicRoutes = ['/user-settings', '/help-center', '/plugin-store'];
    return publicRoutes.any((route) => path.contains(route));
  }
}

/// 导航助手类 - 学习导航栏的跳转方式
class NavigationHelper {
  /// 跳转到用户设置并保存当前位置
  static void goToUserSettings(BuildContext context, WidgetRef ref) {
    // 保存当前路径
    final currentPath = GoRouterState.of(context).uri.toString();
    ref.read(navigationHistoryProvider.notifier).savePath(currentPath);
    
    // 跳转到用户设置
    context.goNamed('userSettings');
    debugPrint('Navigation: Going to user settings from $currentPath');
  }

  /// 跳转到公共分支的任意路由并保存当前位置
  static void goToPublicRoute(
    BuildContext context,
    WidgetRef ref,
    String routeName, {
    Map<String, String>? pathParameters,
    Map<String, dynamic>? queryParameters,
    Object? extra,
  }) {
    // 保存当前路径
    final currentPath = GoRouterState.of(context).uri.toString();
    ref.read(navigationHistoryProvider.notifier).savePath(currentPath);
    
    // 跳转到指定路由
    context.goNamed(
      routeName,
      pathParameters: pathParameters ?? {},
      queryParameters: queryParameters ?? {},
      extra: extra,
    );
    debugPrint('Navigation: Going to $routeName from $currentPath');
  }

  /// 从公共分支返回到上一个页面 - 学习导航栏的跳转逻辑
  static bool popFromPublic(BuildContext context, WidgetRef ref) {
    final previousPath = ref.read(navigationHistoryProvider);
    
    if (previousPath == null || previousPath.isEmpty) {
      debugPrint('Navigation: No previous path to return to');
      return false;
    }

    try {
      debugPrint('Navigation: Attempting to return to $previousPath');
      
      // 使用和导航栏相同的跳转逻辑
      _navigateToPath(context, previousPath);
      
      // 清空历史
      ref.read(navigationHistoryProvider.notifier).clear();
      
      debugPrint('Navigation: Successfully returned to $previousPath');
      return true;
    } catch (e) {
      debugPrint('Navigation: Error returning to $previousPath - $e');
      return false;
    }
  }

  /// 学习导航栏的跳转逻辑
  static void _navigateToPath(BuildContext context, String path) {
    final branchIndex = _getBranchIndexFromPath(path);
    final navigationShell = StatefulNavigationShell.maybeOf(context);
    
    if (branchIndex != null && navigationShell != null) {
      // 使用分支导航（和导航栏一样）
      navigationShell.goBranch(branchIndex);
      debugPrint('Navigation: Used goBranch($branchIndex) for path $path');
    } else {
      // 使用路径导航（和导航栏的子菜单一样）
      context.go(path);
      debugPrint('Navigation: Used context.go($path)');
    }
  }

  /// 将路径映射到分支索引（复制自 IndexPage）
  static int? _getBranchIndexFromPath(String path) {
    final Map<String, int> pathToIndex = {
      '/workbench': 0,
      '/proxy': 1,
      '/team/members': 2,
      '/teamlogs': 3,
      '/team/library': 4,
      '/finance': 5,
      '/auto/api': 6,
      '/auto/factory': 7,
      '/auto/market': 8,
      '/auto/schedule': 9,
      '/auto/records': 10,
    };
    
    // 精确匹配
    if (pathToIndex.containsKey(path)) {
      return pathToIndex[path];
    }
    
    // 前缀匹配（例如 /proxy/self-proxy 匹配到 /proxy）
    for (final entry in pathToIndex.entries) {
      if (path.startsWith('${entry.key}/')) {
        return entry.value;
      }
    }
    
    return null;
  }

  /// 安全的返回方法 - 完全学习导航栏方式，不用 pop
  static void safeGoBack(BuildContext context, WidgetRef ref) {
    if (!popFromPublic(context, ref)) {
      // 如果智能返回失败，直接用 context.go 回到工作台
      // 学习导航栏，不用 Navigator.pop()
      context.go('/workbench');
      debugPrint('Navigation: Direct go to workbench as fallback');
    }
  }

  /// 调试当前导航状态
  static void debugNavigationState(BuildContext context, WidgetRef ref) {
    final currentPath = GoRouterState.of(context).uri.toString();
    final previousPath = ref.read(navigationHistoryProvider);
    final shell = StatefulNavigationShell.maybeOf(context);
    
    debugPrint('=== Navigation Debug Info ===');
    debugPrint('Current path: $currentPath');
    debugPrint('Previous path: $previousPath');
    debugPrint('Current branch index: ${shell?.currentIndex}');
    debugPrint('Branch index for current path: ${_getBranchIndexFromPath(currentPath)}');
    debugPrint('Has previous: ${previousPath != null && previousPath.isNotEmpty}');
    debugPrint('=============================');
  }
} 
