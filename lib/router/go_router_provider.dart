import 'package:flutter/material.dart';
import 'package:frontend_re/domain/models/user_model.dart';
import 'package:frontend_re/features/IndexPage.dart';
import 'package:frontend_re/features/automation/process-factory/views/rpa_list_page.dart';
import 'package:frontend_re/features/login/views/login_screen.dart';
import 'package:frontend_re/features/titlebar/appSetting/app_setting_page.dart';
import 'package:frontend_re/features/titlebar/helpCenter/views/help_center_page.dart';
import 'package:frontend_re/features/titlebar/promotion/views/promotion_page.dart';
import 'package:frontend_re/features/workbench/views/browser_add_page_refactored.dart';
import 'package:frontend_re/widgets/auth_check_widget.dart';
import 'package:frontend_re/features/team/team_log/views/log_index.dart';
import 'package:frontend_re/features/automation/views/api_page.dart';
import 'package:frontend_re/features/automation/process-factory/views/process_factory_page.dart';
import 'package:frontend_re/features/automation/views/task_log_page.dart';
import 'package:frontend_re/features/automation/views/task_worker_page.dart';
import 'package:frontend_re/features/automation/views/template_store.dart';
import 'package:frontend_re/features/financial/views/finance_page.dart';
import 'package:frontend_re/features/proxy/proxy_store/views/buy_proxy_page.dart';
import 'package:frontend_re/features/proxy/proxy_cart/cart_page.dart';
import 'package:frontend_re/features/proxy/proxy_platform/views/platform_proxy_page.dart';
import 'package:frontend_re/features/proxy/proxy_api/proxy_api_page.dart';
import 'package:frontend_re/features/proxy/views/proxy_index.dart';
import 'package:frontend_re/features/proxy/proxy_self/views/self_proxy_page.dart';
import 'package:frontend_re/features/team/team_library/views/library_page.dart';
import 'package:frontend_re/features/team/team_log/views/login_log_page.dart';
import 'package:frontend_re/features/team/team_log/views/operation_log_page.dart';
import 'package:frontend_re/features/team/team_log/views/permission_log_page.dart';
import 'package:frontend_re/features/team/team_manage/views/team_recover_page.dart';
import 'package:frontend_re/features/team/team_manage/views/team_members_page.dart';
import 'package:frontend_re/features/titlebar/userSetting/views/user_settings.dart';
import 'package:frontend_re/features/workbench/views/browser_add_page.dart';
import 'package:frontend_re/features/workbench/views/browser_page.dart';
import 'package:frontend_re/features/workbench/views/browser_trash_page.dart';
import 'package:frontend_re/features/workbench/views/workbench_index.dart';

import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';



part 'go_router_provider.g.dart';

// 记录导航历史的计数器
int _navigationCounter = 0;

// 保存每个路径的访问序号
final Map<String, int> _navigationHistory = {};

// 记录上一个路径
String _previousPath = '/proxy';

// 定义导航键
final _rootNavigatorKey = GlobalKey<NavigatorState>();
final _workbenchNavigatorKey = GlobalKey<NavigatorState>();
final _proxyNavigatorKey = GlobalKey<NavigatorState>();
final _teamNavigatorKey = GlobalKey<NavigatorState>();
final _teamLogsNavigatorKey = GlobalKey<NavigatorState>();
final _teamLibraryNavigatorKey = GlobalKey<NavigatorState>();
final _financeNavigatorKey = GlobalKey<NavigatorState>();
final _autoApiNavigatorKey = GlobalKey<NavigatorState>();
final _autoFactoryNavigatorKey = GlobalKey<NavigatorState>();
final _autoMarketNavigatorKey = GlobalKey<NavigatorState>();
final _autoScheduleNavigatorKey = GlobalKey<NavigatorState>();
final _autoRecordsNavigatorKey = GlobalKey<NavigatorState>();
final _publicNavigatorKey = GlobalKey<NavigatorState>();
@riverpod
GoRouter goRouter(Ref ref) {
  final userAsync = ValueNotifier<AsyncValue<UserModel?>>(const AsyncLoading());

  final router = GoRouter(
    navigatorKey: _rootNavigatorKey,
    initialLocation: '/', // 使用根路径
    debugLogDiagnostics: true,
    refreshListenable: userAsync,
    // 添加防抖动配置，防止快速切换导致的冲突
    routerNeglect: true,
    routes: [
      // 根路径 - 自动判断跳转
      GoRoute(
        parentNavigatorKey: _rootNavigatorKey,
        path: '/',
        name: 'root',
        builder: (context, state) => const AuthCheckWidget(),
      ),
      
      // 登录页
      GoRoute(
        parentNavigatorKey: _rootNavigatorKey,
        path: '/login',
        name: 'login',
        builder: (context, state) => const LoginScreen(),
      ),

      // 合并为单一的StatefulShellRoute，处理所有主要导航
      StatefulShellRoute(
        builder: (context, state, navigationShell) {
          // IndexPage 作为顶层容器，接收 navigationShell 用于显示内容
          // 不再传递 navigationShell 给 child 参数
          return IndexPage(
            navigationShell: navigationShell,
          );
        },
        navigatorContainerBuilder: (context, navigationShell, children) {
          // 这里决定如何渲染当前活动的子路由
          return children[navigationShell.currentIndex]; // 直接显示当前分支的内容
        },
        branches: [
          // 工作台主页分支
          StatefulShellBranch(
            navigatorKey: _workbenchNavigatorKey,
            routes: <RouteBase>[
              ShellRoute(
                builder: (BuildContext context, GoRouterState state, Widget child) {
                  return WorkbenchIndex(child: child);
                },
                routes: <RouteBase>[
                  GoRoute(
                    path: '/workbench',
                    name: 'workbench',
                    builder: (context, state) => const BrowserPage(),
                    routes: [
                      // 添加浏览器 页面
                      GoRoute(
                        path: 'addBrowser',
                        name: 'addBrowser',
                        builder: (context, state) {
                          final extra = state.extra;
                          return BrowserAddPageRefactored(config: extra);
                        },
                      ),
                      // 浏览器回收站
                      GoRoute(
                        path: 'browserTrash',
                        name: 'browserTrash',
                        builder: (context, state) => const BrowserTrashPage(),
                      ),
                    ],
                  ),
                ]
              ),
              
            ],
          ),
          
          // 代理管理主页分支
          StatefulShellBranch(
            navigatorKey: _proxyNavigatorKey,
            routes: <RouteBase>[
              ShellRoute(
                builder: (BuildContext context, GoRouterState state, Widget child) {
                  return ProxyIndex(child: child);
                },
                routes: <RouteBase>[
                  GoRoute(
                    path: '/proxy',
                    name: 'proxy',
                    pageBuilder: (BuildContext context, GoRouterState state) {
                      return buildProxyPageWithTransition(
                        context, state, const PlatformProxyPage(),
                      );
                    },
                  ),
                  GoRoute(
                    path: '/proxy/self-proxy',
                    name: 'selfProxy',
                    pageBuilder: (BuildContext context, GoRouterState state) {
                      return buildProxyPageWithTransition(
                        context, state, const SelfProxyPage(),
                      );
                    },
                  ),
                  GoRoute(
                    path: '/proxy/proxy-api',
                    name: 'proxyApi',
                    pageBuilder: (BuildContext context, GoRouterState state) {
                      return buildProxyPageWithTransition(
                        context, state, const ProxyApiPage(),
                      );
                    },
                  ),
                  GoRoute(
                    path: '/proxy/buy-proxy',
                    name: 'buyProxy',
                    pageBuilder: (BuildContext context, GoRouterState state) {
                      return buildProxyPageWithTransition(
                        context, state, const BuyProxyPage(),
                      );
                    },
                  ),
                  GoRoute(
                    path: '/proxy/cart',
                    name: 'cart',
                    pageBuilder: (BuildContext context, GoRouterState state) {
                      return buildProxyPageWithTransition(
                        context, state, const CartPage(),
                      );
                    },
                  ),
                ],
              ),
            ],
          ),

          // 团队成员主页分支
          StatefulShellBranch(
            navigatorKey: _teamNavigatorKey,
            routes: [
              GoRoute(
                path: '/team/members',
                name: 'teamMembers',
                builder: (context, state) => const TeamMembersPage(),
                routes: [
                  // 团队回收站分支
                  GoRoute(
                    path: 'team-recover',
                    name: 'teamRecover',
                    builder: (context, state) => const TeamRecoverPage(),
                  ),
                ],
              ),
            ],
          ),

          // 团队协作日志主页分支
          StatefulShellBranch(
            navigatorKey: _teamLogsNavigatorKey,
            routes: <RouteBase>[
              ShellRoute(
                  builder: (BuildContext context, GoRouterState state, Widget child) {
                    return LogIndex(child: child);
                  },
                  routes: <RouteBase>[
                    // 登录日志
                    GoRoute(
                      path: '/teamlogs',
                      name: 'loginLog',
                      pageBuilder: (BuildContext context, GoRouterState state) {
                        return buildProxyPageWithTransition(
                          context, state, const LoginLogPage(),
                        );
                      },
                    ),
                    // 操作日志分支
                    GoRoute(
                      path: '/teamlogs/operation-log',
                      name: 'operationLog',
                      pageBuilder: (BuildContext context, GoRouterState state) {
                        return buildProxyPageWithTransition(
                          context, state, const OperationLogPage(),
                        );
                      },
                    ),
                    // 权限日志分支
                    GoRoute(
                      path: '/teamlogs/permission-log',
                      name: 'permissionLog',
                      pageBuilder: (BuildContext context, GoRouterState state) {
                        return buildProxyPageWithTransition(
                          context, state, const PermissionLogPage(),
                        );
                      },
                    ),
                  ])
            ],
          ),

          // 团队资源库主页分支
          StatefulShellBranch(
            navigatorKey: _teamLibraryNavigatorKey,
            routes: [
              GoRoute(
                path: '/team/library',
                name: 'teamLibrary',
                builder: (context, state) => const LibraryPage(),
              ),
            ],
          ),

          // 财务中心分支
          StatefulShellBranch(
            navigatorKey: _financeNavigatorKey,
            routes: [
              GoRoute(
                path: '/finance',
                name: 'finance',
                builder: (context, state) => const FinancePage(),
              ),
            ],
          ),

          // 自动中心 api分支
          StatefulShellBranch(
            navigatorKey: _autoApiNavigatorKey,
            routes: [
              GoRoute(
                path: '/auto/api',
                name: 'autoApi',
                builder: (context, state) => const ApiPage(),
              ),
            ],
          ),

          // 自动中心 流程工厂分支
          StatefulShellBranch(
            navigatorKey: _autoFactoryNavigatorKey,
            routes: [
              GoRoute(
                path: '/auto/rpaList',
                name: 'rpaList',
                builder: (context, state) => const RpaListPage(),
                routes: [
                  GoRoute(
                    path: '/auto/rpaList/rpa',
                    name: 'rpa',
                    builder: (context, state) => const ProcessFactoryPage(),
                  ),
                ],
              ),
            ],
          ),

          // 自动中心 模板市场分支
          StatefulShellBranch(
            navigatorKey: _autoMarketNavigatorKey,
            routes: [
              GoRoute(
                path: '/auto/market',
                name: 'autoMarket',
                builder: (context, state) => const TemplateStorePage(),
              ),
            ],
          ),

          // 自动中心 任务调度分支
          StatefulShellBranch(
            navigatorKey: _autoScheduleNavigatorKey,
            routes: [
              GoRoute(
                path: '/auto/schedule',
                name: 'autoSchedule',
                builder: (context, state) => const TaskWorkerPage(),
              ),
            ],
          ),

          // 自动中心 执行记录分支
          StatefulShellBranch(
            navigatorKey: _autoRecordsNavigatorKey,
            routes: [
              GoRoute(
                path: '/auto/records',
                name: 'autoRecords',
                builder: (context, state) => const TaskLogPage(),
              ),
            ],
          ),

          // 公共分支
          StatefulShellBranch(
            navigatorKey: _publicNavigatorKey,
            routes: [
              GoRoute(
                path: '/user-settings',
                name: 'userSettings',
                builder: (context, state) => const UserSettings(),
              ),
              GoRoute(
                path: '/app-setting',
                name: 'appSetting',
                builder: (context, state) => const AppSettingPage(),
              ),
              GoRoute(
                path: '/help-center',
                name: 'helpCenter',
                builder: (context, state) => const HelpCenterPage(),
              ),
              GoRoute(
                path: '/promotion',
                name: 'promotion',
                builder: (context, state) => const PromotionPage(),
              ),
            ],
          ),
        ],
      ),
    ],
  );
  ref.onDispose(router.dispose);
  return router;
}

// 自定义构建代理页面的过渡效果
Page buildProxyPageWithTransition(
  BuildContext context, 
  GoRouterState state, 
  Widget child
) {
  // 获取当前路径
  final String currentPath = state.uri.path;
  
  // 记录页面访问顺序
  if (!_navigationHistory.containsKey(currentPath)) {
    _navigationHistory[currentPath] = _navigationCounter++;
  }
  
  // 根据导航历史确定转场方向
  bool isRightToLeft = true; // 默认从右到左
  
  if (_previousPath.isNotEmpty && _navigationHistory.containsKey(_previousPath)) {
    // 比较当前路径和前一路径的访问序号决定转场方向
    isRightToLeft = _navigationHistory[currentPath]! > _navigationHistory[_previousPath]!;
  }
  
  // 更新上一个路径
  _previousPath = currentPath;
  
  return CustomTransitionPage(
    key: ValueKey(currentPath),
    child: child,
    transitionsBuilder: (context, animation, secondaryAnimation, child) {
      // 前一个页面的快速消失动画
      secondaryAnimation = CurvedAnimation(
        parent: secondaryAnimation,
        curve: const Interval(0.0, 0.4, curve: Curves.easeOut), // 前一个页面在动画前40%时就消失
      );
      
      // 当前页面的延迟进入动画
      final CurvedAnimation curvedAnimation = CurvedAnimation(
        parent: animation,
        curve: const Interval(0.3, 1.0, curve: Curves.easeInOut), // 当前页面在动画30%后才开始进入
      );
      
      // 组合动画效果
      return Stack(
        children: [
          // 处理前一个页面的退出动画
          FadeTransition(
            opacity: Tween<double>(begin: 1.0, end: 0.0).animate(secondaryAnimation),
            child: SlideTransition(
              position: Tween<Offset>(
                begin: Offset.zero,
                end: isRightToLeft ? const Offset(-0.3, 0.0) : const Offset(0.3, 0.0),
              ).animate(secondaryAnimation),
              child: Container(color: Theme.of(context).scaffoldBackgroundColor),
            ),
          ),
          // 处理当前页面的进入动画
          FadeTransition(
            opacity: Tween<double>(begin: 0.0, end: 1.0).animate(curvedAnimation),
            child: SlideTransition(
              position: Tween<Offset>(
                begin: isRightToLeft ? const Offset(0.8, 0.0) : const Offset(-0.8, 0.0),
                end: Offset.zero,
              ).animate(curvedAnimation),
              child: child,
            ),
          ),
        ],
      );
    },
    transitionDuration: const Duration(milliseconds: 400), // 延长总动画时间
    reverseTransitionDuration: const Duration(milliseconds: 400),
  );
}

class TaskSchedulePage {
  const TaskSchedulePage();
}

// 无过渡效果的页面
class NoTransitionPage<T> extends CustomTransitionPage<T> {
  const NoTransitionPage({
    required super.child,
    super.key,
    super.name,
    super.arguments,
    super.restorationId,
  }) : super(
          transitionsBuilder: _transitionsBuilder,
          transitionDuration: Duration.zero,
          reverseTransitionDuration: Duration.zero,
        );

  static Widget _transitionsBuilder(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) => child;
}
