// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'navigation_history_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$navigationHistoryHash() => r'66eb8fcad678097fa1df4067b6a9294231df8dbe';

/// 简化的导航历史 - 只记录上一个路径
///
/// Copied from [NavigationHistory].
@ProviderFor(NavigationHistory)
final navigationHistoryProvider =
    NotifierProvider<NavigationHistory, String?>.internal(
  NavigationHistory.new,
  name: r'navigationHistoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$navigationHistoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$NavigationHistory = Notifier<String?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
