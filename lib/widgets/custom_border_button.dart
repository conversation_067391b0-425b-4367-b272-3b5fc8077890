import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class CustomBorderButton extends StatefulWidget {
  final VoidCallback? onPressed;
  final String iconPath;
  final String? text;
  final double? horizontalPadding;
  final double? verticalPadding;
  final double? iconSize;
  final double? width;
  final double? height;
  final Color? borderColor;
  final Color? backgroundColor;
  final Color? iconColor;
  final Color? textColor;
  final String? tooltip;
  final bool enableTooltip;
  final Duration? tooltipDelay;
  final bool? isImage;
  final double? borderRadius;
  const CustomBorderButton({
    super.key,
    required this.iconPath,
    this.onPressed,
    this.text,
    this.horizontalPadding = 12,
    this.verticalPadding = 6,
    this.iconSize = 16,
    this.width,
    this.height,
    this.borderColor,
    this.backgroundColor,
    this.iconColor,
    this.textColor,
    this.tooltip,
    this.enableTooltip = true,
    this.tooltipDelay,
    this.isImage = false,
    this.borderRadius = 50,
  });

  @override
  CustomBorderButtonState createState() => CustomBorderButtonState();
}

class CustomBorderButtonState extends State<CustomBorderButton> {
  bool _isHovered = false;
  bool _showTooltip = false;

  void _onEnter() {
    setState(() => _isHovered = true);
    // 只有启用tooltip且有提示内容时才显示
    if (widget.enableTooltip && widget.tooltip != null && widget.tooltip!.isNotEmpty) {
      Future.delayed(
        widget.tooltipDelay ?? const Duration(milliseconds: 500),
        () {
          if (_isHovered && mounted) {
            setState(() => _showTooltip = true);
          }
        },
      );
    }
  }

  void _onExit() {
    setState(() {
      _isHovered = false;
      _showTooltip = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        GestureDetector(
          onTap: widget.onPressed,
          child: Container(
            height: widget.height,
            width: widget.width,
            constraints: const BoxConstraints(minHeight: 32),
            decoration: BoxDecoration(
              color: _isHovered ? const Color(0xFF0C75F8) : widget.backgroundColor, // 鼠标移上去时背景变为蓝色
              borderRadius: BorderRadius.circular(widget.borderRadius!),
              border: _isHovered ? Border.all(color: const Color(0xFF0C75F8)) : Border.all(color: widget.borderColor ?? const Color(0xFFEBEBEB)),
            ),
            child: MouseRegion(
              cursor: SystemMouseCursors.click,
              onEnter: (_) => _onEnter(),
              onExit: (_) => _onExit(),
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: widget.horizontalPadding!, vertical: widget.verticalPadding!),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    if (widget.isImage == true)
                      Image.asset(
                        widget.iconPath,
                        height: widget.iconSize!,
                        width: widget.iconSize!,
                      )
                    else
                      SvgPicture.asset(
                        widget.iconPath,
                      height: widget.iconSize!,
                      width: widget.iconSize!,
                      colorFilter: _isHovered
                          ? const ColorFilter.mode(Color(0xFFFFFFFF), BlendMode.srcIn)
                          : ColorFilter.mode(
                              widget.iconColor ?? const Color(0xFF8D8E93), BlendMode.srcIn),
                    ),
                    if (widget.text != null)
                      const SizedBox(width: 6),
                    if (widget.text != null)
                      Text(
                        widget.text!,
                        style: TextStyle(
                            color: _isHovered
                                ? widget.textColor ?? const Color(0xFFFFFFFF)
                                : widget.textColor ?? const Color(0xFF8D8E93)),
                      ),
                  ],
                ),
              ),
            ),
          ),
        ),
        // 自定义tooltip - 只在启用且有提示内容时显示
        if (_showTooltip && widget.enableTooltip && widget.tooltip != null && widget.tooltip!.isNotEmpty)
          Positioned(
            bottom: (widget.height ?? 40) + 10, // 在按钮上方显示，根据按钮高度动态调整
            child: Center(
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.grey[700], // 更柔和的灰色背景
                  borderRadius: BorderRadius.circular(4),
                  boxShadow: const [
                    BoxShadow(
                      color: Colors.black26,
                      blurRadius: 4,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: Text(
                  widget.tooltip!,
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.9), // 90%透明度的白色文字
                    fontSize: 12,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ),
      ],
    );
  }
} 
