import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:dropdown_button2/dropdown_button2.dart';

///用于List<Map<String, dynamic>>类型的下拉菜单
class CustomDropdownMenu extends HookConsumerWidget {
  // 当前选中的值，可以为null表示未选择
  final String? value;

  // 菜单数据
  final List<Map<String, dynamic>> items;

  // 选中回调
  final ValueChanged<String?> onChanged;

  // 自定义样式
  final double? width;
  final double? height;
  final Color? textColor;
  final double? fontSize;
  final String hintText;

  const CustomDropdownMenu({
    super.key,
    this.value, // 改为可空
    required this.items,
    required this.onChanged,
    this.width,
    this.height,
    this.textColor,
    this.fontSize,
    this.hintText = "请选择", // 添加默认提示文本
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 控制菜单展开状态
    final isOpen = useState(false);

    // 检查当前value是否有效且在items列表中
    final String? currentValue = (value != null && items.any((item) => item['name'] == value)) ? value : null;

    return Container(
      width: width,
      height: height ?? 50,
      decoration: BoxDecoration(
        border: isOpen.value ? Border.all(color: const Color(0xFF0C75F8)) : null,
        borderRadius: BorderRadius.circular(50),
        color: const Color(0xFFF3F4F8),
      ),
      child: DropdownButton2<String>(
        value: currentValue, // 可以为 null
        isExpanded: true,
        hint: Text( // 添加 hint
          hintText,
          style: TextStyle(
            fontSize: fontSize ?? 14,
            color: textColor ?? const Color(0xFF8D8E93),
          ),
        ),
        // 下拉菜单样式配置
        dropdownStyleData: DropdownStyleData(
          // 设置弹出菜单的最大高度
          maxHeight: 300,
          // 设置弹出菜单的宽度
          width: width,
          padding: EdgeInsets.zero, // 移除内边距
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: Colors.white,
          ),
          // 菜单位置上移，减少上边距
          offset: const Offset(0, -5),
        ),
        // 图标样式配置
        iconStyleData: IconStyleData(
          icon: Icon(
            isOpen.value ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
            size: 16,
            color: textColor ?? Colors.black.withValues(alpha: 0.8),
          ),
          iconSize: 16,
        ),
        // 按钮样式配置
        buttonStyleData: ButtonStyleData(
          padding: const EdgeInsets.only(right: 12),
          height: height ?? 50,
          width: width,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(50),
            color: const Color(0xFFF3F4F8),
          ),
        ),
        // 菜单项样式配置
        menuItemStyleData: const MenuItemStyleData(
          padding: EdgeInsets.zero, // 移除菜单项内边距
        ),
        underline: Container(), // 移除下划线
        onMenuStateChange: (isOpenState) {
          // 监听菜单状态
          isOpen.value = isOpenState;
        },
        onChanged: (newValue) {
          onChanged(newValue);
        },
        dropdownSearchData: const DropdownSearchData(
          // 禁用搜索功能
          searchController: null,
          // 当searchInnerWidget为null时，searchInnerWidgetHeight必须也为null
          searchInnerWidgetHeight: null,
          // 禁用搜索功能的其他设置
          searchInnerWidget: null,
          searchMatchFn: null,
        ),
        // 禁用默认的hover效果
        customButton: Container(
          height: height ?? 50,
          width: width,
          padding: const EdgeInsets.symmetric(horizontal: 12),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(50),
            color: const Color(0xFFF3F4F8),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                currentValue ?? hintText, // 如果当前值为null，显示提示文本
                style: TextStyle(
                  fontSize: fontSize ?? 14,
                  color: textColor ?? const Color(0xFF8D8E93),
                ),
              ),
              Icon(
                isOpen.value ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                size: 16,
                color: textColor ?? Colors.black.withValues(alpha: 0.8),
              ),
            ],
          ),
        ),
        items: items.map((option) => _buildDropdownMenuItem(option['name'] as String)).toList(),
      ),
    );
  }

  // 构建下拉菜单项
  DropdownMenuItem<String> _buildDropdownMenuItem(String text) {
    return DropdownMenuItem<String>(
      value: text,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12),
        child: Text(
          text,
          style: TextStyle(
            fontSize: fontSize ?? 14,
            color: textColor ?? Colors.black.withValues(alpha: 0.8),
          ),
        ),
      ),
    );
  }
}
