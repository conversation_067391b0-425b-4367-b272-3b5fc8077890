import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:dropdown_button2/dropdown_button2.dart';

/// 增强的下拉菜单组件，支持label和value分离
/// 数据格式：[{'label': '显示名称', 'value': '实际值'}]
class EnhancedDropdownMenu extends HookConsumerWidget {
  // 当前选中的值（对应value字段）
  final String? value;

  // 菜单数据：[{'label': '显示名称', 'value': '实际值'}]
  final List<Map<String, dynamic>> items;

  // 选中回调，返回选中项的value
  final ValueChanged<String?> onChanged;

  // 自定义样式
  final double? width;
  final double? height;
  final Color? textColor;
  final double? fontSize;
  final String hintText;

  const EnhancedDropdownMenu({
    super.key,
    this.value,
    required this.items,
    required this.onChanged,
    this.width,
    this.height,
    this.textColor,
    this.fontSize,
    this.hintText = "请选择",
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 控制菜单展开状态
    final isOpen = useState(false);

    // 根据value查找对应的label
    String? getCurrentLabel() {
      if (value == null) return null;
      try {
        final item = items.firstWhere((item) => item['value'] == value);
        return item['label'] as String?;
      } catch (e) {
        return null;
      }
    }

    final currentLabel = getCurrentLabel();

    return Container(
      width: width,
      height: height ?? 50,
      decoration: BoxDecoration(
        border: isOpen.value ? Border.all(color: const Color(0xFF0C75F8)) : null,
        borderRadius: BorderRadius.circular(50),
        color: const Color(0xFFF3F4F8),
      ),
      child: DropdownButton2<String>(
        value: value, // 使用实际的value值
        isExpanded: true,
        hint: Text(
          hintText,
          style: TextStyle(
            fontSize: fontSize ?? 14,
            color: textColor ?? const Color(0xFF8D8E93),
          ),
        ),
        // 下拉菜单样式配置
        dropdownStyleData: DropdownStyleData(
          maxHeight: 300,
          width: width,
          padding: EdgeInsets.zero,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: Colors.white,
          ),
          offset: const Offset(0, -5),
        ),
        // 图标样式配置
        iconStyleData: IconStyleData(
          icon: Icon(
            isOpen.value ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
            size: 16,
            color: textColor ?? Colors.black.withValues(alpha: 0.8),
          ),
          iconSize: 16,
        ),
        // 按钮样式配置
        buttonStyleData: ButtonStyleData(
          padding: const EdgeInsets.only(right: 12),
          height: height ?? 50,
          width: width,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(50),
            color: const Color(0xFFF3F4F8),
          ),
        ),
        // 菜单项样式配置
        menuItemStyleData: const MenuItemStyleData(
          padding: EdgeInsets.zero,
        ),
        underline: Container(),
        onMenuStateChange: (isOpenState) {
          isOpen.value = isOpenState;
        },
        onChanged: (newValue) {
          onChanged(newValue);
        },
        dropdownSearchData: const DropdownSearchData(
          searchController: null,
          searchInnerWidgetHeight: null,
          searchInnerWidget: null,
          searchMatchFn: null,
        ),
        // 自定义按钮显示
        customButton: Container(
          height: height ?? 50,
          width: width,
          padding: const EdgeInsets.symmetric(horizontal: 12),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(50),
            color: const Color(0xFFF3F4F8),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  currentLabel ?? hintText, // 显示label，如果没有则显示提示文本
                  style: TextStyle(
                    fontSize: fontSize ?? 14,
                    color: currentLabel != null 
                        ? (textColor ?? Colors.black.withValues(alpha: 0.8))
                        : (textColor ?? const Color(0xFF8D8E93)),
                  ),
                  overflow: TextOverflow.ellipsis, // 处理长文本
                ),
              ),
              Icon(
                isOpen.value ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                size: 16,
                color: textColor ?? Colors.black.withValues(alpha: 0.8),
              ),
            ],
          ),
        ),
        items: items.map((option) => _buildDropdownMenuItem(
          option['label'] as String,
          option['value'] as String,
        )).toList(),
      ),
    );
  }

  // 构建下拉菜单项
  DropdownMenuItem<String> _buildDropdownMenuItem(String label, String value) {
    return DropdownMenuItem<String>(
      value: value, // 使用value作为实际值
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12),
        child: Text(
          label, // 显示label
          style: TextStyle(
            fontSize: fontSize ?? 14,
            color: textColor ?? Colors.black.withValues(alpha: 0.8),
          ),
          overflow: TextOverflow.ellipsis,
        ),
      ),
    );
  }
} 
