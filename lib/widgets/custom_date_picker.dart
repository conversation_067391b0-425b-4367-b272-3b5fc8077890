import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class CustomDateRangePicker extends StatelessWidget {
  final DateTime? startDate;
  final DateTime? endDate;
  final Function(DateTime) onStartDateSelected;
  final Function(DateTime) onEndDateSelected;
  final String startLabel;
  final String endLabel;
  final Color? backgroundColor;
  final double? height;

  const CustomDateRangePicker({
    super.key,
    this.startDate,
    this.endDate,
    required this.onStartDateSelected,
    required this.onEndDateSelected,
    this.startLabel = '开始时间',
    this.endLabel = '结束时间',
    this.backgroundColor = Colors.white,
    this.height = 40,
  });

  @override
  Widget build(BuildContext context) {
    // 格式化日期为 yyyy-MM-dd 格式
    String formattedStartDate = startDate != null 
        ? DateFormat('yyyy-MM-dd').format(startDate!)
        : startLabel;
    String formattedEndDate = endDate != null 
        ? DateFormat('yyyy-MM-dd').format(endDate!)
        : endLabel;

    return Container(
      height: height,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(50),
        // boxShadow: [
        //   BoxShadow(
        //     color: Colors.black.withValues(alpha: 0.05),
        //     blurRadius: 2,
        //     offset: const Offset(0, 1),
        //   ),
        // ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(50),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 开始日期选择
            _buildDateSelector(
              context, 
              formattedStartDate, 
              true,
              (date) => onStartDateSelected(date),
              Icons.arrow_drop_up_sharp,
            ),
            // 分隔线
            Container(
              height: 24,
              width: 1,
              color: const Color.fromRGBO(200, 200, 200, 0.5),
            ),
            // 结束日期选择
            _buildDateSelector(
              context, 
              formattedEndDate, 
              false,
              (date) => onEndDateSelected(date),
              Icons.arrow_drop_down_sharp,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateSelector(
    BuildContext context, 
    String text, 
    bool isStartDate,
    Function(DateTime) onDateSelected,
    IconData? icon,
  ) {
    return InkWell(
      onTap: () async {
        final date = await showDatePicker(
          context: context,
          initialDate: isStartDate ? (startDate ?? DateTime.now()) : (endDate ?? DateTime.now()),
          firstDate: DateTime(2000),
          lastDate: DateTime(2100),
          builder: (context, child) {
            return Theme(
              data: Theme.of(context).copyWith(
                colorScheme: const ColorScheme.light(
                  primary: Color(0xFF0C75F8),
                  onPrimary: Colors.white,
                  surface: Colors.white,
                  onSurface: Colors.black,
                ),
                dialogBackgroundColor: Colors.white,
                datePickerTheme: DatePickerThemeData(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                textButtonTheme: TextButtonThemeData(
                  style: TextButton.styleFrom(
                    foregroundColor: const Color(0xFF0C75F8),
                  ),
                ),
              ),
              child: child!,
            );
          },
          locale: const Locale('zh', 'CN'),
        );

        if (date != null) {
          onDateSelected(date);
        }
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (icon != null)
              Icon(
                icon,
                size: 24,
                color: const Color.fromRGBO(150, 150, 150, 1),
              ),
            const SizedBox(width: 8),
            Text(
              text,
              style: const TextStyle(
                fontSize: 14,
                color: Color.fromRGBO(100, 100, 100, 1),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
