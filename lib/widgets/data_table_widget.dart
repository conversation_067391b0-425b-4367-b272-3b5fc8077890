import 'package:data_table_2/data_table_2.dart';
import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:frontend_re/widgets/custom_border_button.dart';

/// 分组数据表格组件，支持分组、展开/折叠、复选框选择等功能
class DataTableWidget<T> extends HookConsumerWidget {
  const DataTableWidget({
    super.key,
    required this.columns,
    required this.data,
    required this.rowBuilder,
    required this.minWidth,
    this.columnSpacing = 0,
    this.horizontalMargin = 12,
    this.isHorizontalScrollBarVisible = true,
    this.horizontalScrollController,
    this.groupBy,
    this.groupHeaderBuilder,
    this.headingRowHeight = 56.0,
    this.dataRowHeight = 48.0,
    this.backgroundColor = Colors.white,
    this.showCheckbox = false,
    this.onSelectionChanged,
    // 分页相关参数
    this.enablePagination = false,
    this.totalCount,
    this.pageSize = 10,
    this.currentPage = 1,
    this.onPageChanged,
    this.headingTextStyle,
    this.dataTextStyle,
    this.groupHeaderTextStyle,
    // 空数据状态相关参数
    this.noDataImagePath,
    this.noDataText,
    this.noDataButtonText,
    this.noDataButtonEvent,
  });

  /// 表格列定义
  final List<DataColumn2> columns;
  
  /// 表格数据
  final List<T> data;
  
  /// 行构建器，用于构建每行的基本单元格
  final DataRow Function(T data, int index, bool isHovered) rowBuilder;
  
  /// 表格最小宽度
  final double minWidth;
  
  /// 列间距
  final double columnSpacing;
  
  /// 水平边距
  final double horizontalMargin;
  
  /// 是否显示水平滚动条
  final bool isHorizontalScrollBarVisible;
  
  /// 水平滚动控制器
  final ScrollController? horizontalScrollController;
  
  /// 分组函数，接收数据项返回分组键
  final dynamic Function(T data)? groupBy;
  
  /// 分组标题行构建器
  final DataRow Function(dynamic groupKey, int groupSize, bool isExpanded)? groupHeaderBuilder;

  /// 表头行高度
  final double headingRowHeight;

  /// 数据行高度
  final double dataRowHeight;
  
  /// 背景颜色
  final Color backgroundColor;
  
  /// 是否显示复选框
  final bool showCheckbox;
  
  /// 选中行变化时的回调
  final Function(Set<int> selectedRows)? onSelectionChanged;
  
  /// 是否启用分页
  final bool enablePagination;
  
  /// 数据总条数
  final int? totalCount;
  
  /// 每页显示条数
  final int pageSize;
  
  /// 当前页码
  final int currentPage;
  
  /// 页码变化回调
  final Function(int page, int pageSize)? onPageChanged;

  /// 表头文本样式
  final TextStyle? headingTextStyle;

  /// 数据行文本样式
  final TextStyle? dataTextStyle;

  /// 分组标题文本样式
  final TextStyle? groupHeaderTextStyle;

  /// 空数据状态图片路径
  final String? noDataImagePath;

  /// 空数据状态文字
  final String? noDataText;

  /// 空数据状态按钮文字
  final String? noDataButtonText;

  /// 空数据状态按钮事件
  final Function()? noDataButtonEvent;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 状态定义
    final hoveredRow = useState<int?>(null);
    final selectedRows = useState<Set<int>>({});
    final expandedGroups = useState<Map<dynamic, bool>>({}); // 分组展开状态
    final selectedGroups = useState<Set<dynamic>>({}); // 选中的分组

    // 为表头和内容创建独立的滚动控制器，并同步它们
    final headerScrollController = useScrollController();
    final bodyScrollController = horizontalScrollController ?? useScrollController();
    
    // 使用简单的标记来避免循环同步，而不是状态管理
    final isSyncing = useRef(false);
    
    // 分页控制器
    final currentPageState = useState<int>(currentPage);
    final pageSizeState = useState<int>(pageSize); // 添加页面大小状态
    
    // 分组数据处理
    final groupedData = _processGroupData(expandedGroups);
    
    // 行到分组的映射关系
    final rowToGroupMapping = _createRowToGroupMapping(groupedData);
    
    // 预先构建表头列，以便在虚拟行中使用
    _buildColumns(selectedRows, selectedGroups, groupedData, context, isHeaderOnly: true);
    
    // 构建表格
    return ClipRRect(
      borderRadius: const BorderRadius.only(
        topLeft: Radius.circular(20),
        topRight: Radius.circular(20),
      ),
      child: Container(
        decoration: BoxDecoration(
          color: backgroundColor,
        ),
        child: Column(
          children: [
            // 表头部分
            SizedBox(
              height: headingRowHeight,
              child: ScrollbarTheme(
                data: const ScrollbarThemeData(),
                child: Theme(
                  data: Theme.of(context).copyWith(
                    cardTheme: const CardThemeData(
                      elevation: 0,
                      margin: EdgeInsets.zero,
                      clipBehavior: Clip.antiAlias,
                    ),
                    dividerColor: Colors.transparent,
                  ),
                  child: DataTable2(
                    minWidth: minWidth,
                    columnSpacing: columnSpacing,
                    horizontalMargin: horizontalMargin,
                    isHorizontalScrollBarVisible: false,
                    horizontalScrollController: headerScrollController,
                    columns: _buildColumns(selectedRows, selectedGroups, groupedData, context, isHeaderOnly: true),
                    rows: const [],
                    dividerThickness: 0,
                    headingRowHeight: headingRowHeight,
                    dataRowHeight: dataRowHeight,
                    headingRowColor: WidgetStateProperty.resolveWith<Color?>((states) => null),
                  ),
                ),
              ),
            ),
            // 内容部分
            Expanded(
              child: data.isEmpty && noDataImagePath != null
                  ? _buildEmptyState(context)
                  : ScrollbarTheme(
                      data: const ScrollbarThemeData(),
                      child: Theme(
                        data: Theme.of(context).copyWith(
                          cardTheme: const CardThemeData(
                            elevation: 0,
                            margin: EdgeInsets.zero,
                            clipBehavior: Clip.antiAlias,
                          ),
                          dividerColor: Colors.transparent,
                        ),
                        child: NotificationListener<ScrollNotification>(
                          onNotification: (notification) {
                            // 内容区滚动结束时同步表头位置
                            if (notification is ScrollEndNotification && 
                                headerScrollController.hasClients && 
                                !isSyncing.value) {
                              isSyncing.value = true;
                              headerScrollController.jumpTo(bodyScrollController.offset);
                              isSyncing.value = false;
                            }
                            return true;
                          },
                          child: DataTable2(
                            minWidth: minWidth,
                            columnSpacing: columnSpacing,
                            horizontalMargin: horizontalMargin,
                            isHorizontalScrollBarVisible: isHorizontalScrollBarVisible,
                            horizontalScrollController: bodyScrollController,
                            columns: _buildColumns(selectedRows, selectedGroups, groupedData, context),
                            rows: _buildRows(
                              hoveredRow, 
                              selectedRows, 
                              expandedGroups, 
                              selectedGroups, 
                              groupedData, 
                              rowToGroupMapping,
                              context,
                            ),
                            dividerThickness: 0,
                            headingRowHeight: 0,
                            dataRowHeight: dataRowHeight,
                            headingRowColor: WidgetStateProperty.resolveWith<Color?>((states) => null),
                            showCheckboxColumn: false,
                          ),
                        ),
                      ),
                    ),
            ),
            // 分页控件
            if (enablePagination && totalCount != null)
              _buildPagination(context, currentPageState, pageSizeState, selectedRows.value.length),
          ],
        ),
      ),
    );
  }
  
  /// 处理分组数据
  Map<dynamic, List<T>>? _processGroupData(ValueNotifier<Map<dynamic, bool>> expandedGroups) {
    if (groupBy == null) return null;
    
    final groups = <dynamic, List<T>>{};
    for (var item in data) {
      final key = groupBy!(item);
      groups.putIfAbsent(key, () => []).add(item);
      
      // 默认所有分组都是展开的
      if (!expandedGroups.value.containsKey(key)) {
        expandedGroups.value = {
          ...expandedGroups.value,
          key: true, // 默认展开
        };
      }
    }
    return groups;
  }
  
  /// 创建行到分组的映射关系
  Map<int, dynamic> _createRowToGroupMapping(Map<dynamic, List<T>>? groupedData) {
    if (groupedData == null) return <int, dynamic>{};
    
    final mapping = <int, dynamic>{};
    int globalIndex = 0;
    
    groupedData.forEach((groupKey, items) {
      for (int i = 0; i < items.length; i++) {
        mapping[globalIndex] = groupKey;
        globalIndex++;
      }
    });
    
    return mapping;
  }
  
  /// 获取分组下的所有行索引
  Set<int> _getGroupRowIndices(dynamic groupKey, Map<dynamic, List<T>>? groupedData) {
    if (groupedData == null) return {};
    
    final indices = <int>{};
    int globalIndex = 0;
    
    groupedData.forEach((key, items) {
      if (key == groupKey) {
        for (int i = 0; i < items.length; i++) {
          indices.add(globalIndex + i);
        }
      }
      globalIndex += items.length;
    });
    
    return indices;
  }
  
  /// 检查分组是否全选
  bool _isGroupFullySelected(dynamic groupKey, Set<int> selectedRows, Map<dynamic, List<T>>? groupedData) {
    final groupIndices = _getGroupRowIndices(groupKey, groupedData);
    if (groupIndices.isEmpty) return false;
    return groupIndices.every((index) => selectedRows.contains(index));
  }
  
  /// 检查分组是否部分选中
  bool _isGroupPartiallySelected(dynamic groupKey, Set<int> selectedRows, Map<dynamic, List<T>>? groupedData) {
    final groupIndices = _getGroupRowIndices(groupKey, groupedData);
    if (groupIndices.isEmpty) return false;
    return groupIndices.any((index) => selectedRows.contains(index)) && 
           !groupIndices.every((index) => selectedRows.contains(index));
  }
  
  /// 构建表格列
  List<DataColumn2> _buildColumns(
    ValueNotifier<Set<int>> selectedRows,
    ValueNotifier<Set<dynamic>> selectedGroups,
    Map<dynamic, List<T>>? groupedData,
    BuildContext context,
    {bool isHeaderOnly = false}
  ) {
    final allColumns = <DataColumn2>[];
    
    // 处理复选框列
    if (showCheckbox) {
      if (isHeaderOnly) {
        // 表头部分：添加带全选功能的复选框
        allColumns.add(
          DataColumn2(
            label: Checkbox(
              value: data.isNotEmpty && selectedRows.value.length == data.length,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(4),
              ),
              side: const BorderSide(
                color: Color(0xFF2F64E8),
                width: 1.5,
              ),
              onChanged: (selected) {
                if (selected != null) {
                  if (selected) {
                    // 全选
                    selectedRows.value = Set.from(List.generate(data.length, (i) => i));
                    if (groupedData != null) {
                      // 同时选中所有分组
                      selectedGroups.value = Set.from(groupedData.keys);
                    }
                    onSelectionChanged?.call(selectedRows.value);
                  } else {
                    // 全不选
                    selectedRows.value = {};
                    selectedGroups.value = {};
                    onSelectionChanged?.call(selectedRows.value);
                  }
                }
              },
            ),
            size: ColumnSize.S,
            fixedWidth: 50,
          ),
        );
      } else {
        // 内容部分：添加空占位列（复选框在行级别添加）
        allColumns.add(
          const DataColumn2(
            label: SizedBox(),
            size: ColumnSize.S,
            fixedWidth: 50,
          ),
        );
      }
    }
    
    // 添加原有列
    allColumns.addAll(columns);
    
    // 添加样式处理
    if (headingTextStyle != null) {
      // 处理复选框列头
      if (showCheckbox && allColumns.isNotEmpty) {
        // 保持原有复选框列不变
      }
      
      // 处理其他列头
      for (int i = showCheckbox ? 1 : 0; i < allColumns.length; i++) {
        final column = allColumns[i];
        if (column.label is Text) {
          final Text textWidget = column.label as Text;
          allColumns[i] = DataColumn2(
            label: Text(
              textWidget.data ?? '',
              style: headingTextStyle!.merge(textWidget.style),
            ),
            size: column.size,
            fixedWidth: column.fixedWidth,
          );
        }
      }
    }
    
    return allColumns;
  }
  
  /// 构建表格行
  List<DataRow> _buildRows(
    ValueNotifier<int?> hoveredRow,
    ValueNotifier<Set<int>> selectedRows,
    ValueNotifier<Map<dynamic, bool>> expandedGroups,
    ValueNotifier<Set<dynamic>> selectedGroups,
    Map<dynamic, List<T>>? groupedData,
    Map<int, dynamic> rowToGroupMapping,
    BuildContext context,
  ) {
    // 如果没有分组，直接返回原始行
    if (groupBy == null || groupHeaderBuilder == null || groupedData == null) {
      return List.generate(data.length, (index) {
        final isHovered = hoveredRow.value == index;
        return _buildDataRow(
          data[index], 
          index, 
          isHovered,
          selectedRows,
          groupedData,
          rowToGroupMapping,
          selectedGroups,
          context,
          hoveredRow,
        );
      });
    }
    
    // 如果有分组，构建分组行和数据行
    final allRows = <DataRow>[];
    int globalIndex = 0;
    
    groupedData.forEach((groupKey, groupItems) {
      final isExpanded = expandedGroups.value[groupKey] ?? true;
      final isGroupSelected = selectedGroups.value.contains(groupKey);
      final isPartiallySelected = !isGroupSelected && 
          _isGroupPartiallySelected(groupKey, selectedRows.value, groupedData);
      
      // 添加分组标题行
      allRows.add(_buildGroupHeaderRow(
        groupKey, 
        groupItems, 
        isExpanded, 
        isGroupSelected,
        isPartiallySelected,
        expandedGroups,
        selectedRows,
        selectedGroups,
        groupedData,
        context,
      ));
      
      // 如果分组是展开的，添加该分组的所有数据行
      if (isExpanded) {
        for (int i = 0; i < groupItems.length; i++) {
          final isHovered = hoveredRow.value == globalIndex;
          allRows.add(_buildDataRow(
            groupItems[i], 
            globalIndex, 
            isHovered,
            selectedRows,
            groupedData,
            rowToGroupMapping,
            selectedGroups,
            context,
            hoveredRow,
            isIndented: true,
          ));
          globalIndex++;
        }
      } else {
        // 即使折叠了，也需要增加索引
        globalIndex += groupItems.length;
      }
    });
    
    return allRows;
  }
  
  /// 构建分组标题行
  DataRow _buildGroupHeaderRow(
    dynamic groupKey,
    List<T> groupItems,
    bool isExpanded,
    bool isGroupSelected,
    bool isPartiallySelected,
    ValueNotifier<Map<dynamic, bool>> expandedGroups,
    ValueNotifier<Set<int>> selectedRows,
    ValueNotifier<Set<dynamic>> selectedGroups,
    Map<dynamic, List<T>>? groupedData,
    BuildContext context,
  ) {
    // 获取基础分组行
    final groupHeaderRow = groupHeaderBuilder!(groupKey, groupItems.length, isExpanded);
    
    // 构建单元格
    final cells = <DataCell>[];
    final int actualColumnsCount = columns.length + (showCheckbox ? 1 : 0);
    
    // 添加分组复选框
    if (showCheckbox) {
      cells.add(DataCell(
        Checkbox(
          value: isGroupSelected,
          tristate: isPartiallySelected,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(4),
          ),
          side: const BorderSide(
            color: Color(0xFF2F64E8),
            width: 1.5,
          ),
          onChanged: (selected) {
            if (selected != null) {
              // 获取该分组下所有行索引
              final groupIndices = _getGroupRowIndices(groupKey, groupedData);
              
              // 更新选择状态
              final newSelectedRows = {...selectedRows.value};
              final newSelectedGroups = {...selectedGroups.value};
              
              if (selected) {
                newSelectedRows.addAll(groupIndices);
                newSelectedGroups.add(groupKey);
              } else {
                newSelectedRows.removeAll(groupIndices);
                newSelectedGroups.remove(groupKey);
              }
              
              // 更新状态
              selectedRows.value = newSelectedRows;
              selectedGroups.value = newSelectedGroups;
              onSelectionChanged?.call(newSelectedRows);
            }
          },
        ),
      ));
    }
    
    // 添加展开/折叠图标和标题
    if (groupHeaderRow.cells.isNotEmpty) {
      Widget titleWidget = groupHeaderRow.cells.first.child;
      
      // 应用分组标题样式
      if (groupHeaderTextStyle != null && titleWidget is Text) {
        titleWidget = Text(
          titleWidget.data ?? '',
          style: groupHeaderTextStyle!.merge(titleWidget.style),
        );
      }
      
      cells.add(
        DataCell(
          Row(
            children: [
              Icon(
                isExpanded ? Icons.keyboard_arrow_down : Icons.keyboard_arrow_right,
                size: 20,
              ),
              const SizedBox(width: 8),
              Flexible(
                child: titleWidget,
              ),
            ],
          ),
        ),
      );
      
      // 添加其余单元格
      if (groupHeaderRow.cells.length > 1) {
        cells.addAll(groupHeaderRow.cells.skip(1));
      }
    }
    
    // 确保单元格数量正确
    _adjustCellCount(cells, actualColumnsCount);
    
    // 构建分组行
    return DataRow(
      onSelectChanged: (_) {
        // 点击整行时切换展开/折叠状态
        expandedGroups.value = {
          ...expandedGroups.value,
          groupKey: !isExpanded,
        };
      },
      color: WidgetStateProperty.resolveWith<Color?>((states) {
        if (states.contains(WidgetState.hovered)) {
          // 鼠标悬停时的背景颜色
          return Colors.grey.withOpacity(0.1);
        }
        if (isGroupSelected || isPartiallySelected) {
          return Theme.of(context).colorScheme.primary.withValues(alpha: 0.07);
        }
        return null;
      }),
      cells: cells.map((cell) => DataCell(
        MouseRegion(
          cursor: SystemMouseCursors.click, // 设置鼠标光标为点击样式
          child: cell.child,
        ),
      )).toList(),
    );
  }
  
  /// 构建数据行
  DataRow _buildDataRow(
    T data,
    int index,
    bool isHovered,
    ValueNotifier<Set<int>> selectedRows,
    Map<dynamic, List<T>>? groupedData,
    Map<int, dynamic> rowToGroupMapping,
    ValueNotifier<Set<dynamic>> selectedGroups,
    BuildContext context,
    ValueNotifier<int?> hoveredRow,
    {bool isIndented = false}
  ) {
    final row = rowBuilder(data, index, isHovered);
    final bool isSelected = selectedRows.value.contains(index);
    
    // 创建单元格列表
    final newCells = <DataCell>[];
    
    // 添加复选框单元格（带缩进效果）
    if (showCheckbox) {
      newCells.add(DataCell(
        Padding(
          padding: EdgeInsets.only(left: isIndented ? 20 : 0),
          child: Checkbox(
            value: isSelected,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(4),
            ),
            side: const BorderSide(
              color: Color(0xFF2F64E8),
              width: 1.5,
            ),
            onChanged: (selected) => _handleRowSelection(
              index,
              selected,
              selectedRows,
              selectedGroups,
              rowToGroupMapping,
              groupedData,
            ),
          ),
        ),
      ));
    }
    
    // 添加原始单元格
    for (var cell in row.cells) {
      newCells.add(DataCell(
        MouseRegion(
          onEnter: (_) => hoveredRow.value = index,
          onExit: (_) => hoveredRow.value = null,
          child: Container(
            alignment: Alignment.centerLeft,
            child: cell.child,
          ),
        ),
      ));
    }
    
    // 添加文本样式处理
    if (dataTextStyle != null) {
      for (int i = 0; i < newCells.length; i++) {
        final cell = newCells[i];
        // 跳过复选框列
        if (i == 0 && showCheckbox) continue;
        
        // 找到Text组件并应用样式
        Widget cellChild = cell.child;
        if (cellChild is MouseRegion) {
          final mouseRegionChild = cellChild.child;
          if (mouseRegionChild is Container) {
            final containerChild = mouseRegionChild.child;
            if (containerChild is Text) {
              // 应用样式并保持其他属性
              newCells[i] = DataCell(
                MouseRegion(
                  onEnter: (_) => hoveredRow.value = index,
                  onExit: (_) => hoveredRow.value = null,
                  child: Container(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      containerChild.data ?? '',
                      style: dataTextStyle!.merge(containerChild.style),
                    ),
                  ),
                ),
              );
            }
          }
        }
      }
    }
    
    // 行选择配置
    return DataRow(
      color: WidgetStateProperty.resolveWith<Color?>((states) {
        if (isSelected) {
          return Theme.of(context).colorScheme.primary.withValues(alpha: 0.1);
        }
        return null;
      }),
      cells: newCells,
    );
  }
  
  /// 处理行选择
  void _handleRowSelection(
    int index,
    bool? selected,
    ValueNotifier<Set<int>> selectedRows,
    ValueNotifier<Set<dynamic>> selectedGroups,
    Map<int, dynamic> rowToGroupMapping,
    Map<dynamic, List<T>>? groupedData,
  ) {
    if (selected != null) {
      final newSelectedRows = {...selectedRows.value};
      if (selected) {
        newSelectedRows.add(index);
      } else {
        newSelectedRows.remove(index);
      }
      selectedRows.value = newSelectedRows;
      onSelectionChanged?.call(newSelectedRows);
      
      // 处理分组选择状态
      _updateGroupSelectionState(
        index, 
        selectedRows.value, 
        selectedGroups, 
        rowToGroupMapping,
        groupedData,
      );
    }
  }
  
  /// 更新分组选择状态
  void _updateGroupSelectionState(
    int index,
    Set<int> selectedRowsValue,
    ValueNotifier<Set<dynamic>> selectedGroups,
    Map<int, dynamic> rowToGroupMapping,
    Map<dynamic, List<T>>? groupedData,
  ) {
    if (groupedData != null) {
      final groupKey = rowToGroupMapping[index];
      if (groupKey != null) {
        final newSelectedGroups = {...selectedGroups.value};
        if (_isGroupFullySelected(groupKey, selectedRowsValue, groupedData)) {
          newSelectedGroups.add(groupKey);
        } else {
          newSelectedGroups.remove(groupKey);
        }
        selectedGroups.value = newSelectedGroups;
      }
    }
  }
  
  /// 调整单元格数量，确保符合表头列数
  void _adjustCellCount(List<DataCell> cells, int targetCount) {
    // 如果单元格数量少于表头列数，添加空单元格
    while (cells.length < targetCount) {
      cells.add(const DataCell(SizedBox()));
    }
    
    // 如果单元格数量多于表头列数，只保留前targetCount个单元格
    if (cells.length > targetCount) {
      cells.removeRange(targetCount, cells.length);
    }
  }
  
  /// 构建空数据状态组件
  Widget _buildEmptyState(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (noDataImagePath != null)
            Image.asset(
              noDataImagePath!,
              width: 400,
            ),
          const SizedBox(height: 6),
          if (noDataText != null)
            Text(
              noDataText!,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
                fontWeight: FontWeight.bold,
              ),
            ),
          const SizedBox(height: 6),
          if (noDataButtonText != null && noDataButtonEvent != null)
            CustomBorderButton(
              height: 40,
              iconPath: 'assets/svg/add.svg',
              backgroundColor: const Color(0xFF0C75F8),
              textColor: Colors.white,
              iconColor: Colors.white,
              text: noDataButtonText!,
              onPressed: noDataButtonEvent!,
            ),
        ],
      ),
    );
  }

  /// 构建分页控件
  Widget _buildPagination(
    BuildContext context, 
    ValueNotifier<int> currentPageState, 
    ValueNotifier<int> pageSizeState,
    int selectedRowsCount,
  ) {
    final totalPages = (totalCount! / pageSizeState.value).ceil();
    
    return Container(
      height: 56,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
        border: Border(
          top: BorderSide(
            color: Colors.grey.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 左侧显示总条数和选中条数
          Row(
            children: [
              if (selectedRowsCount > 0)
                Text(
                  '已选择 $selectedRowsCount 条，',
                  style: TextStyle(
                    color: Theme.of(context).primaryColor,
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              Text(
                '共 $totalCount 条数据',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 14,
                ),
              ),
            ],
          ),
          
          // 右侧分页控件
          Row(
            children: [
              // 上一页按钮
              IconButton(
                icon: const Icon(Icons.keyboard_arrow_left),
                onPressed: currentPageState.value > 1
                    ? () {
                        final newPage = currentPageState.value - 1;
                        currentPageState.value = newPage;
                        onPageChanged?.call(newPage, pageSizeState.value);
                      }
                    : null,
                color: currentPageState.value > 1 ? Theme.of(context).primaryColor : Colors.grey,
              ),
              
              // 页码显示
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  '${currentPageState.value} / $totalPages',
                  style: TextStyle(
                    color: Theme.of(context).primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              
              // 下一页按钮
              IconButton(
                icon: const Icon(Icons.keyboard_arrow_right),
                onPressed: currentPageState.value < totalPages
                    ? () {
                        final newPage = currentPageState.value + 1;
                        currentPageState.value = newPage;
                        onPageChanged?.call(newPage, pageSizeState.value);
                      }
                    : null,
                color: currentPageState.value < totalPages ? Theme.of(context).primaryColor : Colors.grey,
              ),
              
              // 每页条数选择
              const SizedBox(width: 16),
              Text(
                '每页',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 14,
                ),
              ),
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton2<int>(
                    value: pageSizeState.value,
                    items: [10, 20, 50, 100].map((size) => DropdownMenuItem<int>(
                      value: size,
                      child: Text(
                        '$size',
                        style: const TextStyle(
                          fontSize: 14,
                        ),
                      ),
                    )).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        pageSizeState.value = value; // 更新本地状态
                        currentPageState.value = 1; // 切换每页条数时重置为第一页
                        onPageChanged?.call(1, value);
                      }
                    },
                    buttonStyleData: ButtonStyleData(
                      height: 36,
                      width: 80,
                      padding: const EdgeInsets.symmetric(horizontal: 4),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(4),
                        color: Colors.transparent,
                      ),
                    ),
                    iconStyleData: const IconStyleData(
                      icon: Icon(Icons.arrow_drop_down, size: 20),
                      iconSize: 20,
                      iconEnabledColor: Colors.grey,
                    ),
                    dropdownStyleData: DropdownStyleData(
                      maxHeight: 200,
                      width: 80,
                      padding: EdgeInsets.zero,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(4),
                        color: Colors.white,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      offset: const Offset(0, 0),
                      scrollbarTheme: ScrollbarThemeData(
                        radius: const Radius.circular(2),
                        thickness: WidgetStateProperty.all(6),
                        thumbVisibility: WidgetStateProperty.all(true),
                      ),
                    ),
                    menuItemStyleData: const MenuItemStyleData(
                      height: 40,
                      padding: EdgeInsets.symmetric(horizontal: 12),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
