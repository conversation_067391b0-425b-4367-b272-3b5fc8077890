import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class CheckBoxItem {
  final String label; // 显示的文本
  final String value; // 实际的值

  const CheckBoxItem({
    required this.label,
    required this.value,
  });
}

class CustomCheckboxGroup extends ConsumerWidget {
  // 选项列表
  final List<CheckBoxItem> items;
  // 当前选中的值
  final String selectedValue;
  // 值改变回调
  final Function(String) onChanged;
  // 自定义样式参数
  final Color? checkColor;
  final Color? activeColor;
  final TextStyle? textStyle;
  final EdgeInsetsGeometry? padding;
  final double? spacing;
  final MainAxisAlignment mainAxisAlignment;

  const CustomCheckboxGroup({
    super.key,
    required this.items,
    required this.selectedValue,
    required this.onChanged,
    this.checkColor,
    this.activeColor,
    this.textStyle,
    this.padding = const EdgeInsets.symmetric(horizontal: 8.0),
    this.spacing = 16.0,
    this.mainAxisAlignment = MainAxisAlignment.start,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Row(
      mainAxisAlignment: mainAxisAlignment,
      children: items.map((item) {
        return Padding(
          padding: padding!,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Checkbox(
                value: selectedValue == item.value,
                onChanged: (bool? value) {
                  if (value == true) {
                    onChanged(item.value);
                  }
                },
                checkColor: checkColor,
                activeColor: activeColor,
              ),
              SizedBox(width: spacing! / 2),
              Text(
                item.label,
                style: textStyle ?? Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
        );
      }).toList(),
    );
  }
}
