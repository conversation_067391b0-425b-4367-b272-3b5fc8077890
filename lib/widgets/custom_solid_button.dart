import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class CustomSolidButton extends StatefulWidget {
  final VoidCallback? onPressed;
  final String? iconPath;  // icon资源路径
  final double? iconSize;  // icon大小
  final Color?  iconColor;
  final Color?  iconColorHover;
  final Color?  iconBackgroundColor;
  final Color?  iconBackgroundColorHover;
  final String? text;  // 按钮文本
  final Color?  textColor;
  final Color?  textColorHover;
  final double? width;  // 按钮宽度，如果为null则自适应内容宽度
  final double? height;  // 按钮高度
  final Color?  backgroundColor;  // 背景颜色
  final Color?  backgroundColorHover;  // 背景颜色
  final TextDirection? textDirection;
  final bool? isCenter;
  final double? textSize;
  // 文字和icon的间隔
  final double? iconTextSpacing;
  const CustomSolidButton({
    super.key,
    this.onPressed,
    this.iconPath,
    this.text,
    this.iconSize = 16,
    this.width,
    this.height,
    this.backgroundColor = const Color(0xFF1E1C1D),
    this.backgroundColorHover = const Color(0xFF0C75F8),
    this.iconColor = const Color(0xFF000000),
    this.iconBackgroundColor = const Color.fromARGB(255, 255, 255, 255),
    this.textColor = const Color(0xFFFFFFFF),
    this.textColorHover = const Color(0xFFFFFFFF),
    this.textSize = 14,
    this.textDirection,
    this.isCenter = false, 
    this.iconColorHover = const Color(0xFF0C75F8),
    this.iconBackgroundColorHover = const Color(0xFFFFFFFF),
    this.iconTextSpacing = 10,
  });

  @override
  CustomSolidButtonState createState() => CustomSolidButtonState();
}

class CustomSolidButtonState extends State<CustomSolidButton> {
  // 将 _isHovered 移到 build 方法外部，作为 State 的成员变量
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onPressed,
      child: Container(
        width: widget.width,
        height: widget.height,
        decoration: BoxDecoration(
          color: _isHovered ? widget.backgroundColorHover : widget.backgroundColor,
          borderRadius: BorderRadius.circular(50),
        ),
        child: MouseRegion(
          cursor: SystemMouseCursors.click,
          onEnter: (_) => setState(() => _isHovered = true),
          onExit: (_) => setState(() => _isHovered = false),
          child: Padding(
            padding: widget.textDirection == TextDirection.rtl
                ? const EdgeInsets.only(
                    left: 16,
                    right: 6,
                    top: 6,
                    bottom: 6)
                : const EdgeInsets.only(
                    right: 16,
                    left: 6,
                    top: 6,
                    bottom: 6),
            child: Row(
              mainAxisSize: widget.width == null ? MainAxisSize.min : MainAxisSize.max,
              mainAxisAlignment: widget.isCenter == true ? MainAxisAlignment.center : MainAxisAlignment.start,
              textDirection: widget.textDirection,
              children: [
                Container(
                  width: 30,
                  height: 30,
                  decoration: BoxDecoration(
                    color: _isHovered ? widget.iconBackgroundColorHover : widget.iconBackgroundColor,
                    borderRadius: BorderRadius.circular(50),
                  ),
                  child: Center(
                    child: SvgPicture.asset(
                      widget.iconPath!,
                      height: widget.iconSize!,
                      width: widget.iconSize!,
                      colorFilter: ColorFilter.mode(
                        _isHovered ? widget.iconColorHover! : widget.iconColor!,
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                ),
                SizedBox(width: widget.iconTextSpacing),
                Text(
                  widget.text!,
                  style: TextStyle(
                    color: _isHovered ? widget.textColorHover : widget.textColor,
                    fontSize: widget.textSize,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
