import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

/// 可调整大小的对话框组件
/// 
/// 这是一个自定义的对话框组件，支持通过拖拽边缘来调整大小
/// 
/// 使用示例：
/// ```dart
/// showDialog(
///   context: context,
///   builder: (context) => ResizableDialog(
///     child: YourDialogContent(),
///   ),
/// );
/// ```
class ResizableDialog extends HookWidget {
  const ResizableDialog({
    super.key,
    required this.child,
    this.initialWidth = 600,
    this.initialHeight = 600,
    this.minWidth = 600,
    this.minHeight = 600,
    this.maxWidth = 800,
    this.maxHeight = 700,
    this.resizeHandleSize = 20,
    this.enableResize = true,
    this.showResizeHandle = true,
  });

  /// 对话框内容
  final Widget child;
  
  /// 初始宽度
  final double initialWidth;
  
  /// 初始高度
  final double initialHeight;
  
  /// 最小宽度
  final double minWidth;
  
  /// 最小高度
  final double minHeight;
  
  /// 最大宽度
  final double maxWidth;
  
  /// 最大高度
  final double maxHeight;
  
  /// 调整大小手柄的大小
  final double resizeHandleSize;
  
  /// 是否启用调整大小功能
  final bool enableResize;
  
  /// 是否显示右下角的调整大小手柄
  final bool showResizeHandle;

  @override
  Widget build(BuildContext context) {
    final dialogWidth = useState(initialWidth);
    final dialogHeight = useState(initialHeight);
    final isDragging = useState(false);

    return Center(
      child: Material(
        color: Colors.transparent,
        child: SizedBox(
          width: dialogWidth.value,
          height: dialogHeight.value,
          child: Stack(
            children: [
              // 对话框主体
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surfaceContainer,
                    borderRadius: BorderRadius.circular(16),
                    border: isDragging.value
                        ? Border.all(
                            color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.5),
                            width: 2,
                          )
                        : null,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: child,
                ),
              ),
              
              if (enableResize) ...[
                // 右下角拖拽手柄
                if (showResizeHandle)
                  Positioned(
                    right: 0,
                    bottom: 0,
                    child: _buildResizeHandle(
                      context: context,
                      cursor: SystemMouseCursors.resizeDownRight,
                      isDragging: isDragging,
                      onPanUpdate: (details) {
                        final newWidth = dialogWidth.value + details.delta.dx;
                        final newHeight = dialogHeight.value + details.delta.dy;
                        
                        dialogWidth.value = newWidth.clamp(minWidth, maxWidth);
                        dialogHeight.value = newHeight.clamp(minHeight, maxHeight);
                      },
                      showIcon: true,
                    ),
                  ),
                
                // 右边缘拖拽
                Positioned(
                  right: 0,
                  top: resizeHandleSize,
                  bottom: resizeHandleSize,
                  child: _buildResizeEdge(
                    context: context,
                    cursor: SystemMouseCursors.resizeLeft,
                    isDragging: isDragging,
                    onPanUpdate: (details) {
                      final newWidth = dialogWidth.value + details.delta.dx;
                      dialogWidth.value = newWidth.clamp(minWidth, maxWidth);
                    },
                    width: 8,
                  ),
                ),
                
                // 底边缘拖拽
                Positioned(
                  bottom: 0,
                  left: resizeHandleSize,
                  right: resizeHandleSize,
                  child: _buildResizeEdge(
                    context: context,
                    cursor: SystemMouseCursors.resizeUp,
                    isDragging: isDragging,
                    onPanUpdate: (details) {
                      final newHeight = dialogHeight.value + details.delta.dy;
                      dialogHeight.value = newHeight.clamp(minHeight, maxHeight);
                    },
                    height: 8,
                  ),
                ),
                
                // 左边缘拖拽
                Positioned(
                  left: 0,
                  top: resizeHandleSize,
                  bottom: resizeHandleSize,
                  child: _buildResizeEdge(
                    context: context,
                    cursor: SystemMouseCursors.resizeRight,
                    isDragging: isDragging,
                    onPanUpdate: (details) {
                      final newWidth = dialogWidth.value - details.delta.dx;
                      dialogWidth.value = newWidth.clamp(minWidth, maxWidth);
                    },
                    width: 8,
                  ),
                ),
                
                // 上边缘拖拽
                Positioned(
                  top: 0,
                  left: resizeHandleSize,
                  right: resizeHandleSize,
                  child: _buildResizeEdge(
                    context: context,
                    cursor: SystemMouseCursors.resizeDown,
                    isDragging: isDragging,
                    onPanUpdate: (details) {
                      final newHeight = dialogHeight.value - details.delta.dy;
                      dialogHeight.value = newHeight.clamp(minHeight, maxHeight);
                    },
                    height: 8,
                  ),
                ),
                
                // 左上角拖拽手柄
                Positioned(
                  left: 0,
                  top: 0,
                  child: _buildResizeHandle(
                    context: context,
                    cursor: SystemMouseCursors.resizeUpLeft,
                    isDragging: isDragging,
                    onPanUpdate: (details) {
                      final newWidth = dialogWidth.value - details.delta.dx;
                      final newHeight = dialogHeight.value - details.delta.dy;
                      
                      dialogWidth.value = newWidth.clamp(minWidth, maxWidth);
                      dialogHeight.value = newHeight.clamp(minHeight, maxHeight);
                    },
                  ),
                ),
                
                // 右上角拖拽手柄
                Positioned(
                  right: 0,
                  top: 0,
                  child: _buildResizeHandle(
                    context: context,
                    cursor: SystemMouseCursors.resizeUpRight,
                    isDragging: isDragging,
                    onPanUpdate: (details) {
                      final newWidth = dialogWidth.value + details.delta.dx;
                      final newHeight = dialogHeight.value - details.delta.dy;
                      
                      dialogWidth.value = newWidth.clamp(minWidth, maxWidth);
                      dialogHeight.value = newHeight.clamp(minHeight, maxHeight);
                    },
                  ),
                ),
                
                // 左下角拖拽手柄
                Positioned(
                  left: 0,
                  bottom: 0,
                  child: _buildResizeHandle(
                    context: context,
                    cursor: SystemMouseCursors.resizeDownLeft,
                    isDragging: isDragging,
                    onPanUpdate: (details) {
                      final newWidth = dialogWidth.value - details.delta.dx;
                      final newHeight = dialogHeight.value + details.delta.dy;
                      
                      dialogWidth.value = newWidth.clamp(minWidth, maxWidth);
                      dialogHeight.value = newHeight.clamp(minHeight, maxHeight);
                    },
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// 构建调整大小的手柄（角落）
  Widget _buildResizeHandle({
    required BuildContext context,
    required MouseCursor cursor,
    required ValueNotifier<bool> isDragging,
    required void Function(DragUpdateDetails) onPanUpdate,
    bool showIcon = false,
  }) {
    return MouseRegion(
      cursor: cursor,
      child: GestureDetector(
        onPanStart: (_) => isDragging.value = true,
        onPanEnd: (_) => isDragging.value = false,
        onPanUpdate: onPanUpdate,
        child: Container(
          width: resizeHandleSize,
          height: resizeHandleSize,
          decoration: BoxDecoration(
            color: isDragging.value 
                ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.2)
                : Colors.transparent,
            borderRadius: _getBorderRadius(cursor),
          ),
          child: showIcon ? Icon(
            Icons.drag_handle,
            size: 16,
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.4),
          ) : null,
        ),
      ),
    );
  }

  /// 构建调整大小的边缘
  Widget _buildResizeEdge({
    required BuildContext context,
    required MouseCursor cursor,
    required ValueNotifier<bool> isDragging,
    required void Function(DragUpdateDetails) onPanUpdate,
    double? width,
    double? height,
  }) {
    return MouseRegion(
      cursor: cursor,
      child: GestureDetector(
        onPanStart: (_) => isDragging.value = true,
        onPanEnd: (_) => isDragging.value = false,
        onPanUpdate: onPanUpdate,
        child: Container(
          width: width,
          height: height,
          color: Colors.transparent,
        ),
      ),
    );
  }

  /// 根据光标类型获取对应的边框圆角
  BorderRadius? _getBorderRadius(MouseCursor cursor) {
    if (cursor == SystemMouseCursors.resizeDownRight) {
      return const BorderRadius.only(bottomRight: Radius.circular(16));
    } else if (cursor == SystemMouseCursors.resizeUpLeft) {
      return const BorderRadius.only(topLeft: Radius.circular(16));
    } else if (cursor == SystemMouseCursors.resizeUpRight) {
      return const BorderRadius.only(topRight: Radius.circular(16));
    } else if (cursor == SystemMouseCursors.resizeDownLeft) {
      return const BorderRadius.only(bottomLeft: Radius.circular(16));
    }
    return null;
  }
} 
