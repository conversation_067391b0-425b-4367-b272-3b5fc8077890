import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

/// 广告数据模型
class AdData {
  final String title;
  final String? description;
  final IconData? icon;
  final Color? color;
  final VoidCallback? onTap;

  const AdData({
    required this.title,
    this.description,
    this.icon,
    this.color,
    this.onTap,
  });
}

/// 轮播广告组件
class AdBannerWidget extends HookWidget {
  final List<AdData> ads;
  final Duration duration;
  final double height;
  final TextStyle? titleStyle;
  final Color? backgroundColor;
  final EdgeInsetsGeometry? padding;
  final Function(AdData)? onAdTap;

  const AdBannerWidget({
    super.key,
    required this.ads,
    this.duration = const Duration(seconds: 3),
    this.height = 40.0,
    this.titleStyle,
    this.backgroundColor,
    this.padding,
    this.onAdTap,
  });

  @override
  Widget build(BuildContext context) {
    // 如果没有广告数据，显示空容器
    if (ads.isEmpty) {
      return SizedBox(height: height);
    }

    // 如果只有一个广告，直接显示
    if (ads.length == 1) {
      return _buildSingleAd(context, ads.first);
    }

    // 多个广告时使用轮播
    return _buildCarouselAds(context);
  }

  /// 构建单个广告
  Widget _buildSingleAd(BuildContext context, AdData ad) {
    return Container(
      height: height,
      padding: padding ?? const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: backgroundColor ?? Colors.transparent,
        borderRadius: BorderRadius.circular(height / 2),
      ),
      child: _buildAdContent(context, ad),
    );
  }

  /// 构建轮播广告
  Widget _buildCarouselAds(BuildContext context) {
    return SizedBox(
      height: height,
      child: _AdCarousel(
        ads: ads,
        duration: duration,
        height: height,
        titleStyle: titleStyle,
        backgroundColor: backgroundColor,
        padding: padding,
        onAdTap: onAdTap,
      ),
    );
  }

  /// 构建广告内容
  Widget _buildAdContent(BuildContext context, AdData ad) {
    return InkWell(
      onTap: () => _handleAdTap(context, ad),
      borderRadius: BorderRadius.circular(height / 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (ad.icon != null) ...[
            Icon(
              ad.icon,
              size: 16,
              color: ad.color ?? const Color(0xFF666666),
            ),
            const SizedBox(width: 8),
          ],
          Flexible(
            child: Text(
              ad.title,
              style: titleStyle ??
                  const TextStyle(
                    fontSize: 12,
                    color: Color(0xFF666666),
                  ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  /// 处理广告点击
  void _handleAdTap(BuildContext context, AdData ad) {
    if (ad.onTap != null) {
      ad.onTap!();
    } else if (onAdTap != null) {
      onAdTap!(ad);
    } else {
      // 默认行为：显示详细信息对话框
      _showAdDialog(context, ad);
    }
  }

  /// 显示广告详细信息对话框
  void _showAdDialog(BuildContext context, AdData ad) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            if (ad.icon != null) ...[
              Icon(
                ad.icon,
                color: ad.color ?? Theme.of(context).primaryColor,
              ),
              const SizedBox(width: 8),
            ],
            Expanded(
              child: Text(
                ad.title,
                style: const TextStyle(fontSize: 16),
              ),
            ),
          ],
        ),
        content: ad.description != null
            ? Text(ad.description!)
            : const Text('暂无详细信息'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }
}

/// 内部轮播组件
class _AdCarousel extends HookWidget {
  final List<AdData> ads;
  final Duration duration;
  final double height;
  final TextStyle? titleStyle;
  final Color? backgroundColor;
  final EdgeInsetsGeometry? padding;
  final Function(AdData)? onAdTap;

  const _AdCarousel({
    required this.ads,
    required this.duration,
    required this.height,
    this.titleStyle,
    this.backgroundColor,
    this.padding,
    this.onAdTap,
  });

  @override
  Widget build(BuildContext context) {
    final currentIndex = useState(0);
    final animationController = useAnimationController(
      duration: const Duration(milliseconds: 500),
    );

    // 创建扩展列表用于无缝循环
    final extendedAds = <AdData>[
      if (ads.length > 1) ads.last,
      ...ads,
      if (ads.length > 1) ads.first,
    ];

    // 动画值
    final animation = useAnimation(animationController);

    // 定时器
    useEffect(() {
      if (ads.length <= 1) return null;

      final timer = Stream.periodic(duration).listen((_) {
        if (animationController.isAnimating) return;

        animationController.forward().then((_) {
          final nextIndex = currentIndex.value + 1;
          if (nextIndex >= extendedAds.length - 1) {
            currentIndex.value = 1; // 跳回到真正的第一个
          } else {
            currentIndex.value = nextIndex;
          }
          animationController.reset();
        });
      });

      return timer.cancel;
    }, [duration, ads.length]);

    return Container(
      height: height,
      padding: padding ?? const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: backgroundColor ?? Colors.transparent,
        borderRadius: BorderRadius.circular(height / 2),
      ),
      child: ClipRect(
        child: Stack(
          children: [
            // 当前广告
            if (currentIndex.value < extendedAds.length)
              _buildAdItem(
                context,
                extendedAds[currentIndex.value],
                -animation * height,
              ),

            // 下一个广告
            if (animationController.isAnimating &&
                currentIndex.value + 1 < extendedAds.length)
              _buildAdItem(
                context,
                extendedAds[currentIndex.value + 1],
                (1 - animation) * height,
              ),
          ],
        ),
      ),
    );
  }

  /// 构建广告项目
  Widget _buildAdItem(BuildContext context, AdData ad, double offsetY) {
    return Transform.translate(
      offset: Offset(0, offsetY),
      child: SizedBox(
        height: height,
        child: InkWell(
          onTap: () => _handleAdTap(context, ad),
          borderRadius: BorderRadius.circular(height / 2),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (ad.icon != null) ...[
                Icon(
                  ad.icon,
                  size: 16,
                  color: ad.color ?? const Color(0xFF666666),
                ),
                const SizedBox(width: 8),
              ],
              Flexible(
                child: Text(
                  ad.title,
                  style: titleStyle ??
                      const TextStyle(
                        fontSize: 12,
                        color: Color(0xFF666666),
                      ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 处理广告点击
  void _handleAdTap(BuildContext context, AdData ad) {
    if (ad.onTap != null) {
      ad.onTap!();
    } else if (onAdTap != null) {
      onAdTap!(ad);
    } else {
      // 显示详细信息
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Row(
            children: [
              if (ad.icon != null) ...[
                Icon(
                  ad.icon,
                  color: ad.color ?? Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
              ],
              Expanded(
                child: Text(
                  ad.title,
                  style: const TextStyle(fontSize: 16),
                ),
              ),
            ],
          ),
          content: ad.description != null
              ? Text(ad.description!)
              : const Text('暂无详细信息'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('关闭'),
            ),
          ],
        ),
      );
    }
  }
} 
