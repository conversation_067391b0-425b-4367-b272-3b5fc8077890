import 'package:flutter/material.dart';

class ErrorWindow extends StatelessWidget {
  final String error;
  const ErrorWindow({super.key, required this.error});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: double.infinity,
      child: Center(
        child: Column(
          children: [
            Image.asset('assets/images/common/error.png'),
            Text(error),
          ],
        ),
      ),
    );
  }
}
