import 'package:flutter/material.dart';
import 'package:frontend_re/widgets/primary_botton.dart';
import 'package:frontend_re/widgets/secondary_botton.dart';

class ConfirmDialog extends StatelessWidget {
  // 定义可自定义的参数
  final String? title; // 标题文本，默认为"温馨提示"
  final String content; // 提示内容
  final String? confirmText; // 确认按钮文本
  final String? cancelText; // 取消按钮文本
  final VoidCallback? onConfirm; // 确认回调
  final VoidCallback? onCancel; // 取消回调
  final Widget? icon; // 自定义图标
  final double minWidth; // 最小宽度
  final double minHeight; // 最小高度

  const ConfirmDialog({
    super.key,
    this.title = '温馨提示',
    required this.content,
    this.confirmText = '确认',
    this.cancelText = '取消',
    this.onConfirm,
    this.onCancel,
    this.icon,
    this.minWidth = 200, // 默认最小宽度
    this.minHeight = 50, // 默认最小高度
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: ConstrainedBox(
        constraints: BoxConstraints(
          minWidth: minWidth,
          minHeight: minHeight,
          maxWidth: 600,
          maxHeight: MediaQuery.of(context).size.height * 0.7,
        ),
        child: IntrinsicHeight(
          child: Padding(
            padding: const EdgeInsets.fromLTRB(24, 20, 24, 24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 标题行
                Row(
                  children: [
                    // 图标部分
                    icon ??
                        const Icon(
                          Icons.info_outline,
                          size: 20,
                          color: Colors.blue,
                        ),
                    const SizedBox(width: 10),
                    // 标题
                    Expanded(
                      child: Text(
                        title!,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                // 内容行 - 使用 SingleChildScrollView 使内容可滚动
                Expanded(
                  // 添加 Expanded 来限制内容区域大小
                  child: SingleChildScrollView(
                    child: Text(
                      content,
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),
                ),
                const SizedBox(height: 20),
                // 按钮行
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    // 取消按钮
                    SecondaryButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                        onCancel?.call();
                      },
                      child: Text(cancelText!),
                    ),
                    const SizedBox(width: 10),
                    // 确认按钮
                    PrimaryButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                        onConfirm?.call();
                      },
                      child: Text(confirmText!),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
