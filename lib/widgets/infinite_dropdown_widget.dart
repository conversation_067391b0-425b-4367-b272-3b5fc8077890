import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

/// 支持触底加载的下拉菜单组件
class InfiniteDropdownWidget<T> extends HookConsumerWidget {
  const InfiniteDropdownWidget({
    super.key,
    required this.items, // 当前数据列表
    required this.onLoadMore, // 加载更多数据的回调
    required this.itemBuilder, // 构建每个选项的函数
    this.hintText = '请选择', // 提示文本
    this.width, // 控件宽度
    this.onSelected, // 选择回调
    this.initialSelection, // 初始选中值
    this.enableSearch = true, // 是否启用搜索
    this.enabled = true, // 启用状态
    this.hasMore = true, // 是否还有更多数据
    this.isLoading = false, // 是否正在加载
    this.searchController, // 搜索控制器
    this.onSearchChanged, // 搜索变化回调
  });

  final List<T> items; // 下拉列表数据
  final Future<void> Function() onLoadMore; // 加载更多数据回调
  final String Function(T) itemBuilder; // 构建每个选项显示文本的函数
  final String hintText; // 提示文本
  final double? width; // 控件宽度
  final Function(T?)? onSelected; // 选择回调函数
  final T? initialSelection; // 初始选中值
  final bool enableSearch; // 是否启用搜索功能
  final bool enabled; // 启用状态
  final bool hasMore; // 是否还有更多数据
  final bool isLoading; // 是否正在加载
  final TextEditingController? searchController; // 搜索控制器
  final Function(String)? onSearchChanged; // 搜索变化回调

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isDropdownOpen = useState(false);
    final overlayEntry = useRef<OverlayEntry?>(null);
    final buttonKey = useMemoized(() => GlobalKey());
    final scrollController = useScrollController();
    final searchTextController = searchController ?? useTextEditingController();
    final selectedValue = useState<T?>(initialSelection);

    // 监听搜索文本变化
    useEffect(() {
      void listener() {
        onSearchChanged?.call(searchTextController.text);
      }
      
      if (enableSearch) {
        searchTextController.addListener(listener);
        return () => searchTextController.removeListener(listener);
      }
      return null;
    }, [searchTextController, enableSearch]);

    // 监听滚动，实现触底加载
    useEffect(() {
      void scrollListener() {
        if (scrollController.position.pixels >= 
            scrollController.position.maxScrollExtent - 100) {
          if (hasMore && !isLoading) {
            onLoadMore();
          }
        }
      }

      scrollController.addListener(scrollListener);
      return () => scrollController.removeListener(scrollListener);
    }, [scrollController, hasMore, isLoading]);

    // 关闭下拉菜单
    void closeDropdown() {
      if (overlayEntry.value != null) {
        overlayEntry.value!.remove();
        overlayEntry.value = null;
        isDropdownOpen.value = false;
      }
    }

    // 打开下拉菜单
    void openDropdown() {
      if (!enabled || isDropdownOpen.value) return;

      final RenderBox renderBox = 
          buttonKey.currentContext!.findRenderObject() as RenderBox;
      final size = renderBox.size;
      final offset = renderBox.localToGlobal(Offset.zero);

      overlayEntry.value = OverlayEntry(
        builder: (context) => _DropdownOverlay<T>(
          offset: offset,
          size: size,
          items: items,
          itemBuilder: itemBuilder,
          onSelected: (value) {
            selectedValue.value = value;
            searchTextController.text = value != null ? itemBuilder(value) : '';
            onSelected?.call(value);
            closeDropdown();
          },
          scrollController: scrollController,
          hasMore: hasMore,
          isLoading: isLoading,
          onClose: closeDropdown,
        ),
      );

      Overlay.of(context).insert(overlayEntry.value!);
      isDropdownOpen.value = true;
    }

    // 清理资源
    useEffect(() {
      return () {
        closeDropdown();
      };
    }, []);

    return Container(
      width: width,
      child: GestureDetector(
        key: buttonKey,
        onTap: enabled ? openDropdown : null,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            border: Border.all(
              color: enabled ? Colors.blue : Colors.grey,
              width: 1.0,
            ),
            borderRadius: BorderRadius.circular(50),
            color: Colors.white,
          ),
          child: Row(
            children: [
              Expanded(
                child: enableSearch
                    ? TextField(
                        controller: searchTextController,
                        enabled: enabled,
                        style: const TextStyle(fontSize: 14),
                        decoration: InputDecoration(
                          hintText: selectedValue.value != null 
                              ? itemBuilder(selectedValue.value as T)
                              : hintText,
                          hintStyle: const TextStyle(color: Colors.grey),
                          border: InputBorder.none,
                          isDense: true,
                          contentPadding: EdgeInsets.zero,
                        ),
                        onTap: openDropdown,
                      )
                    : Text(
                        selectedValue.value != null 
                            ? itemBuilder(selectedValue.value as T)
                            : hintText,
                        style: TextStyle(
                          fontSize: 14,
                          color: selectedValue.value != null 
                              ? Colors.black87 
                              : Colors.grey,
                        ),
                      ),
              ),
              Icon(
                isDropdownOpen.value 
                    ? Icons.keyboard_arrow_up 
                    : Icons.keyboard_arrow_down,
                color: enabled ? Colors.blue : Colors.grey,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// 下拉菜单覆盖层
class _DropdownOverlay<T> extends StatelessWidget {
  const _DropdownOverlay({
    required this.offset,
    required this.size,
    required this.items,
    required this.itemBuilder,
    required this.onSelected,
    required this.scrollController,
    required this.hasMore,
    required this.isLoading,
    required this.onClose,
  });

  final Offset offset;
  final Size size;
  final List<T> items;
  final String Function(T) itemBuilder;
  final Function(T?) onSelected;
  final ScrollController scrollController;
  final bool hasMore;
  final bool isLoading;
  final VoidCallback onClose;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // 点击背景关闭
        Positioned.fill(
          child: GestureDetector(
            onTap: onClose,
            child: Container(color: Colors.transparent),
          ),
        ),
        // 下拉菜单内容
        Positioned(
          left: offset.dx,
          top: offset.dy + size.height + 4,
          width: size.width,
          child: Material(
            elevation: 8,
            borderRadius: BorderRadius.circular(8),
            child: Container(
              constraints: const BoxConstraints(maxHeight: 300),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: items.isEmpty && !isLoading
                  ? const Padding(
                      padding: EdgeInsets.all(16),
                      child: Text(
                        '暂无数据',
                        style: TextStyle(color: Colors.grey),
                        textAlign: TextAlign.center,
                      ),
                    )
                  : ListView.builder(
                      controller: scrollController,
                      shrinkWrap: true,
                      itemCount: items.length + (hasMore || isLoading ? 1 : 0),
                      itemBuilder: (context, index) {
                        // 显示数据项
                        if (index < items.length) {
                          final item = items[index];
                          return InkWell(
                            onTap: () => onSelected(item),
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 12,
                              ),
                              decoration: BoxDecoration(
                                border: index < items.length - 1
                                    ? Border(
                                        bottom: BorderSide(
                                          color: Colors.grey.shade200,
                                        ),
                                      )
                                    : null,
                              ),
                              child: Text(
                                itemBuilder(item),
                                style: const TextStyle(fontSize: 14),
                              ),
                            ),
                          );
                        }
                        
                        // 显示加载更多指示器
                        return Container(
                          padding: const EdgeInsets.all(16),
                          child: Center(
                            child: isLoading
                                ? const SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                    ),
                                  )
                                : hasMore
                                    ? const Text(
                                        '滑动加载更多...',
                                        style: TextStyle(
                                          color: Colors.grey,
                                          fontSize: 12,
                                        ),
                                      )
                                    : const Text(
                                        '没有更多数据了',
                                        style: TextStyle(
                                          color: Colors.grey,
                                          fontSize: 12,
                                        ),
                                      ),
                          ),
                        );
                      },
                    ),
            ),
          ),
        ),
      ],
    );
  }
} 
