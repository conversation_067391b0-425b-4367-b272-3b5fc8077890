import 'package:flutter/material.dart';

class ProxyTableBtn extends StatefulWidget {
  final Future<void> Function() onPressed;
  final String label;
  final ButtonStyle? style;
  final bool enableTooltip; // 是否启用tooltip提示
  final int proxyType; // 代理类型：1表示HTTP，2表示HTTPS，3表示SOCKS5

  const ProxyTableBtn({
    super.key,
    required this.onPressed,
    required this.label,
    this.style,
    this.enableTooltip = true, // 默认启用tooltip
    required this.proxyType,
  });

  @override
  State<ProxyTableBtn> createState() => _ProxyTableBtnState();
}

class _ProxyTableBtnState extends State<ProxyTableBtn> {
  bool _isLoading = false;
  bool _isHovered = false;
  bool _showTooltip = false;

  // 获取代理类型文本（首字母）
  String get _proxyTypeText => widget.proxyType == 1 ? 'H' : widget.proxyType == 2 ? 'HS' : 'S';
  
  // 获取代理类型颜色
  Color get _proxyTypeColor => widget.proxyType == 1 
      ? const Color(0xFF1976D2) // HTTP使用蓝色
      : widget.proxyType == 2 
          ? const Color(0xFF2E7D32) // HTTPS使用绿色
          : const Color(0xFFE65100); // SOCKET使用橙色

  Future<void> _handlePress() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await widget.onPressed();
    } catch (e) {
      print('Error during button action: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _onEnter() {
    setState(() => _isHovered = true);
    // 只有启用tooltip且文本较长时才显示
    if (widget.enableTooltip && widget.label.length > 10) {
      Future.delayed(const Duration(milliseconds: 500), () {
        if (_isHovered && mounted) {
          setState(() => _showTooltip = true);
        }
      });
    }
  }

  void _onExit() {
    setState(() {
      _isHovered = false;
      _showTooltip = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        MouseRegion(
          onEnter: (_) => _onEnter(),
          onExit: (_) => _onExit(),
          child: TextButton(
            onPressed: _isLoading ? null : _handlePress,
            style: widget.style ??
                TextButton.styleFrom(
                  backgroundColor: const Color(0xFFF3F4F8),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ).copyWith(
                  // 添加鼠标悬停时的背景色
                  overlayColor: WidgetStateProperty.resolveWith<Color?>(
                    (Set<WidgetState> states) {
                      if (states.contains(WidgetState.hovered)) {
                        // 悬停时的背景色，这里使用稍深一点的紫色
                        return Theme.of(context)
                            .colorScheme
                            .primary
                            .withValues(alpha: 1);
                      }
                      return null; // 返回 null 使用默认颜色
                    },
                  ),
                  foregroundColor: WidgetStateProperty.resolveWith<Color?>(
                    (Set<WidgetState> states) {
                      if (states.contains(WidgetState.hovered)) {
                        return Colors.white;
                      }
                      return const Color(0xFF333333);
                    },
                  ),
                ),
            child: _isLoading
                ? const SizedBox(
                    height: 16,
                    width: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2.0,
                    ),
                  )
                : Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // 代理类型标签（首字母）- 只在 proxyType 不为 0 时显示
                      if (widget.proxyType != 0) ...[
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 3, vertical: 1),
                          margin: const EdgeInsets.only(right: 4),
                          decoration: BoxDecoration(
                            color: _isHovered 
                                ? Colors.white.withValues(alpha: 0.2)
                                : _proxyTypeColor.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(3),
                            border: Border.all(
                              color: _isHovered 
                                  ? Colors.white.withValues(alpha: 0.8)
                                  : _proxyTypeColor, 
                              width: 0.9
                            ),
                          ),
                          child: Text(
                            _proxyTypeText,
                            style: TextStyle(
                              fontSize: 9,
                              color: _isHovered 
                                  ? Colors.white
                                  : _proxyTypeColor.withValues(alpha: 1),
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                      // 代理名称
                      Flexible(
                        child: Text(
                          widget.label,
                          style: TextStyle(
                            fontSize: 12, 
                            color: _isHovered 
                                ? Colors.white
                                : const Color(0xFF8D8E93)
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          textAlign: TextAlign.left,
                        ),
                      ),
                    ],
                  ),
          ),
        ),
        // 自定义tooltip - 只在启用且文本较长时显示
        if (_showTooltip && widget.enableTooltip && widget.label.length > 10)
          Positioned(
            bottom: 60, // 在按钮上方显示
            child: Center(
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.grey[700], // 更柔和的灰色背景
                  borderRadius: BorderRadius.circular(4),
                  boxShadow: const [
                    BoxShadow(
                      color: Colors.black26,
                      blurRadius: 4,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      widget.label,
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.9), // 90%透明度的白色文字
                        fontSize: 12,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 2),
                    Text(
                      '类型: ${widget.proxyType == 0 ? 'HTTP' : 'SOCKET'}',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.9),
                        fontSize: 10,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ),
      ],
    );
  }
}
