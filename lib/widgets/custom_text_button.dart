import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class CustomTextButton extends HookConsumerWidget {
  final String text;
  final VoidCallback? onPressed;
  final TextStyle? textStyle;
  final Color? backgroundColor;
  final Color? hoverColor;
  final Color? textColor;
  final Color? hoverTextColor;
  final BorderRadius? borderRadius;
  final EdgeInsetsGeometry? padding;

  const CustomTextButton({
    super.key,
    required this.text,
    this.onPressed,
    this.textStyle,
    this.backgroundColor = const Color(0xFF0C75F8),
    this.hoverColor = const Color(0xFF7BB4FF),
    this.textColor = const Color(0xFFFFFFFF),
    this.hoverTextColor = const Color(0xFFFFFFFF),
    this.borderRadius,
    this.padding,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 悬停状态
    final isHovered = useState(false);
    
    // 按下状态
    final isPressed = useState(false);

    return MouseRegion(
      onEnter: (_) => isHovered.value = true,
      onExit: (_) => isHovered.value = false,
      cursor: onPressed != null ? SystemMouseCursors.click : SystemMouseCursors.basic,
      child: GestureDetector(
        onTapDown: (_) => isPressed.value = true,
        onTapUp: (_) => isPressed.value = false,
        onTapCancel: () => isPressed.value = false,
        onTap: onPressed,
        child: AnimatedContainer(
          height: 50,
          duration: const Duration(milliseconds: 0),
          padding: padding ?? const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: isHovered.value ? hoverColor: backgroundColor,
            borderRadius: borderRadius ?? BorderRadius.circular(50),
          ),
          child: Center(
            child: AnimatedOpacity(
              duration: const Duration(milliseconds: 150),
              opacity: onPressed != null ? 1.0 : 0.6,
              child: Text(
                text,
                style: textStyle ?? TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: isHovered.value ? hoverTextColor :textColor,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
