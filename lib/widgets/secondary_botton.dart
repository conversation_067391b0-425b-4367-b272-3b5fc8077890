import 'package:flutter/material.dart';
import 'package:frontend_re/theme/color.dart';

class SecondaryButton extends StatelessWidget {
  final Widget child;
  final VoidCallback onPressed;

  const SecondaryButton({
    super.key,
    required this.child,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return TextButton(
      style: TextButton.styleFrom(
        backgroundColor: AppColors.secondaryColor,
        foregroundColor: AppColors.onSecondaryColor,
      ),
      onPressed: onPressed,
      child: child,
    );
  }
}
