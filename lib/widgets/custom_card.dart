import 'package:flutter/material.dart';

class CustomCard extends StatelessWidget {
  final String title;
  final String description;
  final double? width; // 添加可选的宽度参数
  final Widget? child; // 添加可选的子组件参数
  
  const CustomCard({
    super.key,
    this.title = '标题', // 给标题一个默认值
    this.description = '描述', // 给描述一个默认值
    this.width, // 可选宽度参数
    this.child, // 可选子组件参数
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width, // 使用传入的宽度，不设置则自适应
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Padding(
        padding: const EdgeInsets.only(left: 20.0, right: 20.0, bottom: 10.0, top: 0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            // 标题
            Container(
              width: width, // 使用传入的宽度
              height: 60,
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: Color(0xFFE2E8FF),
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                spacing: 8,
                children: [
                  //圆形
                  Container(
                    width: 16,
                    height: 16,
                    decoration: const BoxDecoration(
                      shape: BoxShape.circle,
                      color: Color(0xFF0C75F8),
                    ),
                  ),
                  // 标题
                  Text(title),
                  // 描述
                  Expanded(
                    child: Text(
                      description,
                      style: const TextStyle(fontSize: 12, color: Color(0xFF8D8E93)),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
            // 内容
            Container(
              padding: const EdgeInsets.symmetric(vertical: 16),
              child: child ?? const Text('Custom Card'),
            ),
          ],
        ),
      ),
    );
  }
}
