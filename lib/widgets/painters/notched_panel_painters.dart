import 'package:flutter/material.dart';

/// 带缺口的面板绘制器
/// 用于绘制带有左上角缺口的白色面板
class NotchedPanelPainter extends CustomPainter {
  final double panelRadius;
  final Color panelColor;
  
  const NotchedPanelPainter({
    this.panelRadius = 20.0,
    this.panelColor = const Color(0xFFFFFFFF),
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = panelColor
      ..style = PaintingStyle.fill;

    // 主面板路径
    final panelPath = Path()
      ..addRRect(RRect.fromLTRBR(
        0, 0, size.width, size.height,
        Radius.circular(panelRadius),
      ));

    // 缺口路径（左上角的圆角矩形）
    final notchPath = Path()
      ..moveTo(0, 72)
      ..quadraticBezierTo(0, 52, 24, 52)
      ..quadraticBezierTo(50, 50, 52, 24)
      ..quadraticBezierTo(52, 0, 72, 0)
      ..lineTo(0, 0);

    // 差集 - 从主面板中减去缺口
    final finalPath = Path.combine(
      PathOperation.difference,
      panelPath,
      notchPath,
    );

    canvas.drawPath(finalPath, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// 带缺口的面板裁切器
/// 用于对容器内容进行缺口形状的裁切
class NotchedPanelClipper extends CustomClipper<Path> {
  final double panelRadius;
  
  const NotchedPanelClipper({
    this.panelRadius = 20.0,
  });

  @override
  Path getClip(Size size) {
    // 主面板路径
    final panelPath = Path()
      ..addRRect(RRect.fromLTRBR(
        0, 0, size.width, size.height,
        Radius.circular(panelRadius),
      ));

    // 缺口路径
    final notchPath = Path()
      ..moveTo(0, 72)
      ..quadraticBezierTo(0, 52, 24, 52)
      ..quadraticBezierTo(50, 50, 52, 24)
      ..quadraticBezierTo(52, 0, 72, 0)
      ..lineTo(0, 0);

    // 差集 - 从主面板中减去缺口
    final finalPath = Path.combine(
      PathOperation.difference,
      panelPath,
      notchPath,
    );

    return finalPath;
  }

  @override
  bool shouldReclip(covariant CustomClipper<Path> oldClipper) => false;
}

/// 可参数化的缺口面板绘制器
/// 支持自定义缺口的位置、大小和形状
class CustomNotchedPanelPainter extends CustomPainter {
  final double panelRadius;
  final Color panelColor;
  final List<Offset> notchPoints;
  final List<Offset> notchControlPoints;
  
  const CustomNotchedPanelPainter({
    this.panelRadius = 20.0,
    this.panelColor = const Color(0xFFFFFFFF),
    this.notchPoints = const [
      Offset(0, 72),
      Offset(24, 52),
      Offset(52, 24),
      Offset(72, 0),
      Offset(0, 0),
    ],
    this.notchControlPoints = const [
      Offset(0, 52),
      Offset(50, 50),
      Offset(52, 0),
    ],
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = panelColor
      ..style = PaintingStyle.fill;

    // 主面板路径
    final panelPath = Path()
      ..addRRect(RRect.fromLTRBR(
        0, 0, size.width, size.height,
        Radius.circular(panelRadius),
      ));

    // 动态生成缺口路径
    final notchPath = Path()
      ..moveTo(notchPoints[0].dx, notchPoints[0].dy)
      ..quadraticBezierTo(
        notchControlPoints[0].dx,
        notchControlPoints[0].dy,
        notchPoints[1].dx,
        notchPoints[1].dy,
      )
      ..quadraticBezierTo(
        notchControlPoints[1].dx,
        notchControlPoints[1].dy,
        notchPoints[2].dx,
        notchPoints[2].dy,
      )
      ..quadraticBezierTo(
        notchControlPoints[2].dx,
        notchControlPoints[2].dy,
        notchPoints[3].dx,
        notchPoints[3].dy,
      )
      ..lineTo(notchPoints[4].dx, notchPoints[4].dy);

    // 差集
    final finalPath = Path.combine(
      PathOperation.difference,
      panelPath,
      notchPath,
    );

    canvas.drawPath(finalPath, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// 可参数化的缺口面板裁切器
class CustomNotchedPanelClipper extends CustomClipper<Path> {
  final double panelRadius;
  final List<Offset> notchPoints;
  final List<Offset> notchControlPoints;
  
  const CustomNotchedPanelClipper({
    this.panelRadius = 20.0,
    this.notchPoints = const [
      Offset(0, 72),
      Offset(24, 52),
      Offset(52, 24),
      Offset(72, 0),
      Offset(0, 0),
    ],
    this.notchControlPoints = const [
      Offset(0, 52),
      Offset(50, 50),
      Offset(52, 0),
    ],
  });

  @override
  Path getClip(Size size) {
    // 主面板路径
    final panelPath = Path()
      ..addRRect(RRect.fromLTRBR(
        0, 0, size.width, size.height,
        Radius.circular(panelRadius),
      ));

    // 动态生成缺口路径
    final notchPath = Path()
      ..moveTo(notchPoints[0].dx, notchPoints[0].dy)
      ..quadraticBezierTo(
        notchControlPoints[0].dx,
        notchControlPoints[0].dy,
        notchPoints[1].dx,
        notchPoints[1].dy,
      )
      ..quadraticBezierTo(
        notchControlPoints[1].dx,
        notchControlPoints[1].dy,
        notchPoints[2].dx,
        notchPoints[2].dy,
      )
      ..quadraticBezierTo(
        notchControlPoints[2].dx,
        notchControlPoints[2].dy,
        notchPoints[3].dx,
        notchPoints[3].dy,
      )
      ..lineTo(notchPoints[4].dx, notchPoints[4].dy);

    // 差集
    final finalPath = Path.combine(
      PathOperation.difference,
      panelPath,
      notchPath,
    );

    return finalPath;
  }

  @override
  bool shouldReclip(covariant CustomClipper<Path> oldClipper) => false;
} 
