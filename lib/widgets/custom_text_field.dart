import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// 自定义文本输入框，带有圆角和特定的样式
class CustomTextField extends StatefulWidget {
  const CustomTextField({
    super.key,
    required this.controller,
    this.hint = "选填",
    this.lines = 1,
    this.onEditingComplete,
    this.onChanged,
    this.fillColor = const Color(0xFFF3F4F8),
    this.focusBorderColor = const Color(0xFF0C75F8),
    this.obscureText = false,
    this.height = 16, // 添加高度参数
    this.keyboardType, // 键盘类型
    this.inputFormatters, // 输入格式化器
  });

  final TextEditingController controller;
  final String hint;
  final int lines;
  final Function()? onEditingComplete;
  final Function(String)? onChanged;
  final Color fillColor;
  final Color focusBorderColor;
  final bool obscureText;
  final double height; // 输入框高度
  final TextInputType? keyboardType; // 键盘类型
  final List<TextInputFormatter>? inputFormatters; // 输入格式化器

  @override
  State<CustomTextField> createState() => _CustomTextFieldState();
}

class _CustomTextFieldState extends State<CustomTextField> {
  late bool _obscureText;
  
  @override
  void initState() {
    super.initState();
    _obscureText = widget.obscureText;
  }
  
  @override
  Widget build(BuildContext context) {
    return TextField(
      maxLines: widget.lines,
      minLines: widget.lines,
      obscureText: _obscureText,
      keyboardType: widget.keyboardType,
      inputFormatters: widget.inputFormatters,
      style: const TextStyle(fontSize: 13), // 减小字体大小
      decoration: InputDecoration(
        isCollapsed: true, // 使输入框更紧凑
        filled: true, // 启用填充
        fillColor: widget.fillColor, // 设置背景颜色
        contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: widget.height), // 调整垂直内边距
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(50),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(50),
          borderSide: const BorderSide(color: Colors.transparent),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(50),
          borderSide: BorderSide(color: widget.focusBorderColor),
        ),
        hintText: widget.hint,
        hintStyle: const TextStyle(color: Color(0xFF999999), fontSize: 14), // 减小提示文字大小
        // 添加密码可见性切换按钮
        suffixIcon: widget.obscureText ? IconButton(
          icon: Icon(
            _obscureText ? Icons.visibility_off : Icons.visibility,
            size: 18,
            color: const Color(0xFF8D8E93),
          ),
          onPressed: () {
            setState(() {
              _obscureText = !_obscureText;
            });
          },
        ) : null,
      ),
      controller: widget.controller,
      onChanged: widget.onChanged,
      onEditingComplete: widget.onEditingComplete,
    );
  }
} 
