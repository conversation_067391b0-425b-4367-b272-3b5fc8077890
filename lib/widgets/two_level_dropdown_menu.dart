import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:dropdown_button2/dropdown_button2.dart';

class TwoLevelDropdownMenu extends HookConsumerWidget {
  // 当前选中的值
  final String value;

  // 菜单数据
  final List<Map<String, dynamic>> items;

  // 选中回调
  final Function(String) onSelected;

  // 自定义样式
  final double? width;
  final double? height;
  final Color? textColor;
  final double? fontSize;

  const TwoLevelDropdownMenu({
    super.key,
    required this.value,
    required this.items,
    required this.onSelected,
    this.width,
    this.height,
    this.textColor,
    this.fontSize,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      width: width,
      height: height ?? 50,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(50),
        color: const Color(0xFFF3F4F8),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton2(
          customButton: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    value,
                    style: TextStyle(
                      fontSize: fontSize ?? 14,
                      color: textColor ?? Colors.black.withValues(alpha: 1),
                    ),
                  ),
                ),
                Icon(
                  Icons.keyboard_arrow_down,
                  size: 16,
                  color: textColor ?? Colors.black.withValues(alpha: 1),
                ),
              ],
            ),
          ),
          // 下拉菜单配置
          dropdownStyleData: DropdownStyleData(
            width: width ?? 200,
            padding: EdgeInsets.zero,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: Colors.white,
            ),
          ),
          // 菜单项配置
          menuItemStyleData: const MenuItemStyleData(
            height: 50,
            padding: EdgeInsets.zero,
          ),
          // 构建一级菜单项
          items: items.map((item) {
            return DropdownMenuItem<String>(
              value: item['name'],
              child: _buildFirstLevelMenuItem(item),
            );
          }).toList(),
          onChanged: (value) {},
        ),
      ),
    );
  }

  // 构建一级菜单项
  Widget _buildFirstLevelMenuItem(Map<String, dynamic> item) {
    return Container(
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Color(0xFFEEEEEE), width: 1),
        ),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton2(
          customButton: SizedBox(
            height: 50,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    item['name'],
                    style: TextStyle(
                      fontSize: fontSize ?? 14,
                      color: textColor ?? Colors.black.withValues(alpha: 0.85),
                    ),
                  ),
                  const Icon(
                    Icons.arrow_right,
                    size: 16,
                  ),
                ],
              ),
            ),
          ),
          // 二级菜单样式
          dropdownStyleData: DropdownStyleData(
            width: 180,
            offset: const Offset(180, 0),
            padding: EdgeInsets.zero,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: Colors.white,
            ),
          ),
          // 构建二级菜单项
          items: (item['details'] as List).map<DropdownMenuItem<String>>((detail) {
            return DropdownMenuItem<String>(
              value: detail['url'],
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Text(
                  detail['name'],
                  style: TextStyle(
                    fontSize: fontSize ?? 14,
                    color: textColor ?? Colors.black.withValues(alpha: 0.7),
                  ),
                ),
              ),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              onSelected(value);
            }
          },
        ),
      ),
    );
  }
}
