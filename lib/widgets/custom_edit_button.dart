import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class CustomEditButton extends StatefulWidget {
  final VoidCallback? onPressed;
  final String iconPath;
  final double iconWidth;
  final double iconHeight;
  final Color defaultIconColor; // 图标默认颜色
  final Color hoverIconColor;   // 图标悬停颜色
  final Color backgroundColor;
  final Color hoverBackgroundColor; // 悬停时的背景颜色
  final double? radius; // 简单圆角控制，优先级低于borderRadius
  final BorderRadiusGeometry? borderRadius; // 高级圆角控制，优先级高于radius
  final EdgeInsetsGeometry? padding;
  final String? tooltip;
  final Duration? tooltipDelay; // 提示显示延迟

  const CustomEditButton({
    super.key,
    required this.onPressed,
    required this.iconPath,
    this.iconWidth = 16.0,
    this.iconHeight = 16.0,
    this.defaultIconColor = const Color(0xFF8D8E93),   // 默认图标颜色
    this.hoverIconColor = Colors.white,               // 悬停时图标颜色
    this.backgroundColor = const Color(0xFFF3F4F8),   // 默认背景色
    this.hoverBackgroundColor = const Color(0xFF0C75F8), // 指定的悬停背景色
    this.radius, // 圆角半径，如果设置了borderRadius则此属性无效
    this.borderRadius, // 自定义圆角，优先级高于radius
    this.padding,
    this.tooltip,
    this.tooltipDelay, // 提示延迟时间，默认500ms
  });

  @override
  State<CustomEditButton> createState() => _CustomEditButtonState();
}

class _CustomEditButtonState extends State<CustomEditButton> {
  bool _isHovered = false;
  bool _showTooltip = false;

  void _onEnter() {
    setState(() => _isHovered = true);
    if (widget.tooltip != null && widget.tooltip!.isNotEmpty) {
      Future.delayed(
        widget.tooltipDelay ?? const Duration(milliseconds: 1000),
        () {
          if (_isHovered && mounted) {
            setState(() => _showTooltip = true);
          }
        },
      );
    }
  }

  void _onExit() {
    setState(() {
      _isHovered = false;
      _showTooltip = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    // 圆角优先级：borderRadius > radius > 默认值8.0
    late final BorderRadiusGeometry effectiveBorderRadius;
    if (widget.borderRadius != null) {
      effectiveBorderRadius = widget.borderRadius!;
    } else if (widget.radius != null) {
      effectiveBorderRadius = BorderRadius.circular(widget.radius!);
    } else {
      effectiveBorderRadius = BorderRadius.circular(8.0);
    }
    
    final effectivePadding = widget.padding ?? const EdgeInsets.all(8.0);
    final Color currentIconColor = _isHovered ? widget.hoverIconColor : widget.defaultIconColor;

    return Stack(
      clipBehavior: Clip.none,
      children: [
        MouseRegion(
          onEnter: (_) => _onEnter(),
          onExit: (_) => _onExit(),
          child: IconButton(
            icon: SvgPicture.asset(
              widget.iconPath,
              width: widget.iconWidth,
              height: widget.iconHeight,
              colorFilter: ColorFilter.mode(
                currentIconColor, // 根据悬停状态改变图标颜色
                BlendMode.srcIn,
              ),
            ),
            onPressed: widget.onPressed,
            style: IconButton.styleFrom(
              backgroundColor: _isHovered ? widget.hoverBackgroundColor : widget.backgroundColor,
              shape: RoundedRectangleBorder(
                borderRadius: effectiveBorderRadius,
              ),
              padding: effectivePadding,
            ),
            splashRadius: 20,
          ),
        ),
        // 自定义tooltip
        if (_showTooltip && widget.tooltip != null && widget.tooltip!.isNotEmpty)
          Positioned(
            bottom: 50, // 在按钮上方显示
            child: Center(
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.grey[700], // 更柔和的灰色背景
                  borderRadius: BorderRadius.circular(4),
                  boxShadow: const [
                    BoxShadow(
                      color: Colors.black26,
                      blurRadius: 4,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: Text(
                  widget.tooltip!,
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.9), // 90%透明度的白色文字
                    fontSize: 12,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ),
      ],
    );
  }
}
