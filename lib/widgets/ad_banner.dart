import 'package:flutter/material.dart';
import 'ad_banner_widget.dart';

/// 默认的AdBanner组件
class AdBanner extends StatelessWidget {
  const AdBanner({super.key});

  @override
  Widget build(BuildContext context) {
    // 示例广告数据
    final List<AdData> sampleAds = [
      const AdData(
        title: '我们网页开业啦~购买套餐可享优惠',
        description: '新用户注册即可获得7天免费试用，立即体验我们的专业代理服务！',
        icon: Icons.celebration,
        color: Colors.orange,
      ),
      const AdData(
        title: '限时特惠：代理套餐8折优惠中',
        description: '高性能代理服务器，稳定快速，支持全球多个地区节点。',
        icon: Icons.local_offer,
        color: Colors.red,
      ),
      const AdData(
        title: '新功能上线：智能浏览器环境管理',
        description: '全新的浏览器指纹管理功能，让您的网络活动更加安全可靠。',
        icon: Icons.auto_awesome,
        color: Colors.blue,
      ),
      const AdData(
        title: '专业团队7×24小时技术支持',
        description: '遇到问题？我们的专业技术团队随时为您提供帮助和解决方案。',
        icon: Icons.support_agent,
        color: Colors.green,
      ),
    ];

    return AdBannerWidget(
      ads: sampleAds,
      duration: const Duration(seconds: 4), // 4秒切换一次
      height: 40,
      backgroundColor: Colors.white,
      titleStyle: const TextStyle(
        fontSize: 12,
        color: Color(0xFF666666),
        fontWeight: FontWeight.w400,
      ),
      onAdTap: (ad) {
        // 自定义点击处理逻辑
        _handleAdClick(context, ad);
      },
    );
  }

  /// 处理广告点击
  void _handleAdClick(BuildContext context, AdData ad) {
    // 可以在这里实现自定义的点击处理逻辑
    // 比如跳转到特定页面、打开链接等
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        title: Row(
          children: [
            if (ad.icon != null) ...[
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: ad.color?.withOpacity(0.1) ?? Colors.grey.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  ad.icon,
                  color: ad.color ?? Colors.grey,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
            ],
            Expanded(
              child: Text(
                ad.title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (ad.description != null) ...[
              Text(
                ad.description!,
                style: const TextStyle(
                  fontSize: 14,
                  color: Color(0xFF666666),
                  height: 1.5,
                ),
              ),
              const SizedBox(height: 16),
            ],
            Text(
              '点击下方按钮了解更多详情',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              '稍后再说',
              style: TextStyle(color: Colors.grey),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // 这里可以实现具体的业务逻辑
              // 比如跳转到购买页面、打开链接等
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('您点击了：${ad.title}'),
                  duration: const Duration(seconds: 2),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: ad.color ?? Theme.of(context).primaryColor,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('了解更多'),
          ),
        ],
      ),
    );
  }
} 
