import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

/// 支持多选且保持打开的标签选择器
class TagSelectorWidget extends HookWidget {
  final String hintText;
  final List<Map<String, dynamic>> items;
  final List<Map<String, dynamic>> selectedTags;
  final Function(List<Map<String, dynamic>>) onTagsChanged;
  final double? height;
  final double? width;
  final Widget? suffixWidget; // 可选的后缀widget，用于替换默认的下拉箭头
  final bool showSelectAllButton; // 是否显示全选按钮

  const TagSelectorWidget({
    super.key,
    required this.hintText,
    required this.items,
    required this.selectedTags,
    required this.onTagsChanged,
    this.height = 120,
    this.width,
    this.suffixWidget,
    this.showSelectAllButton = false,
  });

  @override
  Widget build(BuildContext context) {
    final selectedValues = selectedTags.map((tag) => tag['value']).toSet();
    final availableItems = items.where((item) => !selectedValues.contains(item['value'])).toList();
    
    // 控制下拉菜单的打开/关闭状态
    final isDropdownOpen = useState(false);
    final overlayEntry = useRef<OverlayEntry?>(null);
    final buttonKey = useMemoized(() => GlobalKey());

    // 关闭下拉菜单
    void closeDropdown() {
      overlayEntry.value?.remove();
      overlayEntry.value = null;
      isDropdownOpen.value = false;
    }

    // 打开下拉菜单
    void openDropdown() {
      if (isDropdownOpen.value || availableItems.isEmpty) return;
      
      final renderBox = buttonKey.currentContext?.findRenderObject() as RenderBox?;
      if (renderBox == null) return;
      
      final size = renderBox.size;
      final offset = renderBox.localToGlobal(Offset.zero);
      
      overlayEntry.value = OverlayEntry(
        builder: (context) => _DropdownOverlay(
          offset: offset,
          size: size,
          items: items,
          selectedTags: selectedTags,
          onTagsChanged: (newTags) {
            // 直接使用overlay返回的完整标签列表
            onTagsChanged(newTags);
            
            // 如果所有项目都已选择，关闭菜单
            if (newTags.length >= items.length) {
              closeDropdown();
            }
          },
          onClose: closeDropdown,
        ),
      );
      
      Overlay.of(context).insert(overlayEntry.value!);
      isDropdownOpen.value = true;
    }

    // 确保组件销毁时清理overlay
    useEffect(() {
      return () {
        overlayEntry.value?.remove();
      };
    }, []);

    final children = [
      // 下拉选择器按钮
      if (availableItems.isNotEmpty)
        GestureDetector(
          key: buttonKey,
          onTap: isDropdownOpen.value ? closeDropdown : openDropdown,
          child: Container(
            height: 40,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: const Color(0xFFF3F4F8),
              borderRadius: BorderRadius.circular(50),
              border: isDropdownOpen.value 
                  ? Border.all(color: const Color(0xFF0C75F8), width: 2)
                  : null,
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    hintText,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF8D8E93),
                    ),
                  ),
                ),
                suffixWidget ?? 
                (showSelectAllButton 
                  ? GestureDetector(
                      onTap: () {
                        // 全选逻辑：选择所有可选项目
                        final allAvailableItems = List<Map<String, dynamic>>.from(items);
                        onTagsChanged(allAvailableItems);
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: const Color(0xFF016CFA),
                          borderRadius: BorderRadius.circular(50),
                        ),
                        child: const Text(
                          '全选',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    )
                  : AnimatedRotation(
                      duration: const Duration(milliseconds: 200),
                      turns: isDropdownOpen.value ? 0.5 : 0,
                      child: const Icon(
                        Icons.keyboard_arrow_down,
                        size: 16,
                        color: Color(0xFF8D8E93),
                      ),
                    )
                ),
              ],
            ),
          ),
        ),
      
      // 间距
      if (availableItems.isNotEmpty && selectedTags.isNotEmpty)
        const SizedBox(height: 8),
      
      // 已选标签区域
      if (selectedTags.isNotEmpty)
        height == null
          ? Wrap(
              spacing: 8,
              runSpacing: 8,
              children: _buildTagWidgets(),
            )
          : Expanded(
              child: Wrap(
                spacing: 8,
                runSpacing: 8,
                children: _buildTagWidgets(),
              ),
            ),
      
      // 空状态提示
      if (selectedTags.isEmpty && availableItems.isEmpty)
        height == null
          ? const Padding(
              padding: EdgeInsets.symmetric(vertical: 20),
              child: Center(
                child: Text(
                  '暂无可选项',
                  style: TextStyle(
                    color: Colors.grey,
                    fontSize: 14,
                  ),
                ),
              ),
            )
          : const Expanded(
              child: Center(
                child: Text(
                  '暂无可选项',
                  style: TextStyle(
                    color: Colors.grey,
                    fontSize: 14,
                  ),
                ),
              ),
            ),
    ];

    return Container(
      width: width,
      height: height,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: height == null ? MainAxisSize.min : MainAxisSize.max,
        children: children,
      ),
    );
  }

  /// 构建标签组件列表
  List<Widget> _buildTagWidgets() {
    return selectedTags.map((tag) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: const Color(0xFF016CFA).withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: const Color(0xFF016CFA).withValues(alpha: 0.3)),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              tag['label'] ?? '',
              style: const TextStyle(
                fontSize: 12,
                color: Color(0xFF016CFA),
              ),
            ),
            const SizedBox(width: 4),
            InkWell(
              onTap: () {
                final newTags = List<Map<String, dynamic>>.from(selectedTags)
                  ..removeWhere((item) => item['value'] == tag['value']);
                onTagsChanged(newTags);
              },
              child: Container(
                padding: const EdgeInsets.all(2),
                decoration: BoxDecoration(
                  color: const Color(0xFF016CFA).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.close,
                  size: 12,
                  color: Color(0xFF016CFA),
                ),
              ),
            ),
          ],
        ),
      );
    }).toList();
  }
}

/// 自定义下拉菜单覆盖层
class _DropdownOverlay extends StatefulWidget {
  final Offset offset;
  final Size size;
  final List<Map<String, dynamic>> items;
  final List<Map<String, dynamic>> selectedTags;
  final Function(List<Map<String, dynamic>>) onTagsChanged;
  final VoidCallback onClose;

  const _DropdownOverlay({
    required this.offset,
    required this.size,
    required this.items,
    required this.selectedTags,
    required this.onTagsChanged,
    required this.onClose,
  });

  @override
  State<_DropdownOverlay> createState() => _DropdownOverlayState();
}

class _DropdownOverlayState extends State<_DropdownOverlay> {
  late List<Map<String, dynamic>> currentSelectedTags;

  @override
  void initState() {
    super.initState();
    currentSelectedTags = List.from(widget.selectedTags);
  }

  @override
  Widget build(BuildContext context) {
    // 动态计算可选项，排除已选择的项目
    final selectedValues = currentSelectedTags.map((tag) => tag['value']).toSet();
    final availableItems = widget.items.where((item) => !selectedValues.contains(item['value'])).toList();
    
    return Stack(
      children: [
        // 点击背景关闭菜单
        Positioned.fill(
          child: GestureDetector(
            onTap: widget.onClose,
            child: Container(color: Colors.transparent),
          ),
        ),
        // 下拉菜单内容
        Positioned(
          left: widget.offset.dx,
          top: widget.offset.dy + widget.size.height + 4,
          width: widget.size.width,
          child: Material(
            elevation: 8,
            borderRadius: BorderRadius.circular(8),
            child: Container(
              constraints: const BoxConstraints(maxHeight: 200),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: const Color(0xFFE9ECEF)),
              ),
              child: availableItems.isEmpty
                  ? const Padding(
                      padding: EdgeInsets.all(16),
                      child: Text(
                        '无可选项',
                        style: TextStyle(
                          color: Colors.grey,
                          fontSize: 14,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    )
                  : ListView.separated(
                      shrinkWrap: true,
                      padding: const EdgeInsets.symmetric(vertical: 4),
                      itemCount: availableItems.length,
                      separatorBuilder: (context, index) => const Divider(
                        height: 1,
                        color: Color(0xFFF5F5F5),
                      ),
                      itemBuilder: (context, index) {
                        final item = availableItems[index];
                        return InkWell(
                          onTap: () {
                            // 先更新本地状态
                            final newCurrentTags = List<Map<String, dynamic>>.from(currentSelectedTags)..add(item);
                            setState(() {
                              currentSelectedTags = newCurrentTags;
                            });
                            // 调用父组件回调，使用最新的本地状态
                            widget.onTagsChanged(newCurrentTags);
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 12,
                            ),
                            child: Row(
                              children: [
                                Expanded(
                                  child: Text(
                                    item['label'] ?? '',
                                    style: const TextStyle(
                                      fontSize: 14,
                                      color: Color(0xFF333333),
                                    ),
                                  ),
                                ),
                                const Icon(
                                  Icons.add,
                                  size: 16,
                                  color: Color(0xFF0C75F8),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
            ),
          ),
        ),
      ],
    );
  }
} 
