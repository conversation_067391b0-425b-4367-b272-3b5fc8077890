import 'package:flutter/material.dart';

class CustomRadioGroup extends StatefulWidget {
  /// 要显示的选项标签列表。
  final List<String> items;

  /// 当前选中的项目。如果为 null，则初始没有项目被选中。
  final String? selectedItem;

  /// 当选中项改变时的回调。传递新选中项的标签。
  final ValueChanged<String> onChanged;

  /// 单选按钮组的排列方向（水平或垂直）。
  final Axis direction;

  /// 主轴方向上各个单选项之间的间距。
  final double spacing;

  /// 交叉轴方向上各个单选项之间的间距（如果发生换行/换列）。
  final double runSpacing;

  /// 方形单选图标的大小。
  final double radioIconSize;

  /// 单选图标的边框圆角。
  final double radioIconBorderRadius;

  /// 选中状态下单选图标的颜色（填充和边框）。
  final Color activeRadioColor;

  /// 未选中状态下单选图标边框的颜色。
  final Color inactiveRadioBorderColor;

  /// 未选中状态下单选图标的背景颜色。
  final Color inactiveRadioBackgroundColor;

  /// 选项标签的文本样式。
  final TextStyle labelStyle;

  /// 单选图标与其文本标签之间的间距。
  final double spaceBetweenRadioAndText;

  /// 可选：选中项标签的文本样式。如果为 null，则使用 `labelStyle`。
  final TextStyle? selectedLabelStyle;

  /// 选中状态下勾号图标的颜色。
  final Color checkMarkColor;

  const CustomRadioGroup({
    super.key,
    required this.items,
    this.selectedItem,
    required this.onChanged,
    this.direction = Axis.horizontal,
    this.spacing = 16.0, // 项目之间的默认间距
    this.runSpacing = 8.0, // 如果换行，各行之间的默认间距
    this.radioIconSize = 18.0, // 近似图片中的大小
    this.radioIconBorderRadius = 4.0, // 近似图片中的圆角
    this.activeRadioColor = const Color(0xFF3D7AF2), // 选中状态的蓝色
    this.inactiveRadioBorderColor = const Color(0xFF3D7AF2), // 未选中时边框蓝色，根据图片
    this.inactiveRadioBackgroundColor = Colors.transparent, // 未选中时背景透明
    this.labelStyle = const TextStyle(fontSize: 16, color: Color(0xFF757575)), // 灰色文本，根据图片
    this.spaceBetweenRadioAndText = 8.0,
    this.selectedLabelStyle,
    this.checkMarkColor = Colors.white, // 选中时勾号的默认颜色
  });

  @override
  State<CustomRadioGroup> createState() => _CustomRadioGroupState();
}

class _CustomRadioGroupState extends State<CustomRadioGroup> {
  String? _selectedItem;

  @override
  void initState() {
    super.initState();
    _selectedItem = widget.selectedItem;
  }

  @override
  void didUpdateWidget(CustomRadioGroup oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 如果父 Widget 更改了 selectedItem 属性，则更新内部状态
    if (widget.selectedItem != oldWidget.selectedItem) {
      setState(() {
        _selectedItem = widget.selectedItem;
      });
    }
  }

  Widget _buildRadioItem(String item) {
    bool isSelected = _selectedItem == item;
    
    TextStyle currentLabelStyle = widget.labelStyle;
    if (isSelected && widget.selectedLabelStyle != null) {
      currentLabelStyle = widget.selectedLabelStyle!;
    }
    // 示例：如果您希望选中的文本默认变为蓝色：
    // else if (isSelected) {
    //   currentLabelStyle = widget.labelStyle.copyWith(color: widget.activeRadioColor);
    // }

    return MouseRegion(
      cursor: SystemMouseCursors.click, // 鼠标悬停时显示手型光标
      child: GestureDetector(
        onTap: () {
          if (_selectedItem != item) { // 仅当选择实际更改时才更新
            setState(() {
              _selectedItem = item;
            });
            widget.onChanged(item);
          }
        },
        behavior: HitTestBehavior.opaque, // 使包括内边距在内的整个区域都可点击
        child: Row(
          mainAxisSize: MainAxisSize.min, // 对于 Wrap 正确调整项目大小至关重要
          children: [
            Container(
              width: widget.radioIconSize,
              height: widget.radioIconSize,
              decoration: BoxDecoration(
                color: isSelected ? widget.activeRadioColor : widget.inactiveRadioBackgroundColor,
                border: Border.all(
                  color: isSelected ? widget.activeRadioColor : widget.inactiveRadioBorderColor,
                  width: 2.0, // 单选图标的边框宽度
                ),
                borderRadius: BorderRadius.circular(widget.radioIconBorderRadius),
              ),
              child: isSelected
                  ? Icon(
                      Icons.check,
                      size: widget.radioIconSize * 0.8, // 使勾号略小于方框
                      color: widget.checkMarkColor,
                    )
                  : null, // 未选中时不显示任何子Widget
            ),
            SizedBox(width: widget.spaceBetweenRadioAndText),
            Text(item, style: currentLabelStyle),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Wrap(
      direction: widget.direction,
      spacing: widget.spacing,       // 主轴上相邻项目之间的间隙
      runSpacing: widget.runSpacing, // 如果换行，各行之间的间隙（与宽度有限的水平布局相关）
      children: widget.items.map((item) => _buildRadioItem(item)).toList(),
    );
  }
}
