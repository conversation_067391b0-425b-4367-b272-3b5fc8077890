import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class CustomIconTextButton extends HookConsumerWidget {
  final String label;
  final String iconPath;
  final VoidCallback onPressed;
  const CustomIconTextButton({super.key, required this.label, required this.iconPath, required this.onPressed});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return TextButton(
      onPressed: () {
        // 处理帮助中心按钮点击事件
      },
      child: Row(
        children: [
          SvgPicture.asset(
            iconPath,
            width: 20,
            height: 20,
          ),
          const SizedBox(width: 8), // 添加间距
          Text(label, style: const TextStyle(color: Color(0xFF8D8E93)),),
        ],
      ),
    );
  }
}
