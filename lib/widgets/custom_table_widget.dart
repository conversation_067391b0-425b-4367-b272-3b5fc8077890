import 'package:flutter/material.dart';
import 'package:frontend_re/widgets/custom_border_button.dart';
import 'package:dropdown_button2/dropdown_button2.dart';

class TableColumn {
  final String label; // 列标签
  final double width; // 列宽度比例
  final bool isNumeric; // 是否为数字列
  final Widget? customWidget; // 自定义列头部件
  final bool isCenter; // 是否居中
  TableColumn({
    required this.label,
    required this.width,
    this.isNumeric = false,
    this.customWidget,
    this.isCenter = false,
  });
}

class CustomTableWidget extends StatefulWidget {
  final List<TableColumn> columns; // 表头定义
  final List<Map<String, dynamic>> data; // 表格数据
  final Function(int offset, int pageSize) onPageChange; // 回调偏移量和分页大小
  final Function(int, String, dynamic)? onCellTap; // 单元格点击回调
  final double? width; // 表格宽度
  final double? height; // 表格高度
  final int totalRecords; // 总数据量
  final int? initialPage; // 初始页码
  final int? initialPageSize; // 初始分页大小
  final String pageStorageKey; // 唯一标识符
  final bool showRowSelection; // 控制是否显示行选择功能
  final Function(Set<int> selectedRowsIndexes)? onSelectionChanged; // 新增：选中行变化时的回调
  final String noDataImagePath;
  final String noDataText;
  final String noDataButtonText;
  final Function()? noDataButtonEvent;

  const CustomTableWidget({
    super.key,
    required this.columns,
    required this.data,
    required this.onPageChange,
    this.onCellTap,
    this.width,
    this.height,
    required this.totalRecords,
    this.initialPage = 1,
    this.initialPageSize = 10,
    required this.pageStorageKey, 
    this.showRowSelection = false, 
    this.onSelectionChanged, // 初始化回调
    required this.noDataImagePath,
    required this.noDataText,
    required this.noDataButtonText,
    this.noDataButtonEvent,
  });

  @override
  CustomTableWidgetState createState() => CustomTableWidgetState();
}

class CustomTableWidgetState extends State<CustomTableWidget> {
  late int currentPage; // 当前页
  late int pageSize; // 每页条数
  Set<int> hoveredRows = {}; // 存储悬浮行的索引
  Set<int> selectedRows = {}; // 存储选中行的索引
  late PageStorageBucket? pageStorageBucket;

  @override
  void initState() {
    super.initState();
    currentPage = widget.initialPage!;
    pageSize = widget.initialPageSize!;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    pageStorageBucket = PageStorage.of(context);
    if (pageStorageBucket != null) {
      final storedPage = pageStorageBucket!
          .readState(context, identifier: widget.pageStorageKey);
      if (storedPage is Map<String, int>) {
        setState(() {
          currentPage = storedPage['currentPage'] ?? currentPage;
          pageSize = storedPage['pageSize'] ?? pageSize;
        });
      }
    }
  }

  @override
  void dispose() {
    if (pageStorageBucket != null) {
      pageStorageBucket!.writeState(
        context,
        {'currentPage': currentPage, 'pageSize': pageSize},
        identifier: widget.pageStorageKey,
      );
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final totalPages =
        (widget.totalRecords / pageSize).ceil();

    return LayoutBuilder(
      builder: (context, constraints) {
        final effectiveWidth = widget.width ?? constraints.maxWidth;

        return Theme(
          data: Theme.of(context).copyWith(
            dividerColor: Colors.transparent,
            dividerTheme: const DividerThemeData(
              color: Colors.transparent,
              space: 0,
              thickness: 0,
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildHeader(effectiveWidth),
              _buildDataTable(effectiveWidth),
              _buildPagination(totalPages),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeader(double effectiveWidth) {
    final bool allRowsSelected = widget.showRowSelection && widget.data.isNotEmpty && selectedRows.length == widget.data.length;
    List<DataColumn> headerColumns = [];

    if (widget.showRowSelection) {
      headerColumns.add(
        DataColumn(
          label: Checkbox(
            value: allRowsSelected,
            onChanged: (bool? isSelected) {
              setState(() {
                if (isSelected ?? false) {
                  selectedRows = List.generate(widget.data.length, (index) => index).toSet();
                } else {
                  selectedRows.clear();
                }
                widget.onSelectionChanged?.call(selectedRows); // 调用回调
              });
            },
          ),
        ),
      );
    }

    headerColumns.addAll(widget.columns.asMap().entries.map((entry) {
      final column = entry.value;
      return DataColumn(
        numeric: column.isNumeric,
        label: SizedBox(
          width: effectiveWidth * column.width,
          child: Row(
            children: [
              Expanded(
                child: Text(
                  textAlign: column.isCenter ? TextAlign.center : TextAlign.left,
                  column.label,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF333333),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }).toList());

    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(30),
          topRight: Radius.circular(30),
        ),
        border: Border(
          bottom: BorderSide(color: Color(0xFFF8F8F8), width: 2),
        ),
      ),
      child: DataTable(
        headingRowHeight: 50,
        columnSpacing: widget.showRowSelection ? 10 : 20, // 根据是否显示选择框调整列间距
        dividerThickness: 0,
        columns: headerColumns,
        rows: const [],
      ),
    );
  }

  Widget _buildDataTable(double effectiveWidth) {
    return Expanded(
      child: Container(
        color: Colors.white,
        child: widget.data.isEmpty
            ? Center(
                child: Column(
                  spacing: 6,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset(widget.noDataImagePath, width: 400),
                    Text(
                      widget.noDataText,
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    CustomBorderButton(
                      height: 40,
                      // width: 120,
                      iconPath: 'assets/svg/add.svg',
                      backgroundColor: const Color(0xFF0C75F8),
                      textColor: Colors.white,
                      iconColor: Colors.white,
                      text: widget.noDataButtonText,
                      onPressed: () {
                        widget.noDataButtonEvent?.call();
                      },
                    )
                  ],
                ),
              )
            : SingleChildScrollView(
                scrollDirection: Axis.vertical,
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: DataTable(
                    columnSpacing: widget.showRowSelection ? 10 : 20, // 根据是否显示选择框调整列间距
                    headingRowHeight: 0,
                    showCheckboxColumn: false,
                    columns: [
                      if (widget.showRowSelection)
                        const DataColumn(label: SizedBox.shrink()), 
                      ...widget.columns
                          .map((column) => DataColumn(
                                numeric: column.isNumeric,
                                label: const SizedBox.shrink(),
                              ))
                    ],
                    rows: widget.data.asMap().entries.map((entry) {
                      final index = entry.key;
                      final rowData = entry.value;

                      return DataRow(
                        selected: widget.showRowSelection ? selectedRows.contains(index) : false,
                        onSelectChanged: widget.showRowSelection
                            ? (bool? isSelected) {
                                setState(() {
                                  if (isSelected ?? false) {
                                    selectedRows.add(index);
                                  } else {
                                    selectedRows.remove(index);
                                  }
                                  widget.onSelectionChanged?.call(selectedRows); // 调用回调
                                });
                              }
                            : null,
                        color: WidgetStateProperty.resolveWith<Color?>(
                          (Set<WidgetState> states) {
                            if (widget.showRowSelection && states.contains(WidgetState.selected)) {
                              return Theme.of(context).colorScheme.primary.withValues(alpha: 0.08);
                            }
                            if (hoveredRows.contains(index)) {
                              return const Color(0xFFEDF0FD).withValues(alpha: 0.89);
                            }
                            return null;
                          },
                        ),
                        cells: [
                          if (widget.showRowSelection)
                            DataCell(
                              Checkbox(
                                value: selectedRows.contains(index),
                                onChanged: (bool? isSelected) {
                                  setState(() {
                                    if (isSelected ?? false) {
                                      selectedRows.add(index);
                                    } else {
                                      selectedRows.remove(index);
                                    }
                                    widget.onSelectionChanged?.call(selectedRows); // 调用回调
                                  });
                                },
                              ),
                            ),
                          ...widget.columns.map((column) {
                            final cellValue = rowData[column.label];
                            return DataCell(
                              MouseRegion(
                                onEnter: (_) {
                                  setState(() {
                                    hoveredRows.add(index);
                                  });
                                },
                                onExit: (_) {
                                  setState(() {
                                    hoveredRows.remove(index);
                                  });
                                },
                                child: SizedBox(
                                  width: effectiveWidth * column.width,
                                  child: DefaultTextStyle(
                                    style: const TextStyle(fontSize: 12, color: Color(0xFF333333)),
                                    child: _buildCellContent(cellValue),
                                  ), 
                                ),
                              ),
                            );
                          }).toList(),
                        ],
                      );
                    }).toList(),
                  ),
                ),
              ),
      ),
    );
  }

  Widget _buildCellContent(dynamic value) {
    if (value == null) return const Text('-');
    if (value is Widget) {
      return Align(
        alignment: Alignment.centerLeft,
        child: value,
      );
    }
    return Text(value.toString());
  }

  Widget _buildPagination(int totalPages) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
      decoration: const BoxDecoration(
        color: Colors.white,
        // borderRadius: BorderRadius.only(
        //   bottomLeft: Radius.circular(30),
        //   bottomRight: Radius.circular(30),
        // ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text('总条数: ${widget.totalRecords}'),
          IconButton(
            icon: const Icon(Icons.chevron_left),
            onPressed: currentPage > 1
                ? () {
                    _changePage(currentPage - 1);
                  }
                : null,
          ),
          Text('$currentPage / $totalPages'),
          IconButton(
            icon: const Icon(Icons.chevron_right),
            onPressed: currentPage < totalPages
                ? () {
                    _changePage(currentPage + 1);
                  }
                : null,
          ),
          SizedBox(
            width: 120,
            height: 32,
            child: DropdownButtonHideUnderline(
              child: DropdownButton2<int>(
                isExpanded: true,
                value: pageSize,
                items: [10, 20, 50, 100]
                    .map((size) => DropdownMenuItem<int>(
                          value: size,
                          child: Text(
                            '$size条/页',
                            style: const TextStyle(
                              fontSize: 14,
                              color: Color(0xFF333333),
                            ),
                          ),
                        ))
                    .toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      pageSize = value;
                      currentPage = 1;
                    });
                    widget.onPageChange(0, pageSize);
                  }
                },
                iconStyleData: const IconStyleData(
                  icon: Icon(Icons.arrow_drop_down, color: Color(0xFF666666)),
                  iconSize: 20,
                ),
                buttonStyleData: ButtonStyleData(
                  height: 32,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(6),
                    color: Colors.transparent,
                  ),
                ),
                dropdownStyleData: DropdownStyleData(
                  maxHeight: 200,
                  padding: EdgeInsets.zero,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: Colors.white,
                  ),
                  offset: const Offset(0, -4),
                ),
                menuItemStyleData: const MenuItemStyleData(
                  height: 40,
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _changePage(int newPage) {
    setState(() {
      currentPage = newPage;
    });
    final offset = (currentPage - 1) * pageSize;
    widget.onPageChange(offset, pageSize); 
  }
}
