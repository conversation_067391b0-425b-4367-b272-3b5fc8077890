import 'package:flutter/material.dart';
import 'package:frontend_re/core/providers/auth_state_provider.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class AuthCheckWidget extends ConsumerWidget {
  const AuthCheckWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authStateProvider);
    
    return authState.when(
      data: (isAuthenticated) {
        // 认证检查完成，立即跳转
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (isAuthenticated) {
            context.go('/workbench');
          } else {
            context.go('/login');
          }
        });
        
        // 返回透明页面，因为会立即跳转
        return const Scaffold(
          backgroundColor: Colors.transparent,
          body: SizedBox.shrink(),
        );
      },
      loading: () => const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      ),
      error: (error, stack) {
        // 出错时跳转到登录页
        WidgetsBinding.instance.addPostFrameCallback((_) {
          context.go('/login');
        });
        
        return const Scaffold(
          body: SizedBox.shrink(),
        );
      },
    );
  }
} 
