import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:frontend_re/router/navigation_history_provider.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../core/constants/search_config.dart';

// 模拟搜索Provider
final searchResultsProvider = StateProvider<List<SearchResult>>((ref) => []);

class GlobalSearchWidget extends HookConsumerWidget {
  const GlobalSearchWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final searchController = useTextEditingController();
    final focusNode = useFocusNode();
    final isSearching = useState(false);
    final searchResults = ref.watch(searchResultsProvider);
    final overlayEntry = useRef<OverlayEntry?>(null);

    // 搜索逻辑
    void performSearch(String query) {
      final results = SearchConfig.performSearch(query);
      ref.read(searchResultsProvider.notifier).state = results;
    }

    // 快捷键监听
    useEffect(() {
      bool handleKeyEvent(KeyEvent event) {
        if (event is KeyDownEvent &&
            event.logicalKey == LogicalKeyboardKey.keyK &&
            HardwareKeyboard.instance.isControlPressed) {
          focusNode.requestFocus();
          return true;
        }
        if (event is KeyDownEvent && event.logicalKey == LogicalKeyboardKey.escape) {
          searchController.clear();
          isSearching.value = false;
          ref.read(searchResultsProvider.notifier).state = [];
          focusNode.unfocus();
          overlayEntry.value?.remove();
          overlayEntry.value = null;
          return true;
        }
        return false;
      }

      HardwareKeyboard.instance.addHandler(handleKeyEvent);
      return () => HardwareKeyboard.instance.removeHandler(handleKeyEvent);
    }, []);

    // 创建Overlay条目
    OverlayEntry createOverlayEntry() {
      // 获取搜索框的位置
      final RenderBox? renderBox = context.findRenderObject() as RenderBox?;
      final Offset? offset = renderBox?.localToGlobal(Offset.zero);
      final Size? size = renderBox?.size;
      
      // 保存原始的context和ref，用于路由导航
      final originalContext = context;
      final originalRef = ref;

      return OverlayEntry(
        builder: (overlayContext) => Positioned(
          left: offset?.dx ?? 0,
          top: (offset?.dy ?? 0) + (size?.height ?? SearchConfig.searchBoxHeight) + SearchConfig.overlayOffset,
          width: size?.width ?? SearchConfig.searchBoxWidth,
          child: Material(
            elevation: 8,
            borderRadius: BorderRadius.circular(12),
            child: Container(
              constraints: const BoxConstraints(maxHeight: SearchConfig.overlayMaxHeight),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 20,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: searchResults.isEmpty
                  ? Container(
                      padding: const EdgeInsets.all(16),
                      child: const Text(
                        SearchConfig.noResultsText,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 12,
                          color: Color(0xFF999999),
                        ),
                      ),
                    )
                  : ListView.builder(
                      shrinkWrap: true,
                      padding: const EdgeInsets.symmetric(vertical: 4),
                      itemCount: searchResults.length,
                      itemBuilder: (context, index) {
                        final result = searchResults[index];
                        final isLast = index == searchResults.length - 1;
                        
                        return Column(
                          children: [
                            InkWell(
                              onTap: () {
                                // 清除搜索状态和Overlay
                                searchController.clear();
                                isSearching.value = false;
                                originalRef.read(searchResultsProvider.notifier).state = [];
                                focusNode.unfocus();
                                overlayEntry.value?.remove();
                                overlayEntry.value = null;

                                // 跳转 - 使用原始的context和ref
                                NavigationHelper.goToPublicRoute(originalContext, originalRef, result.route);
                              },
                              borderRadius: BorderRadius.circular(8),
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 8,
                                ),
                                child: Row(
                                  children: [
                                    // 图标
                                    Container(
                                      width: 28,
                                      height: 28,
                                      decoration: BoxDecoration(
                                        color: result.color.withValues(alpha: 0.1),
                                        borderRadius: BorderRadius.circular(6),
                                      ),
                                      child: Icon(
                                        result.icon,
                                        size: 14,
                                        color: result.color,
                                      ),
                                    ),
                                    const SizedBox(width: 10),
                                    
                                    // 文本信息
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: [
                                          Text(
                                            result.title,
                                            style: const TextStyle(
                                              fontSize: 13,
                                              fontWeight: FontWeight.w500,
                                              color: Color(0xFF333333),
                                            ),
                                            maxLines: 1,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                          const SizedBox(height: 2),
                                          Text(
                                            result.subtitle,
                                            style: const TextStyle(
                                              fontSize: 11,
                                              color: Color(0xFF999999),
                                            ),
                                            maxLines: 1,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ],
                                      ),
                                    ),
                                    
                                    // 类型标签
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 6,
                                        vertical: 2,
                                      ),
                                      decoration: BoxDecoration(
                                        color: result.color.withValues(alpha: 0.1),
                                        borderRadius: BorderRadius.circular(10),
                                        border: Border.all(
                                          color: result.color.withValues(alpha: 0.2),
                                          width: 0.5,
                                        ),
                                      ),
                                      child: Text(
                                        result.type,
                                        style: TextStyle(
                                          fontSize: 10,
                                          color: result.color,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            
                            // 分隔线
                            if (!isLast)
                              Container(
                                margin: const EdgeInsets.symmetric(horizontal: 12),
                                height: 0.5,
                                color: const Color(0xFFE2E8FF).withValues(alpha: 0.3),
                              ),
                          ],
                        );
                      },
                    ),
            ),
          ),
        ),
      );
    }

    // 显示/隐藏Overlay
    void updateOverlay() {
      // 移除旧的overlay
      overlayEntry.value?.remove();
      overlayEntry.value = null;

      // 如果需要显示搜索结果
      if (isSearching.value && (searchResults.isNotEmpty || searchController.text.isNotEmpty)) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          overlayEntry.value = createOverlayEntry();
          Overlay.of(context).insert(overlayEntry.value!);
        });
      }
    }

    // 监听状态变化，更新Overlay
    useEffect(() {
      updateOverlay();
      return null;
    }, [isSearching.value, searchResults]);

    // 清理Overlay
    useEffect(() {
      return () {
        overlayEntry.value?.remove();
        overlayEntry.value = null;
      };
    }, []);

    return SizedBox(
      width: SearchConfig.searchBoxWidth,
      height: SearchConfig.searchBoxHeight,
      child: TextField(
        controller: searchController,
        focusNode: focusNode,
        style: const TextStyle(
          fontSize: 14,
          color: Color(0xFF666666),
        ),
        onChanged: (value) {
          isSearching.value = value.isNotEmpty;
          performSearch(value);
        },
        decoration: InputDecoration(
          hintText: SearchConfig.hintText,
          hintStyle: const TextStyle(
            color: Color(0xFFBBBBBB),
            fontSize: 12,
          ),
          prefixIcon: const Icon(
            Icons.search,
            color: Color(0xFFBBBBBB),
            size: 16,
          ),
          filled: true,
          fillColor: Colors.white,
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(50),
            borderSide: const BorderSide(
              color: Color(0xFFEBEBEB),
              width: 1,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(50),
            borderSide: const BorderSide(
              color: Color(0xFF4A90E2),
              width: 1,
            ),
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 12,
            vertical: 6,
          ),
        ),
      ),
    );
  }
} 
