import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

class SBackButton extends HookConsumerWidget {
  final String? text;
  final String? iconPath;
  final VoidCallback onPressed;
  final Color? iconBackgroundColor;
  final Color? iconColor;
  final Color? textColor;
  final double? iconSize;
  final double? fontSize;
  final EdgeInsetsGeometry? padding;

  const SBackButton({
    super.key,
    this.text = '返回',
    this.iconPath = 'assets/svg/left.svg',
    required this.onPressed,
    this.iconBackgroundColor = const Color(0xFF0C75F8),
    this.iconColor = Colors.white,
    this.textColor = const Color(0xFF8D8E93),
    this.iconSize = 20.0,
    this.fontSize = 14.0,
    this.padding = const EdgeInsets.all(8.0),
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isHovered = useState(false);
    
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      onEnter: (_) => isHovered.value = true,
      onExit: (_) => isHovered.value = false,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(50),
        child: Padding(
          padding: padding!,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Center(
                child: Container(
                  width: iconSize,
                  height: iconSize,
                  decoration: BoxDecoration(
                    color: iconBackgroundColor,
                    borderRadius: BorderRadius.circular(50),
                  ),
                  child: Center(
                    child: SizedBox(
                      width: iconSize! / 2,
                      height: iconSize! / 2,
                      child: SvgPicture.asset(
                        iconPath!,
                        fit: BoxFit.contain,
                        colorFilter: ColorFilter.mode(
                          iconColor!,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              if (text != null && text!.isNotEmpty) ...[
                const SizedBox(width: 8),
                Text(
                  text!,
                  style: TextStyle(
                    fontSize: fontSize,
                    color: textColor,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
} 
