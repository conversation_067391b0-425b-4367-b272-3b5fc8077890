import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

/// 下划线下拉菜单,有文本框带检索
class UnderlineDropdownWidget<T> extends HookConsumerWidget {
  // 构造函数添加必要的参数
  const UnderlineDropdownWidget({
    super.key,
    required this.items, // 数据列表
    this.hintText = '请选择', // 提示文本
    this.width, // 控件宽度
    this.onSelected, // 选择回调
    this.initialSelection, // 初始选中值
    this.enableSearch = true, // 是否启用搜索
    this.textController, // 文本框控制器
    this.enabled = true, // 添加启用状态参数
    this.enableDropdown = true, // 下拉菜单启用状态
  });

  // 定义属性
  final List<T> items; // 下拉列表数据
  final String hintText; // 提示文本
  final double? width; // 控件宽度
  final Function(T?)? onSelected; // 选择回调函数
  final T? initialSelection; // 初始选中值
  final bool enableSearch; // 是否启用搜索功能
  final TextEditingController? textController; // 文本框控制器
  final bool enabled; // 启用状态
  final bool enableDropdown; // 下拉菜单启用状态
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return DropdownMenu<T>(
      width: width,
      // 启用状态
      enabled: enabled,
      // 文本控制器
      controller: textController,
      // 初始值
      initialSelection: initialSelection,
      // 提示文本
      hintText: hintText,
      // 是否启用搜索
      enableSearch: enableSearch,
      // 选择回调
      onSelected: onSelected,
      // 文本样式
      textStyle: const TextStyle(fontSize: 14),
      // 下拉菜单样式
      menuStyle: const MenuStyle(
        backgroundColor: WidgetStatePropertyAll(Colors.white),
        elevation: WidgetStatePropertyAll(8),
      ),
      // 输入框装饰
      inputDecorationTheme: InputDecorationTheme(
        hintStyle: const TextStyle(color: Colors.grey),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16),
        // 设置默认状态（启用但未聚焦）的边框
        enabledBorder: OutlineInputBorder(
          borderSide: const BorderSide(color: Colors.blue), // 默认蓝色边框
          borderRadius: BorderRadius.circular(50),
        ),
        // 设置聚焦状态的边框
        focusedBorder: OutlineInputBorder(
          borderSide: const BorderSide(color: Colors.blue, width: 2.0), // 聚焦时蓝色边框，可以稍微加粗
          borderRadius: BorderRadius.circular(50),
        ),
        // border 作为后备，但也设为蓝色
        border: const OutlineInputBorder(
          borderSide: BorderSide(color: Colors.blue),
        ),
        // 可以移除或修改填充色
        // fillColor: Color.fromARGB(255, 250, 97, 97),
      ),
      // 下拉选项列表
      dropdownMenuEntries: items
          .map((item) => DropdownMenuEntry<T>(
                value: item,
                label: item.toString(),
              ))
          .toList(),
    );
  }
}
