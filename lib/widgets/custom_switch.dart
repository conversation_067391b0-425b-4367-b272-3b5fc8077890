import 'package:flutter/material.dart';

class CustomSwitch extends StatelessWidget {
  /// 选项列表
  final List<String> options;
  
  /// 当前选中的索引
  final int selectedIndex;
  
  /// 选项变更回调
  final Function(int) onChanged;
  
  /// 自定义高度
  final double? height;

  /// 自定义宽度
  final double? width;
  
  /// 自定义背景颜色
  final Color? backgroundColor;
  
  /// 选中项的背景颜色
  final Color? selectedColor;
  
  /// 选中项的文字颜色
  final Color? selectedTextColor;
  
  /// 未选中项的文字颜色
  final Color? unselectedTextColor;

  const CustomSwitch({
    super.key,
    required this.options,
    required this.selectedIndex,
    required this.onChanged,
    this.height,
    this.width,
    this.backgroundColor,
    this.selectedColor,
    this.selectedTextColor,
    this.unselectedTextColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height ?? 50,
      width: width,
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      decoration: BoxDecoration(
        color: backgroundColor ?? const Color(0xFFF3F4F8),
        borderRadius: BorderRadius.circular(50),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: List.generate(
          options.length,
          (index) => MouseRegion(
            cursor: SystemMouseCursors.click,
            child: GestureDetector(
              onTap: () => onChanged(index),
              child: Container(
                width: 100,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: selectedIndex == index
                      ? selectedColor ?? const Color(0xFF0C75F8)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(50),
                ),
                child: Center(
                  child: Text(
                    options[index],
                    style: TextStyle(
                      color: selectedIndex == index 
                          ? selectedTextColor ?? Colors.white 
                          : unselectedTextColor ?? const Color(0xFF666666),
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
} 
