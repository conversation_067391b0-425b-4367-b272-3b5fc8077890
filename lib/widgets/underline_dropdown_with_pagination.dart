import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

/// 带触底刷新功能的下划线下拉菜单
class UnderlineDropdownWithPagination<T> extends HookConsumerWidget {
  // 构造函数添加必要的参数
  const UnderlineDropdownWithPagination({
    super.key,
    required this.items, // 数据列表
    required this.onLoadMore, // 加载更多回调
    this.hasMoreData = true, // 是否还有更多数据
    this.isLoading = false, // 是否正在加载
    this.hintText = '请选择', // 提示文本
    this.width, // 控件宽度
    this.onSelected, // 选择回调
    this.initialSelection, // 初始选中值
    this.enableSearch = true, // 是否启用搜索
    this.textController, // 文本框控制器
    this.enabled = true, // 添加启用状态参数
    this.enableDropdown = true, // 下拉菜单启用状态
    this.loadingIndicatorColor = Colors.blue, // 加载指示器颜色
  });

  // 定义属性
  final List<T> items; // 下拉列表数据
  final Future<void> Function() onLoadMore; // 加载更多回调函数
  final bool hasMoreData; // 是否还有更多数据
  final bool isLoading; // 是否正在加载
  final String hintText; // 提示文本
  final double? width; // 控件宽度
  final Function(T?)? onSelected; // 选择回调函数
  final T? initialSelection; // 初始选中值
  final bool enableSearch; // 是否启用搜索功能
  final TextEditingController? textController; // 文本框控制器
  final bool enabled; // 启用状态
  final bool enableDropdown; // 下拉菜单启用状态
  final Color loadingIndicatorColor; // 加载指示器颜色

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 使用hook创建滚动控制器
    final scrollController = useScrollController();
    
    // 使用Effect监听滚动事件
    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        // 设置滚动监听器
        scrollController.addListener(() {
          // 检测是否滚动到底部
          if (scrollController.hasClients && 
              scrollController.position.pixels >= 
              scrollController.position.maxScrollExtent - 20 && 
              !isLoading && 
              hasMoreData) {
            onLoadMore();
          }
        });
      });
      
      return () {
        // 移除监听器
        scrollController.removeListener(() {});
      };
    }, [scrollController, isLoading, hasMoreData]);
    
    // 下拉菜单对应的内容
    final dropdownMenuEntries = items.map((item) => 
      DropdownMenuEntry<T>(
        value: item,
        label: item.toString(),
      )
    ).toList();
    
    // 如果有更多数据或正在加载，添加一个加载指示器项
    if (hasMoreData || isLoading) {
      // 添加一个用于显示加载状态的项
      dropdownMenuEntries.add(
        DropdownMenuEntry<T>(
          value: null as T,
          label: '',
          labelWidget: Center(
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: isLoading 
                ? SizedBox(
                    height: 20, 
                    width: 20, 
                    child: CircularProgressIndicator(
                      strokeWidth: 2, 
                      color: loadingIndicatorColor,
                    )
                  )
                : const Text('上拉加载更多', style: TextStyle(color: Colors.grey, fontSize: 12)),
            ),
          ),
          enabled: false, // 禁用该项，防止被选中
        ),
      );
    }

    // 用NotificationListener包装DropdownMenu，来监听滚动事件
    return NotificationListener<ScrollNotification>(
      onNotification: (ScrollNotification notification) {
        // 在这里处理滚动触底事件
        if (notification is ScrollEndNotification) {
          if (notification.metrics.pixels >= notification.metrics.maxScrollExtent - 20 &&
              !isLoading && 
              hasMoreData) {
            onLoadMore();
          }
        }
        return false; // 返回false允许事件继续冒泡
      },
      child: DropdownMenu<T>(
        width: width,
        enabled: enabled,
        controller: textController,
        initialSelection: initialSelection,
        hintText: hintText,
        enableSearch: enableSearch,
        onSelected: onSelected,
        textStyle: const TextStyle(fontSize: 14),
        menuStyle: const MenuStyle(
          backgroundColor: WidgetStatePropertyAll(Colors.white),
          elevation: WidgetStatePropertyAll(8),
        ),
        inputDecorationTheme: InputDecorationTheme(
          hintStyle: const TextStyle(color: Colors.grey),
          contentPadding: const EdgeInsets.symmetric(horizontal: 16),
          enabledBorder: OutlineInputBorder(
            borderSide: const BorderSide(color: Colors.blue),
            borderRadius: BorderRadius.circular(50),
          ),
          focusedBorder: OutlineInputBorder(
            borderSide: const BorderSide(color: Colors.blue, width: 2.0),
            borderRadius: BorderRadius.circular(50),
          ),
          border: const OutlineInputBorder(
            borderSide: BorderSide(color: Colors.blue),
          ),
        ),
        dropdownMenuEntries: dropdownMenuEntries,
      ),
    );
  }
}

/// 使用示例：
/// ```dart
/// final items = useState<List<String>>(['项目1', '项目2', '项目3']);
/// final isLoading = useState(false);
/// final hasMoreData = useState(true);
///
/// Future<void> loadMoreItems() async {
///   isLoading.value = true;
///   await Future.delayed(const Duration(seconds: 1)); // 模拟网络请求
///   
///   // 加载更多数据
///   final newItems = ['新项目1', '新项目2', '新项目3'];
///   items.value = [...items.value, ...newItems];
///   
///   // 判断是否还有更多数据
///   hasMoreData.value = items.value.length < 20; // 示例：最多20项
///   isLoading.value = false;
/// }
///
/// return UnderlineDropdownWithPagination<String>(
///   items: items.value,
///   onLoadMore: loadMoreItems,
///   hasMoreData: hasMoreData.value,
///   isLoading: isLoading.value,
///   onSelected: (value) {
///     // 处理选择
///   },
/// );
/// ``` 
