import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class GlassCardWidget extends StatelessWidget {
  // 卡片尺寸
  final double width;
  final double height;
  
  // 图标相关
  final String? iconPath;
  final IconData? iconData;
  final Color iconColor;
  final Color iconBackgroundColor;
  final double iconSize;
  final double iconContainerSize;
  
  // 文字内容
  final String title;
  final String description;
  final String buttonText;
  
  // 文字样式
  final Color titleColor;
  final Color descriptionColor;
  final double titleFontSize;
  final double descriptionFontSize;
  
  // 按钮相关
  final Color buttonBackgroundColor;
  final Color buttonTextColor;
  final double buttonFontSize;
  final String? buttonIconPath;
  final VoidCallback? onButtonPressed;
  
  // 玻璃效果配置
  final Color glassColor;
  final double glassOpacity;
  final Color borderColor;
  final double borderOpacity;
  final double blurSigma;
  final double borderRadius;
  
  // 阴影配置
  final List<BoxShadow>? customShadows;
  final bool enableGlow;
  
  // 布局配置
  final EdgeInsetsGeometry? padding;
  final double iconTopOffset;


  const GlassCardWidget({
    super.key,
    // 必需参数
    required this.title,
    required this.description,
    required this.buttonText,
    
    // 尺寸
    this.width = 240,
    this.height = 160,
    
    // 图标
    this.iconPath,
    this.iconData,
    this.iconColor = Colors.white,
    this.iconBackgroundColor = const Color(0xFFFF7043),
    this.iconSize = 16,
    this.iconContainerSize = 40,
    
    // 文字样式
    this.titleColor = Colors.white,
    this.descriptionColor = Colors.white70,
    this.titleFontSize = 16,
    this.descriptionFontSize = 12,
    
    // 按钮
    this.buttonBackgroundColor = const Color(0xFF4A90E2),
    this.buttonTextColor = Colors.white,
    this.buttonFontSize = 12,
    this.buttonIconPath,
    this.onButtonPressed,
    
    // 玻璃效果
    this.glassColor = Colors.white,
    this.glassOpacity = 0.05,
    this.borderColor = Colors.white,
    this.borderOpacity = 0.2,
    this.blurSigma = 10,
    this.borderRadius = 16,
    
    // 阴影和发光
    this.customShadows,
    this.enableGlow = true,
    
    // 布局
    this.padding,
    this.iconTopOffset = -20,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding ?? const EdgeInsets.all(20),
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          // 玻璃质感容器
          Container(
            width: width,
            height: height,
            decoration: BoxDecoration(
              color: glassColor.withValues(alpha: glassOpacity),
              borderRadius: BorderRadius.circular(borderRadius),
              border: Border.all(
                color: borderColor.withValues(alpha: borderOpacity),
                width: 1,
              ),
              boxShadow: customShadows ?? [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(borderRadius),
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: blurSigma, sigmaY: blurSigma),
                child: Container(
                  padding: const EdgeInsets.fromLTRB(20, 32, 20, 16),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        glassColor.withValues(alpha: glassOpacity + 0.05),
                        glassColor.withValues(alpha: glassOpacity - 0.05),
                      ],
                    ),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // 文字内容区域
                      Column(
                        children: [
                          Text(
                            title,
                            style: TextStyle(
                              fontSize: titleFontSize,
                              fontWeight: FontWeight.bold,
                              color: titleColor,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 6),
                          Text(
                            description,
                            style: TextStyle(
                              fontSize: descriptionFontSize,
                              color: descriptionColor,
                            ),
                            textAlign: TextAlign.left,
                          ),
                        ],
                      ),
                      // 按钮区域
                      GestureDetector(
                        onTap: onButtonPressed,
                        child: Container(
                          width: double.infinity,
                          height: 32,
                          decoration: BoxDecoration(
                            color: buttonBackgroundColor,
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Center(
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                if (buttonIconPath != null) ...[
                                  SvgPicture.asset(buttonIconPath!),
                                  const SizedBox(width: 4),
                                ],
                                Text(
                                  buttonText,
                                  style: TextStyle(
                                    color: buttonTextColor,
                                    fontSize: buttonFontSize,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          // 顶部图标
          if (iconPath != null || iconData != null)
            Positioned(
              top: iconTopOffset,
              left: (width - (iconContainerSize * 2)) / 2,
              child: Container(
                width: iconContainerSize,
                height: iconContainerSize,
                decoration: BoxDecoration(
                  color: iconBackgroundColor,
                  shape: BoxShape.circle,
                  boxShadow: enableGlow ? [
                    BoxShadow(
                      color: iconBackgroundColor.withValues(alpha: 0.4),
                      blurRadius: 15,
                      offset: const Offset(0, 5),
                    ),
                    BoxShadow(
                      color: iconBackgroundColor.withValues(alpha: 0.2),
                      blurRadius: 25,
                      offset: const Offset(0, 10),
                    ),
                  ] : null,
                ),
                child: Center(
                  child: iconPath != null
                      ? SizedBox(
                          width: iconSize,
                          height: iconSize,
                          child: SvgPicture.asset(iconPath!),
                        )
                      : Icon(
                          iconData,
                          color: iconColor,
                          size: iconSize,
                        ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
