/// 时间格式化工具类
/// 提供各种时间格式化方法，统一项目中的时间显示格式
class DateFormatter {
  /// 格式化为相对时间显示
  /// 
  /// 规则：
  /// - 今天: 今天 14:30
  /// - 昨天: 昨天 09:15  
  /// - 一周内: 周三 16:45
  /// - 今年内: 6月11日
  /// - 往年: 2024年12月25日
  static String formatRelativeTime(String dateTimeString) {
    try {
      final dateTime = DateTime.parse(dateTimeString);
      final now = DateTime.now();
      final difference = now.difference(dateTime);

      // 如果是今天
      if (difference.inDays == 0) {
        return '今天 ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
      }
      // 如果是昨天
      else if (difference.inDays == 1) {
        return '昨天 ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
      }
      // 如果是一周内
      else if (difference.inDays < 7) {
        final weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
        final weekday = weekdays[dateTime.weekday - 1];
        return '$weekday ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
      }
      // 如果是今年
      else if (dateTime.year == now.year) {
        return '${dateTime.month}月${dateTime.day}日';
      }
      // 其他情况显示完整日期
      else {
        return '${dateTime.year}年${dateTime.month}月${dateTime.day}日';
      }
    } catch (e) {
      // 如果解析失败，尝试简单的字符串处理
      if (dateTimeString.contains('T')) {
        return dateTimeString.split('T')[0];
      }
      return dateTimeString.split(' ')[0];
    }
  }

  /// 格式化为标准日期格式
  /// 例如: 2025年6月11日
  static String formatStandardDate(String dateTimeString) {
    try {
      final dateTime = DateTime.parse(dateTimeString);
      return '${dateTime.year}年${dateTime.month}月${dateTime.day}日';
    } catch (e) {
      if (dateTimeString.contains('T')) {
        return dateTimeString.split('T')[0];
      }
      return dateTimeString.split(' ')[0];
    }
  }

  /// 格式化为日期时间格式
  /// 例如: 2025年6月11日 14:30
  static String formatDateTime(String dateTimeString) {
    try {
      final dateTime = DateTime.parse(dateTimeString);
      return '${dateTime.year}年${dateTime.month}月${dateTime.day}日 ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return dateTimeString;
    }
  }

  /// 格式化为简短日期格式
  /// 例如: 06-11
  static String formatShortDate(String dateTimeString) {
    try {
      final dateTime = DateTime.parse(dateTimeString);
      return '${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')}';
    } catch (e) {
      if (dateTimeString.contains('T')) {
        final datePart = dateTimeString.split('T')[0];
        final parts = datePart.split('-');
        if (parts.length >= 3) {
          return '${parts[1]}-${parts[2]}';
        }
      }
      return dateTimeString;
    }
  }

  /// 格式化为时间格式
  /// 例如: 14:30
  static String formatTime(String dateTimeString) {
    try {
      final dateTime = DateTime.parse(dateTimeString);
      return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      if (dateTimeString.contains('T')) {
        final timePart = dateTimeString.split('T')[1];
        final timeOnly = timePart.split('.')[0]; // 移除毫秒部分
        final parts = timeOnly.split(':');
        if (parts.length >= 2) {
          return '${parts[0]}:${parts[1]}';
        }
      }
      return dateTimeString;
    }
  }

  /// 格式化为距离现在的时间差
  /// 例如: 2小时前、3天前、1个月前
  static String formatTimeAgo(String dateTimeString) {
    try {
      final dateTime = DateTime.parse(dateTimeString);
      final now = DateTime.now();
      final difference = now.difference(dateTime);

      if (difference.inMinutes < 1) {
        return '刚刚';
      } else if (difference.inMinutes < 60) {
        return '${difference.inMinutes}分钟前';
      } else if (difference.inHours < 24) {
        return '${difference.inHours}小时前';
      } else if (difference.inDays < 30) {
        return '${difference.inDays}天前';
      } else if (difference.inDays < 365) {
        final months = (difference.inDays / 30).floor();
        return '$months个月前';
      } else {
        final years = (difference.inDays / 365).floor();
        return '$years年前';
      }
    } catch (e) {
      return dateTimeString;
    }
  }
} 
