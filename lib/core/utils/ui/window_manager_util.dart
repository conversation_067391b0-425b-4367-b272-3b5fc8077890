import 'package:flutter/material.dart';
import 'package:window_manager/window_manager.dart';
import 'package:frontend_re/core/storage/window_storage.dart';
import 'package:frontend_re/features/workbench/controllers/simple_browser_manager.dart';

/// 窗口管理工具类
/// 封装了 window_manager 的常用功能
class WindowManagerUtil with WindowListener {
  // 私有构造函数
  WindowManagerUtil._();

  // 单例实例
  static final WindowManagerUtil _instance = WindowManagerUtil._();

  // 工厂构造函数
  factory WindowManagerUtil() => _instance;

  // 记录窗口状态
  static bool _isMaximized = false;
  static Size? _previousSize;
  static Offset? _previousPosition;
  static bool _browsersClosed = false; // 标记浏览器是否已经关闭

  /// 初始化窗口管理器
  /// 会自动恢复上次保存的窗口大小
  static Future<void> initWindowManager() async {
    // 确保 WindowManager 已初始化
    await windowManager.ensureInitialized();

    // 获取保存的窗口大小
    final savedSize = await WindowStorage.getWindowSize();
    final isMaximized = await WindowStorage.getWindowMaximized();
    
    // 如果有保存的窗口大小，使用保存的大小；否则使用默认大小
    final windowSize = savedSize ?? const Size(1280, 720);

    // 窗口配置
    WindowOptions windowOptions = WindowOptions(
      size: windowSize, // 使用保存的或默认的窗口大小
      minimumSize: const Size(1280, 720), // 最小窗口大小
      center: true, // 窗口居中显示
      backgroundColor: Colors.transparent, // 背景色透明
      skipTaskbar: false, // 是否在任务栏显示
      titleBarStyle: TitleBarStyle.hidden, // 隐藏菜单栏
    );

    // 添加窗口监听器
    windowManager.addListener(_instance);

    // 应用窗口配置
    await windowManager.waitUntilReadyToShow(windowOptions, () async {
      await windowManager.show(); // 显示窗口
      await windowManager.focus(); // 窗口获取焦点
      
      // 如果上次是最大化状态，则最大化窗口
      if (isMaximized) {
        await windowManager.maximize();
        _isMaximized = true;
      }
    });
  }

  /// 窗口关闭时保存窗口大小
  @override
  Future<void> onWindowClose() async {
    debugPrint('🚪 WindowManagerUtil.onWindowClose() 被调用');
    
    await _saveWindowState();
    
    // 关闭所有运行中的浏览器
    await _closeAllRunningBrowsers();
    
    debugPrint('🚪 WindowManagerUtil.onWindowClose() 完成');
    
    // 调用父类方法，确保窗口正常关闭
    super.onWindowClose();
  }

  /// 窗口大小改变时保存状态
  @override
  Future<void> onWindowResize() async {
    // 延迟保存，避免频繁保存
    await Future.delayed(const Duration(milliseconds: 500));
    await _saveWindowState();
    super.onWindowResize();
  }

  /// 窗口最大化/还原时保存状态
  @override
  Future<void> onWindowMaximize() async {
    _isMaximized = true;
    await WindowStorage.saveWindowMaximized(true);
    super.onWindowMaximize();
  }

  @override
  Future<void> onWindowUnmaximize() async {
    _isMaximized = false;
    await WindowStorage.saveWindowMaximized(false);
    // 保存当前非最大化的窗口大小
    await _saveWindowState();
    super.onWindowUnmaximize();
  }

  /// 保存当前窗口状态
  static Future<void> _saveWindowState() async {
    try {
      final isMaximized = await windowManager.isMaximized();
      if (!isMaximized) {
        // 只有在非最大化状态下才保存窗口大小
        final size = await windowManager.getSize();
        await WindowStorage.saveWindowSize(size);
      }
    } catch (e) {
      // 忽略保存错误，避免影响应用运行
      debugPrint('保存窗口状态时发生错误: $e');
    }
  }

  /// 关闭所有运行中的浏览器
  static Future<void> _closeAllRunningBrowsers() async {
    // 防止重复关闭
    if (_browsersClosed) {
      debugPrint('🔄 WindowManagerUtil: 浏览器已经关闭过了，跳过');
      return;
    }
    
    _browsersClosed = true;
    
    try {
      debugPrint('🔄 WindowManagerUtil: 开始关闭所有运行中的浏览器...');
      
      final runningBrowsers = SimpleBrowserManager.instance.runningBrowsers;
      debugPrint('🔄 WindowManagerUtil: 发现${runningBrowsers.length}个运行中的浏览器');
      
      if (runningBrowsers.isNotEmpty) {
        for (final browser in runningBrowsers) {
          debugPrint('🔄 WindowManagerUtil: 正在关闭浏览器 ${browser.name} (PID: ${browser.pid})');
        }
        
        await SimpleBrowserManager.instance.stopAllBrowsers();
        debugPrint('✅ WindowManagerUtil: 所有浏览器关闭命令已发送');
        
        // 简短等待让关闭操作有时间完成
        await Future.delayed(const Duration(milliseconds: 200));
        
        final remainingBrowsers = SimpleBrowserManager.instance.runningBrowsers;
        debugPrint('🔄 WindowManagerUtil: 剩余运行中的浏览器: ${remainingBrowsers.length}个');
      } else {
        debugPrint('🔄 WindowManagerUtil: 没有运行中的浏览器需要关闭');
      }
    } catch (e) {
      // 忽略关闭浏览器的错误，避免影响应用正常关闭
      debugPrint('❌ WindowManagerUtil: 关闭浏览器时发生错误: $e');
    }
  }

  /// 手动保存窗口状态
  /// 可以在应用退出前主动调用
  static Future<void> saveCurrentWindowState() async {
    await _saveWindowState();
    final isMaximized = await windowManager.isMaximized();
    await WindowStorage.saveWindowMaximized(isMaximized);
  }

  /// 处理双击标题栏缩放功能
  /// 这个方法可以绑定到自定义标题栏的双击事件上
  static Future<void> handleTitleBarDoubleClick() async {
    _isMaximized = await windowManager.isMaximized();
    
    if (_isMaximized) {
      // 如果已最大化，则还原到之前的大小和位置
      if (_previousSize != null && _previousPosition != null) {
        await windowManager.unmaximize();
        await windowManager.setSize(_previousSize!);
        await windowManager.setPosition(_previousPosition!);
      } else {
        await windowManager.unmaximize();
      }
    } else {
      // 保存当前窗口大小和位置
      _previousSize = await windowManager.getSize();
      _previousPosition = await windowManager.getPosition();
      
      // 最大化窗口
      await windowManager.maximize();
    }
  }

  /// 创建可响应双击的自定义标题栏Widget
  static Widget buildDraggableTitleBar({
    required Widget child,
    double height = 30.0,
  }) {
    return GestureDetector(
      onDoubleTap: handleTitleBarDoubleClick,
      onPanStart: (details) {
        // 开始拖拽窗口
        windowManager.startDragging();
      },
      child: Container(
        height: height,
        child: child,
      ),
    );
  }

  /// 设置窗口大小
  /// [width]: 窗口宽度
  /// [height]: 窗口高度
  Future<void> setSize(double width, double height) async {
    await windowManager.setSize(Size(width, height));
  }

  /// 设置窗口最小大小
  /// [width]: 最小宽度
  /// [height]: 最小高度
  Future<void> setMinimumSize(double width, double height) async {
    await windowManager.setMinimumSize(Size(width, height));
  }

  /// 设置窗口最大大小
  /// [width]: 最大宽度
  /// [height]: 最大高度
  Future<void> setMaximumSize(double width, double height) async {
    await windowManager.setMaximumSize(Size(width, height));
  }

  /// 设置窗口位置
  /// [x]: x坐标
  /// [y]: y坐标
  Future<void> setPosition(double x, double y) async {
    await windowManager.setPosition(Offset(x, y));
  }

  /// 设置窗口是否总在最前
  /// [isAlwaysOnTop]: 是否总在最前
  Future<void> setAlwaysOnTop(bool isAlwaysOnTop) async {
    await windowManager.setAlwaysOnTop(isAlwaysOnTop);
  }

  /// 开始拖拽窗口
  Future<void> startDragging() async {
    await windowManager.startDragging();
  }

  /// 设置窗口是否可以调整大小
  /// [isResizable]: 是否可调整大小
  Future<void> setResizable(bool isResizable) async {
    await windowManager.setResizable(isResizable);
  }

  /// 设置窗口是否可以移动
  /// [isMovable]: 是否可移动
  Future<void> setMovable(bool isMovable) async {
    await windowManager.setMovable(isMovable);
  }

  /// 最小化窗口
  Future<void> minimize() async {
    await windowManager.minimize();
  }

  /// 最大化窗口
  Future<void> maximize() async {
    await windowManager.maximize();
  }

  /// 还原窗口
  Future<void> unmaximize() async {
    await windowManager.unmaximize();
  }

  /// 关闭窗口
  static Future<void> close() async {
    await windowManager.close();
  }

  /// 隐藏窗口
  Future<void> hide() async {
    await windowManager.hide();
  }

  /// 显示窗口
  Future<void> show() async {
    await windowManager.show();
  }

  /// 窗口是否最大化
  Future<bool> isMaximized() async {
    return await windowManager.isMaximized();
  }

  /// 窗口是否最小化
  Future<bool> isMinimized() async {
    return await windowManager.isMinimized();
  }

  /// 窗口是否全屏
  Future<bool> isFullScreen() async {
    return await windowManager.isFullScreen();
  }

  /// 设置窗口标题
  /// [title]: 窗口标题
  Future<void> setTitle(String title) async {
    await windowManager.setTitle(title);
  }

  /// 设置窗口透明度
  /// [opacity]: 透明度 0.0-1.0
  Future<void> setOpacity(double opacity) async {
    await windowManager.setOpacity(opacity);
  }
}
