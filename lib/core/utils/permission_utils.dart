import 'dart:convert';
import 'package:frontend_re/core/constants/permission_constants.dart';

/// 权限管理工具类
class PermissionUtils {
  /// 解析角色权限到内部格式
  /// 将后端返回的权限格式转换为前端使用的 Map<String, bool> 格式
  static Map<String, bool> parsePermissions(dynamic permissions) {
    final Map<String, bool> parsedPermissions = {};
    
    if (permissions == null) return parsedPermissions;
    
    // 处理数组格式的权限
    if (permissions is List) {
      for (final permission in permissions) {
        final permissionStr = permission.toString();
        parsedPermissions[permissionStr] = true;
      }
      return parsedPermissions;
    }
    
    // 处理字符串格式的权限（向后兼容）
    if (permissions is String) {
      try {
        // 尝试解析为数组
        final permissionList = jsonDecode(permissions) as List;
        for (final permission in permissionList) {
          final permissionStr = permission.toString();
          parsedPermissions[permissionStr] = true;
        }
        return parsedPermissions;
      } catch (e) {
        try {
          // 尝试解析为Map（旧格式兼容）
          final permissionMap = jsonDecode(permissions) as Map;
          for (final entry in permissionMap.entries) {
            if (entry.value == true) {
              final permissionStr = entry.key.toString();
              if (permissionStr.endsWith(':*')) {
                // 全权限模块：如 "users:*" -> "users"
                final module = permissionStr.substring(0, permissionStr.length - 2);
                parsedPermissions[module] = true;
              } else {
                // 具体权限：如 "users:profile_read"
                parsedPermissions[permissionStr] = true;
              }
            }
          }
          return parsedPermissions;
        } catch (e2) {
          // 如果解析失败，返回空权限
          return parsedPermissions;
        }
      }
    }
    
    // 处理Map格式的权限（向后兼容）
    if (permissions is Map) {
      for (final entry in permissions.entries) {
        if (entry.value == true) {
          final permissionStr = entry.key.toString();
          if (permissionStr.endsWith(':*')) {
            // 全权限模块：如 "users:*" -> "users"
            final module = permissionStr.substring(0, permissionStr.length - 2);
            parsedPermissions[module] = true;
          } else {
            // 具体权限：如 "users:profile_read"
            parsedPermissions[permissionStr] = true;
          }
        }
      }
    }
    
    return parsedPermissions;
  }

  /// 转换权限为后端格式
  /// 将前端的 Map<String, bool> 格式转换为后端需要的 JSON 字符串数组
  static String convertPermissionsToBackendFormat(Map<String, bool> permissions) {
    final List<String> permissionList = [];
    
    for (final entry in permissions.entries) {
      if (entry.value) {
        if (entry.key.contains(':')) {
          // 具体权限：如 "users:profile_read"
          permissionList.add(entry.key);
        } else {
          // 模块全权限：展开为该模块的所有具体权限
          final module = entry.key;
          final modulePermissions = PermissionConstants.permissionModules[module]?['permissions'] as List<String>?;
          if (modulePermissions != null) {
            for (final permission in modulePermissions) {
              permissionList.add('$module:$permission');
            }
          }
        }
      }
    }
    
    return jsonEncode(permissionList);
  }

  /// 检查模块是否全选
  static bool isModuleFullySelected(String module, Map<String, bool> permissions) {
    return permissions.containsKey(module);
  }

  /// 检查具体权限是否选中
  static bool isSpecificPermissionSelected(
    String module,
    String permission,
    Map<String, bool> permissions,
  ) {
    // 如果模块全选了，那么所有具体权限都被认为是选中的
    if (permissions.containsKey(module)) {
      return true;
    }
    // 否则检查具体权限
    return permissions.containsKey('$module:$permission');
  }

  /// 获取模块的选中状态（全选、部分选中、未选中）
  /// 返回值：true = 全选，false = 未选中，null = 部分选中
  static bool? getModuleCheckboxState(String module, Map<String, bool> permissions) {
    final isFullySelected = isModuleFullySelected(module, permissions);
    if (isFullySelected) return true;
    
    final modulePermissions = PermissionConstants.permissionModules[module]?['permissions'] as List<String>?;
    if (modulePermissions == null) return false;
    
    final selectedCount = modulePermissions
        .where((p) => permissions.containsKey('$module:$p'))
        .length;
    
    if (selectedCount == 0) return false;
    if (selectedCount == modulePermissions.length) {
      return true; // 所有具体权限都选中，显示为全选状态
    }
    return null; // 部分选中
  }

  /// 切换模块权限
  static Map<String, bool> toggleModulePermissions(
    String module,
    bool value,
    Map<String, bool> currentPermissions,
  ) {
    final newPermissions = Map<String, bool>.from(currentPermissions);
    final modulePermissions = PermissionConstants.permissionModules[module]?['permissions'] as List<String>?;
    
    if (modulePermissions == null) return newPermissions;
    
    if (value) {
      // 全选该模块
      newPermissions[module] = true;
      // 移除具体权限，避免冲突
      for (final permission in modulePermissions) {
        newPermissions.remove('$module:$permission');
      }
    } else {
      // 取消全选，清除该模块的所有权限
      newPermissions.remove(module);
      // 移除该模块的所有具体权限
      for (final permission in modulePermissions) {
        newPermissions.remove('$module:$permission');
      }
    }
    
    return newPermissions;
  }

  /// 切换具体权限
  static Map<String, bool> toggleSpecificPermission(
    String module,
    String permission,
    bool value,
    Map<String, bool> currentPermissions,
  ) {
    final newPermissions = Map<String, bool>.from(currentPermissions);
    final permissionKey = '$module:$permission';
    final modulePermissions = PermissionConstants.permissionModules[module]?['permissions'] as List<String>?;
    
    if (modulePermissions == null) return newPermissions;
    
    if (value) {
      // 选中具体权限
      newPermissions[permissionKey] = true;
      // 如果是全选状态，则取消全选
      newPermissions.remove(module);
    } else {
      // 取消具体权限
      if (newPermissions.containsKey(module)) {
        // 如果当前是模块全选状态，需要展开为具体权限
        newPermissions.remove(module);
        // 添加该模块的所有其他权限（除了要取消的这个）
        for (final p in modulePermissions) {
          if (p != permission) {
            newPermissions['$module:$p'] = true;
          }
        }
      } else {
        // 如果不是全选状态，直接移除具体权限
        newPermissions.remove(permissionKey);
      }
    }
    
    return newPermissions;
  }

  /// 验证权限是否有效
  static bool isPermissionValid(Map<String, bool> permissions) {
    return permissions.isNotEmpty;
  }
} 
