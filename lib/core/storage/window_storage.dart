import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/material.dart';

/// 窗口配置存储类
/// 负责处理窗口大小等配置的本地存储
class WindowStorage {
  static const String _windowWidthKey = 'window_width';
  static const String _windowHeightKey = 'window_height';
  static const String _windowMaximizedKey = 'window_maximized';
  
  /// 保存窗口大小
  static Future<void> saveWindowSize(Size size) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setDouble(_windowWidthKey, size.width);
    await prefs.setDouble(_windowHeightKey, size.height);
  }

  /// 获取保存的窗口大小
  static Future<Size?> getWindowSize() async {
    final prefs = await SharedPreferences.getInstance();
    final width = prefs.getDouble(_windowWidthKey);
    final height = prefs.getDouble(_windowHeightKey);
    
    if (width != null && height != null) {
      return Size(width, height);
    }
    return null;
  }

  /// 保存窗口最大化状态
  static Future<void> saveWindowMaximized(bool isMaximized) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_windowMaximizedKey, isMaximized);
  }

  /// 获取窗口最大化状态
  static Future<bool> getWindowMaximized() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_windowMaximizedKey) ?? false;
  }

  /// 清除窗口配置
  static Future<void> clearWindowConfig() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_windowWidthKey);
    await prefs.remove(_windowHeightKey);
    await prefs.remove(_windowMaximizedKey);
  }
} 
