import 'dart:io';
import 'package:shared_preferences/shared_preferences.dart';

/// 认证存储类
/// 负责处理 token 和其他认证信息的本地存储
class AuthStorage {
  static const String _tokenKey = 'auth_token';
  static const String _cookieKey = 'auth_cookie';
  static const String _autoLoginKey = 'auto_login';
  static const String _accountKey = 'account';
  static const String _passwordKey = 'password';
  
  /// 保存 token
  static Future<void> saveToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_tokenKey, token);
  }

  /// 获取 token
  static Future<String?> getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_tokenKey);
  }

  /// 保存 cookie
  static Future<void> saveCookie(String cookie) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_cookieKey, cookie);
  }

  /// 获取 cookie
  static Future<String?> getCookie() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_cookieKey);
  }

  /// 清除所有认证信息
  static Future<bool> clear() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_tokenKey);
      await prefs.remove(_cookieKey);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// 保存自动登录状态
  static Future<void> setAutoLogin(bool autoLogin) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_autoLoginKey, autoLogin);
  }

  /// 获取自动登录状态
  static Future<bool?> getAutoLogin() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_autoLoginKey);
  }

  /// 保存账号密码
  static Future<void> saveAccountPassword(String account, String password) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_accountKey, account);
    await prefs.setString(_passwordKey, password);
  }

  /// 应用启动时初始化认证状态
  /// 检查并清理过期的cookie
  static Future<void> initializeAuthState() async {
    try {
      final cookieString = await getCookie();
      
      if (cookieString == null || cookieString.isEmpty) {
        return; // 没有cookie，无需处理
      }

      print('🚀 找到cookie，开始检查有效性: $cookieString');

      // 使用与HttpService相同的cookie检查逻辑
      final isValid = await _checkCookieValidInternal(cookieString);
      
      if (!isValid) {
        print('🚀 Cookie无效，清除认证信息');
        await clear();
      } else {
        print('🚀 Cookie有效，保留认证状态');
      }
    } catch (e) {
      // 发生错误时，不要自动清除，让用户手动处理
      print('🚀 初始化过程中发生错误，但保留认证信息: $e');
    }
  }

  /// 内部cookie验证逻辑（与HttpService保持一致）
  static Future<bool> _checkCookieValidInternal(String cookieString) async {
    try {
      DateTime? expiresDate;
      final parts = cookieString.split(';');
      
      print('🚀 Cookie部分: $parts');
      
      for (var part in parts) {
        final trimmedPart = part.trim();
        if (trimmedPart.toLowerCase().startsWith('expires=')) {
          final dateString = trimmedPart.substring('expires='.length);
          print('🚀 找到Expires: $dateString');
          try {
            expiresDate = HttpDate.parse(dateString);
            print('🚀 解析成功(HttpDate): $expiresDate');
            break;
          } catch (e) {
            print('🚀 HttpDate解析失败: $e，尝试DateTime解析');
            try {
              expiresDate = DateTime.parse(dateString);
              print('🚀 解析成功(DateTime): $expiresDate');
              break;
            } catch (e2) {
              print('🚀 DateTime解析也失败: $e2');
              return false;
            }
          }
        }
      }

      if (expiresDate != null) {
        final now = DateTime.now();
        final isExpired = expiresDate.isBefore(now);
        print('🚀 当前时间: $now');
        print('🚀 过期时间: $expiresDate');
        print('🚀 是否过期: $isExpired');
        if (isExpired) {
          print('🚀 Cookie已过期，判定为无效');
          return false;
        }
        print('🚀 Cookie未过期，判定为有效');
        return true;
      } else {
        // 没有找到Expires属性，按session cookie处理，认为有效
        print('🚀 未找到Expires属性，按session cookie处理，判定为有效');
        return true;
      }
    } catch (e) {
      print('🚀 Cookie检查过程中发生异常: $e');
      return false;
    }
  }
}
