import 'package:flutter/material.dart';

/// 权限配置常量
class PermissionConstants {
  // 权限模块配置
  static const Map<String, Map<String, dynamic>> permissionModules = {
    'users': {
      'label': '用户管理',
      'icon': Icons.person,
      'permissions': ['profile_read', 'profile_update'],
    },
    'logs': {
      'label': '日志管理',
      'icon': Icons.history,
      'permissions': ['login_read', 'operation_read'],
    },
    'commissions': {
      'label': '佣金管理',
      'icon': Icons.monetization_on,
      'permissions': ['create', 'read'],
    },
    'wallet': {
      'label': '钱包管理',
      'icon': Icons.account_balance_wallet,
      'permissions': ['read', 'transaction'],
    },
    'user_subscriptions': {
      'label': '用户订阅',
      'icon': Icons.subscriptions,
      'permissions': ['read', 'create', 'update'],
    },
    'subscriptions': {
      'label': '订阅产品',
      'icon': Icons.shopping_cart,
      'permissions': ['read', 'purchase'],
    },
    'roles': {
      'label': '角色管理',
      'icon': Icons.admin_panel_settings,
      'permissions': ['create', 'read', 'update', 'delete'],
    },
    'groups': {
      'label': '分组管理',
      'icon': Icons.group,
      'permissions': ['create', 'read', 'update', 'delete'],
    },
    'ip_whitelists': {
      'label': 'IP白名单',
      'icon': Icons.security,
      'permissions': ['create', 'read', 'delete', 'authorize'],
    },
    'self_host_proxies': {
      'label': '自建代理',
      'icon': Icons.router,
      'permissions': ['create', 'read', 'update', 'delete'],
    },
    'environments': {
      'label': '环境管理',
      'icon': Icons.computer,
      'permissions': [
        'create',
        'read',
        'update',
        'delete',
        'list_deleted',
        'restore',
        'permanent_delete',
        'download',
        'redis_manage',
        'proxy_update',
        'file_manage'
      ],
    },
    'team_users': {
      'label': '团队用户',
      'icon': Icons.people,
      'permissions': ['read', 'create', 'update', 'delete'],
    },
    'teams': {
      'label': '团队设置',
      'icon': Icons.settings,
      'permissions': ['update'],
    },
    'orders': {
      'label': '订单管理',
      'icon': Icons.receipt,
      'permissions': ['read', 'create', 'cancel'],
    },
  };

  // 权限名称映射
  static const Map<String, String> permissionLabels = {
    'profile_read': '读取资料',
    'profile_update': '更新资料',
    'login_read': '登录日志',
    'operation_read': '操作日志',
    'create': '创建',
    'read': '读取',
    'update': '更新',
    'delete': '删除',
    'transaction': '交易',
    'purchase': '购买',
    'authorize': '授权',
    'list_deleted': '查看已删除',
    'restore': '恢复',
    'permanent_delete': '永久删除',
    'download': '下载',
    'redis_manage': 'Redis管理',
    'proxy_update': '代理更新',
    'file_manage': '文件管理',
    'cancel': '取消',
  };
} 
