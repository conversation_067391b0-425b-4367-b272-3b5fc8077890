import 'package:flutter/material.dart';

/// 搜索结果数据结构
class SearchResult {
  final String title;
  final String subtitle;
  final String type;
  final String route;
  final IconData icon;
  final Color color;

  const SearchResult({
    required this.title,
    required this.subtitle,
    required this.type,
    required this.route,
    required this.icon,
    required this.color,
  });
}

/// 全局搜索配置
class SearchConfig {
  /// 搜索数据源
  static const List<SearchResult> searchData = [
    SearchResult(
      title: '自有代理',
      subtitle: '管理自有代理服务器',
      type: '跳转',
      route: 'selfProxy',
      icon: Icons.vpn_lock,
      color: Colors.green,
    ),
    SearchResult(
      title: '平台代理',
      subtitle: '使用平台提供的代理',
      type: '跳转',
      route: 'proxy',
      icon: Icons.public,
      color: Colors.blue,
    ),
    SearchResult(
      title: '浏览器环境',
      subtitle: '管理浏览器窗口环境',
      type: '跳转',
      route: 'workbench',
      icon: Icons.window,
      color: Colors.purple,
    ),
    SearchResult(
      title: '添加浏览器环境',
      subtitle: '添加浏览器窗口环境',
      type: '跳转',
      route: 'addBrowser',
      icon: Icons.window,
      color: Colors.purple,
    ),
    SearchResult(
      title: '浏览器回收站',
      subtitle: '管理浏览器窗口环境',
      type: '跳转',
      route: 'browserTrash',
      icon: Icons.window,
      color: Colors.purple,
    ),
    SearchResult(
      title: '团队管理',
      subtitle: '管理团队成员和权限',
      type: '跳转',
      route: 'teamMembers',
      icon: Icons.group,
      color: Colors.indigo,
    ),
    SearchResult(
      title: '操作日志',
      subtitle: '查看系统操作记录',
      type: '跳转',
      route: 'operationLog',
      icon: Icons.history,
      color: Colors.brown,
    ),
    SearchResult(
      title: '登录日志',
      subtitle: '查看系统登录记录',
      type: '跳转',
      route: 'loginLog',
      icon: Icons.history,
      color: Colors.brown,
    ),
    SearchResult(
      title: '用户设置',
      subtitle: '个人账户设置',
      type: '跳转',
      route: 'userSettings',
      icon: Icons.settings,
      color: Colors.grey,
    ),
    SearchResult(
      title: '应用设置',
      subtitle: '应用全局配置',
      type: '跳转',
      route: 'appSetting',
      icon: Icons.tune,
      color: Colors.blueGrey,
    ),
  ];

  /// 搜索配置参数
  static const double searchBoxWidth = 400.0;
  static const double searchBoxHeight = 34.0;
  static const double overlayMaxHeight = 300.0;
  static const double overlayOffset = 5.0;
  
  /// 搜索占位符文本
  static const String hintText = '全局搜索';
  
  /// 无结果提示文本
  static const String noResultsText = '未找到相关结果';

  /// 执行搜索的方法
  static List<SearchResult> performSearch(String query) {
    if (query.isEmpty) {
      return [];
    }

    return searchData
        .where((item) =>
            item.title.toLowerCase().contains(query.toLowerCase()) ||
            item.subtitle.toLowerCase().contains(query.toLowerCase()) ||
            item.type.toLowerCase().contains(query.toLowerCase()))
        .toList();
  }
}

