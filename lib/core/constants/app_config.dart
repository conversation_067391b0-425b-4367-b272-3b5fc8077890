/// 应用配置常量
/// 统一管理应用的全局配置信息
class AppConfig {
  // 私有构造函数，防止实例化
  AppConfig._();

  // ========== 应用基础信息 ==========
  /// 应用名称
  static const String appName = 'Suiyu Browser';
  
  /// 应用版本号
  static const String version = '1.0.0';
  
  /// 构建号
  static const String buildNumber = '1';
  
  /// 应用包名
  static const String packageName = 'com.suiyu.browser';
  
  /// 应用描述
  static const String appDescription = '新一代智能浏览器';

  // ========== 网络配置 ==========
  /// API基础URL
  static const String apiBaseUrl = 'http://127.0.0.1:52618/api/v1';
  
  /// 连接超时时间（秒）
  static const int connectTimeout = 10;
  
  /// 接收超时时间（秒）
  static const int receiveTimeout = 10;
  
  /// 发送超时时间（秒）
  static const int sendTimeout = 10;

  // ========== UI配置 ==========
  /// 设计稿尺寸
  static const double designWidth = 1280;
  static const double designHeight = 720;
  
  /// 窗口最小尺寸
  static const double minWindowWidth = 800;
  static const double minWindowHeight = 600;

  // ========== 存储配置 ==========
  /// SharedPreferences键前缀
  static const String storagePrefix = 'suiyu_';
  
  /// 缓存最大数量
  static const int maxCacheCount = 100;

  // ========== 开发配置 ==========
  /// 是否开启调试模式
  static const bool isDebugMode = true;
  
  /// 是否显示性能监控
  static const bool showPerformanceOverlay = false;
  
  /// 是否启用日志
  static const bool enableLogging = true;

  // ========== 业务配置 ==========
  /// 浏览器环境最大数量
  static const int maxBrowserEnvironments = 1000;
  
  /// 代理检测超时时间（秒）
  static const int proxyTestTimeout = 5;
  
  /// 自动保存间隔（分钟）
  static const int autoSaveInterval = 5;

  // ========== 联系信息 ==========
  /// 官方网站
  static const String officialWebsite = 'https://suiyu.com';
  
  /// 技术支持邮箱
  static const String supportEmail = '<EMAIL>';
  
  /// GitHub仓库
  static const String githubRepo = 'https://github.com/suiyu/browser';

  // ========== 帮助方法 ==========
  /// 获取完整版本信息
  static String get fullVersion => '$version+$buildNumber';
  
  /// 获取用户代理字符串
  static String get userAgent => '$appName/$version';
  
  /// 获取API端点URL
  static String getApiUrl(String endpoint) => '$apiBaseUrl$endpoint';
}
