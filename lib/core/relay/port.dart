import 'dart:io';
import 'dart:math';

const int minPort = 30000;
const int maxPort = 50000;
final Random _random = Random();

/// 生成一个可用的随机端口号
Future<int> generateAvailablePort() async {
  int maxAttempts = 100;
  int attempts = 0;

  while (attempts < maxAttempts) {
    int port = _generateRandomPort();
    bool isAvailable = await _isPortAvailable(port);

    if (isAvailable) {
      print('找到可用端口: $port');
      return port;
    }

    attempts++;
    print('端口 $port 已被占用，重新生成... (尝试 $attempts/$maxAttempts)');
  }

  throw Exception('在 $maxAttempts 次尝试后仍未找到可用端口');
}

/// 生成30000-50000之间的随机端口
int _generateRandomPort() {
  return minPort + _random.nextInt(maxPort - minPort + 1);
}

/// 检查端口是否可用
Future<bool> _isPortAvailable(int port) async {
  try {
    Set<int> usedPorts = await _getUsedPorts();
    return !usedPorts.contains(port);
  } catch (e) {
    print('检查端口时出错: $e');
    return await _checkPortByBinding(port);
  }
}

/// 获取所有被占用的端口
Future<Set<int>> _getUsedPorts() async {
  if (Platform.isWindows) {
    return await _getUsedPortsWindows();
  } else if (Platform.isMacOS || Platform.isLinux) {
    return await _getUsedPortsUnix();
  } else {
    throw UnsupportedError('不支持的操作系统: ${Platform.operatingSystem}');
  }
}

/// Windows系统获取占用端口
Future<Set<int>> _getUsedPortsWindows() async {
  final result = await Process.run(
    'powershell',
    [
      '-Command',
      '(Get-NetTCPConnection).LocalPort + (Get-NetUDPEndpoint).LocalPort | Sort-Object -Unique'
    ],
  );

  if (result.exitCode != 0) {
    throw Exception('PowerShell命令执行失败: ${result.stderr}');
  }

  return _parsePortOutput(result.stdout.toString());
}

/// Unix系统(Mac/Linux)获取占用端口
Future<Set<int>> _getUsedPortsUnix() async {
  final result = await Process.run(
    'sh',
    [
      '-c',
      "lsof -nP -iTCP -iUDP | awk '{print \$9}' | grep -Eo '[^:]+:[0-9]+' | cut -d: -f2 | sort -n | uniq"
    ],
  );

  if (result.exitCode != 0) {
    throw Exception('lsof命令执行失败: ${result.stderr}');
  }

  return _parsePortOutput(result.stdout.toString());
}

/// 解析命令输出，提取端口号
Set<int> _parsePortOutput(String output) {
  Set<int> ports = <int>{};

  List<String> lines = output.split('\n');
  for (String line in lines) {
    String trimmed = line.trim();
    if (trimmed.isNotEmpty) {
      try {
        int port = int.parse(trimmed);
        ports.add(port);
      } catch (e) {
        continue;
      }
    }
  }

  return ports;
}

/// 备用方法：通过Socket绑定检测端口是否可用
Future<bool> _checkPortByBinding(int port) async {
  try {
    ServerSocket socket = await ServerSocket.bind(
      InternetAddress.anyIPv4,
      port,
    );
    await socket.close();
    return true;
  } catch (e) {
    return false;
  }
}