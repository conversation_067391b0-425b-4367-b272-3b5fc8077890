import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart'; // 引入 kDebugMode
import 'port.dart'; // 引入端口生成函数

// 获取 config.json 文件路径
String getConfigPath() {
  return kDebugMode
      ? r'C:\Users\<USER>\Desktop\Suiyu\Suiyu-re\assets\config.json'
      : '${Directory.current.path}/data/flutter_assets/assets/config.json';
}

Future<int> addTrojanOutbound({
  required String serverAddress,
  required int serverPort,
  required String password,
}) async {
  // 动态获取文件路径
  String filePath = getConfigPath();

  // 读取文件
  final file = File(filePath);
  if (!await file.exists()) {
    throw Exception('JSON 文件不存在: $filePath');
  }

  String content = await file.readAsString();
  Map<String, dynamic> json = jsonDecode(content);

  // Step 1: 处理 inbounds
  bool inboundExists = false;
  int returnPort = 0;

  for (var inbound in json['inbounds']) {
    if (inbound['tag'] == password) {
      returnPort = inbound['listen_port'];
      inboundExists = true;
      break;
    }
  }

  if (!inboundExists) {
    returnPort = await generateAvailablePort();
    json['inbounds'].add({
      "type": "socks",
      "tag": password,
      "listen": "127.0.0.1",
      "listen_port": returnPort,
    });
  }

  // Step 2: 处理 outbounds
  bool outboundExists = false;
  for (var outbound in json['outbounds']) {
    if (outbound['tag'] == password) {
      // 修改对应的服务器地址、端口和密码
      outbound['server'] = serverAddress;
      outbound['server_port'] = serverPort;
      outbound['password'] = password;
      outboundExists = true;
      break;
    }
  }

  if (!outboundExists) {
    json['outbounds'].add({
      "type": "trojan",
      "tag": password,
      "server": serverAddress,
      "server_port": serverPort,
      "password": password,
      "tls": {
        "enabled": true,
        "insecure": true,
        "server_name": ""
      },
      "multiplex": {
        "enabled": true,
        "protocol": "h2mux",
        "max_connections": 8,
        "min_streams": 16,
        "padding": true,
        "brutal": {"enabled": false, "up_mbps": 1000, "down_mbps": 1000}
      }
    });
  }

  // Step 3: 处理 route - 直接添加路由规则
  json['route']['rules'].add({
    "inbound": [password],
    "action": "route",
    "outbound": password
  });

  // 保存修改后的 JSON 文件
  await file.writeAsString(const JsonEncoder.withIndent('  ').convert(json));

  // 返回最终的端口号
  return returnPort;
}

Future<int> addSocksOutbound({
  required String serverAddress,
  required int serverPort,
  String? username,
  String? password,
}) async {
  // 动态获取文件路径
  String filePath = getConfigPath();

  // 读取文件
  final file = File(filePath);
  if (!await file.exists()) {
    throw Exception('JSON 文件不存在: $filePath');
  }

  String content = await file.readAsString();
  Map<String, dynamic> json = jsonDecode(content);

  // 生成新的tag - 查找现有的socks标签并生成下一个序号
  String newTag = _generateNextSocksTag(json['outbounds']);

  // Step 1: 处理 inbounds
  bool inboundExists = false;
  int returnPort = 0;

  for (var inbound in json['inbounds']) {
    if (inbound['tag'] == newTag) {
      returnPort = inbound['listen_port'];
      inboundExists = true;
      break;
    }
  }

  if (!inboundExists) {
    returnPort = await generateAvailablePort();
    json['inbounds'].add({
      "type": "socks",
      "tag": newTag,
      "listen": "127.0.0.1",
      "listen_port": returnPort,
    });
  }

  // Step 2: 处理 outbounds - 添加新的socks outbound
  Map<String, dynamic> socksOutbound = {
    "server": serverAddress,
    "server_port": serverPort,
    "version": "5",
    "tag": newTag,
    "type": "socks"
  };

  // 只有在提供了username和password时才添加这两个字段
  if (username != null && username.isNotEmpty) {
    socksOutbound["username"] = username;
  }
  if (password != null && password.isNotEmpty) {
    socksOutbound["password"] = password;
  }

  json['outbounds'].add(socksOutbound);

  // Step 3: 处理 route - 直接添加路由规则
  json['route']['rules'].add({
    "inbound": [newTag],
    "action": "route",
    "outbound": newTag
  });

  // 保存修改后的 JSON 文件
  await file.writeAsString(const JsonEncoder.withIndent('  ').convert(json));

  print('已添加SOCKS出站配置，tag: $newTag，端口: $returnPort，服务器: $serverAddress:$serverPort');

  // 返回最终的端口号
  return returnPort;
}

/// 生成下一个socks标签
String _generateNextSocksTag(List<dynamic> outbounds) {
  int maxNumber = 0;

  // 遍历现有的outbounds，找出最大的socks序号
  for (var outbound in outbounds) {
    String tag = outbound['tag'] ?? '';
    if (tag.startsWith('socks')) {
      String numberPart = tag.substring(5); // 去掉"socks"前缀
      if (numberPart.isNotEmpty) {
        try {
          int number = int.parse(numberPart);
          if (number > maxNumber) {
            maxNumber = number;
          }
        } catch (e) {
          // 忽略无法解析的标签
          continue;
        }
      }
    }
  }

  // 返回下一个序号的标签
  return 'socks${maxNumber + 1}';
}


Future<int> addHttpOutbound({
  required String serverAddress,
  required int serverPort,
  String? username,
  String? password,
}) async {
  // 动态获取文件路径
  String filePath = getConfigPath();

  // 读取文件
  final file = File(filePath);
  if (!await file.exists()) {
    throw Exception('JSON 文件不存在: $filePath');
  }

  String content = await file.readAsString();
  Map<String, dynamic> json = jsonDecode(content);

  // 生成新的tag - 查找现有的http标签并生成下一个序号
  String newTag = _generateNextHttpTag(json['outbounds']);

  // Step 1: 处理 inbounds
  bool inboundExists = false;
  int returnPort = 0;

  for (var inbound in json['inbounds']) {
    if (inbound['tag'] == newTag) {
      returnPort = inbound['listen_port'];
      inboundExists = true;
      break;
    }
  }

  if (!inboundExists) {
    returnPort = await generateAvailablePort();
    json['inbounds'].add({
      "type": "socks",
      "tag": newTag,
      "listen": "127.0.0.1",
      "listen_port": returnPort,
    });
  }

  // Step 2: 处理 outbounds - 添加新的http outbound
  Map<String, dynamic> httpOutbound = {
    "server": serverAddress,
    "server_port": serverPort,
    "tag": newTag,
    "type": "http"
  };

  // 只有在提供了username和password时才添加这两个字段
  if (username != null && username.isNotEmpty) {
    httpOutbound["username"] = username;
  }
  if (password != null && password.isNotEmpty) {
    httpOutbound["password"] = password;
  }

  json['outbounds'].add(httpOutbound);

  // Step 3: 处理 route - 直接添加路由规则
  json['route']['rules'].add({
    "inbound": [newTag],
    "action": "route",
    "outbound": newTag
  });

  // 保存修改后的 JSON 文件
  await file.writeAsString(const JsonEncoder.withIndent('  ').convert(json));

  print('已添加HTTP出站配置，tag: $newTag，端口: $returnPort，服务器: $serverAddress:$serverPort');

  // 返回最终的端口号
  return returnPort;
}

// 生成下一个http标签
String _generateNextHttpTag(List<dynamic> outbounds) {
  int maxNumber = 0;

  // 遍历现有的outbounds，找出最大的http序号
  for (var outbound in outbounds) {
    String tag = outbound['tag'] ?? '';
    if (tag.startsWith('http')) {
      String numberPart = tag.substring(4); // 去掉"http"前缀
      if (numberPart.isNotEmpty) {
        try {
          int number = int.parse(numberPart);
          if (number > maxNumber) {
            maxNumber = number;
          }
        } catch (e) {
          // 忽略无法解析的标签
          continue;
        }
      }
    }
  }

  // 返回下一个序号的标签
  return 'http${maxNumber + 1}';
}
