import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart'; // 引入 kDebugMode

/// 获取 JSON 文件路径
String getJsonFilePath() {
  return kDebugMode
      ? r'C:\Users\<USER>\IdeaProjects\Suiyu-re\assets\config.json'
      : '${Directory.current.path}/data/flutter_assets/assets/config.json';
}

/// 创建或覆盖 JSON 文件
Future<void> createOrOverwriteJsonFile() async {
  // 定义 JSON 内容
  const Map<String, dynamic> jsonContent = {
    "log": {
      "disabled": false,
      "level": "info",
      "timestamp": true
    },
    "dns": {
      "servers": [
        {
          "type": "udp",
          "tag": "local",
          "server": "************",
          "server_port": 53
        }
      ],
      "final": "local",
      "strategy": "prefer_ipv4",
      "reverse_mapping": true
    },
    "ntp": {
      "enabled": true,
      "server": "time.apple.com",
      "server_port": 123,
      "interval": "30m"
    },
    "inbounds": [
      {
        "type": "socks",
        "tag": "socks1",
        "listen": "127.0.0.1",
        "listen_port": 20001
      }
    ],
    "outbounds": [
      {
        "type": "direct",
        "tag": "direct"
      },
      {
        "server": "*******",
        "server_port": 45889,
        "version": "5",
        "username": "sekai",
        "password": "admin",
        "tag": "socks1",
        "type": "socks"
      }
    ],
    "route": {
      "default_domain_resolver": {
        "server": "local",
        "rewrite_ttl": 60,
        "client_subnet": "***************"
      },
      "rules": [
        {
          "inbound": "socks1",
          "action": "route",
          "outbound": "socks1"
        }
      ]
    }
  };

  // 转换 JSON 为格式化字符串
  String jsonString = const JsonEncoder.withIndent('  ').convert(jsonContent);

  // 获取文件路径
  String filePath = getJsonFilePath();

  // 创建或覆盖文件
  final file = File(filePath);
  await file.writeAsString(jsonString);

  print('JSON 文件已创建或覆盖: $filePath');
}