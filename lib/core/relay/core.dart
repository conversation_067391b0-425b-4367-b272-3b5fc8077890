import 'dart:io';
import 'package:flutter/foundation.dart';

class ProcessManager {
  // 静态私有实例
  static ProcessManager? _instance;

  Process? _process;
  late final String exePath;
  late final String workingPath;
  bool isRunning = false;

  // 私有构造函数
  ProcessManager._internal() {
    // 自动设置 exePath 和 workingPath
    exePath = kDebugMode
        ? r'C:\Users\<USER>\Desktop\Suiyu\Suiyu-re\assets\relay.exe'
        : '${Directory.current.path}/data/flutter_assets/assets/relay.exe';
    workingPath = kDebugMode
        ? r'C:\Users\<USER>\Desktop\Suiyu\Suiyu-re\assets'
        : '${Directory.current.path}/data/flutter_assets/assets';
  }

  // factory 构造函数，确保单例
  factory ProcessManager() {
    _instance ??= ProcessManager._internal();
    return _instance!;
  }

  // 静态方法获取实例（可选）
  static ProcessManager getInstance() {
    _instance ??= ProcessManager._internal();
    return _instance!;
  }

  /// 启动 exe 进程
  Future<void> start() async {
    if (isRunning) {
      print("进程已经在运行");
      return;
    }
    try {
      final List<String> arguments = ['-c', '$workingPath/config.json', 'run'];
      print("启动进程: $exePath ${arguments.join(' ')}");
      _process = await Process.start(
        exePath,
        arguments,
        workingDirectory: workingPath,
      );
      isRunning = true;
      print("进程已启动，PID: ${_process!.pid}");

      _process!.stdout.transform(const SystemEncoding().decoder).listen((data) {
        // print("STDOUT: $data");
      });

      _process!.stderr.transform(const SystemEncoding().decoder).listen((data) {
        // print("STDERR: $data");
      });

      _process!.exitCode.then((code) {
        isRunning = false;
        print("进程已退出，退出码: $code");
      });
    } catch (e) {
      print("启动进程失败: $e");
    }
  }

  /// 停止 exe 进程
  Future<void> stop() async {
    if (_process != null && isRunning) {
      print("正在尝试停止进程...");
      _process!.kill(ProcessSignal.sigterm);
      isRunning = false;
      print("进程已停止");
    } else {
      print("当前没有正在运行的进程");
    }
  }

  /// 检查进程状态
  bool status() {
    return isRunning;
  }
}
