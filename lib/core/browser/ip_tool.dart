import 'chrome_tool.dart';
import 'package:ffi/ffi.dart'; // For working with C-style strings


// String getGeoInfo(String proxyTypeRaw,String proxyAddressRaw) {
//   final tool = ToolLibrary();
//
//   final proxyType = proxyTypeRaw.toNativeUtf8();
//   final proxyAddress = proxyAddressRaw.toNativeUtf8();
//
//   final result = tool.getGeoInfo(proxyType, proxyAddress);
//
//   calloc.free(proxyType);
//   calloc.free(proxyAddress);
//   return result.toDartString();
// }
