import 'dart:io';
import 'dart:convert';
import 'package:browser_tool/browser_tool.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:archive/archive.dart';
import 'package:icon/icon.dart';

import '../relay/config.dart';
import '../relay/core.dart';
import 'chrome_tool.dart';

String extractLastStringFromPath(String path) {
  final segments = path.split('/');
  return segments.last;
}





Future<int?> launchChromeWithConfig(Map<String, dynamic> config, String configFilePath) async {
  // // 打印整个config内容
  print('=== 完整 Config 内容 ===');
  print(JsonEncoder.withIndent('  ').convert(config));
  print('========================');

  // 处理configFilePath
  configFilePath = configFilePath.replaceAll("\\", "/");

  // 提取必要的信息
  final url = config['account'] ?? '';
  final username = config['account_username'] ?? '';
  final password = config['account_password'] ?? '';
  final cookie = config['cookie'] ?? '';
  String proxyType = config['proxy_type'] ?? '';
  String proxyAddress = config['proxy_address'] ?? '';
  int proxyPort = config['proxy_port'] ?? '';
  final proxyUsername = config['proxy_username'] ?? '';
  final proxyPassword = config['proxy_password'] ?? '';
  print(proxyType);

  if (proxyType == 'trojan') {
    proxyPort = await addTrojanOutbound(
      serverAddress: proxyAddress,
      serverPort: proxyPort, // 原始服务器端口
      password: proxyPassword,
    );
    // 只有trojan类型才设置为socks5
    proxyType = 'socks5';
    proxyAddress = '127.0.0.1';
  } else if (proxyType == 'socks5') {
    proxyPort = await addSocksOutbound(
      serverAddress: proxyAddress,
      serverPort: proxyPort, // 原始服务器端口
      username: proxyUsername, // 如果没有用户名就传null
      password: proxyPassword,
    );
    proxyAddress = '127.0.0.1';
  } else if (proxyType == 'http' || proxyType == 'https') {
    proxyPort = await addSocksOutbound(
      serverAddress: proxyAddress,
      serverPort: proxyPort, // 原始服务器端口
      username: proxyUsername, // 如果没有用户名就传null
      password: proxyPassword,
    );
    proxyType = 'socks5';
    proxyAddress = '127.0.0.1';
  }
  if (proxyType == 'socks5') {
    // 获取ProcessManager单例实例
    final processManager = ProcessManager();
    // 或者使用静态方法: final processManager = ProcessManager.getInstance();

    if (processManager.status()) {
      await processManager.stop();
      await processManager.start();
    } else {
      await processManager.start();
    }
  }

  // 检查目录并创建初始化，然后执行完整操作
  final needExtract = !await checkDirectory(configFilePath);
  if (needExtract) {
    // 创建目录
    final configDir = Directory(configFilePath);
    await configDir.create(recursive: true);
    print('Created directory: $configFilePath');
  }

  // 使用合并的操作函数 - 在isolate中按顺序执行解压和BrowserTool操作
  String? geoInfo;
  try {
    // 构建代理地址字符串
    String proxyAddressForGeo = '';
    if (proxyType != 'none' && proxyType.isNotEmpty && proxyAddress.isNotEmpty) {
      proxyAddressForGeo = '$proxyAddress:$proxyPort';
    }

    // 构建archive路径
    String? archivePath;
    if (needExtract) {
      archivePath = kDebugMode
          ? 'C:/Users/<USER>/IdeaProjects/Suiyu-re/assets/119.tar.xz'
          : '${Directory.current.path}/data/flutter_assets/assets/119.tar.xz';
    }

    print('Executing complete operations in main thread...');
    
    // 第一步：如果需要，先进行文件解压
    if (needExtract && archivePath != null) {
      print('Step 1: Extracting archive...');
      final archiveFile = File(archivePath);
      if (!await archiveFile.exists()) {
        throw Exception('Archive file not found: $archivePath');
      }

      print('Extracting $archivePath to $configFilePath');

      // 读取.tar.xz文件
      final archiveBytes = await archiveFile.readAsBytes();

      // 首先解压XZ
      final xzDecoder = XZDecoder();
      final tarBytes = xzDecoder.decodeBytes(archiveBytes);

      // 然后解压TAR
      final tarDecoder = TarDecoder();
      final archive = tarDecoder.decodeBytes(tarBytes);

      // 提取文件到目标目录
      for (final file in archive) {
        final filePath = '$configFilePath/${file.name}';
        if (file.isFile) {
          final outputFile = File(filePath);
          await outputFile.create(recursive: true);
          await outputFile.writeAsBytes(file.content as List<int>);
          print('Extracted file: ${file.name}');
        } else if (file.isDirectory) {
          final outputDir = Directory(filePath);
          await outputDir.create(recursive: true);
          print('Created directory: ${file.name}');
        }
      }

      print('Step 1 completed: Successfully extracted archive to $configFilePath');
    }

    // 第二步：进行BrowserTool操作
    print('Step 2: Starting browser tool operations...');
    
    try {
      // 1. 写入固定密钥
      print('Writing fixed key to $configFilePath');
      final keyResult = BrowserTool.writeFixedKey(configFilePath);
      if (keyResult == null) {
        print('Failed to write fixed key');
      } else {
        print('writeFixedKey result: $keyResult');
      }

      // 2. 设置密码
      if (username.isNotEmpty && password.isNotEmpty) {
        print('Setting password for $url');
        final passwordResult = BrowserTool.changePassword(
          url, 
          username, 
          password, 
          '$configFilePath/'
        );
        if (passwordResult == null) {
          print('Failed to change password');
        } else {
          print('changePassword result: $passwordResult');
        }
      }

      // 3. 更新Cookie
      if (cookie.isNotEmpty) {
        print('Updating cookie for $configFilePath/');
        final cookieResult = BrowserTool.updateCookie(cookie, '$configFilePath/');
        if (cookieResult == null) {
          print('Failed to update cookie');
        } else {
          print('updateCookie result: $cookieResult');
        }
      }

      // 4. 获取地理信息
      if (proxyType != 'none' && proxyType.isNotEmpty) {
        print('Fetching geo info...');
        final geoInfoResult = BrowserTool.fetchGeoInfo(
          proxyType == 'none' || proxyType.isEmpty ? 'none' : proxyType,
          proxyAddressForGeo
        );
        if (geoInfoResult != null) {
          geoInfo = geoInfoResult;
          print('Received geo info: $geoInfoResult');
        } else {
          print('Failed to fetch geo info');
        }
      }

      print('Step 2 completed: Browser tool operations completed successfully');
    } catch (e) {
      print('Error in browser tool operations: $e');
      // 继续执行，不阻断启动流程
    }
  } catch (e) {
    print('Error in complete operations: $e');
    if (needExtract) {
      // 如果是新目录且操作失败，重新抛出异常
      rethrow;
    }
    // 否则继续执行，不阻断启动流程
  }

  // 声明geoInfo变量在外层作用域
  Map<String, dynamic>? parsedGeoInfo;

  // 检查并获取地理信息以自动填充相关参数
  final needGeoInfo = (config['timezone'] == null || config['timezone'].toString().isEmpty) ||
      (config['language'] == null || config['language'].toString().isEmpty) ||
      (config['locale'] == null || config['locale'].toString().isEmpty) ||
      (config['latitude'] == null || config['latitude'].toString().isEmpty) ||
      (config['longitude'] == null || config['longitude'].toString().isEmpty);

  if (needGeoInfo && geoInfo != null) {
    try {
      parsedGeoInfo = jsonDecode(geoInfo);
      print('Using geo info for auto-fill: $geoInfo');

      // 自动填充缺失的参数
      if (config['timezone'] == null || config['timezone'].toString().isEmpty) {
        config['timezone'] = parsedGeoInfo!['timezone'];
        print('Auto-set timezone: ${parsedGeoInfo['timezone']}');
      }

      if (config['language'] == null || config['language'].toString().isEmpty) {
        // 从locale中提取language (例如: en-US -> en)
        final locale = parsedGeoInfo!['locale'] as String?;
        if (locale != null && locale.contains('-')) {
          config['language'] = locale.split('-')[0];
          print('Auto-set language: ${config['language']}');
        }
      }

      if (config['locale'] == null || config['locale'].toString().isEmpty) {
        config['locale'] = parsedGeoInfo!['locale'];
        print('Auto-set locale: ${parsedGeoInfo['locale']}');
      }

      if (config['latitude'] == null || config['latitude'].toString().isEmpty) {
        config['latitude'] = parsedGeoInfo!['latitude'].toString();
        print('Auto-set latitude: ${parsedGeoInfo['latitude']}');
      }

      if (config['longitude'] == null || config['longitude'].toString().isEmpty) {
        config['longitude'] = parsedGeoInfo!['longitude'].toString();
        print('Auto-set longitude: ${parsedGeoInfo['longitude']}');
      }
    } catch (e) {
      print('Error parsing geo info JSON: $e');
    }
  }

  // 构建Chrome启动参数 - 修改为ruyi格式
  final chromeArgs = <String>[];

  // 构建ruyi JSON参数
  final ruyiConfig = <String, dynamic>{
    'post': 'Ruyi Education bilibili: https://space.bilibili.com/172381477',
    'portScan': {
      'enable': config['portScan']?['enable'] ?? 'no',
    },
    'canvas': {
      'noise': double.tryParse(config['canvas']?['noise']?.toString() ?? '1.15') ?? 1.15,
    },
    'webrtc': {
      'public': config['webrtc']?['public'] ?? parsedGeoInfo?['ip'] ?? '127.0.0.1',
      'private': config['webrtc']?['private'] ?? parsedGeoInfo?['ip'] ?? '127.0.0.1',
    },
    'position': {
      'longitude': double.tryParse(config['longitude']?.toString() ?? '0') ?? 0.0,
      'latitude': double.tryParse(config['latitude']?.toString() ?? '0') ?? 0.0,
      'altitude': 100.0,
      'accuracy': 10.0,
    },
    'webgl': {
      'vendor': config['webgl_vendor'] ?? config['webgl']?['vendor'] ?? 'Google Inc. (Intel)',
      'renderer': config['webgl_render'] ?? config['webgl']?['renderer'] ?? 'ANGLE (Intel)',
    },
    'gpu': {
      'device': config['gpu']?['device'] ?? 'ruyi',
      'description': config['gpu']?['description'] ?? 'ruyi gpu',
    },
    'webaudio': double.tryParse(config['webaudio']?.toString() ?? '70') ?? 70.0,
    'clientrect': {
      'x': double.tryParse(config['clientrect']?['x']?.toString() ?? config['client_rect']?.toString() ?? '0') ?? 0.0,
      'y': double.tryParse(config['clientrect']?['y']?.toString() ?? '0') ?? 0.0,
    },
    'screen': {
      'width': double.tryParse(config['screen']?['width']?.toString() ?? config['resolution_width']?.toString() ?? '800') ?? 800.0,
      'height': double.tryParse(config['screen']?['height']?.toString() ?? config['resolution_height']?.toString() ?? '600') ?? 600.0,
      'colorDepth': double.tryParse(config['screen']?['colorDepth']?.toString() ?? '24') ?? 24.0,
      'availWidth': double.tryParse(config['screen']?['availWidth']?.toString() ?? '795') ?? 795.0,
      'availHeight': double.tryParse(config['screen']?['availHeight']?.toString() ?? '574') ?? 574.0,
    },
    'mobile': {
      'touchsupport': double.tryParse(config['mobile']?['touchsupport']?.toString() ?? '0') ?? 0.0,
    },
    'hardware': {
      'concurrency': double.tryParse(config['hardware']?['concurrency']?.toString() ?? config['kernel']?.toString() ?? '8') ?? 8.0,
      'memory': double.tryParse(config['hardware']?['memory']?.toString() ?? config['memory']?.toString() ?? '2') ?? 2.0,
    },
    'clientHint': {
      'platform': config['clientHint']?['platform'] ?? config['platform'] ?? 'Windows',
      'navigator.platform': config['clientHint']?['navigator.platform'] ?? 'Win32',
      'platform_version': config['clientHint']?['platform_version'] ?? config['platform_version'] ?? '15.0.0',
      'ua_full_version': config['clientHint']?['ua_full_version'] ?? config['full_version'] ?? '*********',
      'mobile': config['clientHint']?['mobile'] ?? '?0',
      'architecture': config['clientHint']?['architecture'] ?? 'x64',
      'bitness': config['clientHint']?['bitness'] ?? '64',
    },
    'languages': () {
      // 获取当前的locale值（可能来自geoInfo或config）
      final currentLocale = config['locale'] ?? 'en-US';

      // 如果有自定义的languages配置就使用，否则基于locale生成
      if (config['languages'] != null) {
        return {
          'js': config['languages']['js'] ?? currentLocale,
          'http': config['languages']['http'] ?? '$currentLocale;q=0.9',
        };
      } else {
        // 从locale中提取主语言代码 (en-HK -> en)
        final mainLanguage = currentLocale.contains('-')
            ? currentLocale.split('-')[0]
            : currentLocale;

        return {
          'js': '$currentLocale,$mainLanguage',
          'http': '$currentLocale;q=0.9,$mainLanguage;q=0.8',
        };
      }
    }(),
    'software': {
      'cookie': config['software']?['cookie'] ?? 'yes',
      'java': config['software']?['java'] ?? 'no',
      'dnt': config['software']?['dnt'] ?? 'no',
    },
    'font': {
      'removefont': config['font']?['removefont'] ?? 'Arial Unicode MS,Helvetica Neue',
    },
  };

  // 将ruyi配置转换为JSON字符串并添加到参数中
  final ruyiJsonString = jsonEncode(ruyiConfig);
  chromeArgs.add('--ruyi=$ruyiJsonString');

  // 处理代理设置
  if (proxyType != "none" && proxyType.isNotEmpty && proxyAddress.isNotEmpty) {
    chromeArgs.add('--proxy-server=$proxyType://$proxyAddress:$proxyPort');
  }

  // 添加标准Chrome参数
  final userAgent = config['useragent'] ?? 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36';
  chromeArgs.add('--user-agent=$userAgent');

  final locale = config['locale'] ?? 'en-US';
  chromeArgs.add('--lang=$locale');

  final timezone = config['timezone'] ?? 'America/Los_Angeles';
  chromeArgs.add('--time-zone-for-testing=$timezone');

  // 添加URL和用户数据目录参数
  if (url.isNotEmpty) {
    chromeArgs.add(url);
  }
  chromeArgs.add('--user-data-dir=$configFilePath');

  final argsString = chromeArgs.join(' ');

  print('Chrome启动参数:');
  print(argsString);

  final chromePath = kDebugMode
      ? '"C:/Users/<USER>/IdeaProjects/Suiyu-re/assets/chrome2/chrome.exe"'
      : '${Directory.current.path}/data/flutter_assets/assets/chrome2/chrome.exe';

  // 使用Process.start启动Chrome并获取PID
  try {
    // 准备参数列表
    final List<String> arguments = ['--no-first-run'];
    
    // 解析chromeArgs中的参数
    for (final arg in chromeArgs) {
      if (arg.contains('=')) {
        // 处理key=value格式的参数
        arguments.add(arg);
      } else {
        // 处理URL或其他独立参数
        arguments.add(arg);
      }
    }
    
    print('Chrome arguments: $arguments');
    
    final process = await Process.start(
      chromePath.replaceAll('"', ''), // 移除路径中的引号
      arguments,
    );
    
    final pid = process.pid;
    final _iconManager = IconManager();
    int sort = config['sort'] ?? 0;
    await Future.delayed(Duration(seconds: 1));
    await _iconManager.setIconWithNumber(pid, sort);

    print('Chrome launched with PID: $pid');
    
    // 存储进程对象到全局变量（供管理器使用）
    _activeProcesses[pid] = process;
    
    // 监听进程退出，清理全局存储
    process.exitCode.then((exitCode) {
      print('Chrome process $pid exited with code: $exitCode');
      _activeProcesses.remove(pid);
    });
    
    return pid; // 返回PID给调用者
  } catch (e) {
    print('Failed to launch Chrome: $e');
    return null;
    }
}

/// 简化版的Chrome启动函数，用于测试UI响应性
Future<int?> launchChromeSimple(Map<String, dynamic> config, String configFilePath) async {
  print('=== 简化版启动开始 ===');
  
  // 处理configFilePath
  configFilePath = configFilePath.replaceAll("\\", "/");

  // 提取必要的信息
  final url = config['account'] ?? '';
  String proxyType = config['proxy_type'] ?? '';
  final proxyAddress = config['proxy_address'] ?? '';
  int proxyPort = config['proxy_port'] ?? '';
  
  print('启动参数: url=$url, proxyType=$proxyType, proxyAddress=$proxyAddress:$proxyPort');

  // 给UI一个机会更新
  await Future.delayed(const Duration(milliseconds: 10));

  // 检查目录是否存在
  if (!await checkDirectory(configFilePath)) {
    // 如果目录不存在，就先创建一个空目录，不解压文件
    final configDir = Directory(configFilePath);
    await configDir.create(recursive: true);
    print('Created directory: $configFilePath');
    
    // 给UI一个机会更新
    await Future.delayed(const Duration(milliseconds: 10));
  }

  // 跳过所有BrowserTool操作，直接启动Chrome
  print('跳过BrowserTool操作，直接启动Chrome...');

  // 给UI一个机会更新
  await Future.delayed(const Duration(milliseconds: 10));

  // 构建最简单的Chrome启动参数
  final chromeArgs = <String>[];
  
  // 处理代理设置
  if (proxyType != "none" && proxyType.isNotEmpty && proxyAddress.isNotEmpty) {
    chromeArgs.add('--proxy-server=$proxyType://$proxyAddress:$proxyPort');
  }

  // 添加基础参数
  chromeArgs.add('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
  chromeArgs.add('--lang=en-US');
  
  // 添加URL和用户数据目录参数
  if (url.isNotEmpty) {
    chromeArgs.add(url);
  }
  chromeArgs.add('--user-data-dir=$configFilePath');

  print('简化Chrome启动参数: ${chromeArgs.join(' ')}');

  final chromePath = kDebugMode
      ? 'C:/Users/<USER>/Desktop/Suiyu/Suiyu-re/assets/chrome2/chrome.exe'
      : '${Directory.current.path}/data/flutter_assets/assets/chrome2/chrome.exe';

  // 给UI一个机会更新
  await Future.delayed(const Duration(milliseconds: 10));

  // 使用Process.start启动Chrome
  try {
    final List<String> arguments = ['--no-first-run'];
    arguments.addAll(chromeArgs);
    
    print('启动Chrome: $chromePath');
    print('参数: $arguments');
    
    final process = await Process.start(chromePath, arguments);
    final pid = process.pid;
    
    print('Chrome launched with PID: $pid');
    
    // 存储进程对象
    _activeProcesses[pid] = process;
    
    // 监听进程退出
    process.exitCode.then((exitCode) {
      print('Chrome process $pid exited with code: $exitCode');
      _activeProcesses.remove(pid);
    });
    
    return pid;
  } catch (e) {
    print('Failed to launch Chrome: $e');
    return null;
  }
}

// 全局变量存储活动进程
final Map<int, Process> _activeProcesses = {};

/// 根据PID获取进程对象
Process? getProcessByPid(int pid) {
  return _activeProcesses[pid];
}
