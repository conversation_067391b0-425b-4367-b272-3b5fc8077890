import 'dart:ffi'; // For FFI
import 'dart:io'; // For Platform
import 'package:ffi/ffi.dart'; // For working with C-style strings
import 'package:flutter/foundation.dart';

// // Define the function signatures
// typedef ChangePasswordNative = Pointer<Utf8> Function(
//     Pointer<Utf8> url,
//     Pointer<Utf8> username,
//     Pointer<Utf8> password,
//     Pointer<Utf8> path,
//     );
//
// typedef GetCookieNative = Pointer<Utf8> Function(
//     Pointer<Utf8> path,
//     );
//
// typedef UpdateCookieNative = Pointer<Utf8> Function(
//     Pointer<Utf8> cookie,
//     Pointer<Utf8> path,
//     );
//
// typedef GetGeoInfoNative = Pointer<Utf8> Function(
//     Pointer<Utf8> proxyType,
//     Pointer<Utf8> proxyAddress,
//     );
//
// class ToolLibrary {
//   late final String path;
//
//   late final DynamicLibrary _lib;
//
//   // Constructor to initialize paths and load the DLL
//   ToolLibrary() {
//     // Determine the current path based on the mode (debug or release)
//     String pathDebug = '${Directory.current.path}/assets';
//     String pathRelease = '${Directory.current.path}/data/flutter_assets/assets';
//     path = kDebugMode ? pathDebug : pathRelease;
//
//     // Load the DLL
//     _lib = DynamicLibrary.open('$path/tool.dll');
//
//     // Load the functions
//     changePassword = _lib
//         .lookup<NativeFunction<ChangePasswordNative>>('changePassword')
//         .asFunction();
//
//     getCookie = _lib
//         .lookup<NativeFunction<GetCookieNative>>('getCookie')
//         .asFunction();
//
//     updateCookie = _lib
//         .lookup<NativeFunction<UpdateCookieNative>>('updateCookie')
//         .asFunction();
//
//     getGeoInfo = _lib
//         .lookup<NativeFunction<GetGeoInfoNative>>('getGeoInfo')
//         .asFunction();
//   }
//
//   // Functions that can be called via FFI
//   late final ChangePasswordNative changePassword;
//   late final GetCookieNative getCookie;
//   late final UpdateCookieNative updateCookie;
//   late final GetGeoInfoNative getGeoInfo;
// }
//
// Future<String> getCookie(String pathRaw) async {
//   if (!await checkDirectory(pathRaw)) {
//     return '';
//   }
//   final tool = ToolLibrary();
//
//   final path = pathRaw.toNativeUtf8();
//
//   final result = tool.getCookie(path);
//
//   calloc.free(path);
//   return (result.toDartString() == 'null') ? '' : result.toDartString();
// }
//
// String setCookie(String cookieRaw,String pathRaw) {
//   final tool = ToolLibrary();
//
//   final cookie = cookieRaw.toNativeUtf8();
//   final path = pathRaw.toNativeUtf8();
//
//   final result = tool.updateCookie(cookie, path);
//
//   calloc.free(path);
//   calloc.free(cookie);
//   return result.toDartString();
// }
//
// String setPassword(String urlRaw,String usernameRaw,String passwordRaw,String pathRaw) {
//   final tool = ToolLibrary();
//
//   final url = urlRaw.toNativeUtf8();
//   final username = usernameRaw.toNativeUtf8();
//   final password = passwordRaw.toNativeUtf8();
//   final path = pathRaw.toNativeUtf8();
//
//   final result = tool.changePassword(url,username,password,path);
//
//
//   calloc.free(url);
//   calloc.free(username);
//   calloc.free(password);
//   calloc.free(path);
//   return result.toDartString();
// }

Future<bool> checkDirectory(String path) async {
  final directory = Directory(path);

  return await directory.exists();
}
