import 'dart:math';

String generateUserAgent(String chromeVersion, String osVersion) {
  // 基础的 User-Agent 前缀
  String baseAgent = "Mozilla/5.0";

  // 处理不同的系统版本
  String platform;
  if (osVersion.startsWith('Windows')) {
    // Windows 10 和 Windows 11 都应为 "Windows NT 10.0; Win64; x64"
    platform = 'Windows NT 10.0; Win64; x64';
  } else if (osVersion.startsWith('macOS')) {
    platform = getMacOSVersion(osVersion);
  } else if (osVersion == 'Linux') {
    platform = 'X11; Linux x86_64';
  } else if (osVersion.startsWith('Android')) {
    platform = 'Linux; Android ${osVersion.replaceFirst('Android ', '')}';
  } else if (osVersion.startsWith('iOS')) {
    platform = 'iPhone; CPU iPhone OS ${osVersion.replaceFirst('iOS ', '').replaceAll('.', '_')} like Mac OS X';
  } else {
    throw ArgumentError('不支持的系统版本: $osVersion');
  }


  // 生成最终的 User-Agent 字符串
  String userAgent = "$baseAgent ($platform) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/$chromeVersion Safari/537.36";

  return userAgent;
}

// 优化 macOS 版本处理逻辑
String getMacOSVersion(String osVersion) {
  String platform;
  if (osVersion == 'macOS 14' || osVersion == 'macOS 13' || osVersion == 'macOS 12' || osVersion == 'macOS 11') {
    // macOS 14 到 macOS 11 的版本号都映射为 "10_15_7"
    platform = 'Macintosh; Intel Mac OS X 10_15_7';
  } else if (osVersion == 'macOS 10') {
    // macOS 10 映射为 "10_13_1"
    platform = 'Macintosh; Intel Mac OS X 10_15_7';
  } else {
    // 处理其他 macOS 版本，按常规转换
    String macVersion = osVersion.replaceFirst('macOS ', '');
    platform = 'Macintosh; Intel Mac OS X ${macVersion.replaceAll('.', '_')}';
  }
  return platform;
}

// 生成 Chrome 详细版本号
String generateDetailedChromeVersion(String chromeVersion) {
  // 定义一些细致的版本号范围 (根据常见版本)
  Map<String, List<String>> chromeVersionDetails = {
    '127': ['127.0.6533.72', '127.0.6533.88', '127.0.6533.90', '127.0.6533.89', '127.0.6533.73'],
    '126': ['126.0.6478.114', '126.0.6478.115', '126.0.6478.61', '126.0.6478.56', '126.0.6478.182', '126.0.6478.183', '126.0.6478.57'],
    '125': ['125.0.6422.76', '125.0.6422.113', '125.0.6422.61', '125.0.6422.77', '125.0.6422.141','125.0.6422.60'],
    '124': ['124.0.6367.79', '124.0.6367.202', '124.0.6367.60', '124.0.6367.156', '124.0.6367.155', '124.0.6367.207','124.0.6367.155'],
    '123': ['123.0.6312.86', '123.0.6312.59', '123.0.6312.87', '123.0.6312.122', '123.0.6312.123'],
    '120': ['120.0.6099.71', '120.0.6099.129', '120.0.6099.130', '120.0.6099.199', '120.0.6099.200', '120.0.6099.216', '120.0.6099.217'],
    '119': ['119.0.6045.105', '119.0.6045.123', '119.0.6045.124', '119.0.6045.159', '119.0.6045.160', '119.0.6045.199', '119.0.6045.200'],
    '118': ['118.0.5993.70', '118.0.5993.88', '118.0.5993.117', '118.0.5993.118', '118.0.5993.159', '118.0.5993.160', '118.0.5993.200'],
    '117': ['117.0.5938.62', '117.0.5938.88', '117.0.5938.92', '117.0.5938.132', '117.0.5938.149', '117.0.5938.150', '117.0.5938.153']
    // 你可以继续为其他版本增加细致的版本号
  };

  // 如果版本号在预设列表中，则随机选择一个详细版本号
  if (chromeVersionDetails.containsKey(chromeVersion)) {
    List<String> versions = chromeVersionDetails[chromeVersion]!;
    return versions[Random().nextInt(versions.length)];
  }

  // 如果版本号没有详细版本号信息，返回一个默认的格式
  return "$chromeVersion.0.0.0";
}

String identifyOperatingSystem(String input) {
  if (input.contains('Windows')) {
    return 'Windows';
  } else if (input.contains('macOS')) {
    return 'macOS';
  } else {
    return 'Unknown';
  }
}

String generateVersion(String input) {
  final random = Random();

  if (input == 'Windows 10') {
    // 生成随机的 1 到 10 之间的数，后面固定为 ".0.0"
    int majorVersion = random.nextInt(10) + 1;
    return '$majorVersion.0.0';
  } else if (input == 'Windows 11') {
    // 返回 13 到 15 之间的随机数
    int version = random.nextInt(3) + 13; // 随机数范围 [0, 2] + 13 = [13, 14, 15]
    return '$version.0.0';
  } else if (input.contains('macOS')) {
    switch (input) {
      case 'macOS 14':
        return '14.6.1';
      case 'macOS 13':
        return '13.6.9';
      case 'macOS 12':
        return '12.7.6';
      case 'macOS 11':
        return '11.7.10';
      case 'macOS 10':
        return '10.15.7';
      default:
        return 'Unknown macOS version';
    }
  } else {
    return 'Unknown';
  }
}