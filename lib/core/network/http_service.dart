import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:frontend_re/core/storage/auth_storage.dart';
import 'package:frontend_re/router/go_router_provider.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

// HttpService Provider - 确保GoRouter正确注入到单例中
final httpServiceProvider = Provider<HttpService>((ref) {
  final router = ref.watch(goRouterProvider);
  final service = HttpService.instance; // 使用真正的单例
  service.setRouter(router);
  return service;
});

// 自动初始化Provider - 确保HttpService在应用启动时就获得GoRouter
final httpServiceInitProvider = Provider<void>((ref) {
  // 监听goRouter的变化并自动更新HttpService
  ref.listen(goRouterProvider, (previous, next) {
    HttpService.instance.setRouter(next);
  });
  // 立即设置当前的router
  final router = ref.read(goRouterProvider);
  HttpService.instance.setRouter(router);
});


/// API服务类
/// 提供全局统一的网络请求接口
class HttpService {
  static HttpService? _instance;
  late final Dio _dio;
  GoRouter? _router;

  /// 私有构造函数，防止外部实例化
  HttpService._internal() {
    _dio = Dio(
      BaseOptions(
        baseUrl: 'http://127.0.0.1:52618/api/v1', // 设置基础URL https://fp.api.suiyukeji.com/api/v1
        // 只有2xx状态码被认为是成功，其他状态码会触发onError
        validateStatus: (status) => status != null && status >= 200 && status < 300,
        connectTimeout: const Duration(seconds: 10), // 连接超时时间
        receiveTimeout: const Duration(seconds: 10), // 接收超时时间
        sendTimeout: const Duration(seconds: 10), // 添加发送超时时间
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ),
    );

    _initInterceptors();
  }

  /// 获取单例实例
  static HttpService get instance {
    _instance ??= HttpService._internal();
    return _instance!;
  }

  /// 工厂构造函数，重定向到单例
  factory HttpService() => instance;

  /// Set the GoRouter instance for navigation
  void setRouter(GoRouter router) {
    _router = router;
  }

  /// 初始化拦截器
  void _initInterceptors() {
    // 请求拦截器
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          final bool shouldSkipCheck = options.extra['skipAuthCheck'] ?? false;

          if (!shouldSkipCheck) {
            if (!await _checkCookieValid()) {
              _navigateToLogin();
              return handler.reject(
                DioException(
                  requestOptions: options,
                  error: '登录已过期，请重新登录',
                  type: DioExceptionType.cancel,
                ),
              );
            }
          }

          final token = await _getToken();
          if (token != null) {
            options.headers['Authorization'] = 'Bearer $token';
          }

          final cookie = await AuthStorage.getCookie();
          if (cookie != null) {
            options.headers['Cookie'] = cookie;
          }

          return handler.next(options);
        },
        onResponse: (response, handler) async {
          if (response.statusCode != null &&
              response.statusCode! >= 200 &&
              response.statusCode! < 300) {
            final cookies = response.headers['set-cookie'];
            if (cookies != null && cookies.isNotEmpty) {
              await AuthStorage.saveCookie(cookies.join('; '));
            }
          }
          return handler.next(response);
        },
        onError: (DioException e, handler) {
          // 如果是 401 或 403 错误（未授权/无权限），清除 token 并跳转到登录页
          if (e.response?.statusCode == 401 || e.response?.statusCode == 403) {
            // 打印调试信息
            AuthStorage.clear();
            _navigateToLogin();
          }
          return handler.next(e);
        },
      ),
    );

    _dio.interceptors.add(LogInterceptor(
      request: true,
      requestHeader: true,
      requestBody: true,
      responseHeader: true,
      responseBody: true,
      error: true,
    ));
  }

  /// 检查 Cookie 是否有效
  Future<bool> _checkCookieValid() async {
    final cookieString = await AuthStorage.getCookie();

    if (cookieString == null || cookieString.isEmpty) {
      return false;
    }

    try {
      DateTime? expiresDate;
      final parts = cookieString.split(';');
      for (var part in parts) {
        final trimmedPart = part.trim();
        if (trimmedPart.toLowerCase().startsWith('expires=')) {
          final dateString = trimmedPart.substring('expires='.length);
          try {
            expiresDate = HttpDate.parse(dateString);
            break;
          } catch (e) {
            try {
              expiresDate = DateTime.parse(dateString);
              break;
            } catch (e2) {
              return false;
            }
          }
        }
      }

      if (expiresDate != null) {
        final isExpired = expiresDate.isBefore(DateTime.now());
        if (isExpired) {
          return false;
        }
        return true;
      } else {
        // 没有找到Expires属性，按session cookie处理，认为有效
        return true;
      }
    } catch (e) {
      return false;
    }
  }

  /// 导航到登录页面
  void _navigateToLogin() {
    if (_router != null) {
      try {
        _router!.pushReplacementNamed('login');
      } catch (e) {
        try {
          _router!.goNamed('login');
        } catch (e2) {
          if (kDebugMode) {
            debugPrint('导航到登录页面失败: $e2');
          }
        }
      }
    }
  }

  /// 获取存储的token
  Future<String?> _getToken() async {
    final token = await AuthStorage.getToken();
    return token;
  }

  /// 设置基础URL
  void setBaseUrl(String baseUrl) {
    _dio.options.baseUrl = baseUrl;
  }

  /// 设置请求头
  void setHeaders(Map<String, dynamic> headers) {
    _dio.options.headers.addAll(headers);
  }

  /// GET请求
  Future<dynamic> get(
      String path, {
        Map<String, dynamic>? queryParameters,
        Options? options,
        CancelToken? cancelToken,
        ProgressCallback? onReceiveProgress,
      }) async {
    try {
      final response = await _dio.get(
        path,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onReceiveProgress: onReceiveProgress,
      );
      return _handleResponse(response);
    } catch (e) {
      return _handleError(e);
    }
  }

  /// POST请求
  Future<dynamic> post(
      String path, {
        dynamic data,
        Map<String, dynamic>? queryParameters,
        Options? options,
        CancelToken? cancelToken,
        ProgressCallback? onSendProgress,
        ProgressCallback? onReceiveProgress,
      }) async {
    try {
      final response = await _dio.post(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      );
      return _handleResponse(response);
    } catch (e) {
      return _handleError(e);
    }
  }

  /// PUT请求
  Future<dynamic> put(
      String path, {
        dynamic data,
        Map<String, dynamic>? queryParameters,
        Options? options,
        CancelToken? cancelToken,
        ProgressCallback? onSendProgress,
        ProgressCallback? onReceiveProgress,
      }) async {
    try {
      final response = await _dio.put(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      );
      return _handleResponse(response);
    } catch (e) {
      return _handleError(e);
    }
  }

  /// DELETE请求
  Future<dynamic> delete(
      String path, {
        dynamic data,
        Map<String, dynamic>? queryParameters,
        Options? options,
        CancelToken? cancelToken,
      }) async {
    try {
      final response = await _dio.delete(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
      return _handleResponse(response);
    } catch (e) {
      return _handleError(e);
    }
  }

  /// PATCH请求
  Future<dynamic> patch(
      String path, {
        dynamic data,
        Map<String, dynamic>? queryParameters,
        Options? options,
        CancelToken? cancelToken,
        ProgressCallback? onSendProgress,
        ProgressCallback? onReceiveProgress,
      }) async {
    try {
      final response = await _dio.patch(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      );
      return _handleResponse(response);
    } catch (e) {
      return _handleError(e);
    }
  }

  /// 下载文件
  Future<dynamic> download(
      String urlPath,
      String savePath, {
        Map<String, dynamic>? queryParameters,
        Options? options,
        CancelToken? cancelToken,
        ProgressCallback? onReceiveProgress,
      }) async {
    try {
      final response = await _dio.download(
        urlPath,
        savePath,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onReceiveProgress: onReceiveProgress,
      );
      return _handleResponse(response);
    } catch (e) {
      return _handleError(e);
    }
  }

  /// 统一处理响应
  dynamic _handleResponse(Response response) {
    // 由于validateStatus已经过滤了非2xx状态码，这里只处理成功响应
    return response.data;
  }

  /// 统一处理错误
  dynamic _handleError(dynamic error) {
    if (error is DioException) {
      if (error.type == DioExceptionType.cancel &&
          error.error == '登录已过期，请重新登录') {
        AuthStorage.clear();
        _navigateToLogin();
        throw error;
      }

      switch (error.type) {
        case DioExceptionType.connectionTimeout:
          throw DioException(
            requestOptions: error.requestOptions,
            type: DioExceptionType.connectionTimeout,
            error: '连接超时',
          );
        case DioExceptionType.sendTimeout:
          throw DioException(
            requestOptions: error.requestOptions,
            type: DioExceptionType.sendTimeout,
            error: '请求超时',
          );
        case DioExceptionType.receiveTimeout:
          throw DioException(
            requestOptions: error.requestOptions,
            type: DioExceptionType.receiveTimeout,
            error: '响应超时',
          );
        case DioExceptionType.badCertificate:
          throw DioException(
            requestOptions: error.requestOptions,
            type: DioExceptionType.badCertificate,
            error: '证书验证失败',
          );
        case DioExceptionType.badResponse:
          String message = '未知错误';
          
          // 根据记忆，后端错误信息应该从message字段获取，也要检查error字段
          if (error.response?.data is Map) {
            final responseData = error.response?.data as Map;
            message = responseData['message'] ?? responseData['error'] ?? '服务器错误';
          }
          throw DioException(
            requestOptions: error.requestOptions,
            response: error.response,
            type: DioExceptionType.badResponse,
            error: message,
          );
        case DioExceptionType.cancel:
          throw DioException(
            requestOptions: error.requestOptions,
            type: DioExceptionType.cancel,
            error: '请求取消',
          );
        case DioExceptionType.connectionError:
          throw DioException(
            requestOptions: error.requestOptions,
            type: DioExceptionType.connectionError,
            error: '连接错误',
          );
        case DioExceptionType.unknown:
          throw DioException(
            requestOptions: error.requestOptions,
            type: DioExceptionType.unknown,
            error: '网络错误: ${error.message}',
          );
      }
    } else {
      throw Exception('未知错误: $error');
    }
  }

  /// 创建取消令牌
  CancelToken createCancelToken() {
    return CancelToken();
  }

  /// 取消请求
  void cancelRequests(CancelToken token) {
    token.cancel('用户取消请求');
  }
}
