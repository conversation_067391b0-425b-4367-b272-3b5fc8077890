import 'package:frontend_re/core/storage/auth_storage.dart';
import 'package:frontend_re/core/utils/ui/window_manager_util.dart';
import 'package:frontend_re/core/relay/init.dart';

/// 应用初始化服务
/// 统一管理应用启动时的各种初始化逻辑
class AppInitializationService {
  
  /// 初始化应用
  /// 按顺序执行各种启动时必需的初始化操作
  static Future<void> initialize() async {
    // 1. 初始化窗口管理器
    await WindowManagerUtil.initWindowManager();
    
    // 2. 初始化认证状态，检查并清理过期cookie
    await AuthStorage.initializeAuthState();
    
    // 3. 初始化其他核心服务
    await createOrOverwriteJsonFile();
    
    // 可以在这里添加更多初始化逻辑，比如：
    // - 检查应用更新
    // - 初始化崩溃报告
    // - 初始化统计分析
    // - 预加载必要资源
  }
} 
