import 'package:frontend_re/core/storage/auth_storage.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'auth_state_provider.g.dart';

@riverpod
class AuthState extends _$AuthState {
  @override
  Future<bool> build() async {
    // 异步检查认证状态
    final cookie = await AuthStorage.getCookie();
    return cookie != null && cookie.isNotEmpty;
  }

  // 更新认证状态
  void setAuthenticated(bool isAuthenticated) {
    state = AsyncValue.data(isAuthenticated);
  }
} 
