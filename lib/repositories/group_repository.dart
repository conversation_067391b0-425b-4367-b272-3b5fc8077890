import 'package:frontend_re/core/network/http_service.dart';
import 'package:frontend_re/domain/models/group_model.dart';
import 'package:frontend_re/domain/models/common_model.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'group_repository.g.dart';

@riverpod
class GroupRepository extends _$GroupRepository {
  final HttpService _httpService = HttpService.instance;

  @override
  Future<void> build() async {
    // 初始化方法，不需要返回值
  }

  // 获取分组列表
  Future<GroupResponse> getGroups(int userId) async {
    final res = await _httpService.get('/groups', queryParameters: {'user_id': userId});
    
    final response = CommonResponse.fromJson(res);
    if (response.isSuccess && response.hasData) {
      // 使用安全解析方法，为可能为null的字段设置默认值
      final groupResponse = response.parseDataSafely(
        GroupResponse.fromJson,
        {
          'groups': <Map<String, dynamic>>[], // 默认空列表
          'total': 0, // 默认0
        },
      );
      
      if (groupResponse != null) {
        return groupResponse;
      }
      
      // 如果解析失败，返回空的响应
      return const GroupResponse(groups: [], total: 0);
    }
    throw Exception(response.bestMessage);
  }

  // 创建分组
  Future<String> createGroup(String groupName) async {
    final res = await _httpService.post('/groups', data: {'name': groupName});
    
    final response = CommonResponse.fromJson(res);
    if (response.isSuccess) {
      return response.bestMessage; // 优先使用data中的message
    }
    throw Exception(response.bestMessage);
  }

  // 更新分组
  Future<String> updateGroup(int id, String groupName) async {
    final res = await _httpService.put('/groups', data: {'id': id, 'name': groupName});
    
    final response = CommonResponse.fromJson(res);
    if (response.isSuccess) {
      return response.bestMessage; // 优先使用data中的message
    }
    throw Exception(response.bestMessage);
  }

  // 删除分组
  Future<String> deleteGroup(int id) async {
    final res = await _httpService.delete('/groups', data: {'id': id});
    
    final response = CommonResponse.fromJson(res);
    if (response.isSuccess) {
      return response.bestMessage; // 优先使用data中的message
    }
    throw Exception(response.bestMessage);
  }
}
