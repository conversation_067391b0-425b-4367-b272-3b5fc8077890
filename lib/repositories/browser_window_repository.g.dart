// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'browser_window_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$browserWindowRepositoryHash() =>
    r'87cbc6fb26e39ddc0960055ef507e8d413498aeb';

/// See also [BrowserWindowRepository].
@ProviderFor(BrowserWindowRepository)
final browserWindowRepositoryProvider =
    AutoDisposeAsyncNotifierProvider<BrowserWindowRepository, void>.internal(
  BrowserWindowRepository.new,
  name: r'browserWindowRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$browserWindowRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$BrowserWindowRepository = AutoDisposeAsyncNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
