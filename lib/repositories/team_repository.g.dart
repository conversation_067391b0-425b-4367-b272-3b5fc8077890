// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'team_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$roleRepositoryHash() => r'fdf78a31f8afa3221bac824fffe5fec1b9534c7b';

/// See also [RoleRepository].
@ProviderFor(RoleRepository)
final roleRepositoryProvider =
    AutoDisposeAsyncNotifierProvider<RoleRepository, void>.internal(
  RoleRepository.new,
  name: r'roleRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$roleRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$RoleRepository = AutoDisposeAsyncNotifier<void>;
String _$teamRepositoryHash() => r'a987314790d87991118a96081fb2d9f0cc15dbea';

/// See also [TeamRepository].
@ProviderFor(TeamRepository)
final teamRepositoryProvider =
    AutoDisposeAsyncNotifierProvider<TeamRepository, void>.internal(
  TeamRepository.new,
  name: r'teamRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$teamRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$TeamRepository = AutoDisposeAsyncNotifier<void>;
String _$loginLogRepositoryHash() =>
    r'b351f22f082d181c2aa5b9aaeec2322481387785';

/// See also [LoginLogRepository].
@ProviderFor(LoginLogRepository)
final loginLogRepositoryProvider =
    AutoDisposeAsyncNotifierProvider<LoginLogRepository, void>.internal(
  LoginLogRepository.new,
  name: r'loginLogRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$loginLogRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$LoginLogRepository = AutoDisposeAsyncNotifier<void>;
String _$operationLogRepositoryHash() =>
    r'1d8d6abd462895ca2ff581a2f1c83c49788e5021';

/// See also [OperationLogRepository].
@ProviderFor(OperationLogRepository)
final operationLogRepositoryProvider =
    AutoDisposeAsyncNotifierProvider<OperationLogRepository, void>.internal(
  OperationLogRepository.new,
  name: r'operationLogRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$operationLogRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$OperationLogRepository = AutoDisposeAsyncNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
