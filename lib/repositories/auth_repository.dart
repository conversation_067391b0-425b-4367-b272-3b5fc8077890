import 'package:dio/dio.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:frontend_re/core/network/http_service.dart';
import 'package:frontend_re/domain/models/auth_model.dart';
import 'package:frontend_re/domain/models/common_model.dart';
part 'auth_repository.g.dart';

@riverpod
class AuthRepository extends _$AuthRepository {
  
  @override
  Future<void> build() async {
    // 初始化方法，不需要返回值
  }

  /// 获取已配置好GoRouter的HttpService实例
  final HttpService _httpService = HttpService.instance;

  /// 用户登录
  Future<AuthResponse> login(LoginRequest request) async {
    final res = await _httpService.post(
      '/login',
      data: request.toJson(),
      options: Options(extra: {'skipAuthCheck': true}),
    );

    final response = CommonResponse.fromJson(res);
    if (response.isSuccess && response.hasData) {
      final authResponse = response.parseData(AuthResponse.fromJson);
      if (authResponse != null) {
        return authResponse;
      }
    }
    throw Exception(response.bestMessage);
  }

  /// 用户注册
  Future<AuthResponse> register(RegisterRequest request) async {
    final res = await _httpService.post(
      '/auth/register',
      data: request.toJson(),
      options: Options(extra: {'skipAuthCheck': true}),
    );
    
    final response = CommonResponse.fromJson(res);
    if (response.isSuccess) {
      // 注册可能只返回消息，不一定有复杂数据
      if (response.hasData) {
        final authResponse = response.parseData(AuthResponse.fromJson);
        if (authResponse != null) {
          return authResponse;
        }
      }
      // 如果没有数据，创建一个只包含消息的响应
      return AuthResponse(message: response.bestMessage);
    }
    throw Exception(response.bestMessage);
  }

  /// 生成验证码
  Future<CaptchaResponse> generateCaptcha() async {
    final res = await _httpService.post(
      '/captcha/generate',
      options: Options(extra: {'skipAuthCheck': true}),
    );
    return CaptchaResponse.fromJson(res);
  }

  /// 发送注册验证邮件
  Future<SendRegisterEmailResponse> sendRegisterEmail(SendRegisterEmailRequest request) async {
    final res = await _httpService.post(
      '/email/send-register-verification',
      data: request.toJson(),
      options: Options(extra: {'skipAuthCheck': true}),
    );
    
    // 后端直接返回SendRegisterEmailResponse格式，不是通用的CommonResponse
    try {
      return SendRegisterEmailResponse.fromJson(res);
    } catch (e) {
      // 如果解析失败，尝试作为CommonResponse处理
      final response = CommonResponse.fromJson(res);
      if (response.isSuccess && response.hasData) {
        final sendRegisterEmailResponse = response.parseData(SendRegisterEmailResponse.fromJson);
        if (sendRegisterEmailResponse != null) {
          return sendRegisterEmailResponse;
        }
      }
      throw Exception(response.bestMessage);
    }
  }
} 
