import 'package:frontend_re/domain/models/proxy_model.dart';
import 'package:frontend_re/domain/models/common_model.dart';
import 'package:frontend_re/core/network/http_service.dart';


import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'proxy_repository.g.dart';

@riverpod
class ProxyRepository extends _$ProxyRepository {
  final HttpService _httpService = HttpService.instance;
  
  @override
  Future<void> build() async {
    // 初始化方法，不需要返回值
  }

  /// -------------------自建代理-----------------------------------------

  /// 获取单一自建代理
  Future<SelfProxyDetail> getSelfHostProxy(int id) async {
    final res = await _httpService.get(
      '/self-host-proxies/$id',
    );
    
    final response = CommonResponse.fromJson(res);
    if (response.isSuccess && response.hasData) {
      final proxyDetail = response.parseData(SelfProxyDetail.fromJson);
      if (proxyDetail != null) {
        return proxyDetail;
      }
    }
    throw Exception(response.bestMessage);
  }

  /// 获取自建代理列表
  Future<SelfProxyResponse> getSelfHostProxies(ProxyListRequest request) async {
    final res = await _httpService.get(
      '/self-host-proxies',
      queryParameters: request.toJson(),
    );
    
    final response = CommonResponse.fromJson(res);
    if (response.isSuccess && response.hasData) {
      final proxyResponse = response.parseData(SelfProxyResponse.fromJson);
      if (proxyResponse != null) {
        return proxyResponse;
      }
    }
    throw Exception(response.bestMessage);
  }

  /// 创建自建代理
  Future<String> createSelfHostProxy(SelfProxyCreateRequest request) async {
    final res = await _httpService.post(
      '/self-host-proxies',
      data: request.toJson(),
    );
    
    final response = CommonResponse.fromJson(res);
    if (response.isSuccess) {
      return response.bestMessage;
    }
    throw Exception(response.bestMessage);
  }

  /// 批量创建自建代理
  Future<String> createSelfHostProxies(List<SelfProxyCreateRequest> requests) async {
    final res = await _httpService.post(
      '/self-host-proxies/batch',
      data: {'proxies': requests.map((e) => e.toJson()).toList()},
    );
    
    final response = CommonResponse.fromJson(res);
    if (response.isSuccess) {
      return response.bestMessage;
    }
    throw Exception(response.bestMessage);
  }

  /// 更新自建代理
  Future<String> updateSelfHostProxy(SelfProxyUpdateRequest request) async {
    final res = await _httpService.put(
      '/self-host-proxies',
      data: request.toJson(),
    );
    
    final response = CommonResponse.fromJson(res);
    if (response.isSuccess) {
      return response.bestMessage;
    }
    throw Exception(response.bestMessage);
  }

  /// 删除自建代理
  Future<String> deleteSelfHostProxy(List<int> ids) async {
    final res = await _httpService.delete(
      '/self-host-proxies',
      data: {'ids': ids},
    );
    
    final response = CommonResponse.fromJson(res);
    if (response.isSuccess) {
      return response.bestMessage;
    }
    throw Exception(response.bestMessage);
  }

  /// -------------------平台代理-----------------------------------------

  /// 获取平台代理列表
  // Future<PlatformProxyModel> getPlatformProxies(ProxyListRequest request) async {
  //   final res = await _httpService.get(
  //     '/self-host-proxies',
  //     queryParameters: request.toJson(),
  //   );
  //   
  //   final response = CommonResponse.fromJson(res);
  //   if (response.isSuccess && response.hasData) {
  //     final platformProxy = response.parseData(PlatformProxyModel.fromJson);
  //     if (platformProxy != null) {
  //       return platformProxy;
  //     }
  //   }
  //   throw Exception(response.bestMessage);
  // }
}
