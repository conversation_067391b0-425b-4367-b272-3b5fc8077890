import 'package:frontend_re/core/network/http_service.dart';
import 'package:frontend_re/domain/models/common_model.dart';
import 'package:frontend_re/domain/models/order_model.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'order_repository.g.dart';

@riverpod
class OrderRepository extends _$OrderRepository {
  final HttpService _httpService = HttpService.instance;

  @override
  Future<void> build() async {
    // 初始化方法，不需要返回值
  }

  // 获取订单列表
  Future<OrderListResponse> getOrders(OrderListRequest request) async {
    final res = await _httpService.get('/orders', queryParameters: request.toJson());
    final response = CommonResponse.fromJson(res);
    if (response.isSuccess) {
      final orderListResponse = response.parseData(OrderListResponse.fromJson);
      if (orderListResponse != null) {
        return orderListResponse;
      }
    }
    throw Exception(response.bestMessage);
  }

  // 创建订单
  Future<CreateOrderResponse> createOrder(CreateOrderRequest request) async {
    final res = await _httpService.post('/orders', data: request.toJson());
    final response = CommonResponse.fromJson(res);
    if (response.isSuccess) {
      final createOrderResponse = response.parseData(CreateOrderResponse.fromJson);
      if (createOrderResponse != null) {
        return createOrderResponse;
      }
    }
    throw Exception(response.bestMessage);
  }

  // 取消订单
  Future<String> cancelOrder({int? orderId, int? orderNumber}) async {
    final res = await _httpService.post('/orders/cancel', data: {'order_id': orderId, 'order_number': orderNumber});
    final response = CommonResponse.fromJson(res);
    if (response.isSuccess) {
      return response.bestMessage;
    }
    throw Exception(response.bestMessage);
  }

  // 获取订单详情
  Future<Order> getOrderDetail({int? orderId, int? orderNumber}) async {
    final res = await _httpService.get('/orders/detail', queryParameters: {'order_id': orderId, 'order_number': orderNumber});
    final response = CommonResponse.fromJson(res);
    if (response.isSuccess && response.data != null) {
      // 手动提取 order 字段
      final dataMap = response.data as Map<String, dynamic>;
      final orderData = dataMap['order'];
      if (orderData != null) {
        return Order.fromJson(orderData as Map<String, dynamic>);
      }
    }
    throw Exception(response.bestMessage);
  }

  // 计算订单价格
  Future<double> calculateOrderPrice(CalculateOrderPriceRequest request) async {
    final res = await _httpService.post('/orders/calculate', data: request.toJson());
    final response = CommonResponse.fromJson(res);
    if (response.isSuccess) {
      final dataMap = response.data as Map<String, dynamic>;
      return dataMap['total_price'] as double;
    }
    throw Exception(response.bestMessage);
  }
}
