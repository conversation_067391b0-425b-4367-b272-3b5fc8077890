import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:frontend_re/domain/models/browser_window_model.dart';
import 'package:frontend_re/domain/models/common_model.dart';
import 'package:frontend_re/core/network/http_service.dart';

part 'browser_window_repository.g.dart';

@riverpod
class BrowserWindowRepository extends _$BrowserWindowRepository {
  final HttpService _httpService = HttpService.instance;
  
  @override
  Future<void> build() async {
    // 初始化方法，不需要返回值
  }

  /// 获取环境列表
  Future<BrowserWindowModel> getEnvironments(BrowserWindowRequest request) async {
    final res = await _httpService.get(
      '/environments',
      queryParameters: request.toJson(),
    );
    
    final response = CommonResponse.fromJson(res);
    if (response.isSuccess && response.hasData) {
      final browserModel = response.parseData(BrowserWindowModel.fromJson);
      return browserModel ?? const BrowserWindowModel(windows: [], total: 0);
    }
    throw Exception(response.bestMessage);
  }

  /// 获取环境详情
  Future<BrowserWindow> getEnvironmentByID(int id) async {
    final res = await _httpService.get(
      '/environments/$id',
    );
    
    final response = CommonResponse.fromJson(res);
    if (response.isSuccess && response.hasData) {
      final browserWindow = response.parseData(BrowserWindow.fromJson);
      if (browserWindow != null) {
        return browserWindow;
      }
    }
    throw Exception(response.bestMessage);
  }

  /// 创建环境
  Future<String> createEnvironment(BrowserCreateRequest request) async {
    final res = await _httpService.post(
      '/environments',
      data: request.toJson(),
    );
    
    final response = CommonResponse.fromJson(res);
    if (response.isSuccess) {
      return response.bestMessage;
    }
    throw Exception(response.bestMessage);
  }

  /// 更新环境信息，批量
  Future<String> updateEnvironment(List<BrowserWindowUpdateRequest> updateRequests) async {
    final res = await _httpService.put(
      '/environments',
      data: {'environments': updateRequests.map((e) => e.toJson()).toList()},
    );
    
    final response = CommonResponse.fromJson(res);
    if (response.isSuccess) {
      return response.bestMessage;
    }
    throw Exception(response.bestMessage);
  }

  /// 删除环境，批量
  Future<String> deleteEnvironments(List<int> ids) async {
    final res = await _httpService.delete(
      '/environments',
      data: {'ids': ids},
    );
    
    final response = CommonResponse.fromJson(res);
    if (response.isSuccess) {
      return response.bestMessage;
    }
    throw Exception(response.bestMessage);
  }

  /// 批量更新环境代理
  Future<String> updateEnvironmentProxy(List<int> ids, int proxyId) async {
    final res = await _httpService.put(
      '/environments/proxy',
      data: {'ids': ids, 'proxy_id': proxyId},
    );
    
    final response = CommonResponse.fromJson(res);
    if (response.isSuccess) {
      return response.bestMessage;
    }
    throw Exception(response.bestMessage);
  }

  /// 获取已删除的环境
  Future<BrowserWindowModel> getDeletedEnvironments(DeletedBrowserWindowRequest request) async {
    final res = await _httpService.get(
      '/environments/deleted',
      queryParameters: request.toJson(),
    );
    
    final response = CommonResponse.fromJson(res);
    if (response.isSuccess && response.hasData) {
      final browserModel = response.parseData(BrowserWindowModel.fromJson);
      return browserModel ?? const BrowserWindowModel(windows: [], total: 0);
    }
    throw Exception(response.bestMessage);
  }

  /// 恢复已删除的环境
  Future<String> restoreEnvironments(List<int> ids) async {
    final res = await _httpService.put(
      '/environments/restore',
      data: {'ids': ids},
    );
    
    final response = CommonResponse.fromJson(res);
    if (response.isSuccess) {
      return response.bestMessage;
    }
    throw Exception(response.bestMessage);
  }

  /// 永久删除环境
  Future<String> hardDeleteEnvironments(List<int> ids) async {
    final res = await _httpService.delete(
      '/environments/permanent',
      data: {'ids': ids},
    );
    
    final response = CommonResponse.fromJson(res);
    if (response.isSuccess) {
      return response.bestMessage;
    }
    throw Exception(response.bestMessage);
  }
}
