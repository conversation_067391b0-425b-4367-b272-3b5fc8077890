import 'package:frontend_re/core/network/http_service.dart';
import 'package:frontend_re/domain/models/role_model.dart';
import 'package:frontend_re/domain/models/team_model.dart';
import 'package:frontend_re/domain/models/common_model.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:intl/intl.dart';


part 'team_repository.g.dart';

// 角色仓库
@riverpod
class RoleRepository extends _$RoleRepository {
  final HttpService _httpService = HttpService.instance;
  
  @override
  Future<void> build() async {
    // 初始化方法，不需要返回值
  }

  /// 获取角色列表，或者根据ID获取特定角色,当id为0时，获取所有角色
  Future<List<RoleModel>> getRoles(int id) async {
    final res = await _httpService.get(
      '/roles',
      queryParameters: {'id': id},
    );
    
    final response = CommonResponse.fromJson(res);
    if (response.isSuccess && response.hasData) {
      final roles = response.parseDataList(RoleModel.fromJson);
      if (roles != null) {
        return roles;
      }
    }
    throw Exception(response.bestMessage);
  }

  /// 删除角色
  Future<String> deleteRole(int roleId) async {
    final res = await _httpService.delete(
      '/roles',
      data: {'role_id': roleId},
    );
    
    final response = CommonResponse.fromJson(res);
    if (response.isSuccess) {
      return response.bestMessage;
    }
    throw Exception(response.bestMessage);
  }

  /// 更新角色
  Future<String> updateRole(UpdateRoleRequest request) async {
    final res = await _httpService.put(
      '/roles',
      data: request.toJson(),
    );
    
    final response = CommonResponse.fromJson(res);
    if (response.isSuccess) {
      return response.bestMessage;
    }
    throw Exception(response.bestMessage);
  }

  /// 创建角色
  Future<String> createRole(CreateRoleRequest request) async {
    final res = await _httpService.post(
      '/roles',
      data: request.toJson(),
    );
    
    final response = CommonResponse.fromJson(res);
    if (response.isSuccess) {
      return response.bestMessage;
    }
    throw Exception(response.bestMessage);
  }
}

// 团队仓库
@riverpod
class TeamRepository extends _$TeamRepository {
  final HttpService _httpService = HttpService();
  
  @override
  Future<void> build() async {
    // 初始化方法，不需要返回值
  }

  /// 获取团队成员列表
  Future<TeamModelResponse> getUsers(TeamRequest request) async {
    final res = await _httpService.get(
      '/teams/users',
      queryParameters: request.toJson(),
    );
    
    final response = CommonResponse.fromJson(res);
    if (response.isSuccess && response.hasData) {
      final teamResponse = response.parseData(TeamModelResponse.fromJson);
      if (teamResponse != null) {
        return teamResponse;
      }
    }
    throw Exception(response.bestMessage);
  }

  /// 创建用户
  Future<String> addUsers(List<AddTeamUserRequest> users) async {    
    final res = await _httpService.post(
      '/teams/users',
      data: {'users': users.map((e) => e.toJson()).toList()},
    );
    
    final response = CommonResponse.fromJson(res);
    if (response.isSuccess) {
      return response.bestMessage;
    }
    throw Exception(response.bestMessage);
  }

  /// 更新团队用户信息
  Future<String> updateUsers(List<UpdateTeamUserRequest> users) async {    
    final res = await _httpService.put(
      '/teams/users',
      data: {'users': users.map((e) => e.toJson()).toList()},
    );
    
    final response = CommonResponse.fromJson(res);
    if (response.isSuccess) {
      return response.bestMessage;
    }
    throw Exception(response.bestMessage);
  }

  /// 删除团队用户
  Future<String> deleteUsers(List<int> userIds) async {
    final res = await _httpService.delete(
      '/teams/users',
      data: {'user_ids': userIds},
    );
    
    final response = CommonResponse.fromJson(res);
    if (response.isSuccess) {
      return response.bestMessage;
    }
    throw Exception(response.bestMessage);
  }

  /// 更新团队名称
  Future<String> updateTeam(String newName) async {
    final res = await _httpService.put(
      '/teams',
      data: {'name': newName},
    );
    
    final response = CommonResponse.fromJson(res);
    if (response.isSuccess) {
      return response.bestMessage;
    }
    throw Exception(response.bestMessage);
  }
}

// 登录日志
@riverpod
class LoginLogRepository extends _$LoginLogRepository {
  final HttpService _httpService = HttpService();
  
  // 定义日期格式化器
  final DateFormat _dateFormatter = DateFormat('yyyy-MM-dd HH:mm:ss');
  
  @override
  Future<void> build() async {
    // 初始化方法，不需要返回值
  }

  /// 获取登录日志列表
  Future<LoginLogModel> getLoginLogs(LoginLogRequest request) async {
    final res = await _httpService.get(
      '/login-logs',
      queryParameters: request.toJson(),
    );

    final response = CommonResponse.fromJson(res);
    if (response.isSuccess && response.hasData) {
      // 获取原始数据进行日期格式化
      final dataMap = response.dataAsMap;
      if (dataMap != null) {
        // 格式化日期时间
        if (dataMap['logs'] != null && dataMap['logs'] is List) {
          for (var log in dataMap['logs']) {
            if (log['created_at'] != null) {
              // 将 ISO 8601 格式转换为易读格式
              final DateTime dateTime = DateTime.parse(log['created_at']);
              log['created_at'] = _dateFormatter.format(dateTime);
            }
          }
        }
        
        final loginLogModel = LoginLogModel.fromJson(dataMap);
        return loginLogModel;
      }
    }
    throw Exception(response.bestMessage);
  }
}

// 操作日志
@riverpod
class OperationLogRepository extends _$OperationLogRepository {
  final HttpService _httpService = HttpService();
  
  @override
  Future<void> build() async {
    // 初始化方法，不需要返回值
  }

  /// 获取操作日志
  Future<OperationLogModel> getOperationLogs(OperationLogRequest request) async {
    final res = await _httpService.get(
      '/operation-logs',
      queryParameters: request.toJson(),
    );
    
    final response = CommonResponse.fromJson(res);
    if (response.isSuccess && response.hasData) {
      final operationLogModel = response.parseData(OperationLogModel.fromJson);
      if (operationLogModel != null) {
        return operationLogModel;
      }
    }
    throw Exception(response.bestMessage);
  }
}
