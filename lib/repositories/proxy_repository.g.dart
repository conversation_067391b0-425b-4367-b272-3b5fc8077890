// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'proxy_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$proxyRepositoryHash() => r'cfdb950874ab16b050f7dea89736698a0b61f109';

/// See also [ProxyRepository].
@ProviderFor(ProxyRepository)
final proxyRepositoryProvider =
    AutoDisposeAsyncNotifierProvider<ProxyRepository, void>.internal(
  ProxyRepository.new,
  name: r'proxyRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$proxyRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ProxyRepository = AutoDisposeAsyncNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
