import 'package:frontend_re/core/network/http_service.dart';
import 'package:frontend_re/domain/models/user_model.dart';
import 'package:frontend_re/domain/models/common_model.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'user_repository.g.dart';

@riverpod
class UserRepository extends _$UserRepository {
  final HttpService _httpService = HttpService.instance;
  
  @override
  Future<void> build() async {
    // 初始化方法，不需要返回值
  }

  /// 获取用户个人信息
  Future<UserModel> getUserInfo() async {
    final res = await _httpService.get(
      '/users/profile',
    );
    
    final response = CommonResponse.fromJson(res);
    if (response.isSuccess && response.hasData) {
      final userModel = response.parseData(UserModel.fromJson);
      if (userModel != null) {
        return userModel;
      }
    }
    throw Exception(response.bestMessage);
  }

  /// 更新用户个人信息
  Future<String> updateUserInfo(UpdateUserInfoRequest user) async {
    final res = await _httpService.put(
      '/users/profile',
      data: user,
    );
    
    final response = CommonResponse.fromJson(res);
    if (response.isSuccess) {
      return response.bestMessage;
    }
    throw Exception(response.bestMessage);
  }

  /// 发送更换邮箱验证码
  Future<String> sendChangeEmailCode(String email) async {
    final res = await _httpService.post(
      '/user/sendEmail',
      data: {'email': email},
    );
    
    final response = CommonResponse.fromJson(res);
    if (response.isSuccess) {
      return response.bestMessage;
    }
    throw Exception(response.bestMessage);
  }
}
