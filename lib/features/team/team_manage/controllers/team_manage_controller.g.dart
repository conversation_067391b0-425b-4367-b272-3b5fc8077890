// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'team_manage_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$roleControllerHash() => r'abca1b5bf97faf79746a03d392991e06ded76c0c';

/// See also [RoleController].
@ProviderFor(RoleController)
final roleControllerProvider =
    AutoDisposeAsyncNotifierProvider<RoleController, List<RoleModel>>.internal(
  RoleController.new,
  name: r'roleControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$roleControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$RoleController = AutoDisposeAsyncNotifier<List<RoleModel>>;
String _$teamControllerHash() => r'2fd81b6cf99e50c42edd25dc5ec9689bed75cc05';

/// See also [TeamController].
@ProviderFor(TeamController)
final teamControllerProvider = AutoDisposeAsyncNotifierProvider<TeamController,
    TeamModelResponse>.internal(
  TeamController.new,
  name: r'teamControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$teamControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$TeamController = AutoDisposeAsyncNotifier<TeamModelResponse>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
