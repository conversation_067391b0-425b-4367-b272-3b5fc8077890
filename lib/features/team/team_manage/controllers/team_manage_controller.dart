import 'package:frontend_re/domain/models/role_model.dart';
import 'package:frontend_re/domain/models/team_model.dart';
import 'package:frontend_re/domain/services/team_service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';


part 'team_manage_controller.g.dart';

// 角色控制器
@riverpod
class RoleController extends _$RoleController {
  @override
  FutureOr<List<RoleModel>> build() async {
    final res = await ref.read(roleServiceProvider.notifier).getRoles(0);
    return res;
  }

  /// 加载角色列表，可选传入ID参数，当id为0时，获取所有角色
  Future<void> loadRoles({int? id}) async {
    state = const AsyncValue.loading();
    try {
      final roles = await ref.read(roleServiceProvider.notifier).getRoles(id ?? 0);
      state = AsyncValue.data(roles);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// 添加角色
  Future<void> addRole(String name, String permissions, bool secure) async {
    await ref.read(roleServiceProvider.notifier).createRole(CreateRoleRequest(name: name, permissions: permissions, secure: secure));
  }

  /// 更新角色
  Future<void> updateRole(int id, String name, String permissions, bool secure) async {
    await ref.read(roleServiceProvider.notifier).updateRole(UpdateRoleRequest(id: id, name: name, permissions: permissions, secure: secure));
  }

  /// 删除角色
  Future<void> deleteRole(int id) async {
    await ref.read(roleServiceProvider.notifier).deleteRole(id);
  }
}

// 团队成员控制器
@riverpod
class TeamController extends _$TeamController {
  @override
  FutureOr<TeamModelResponse> build() async {
    return await ref.read(teamServiceProvider.notifier).getUsers(const TeamRequest(limit: 10, offset: 0));
  }

  /// 获取团队成员列表
  Future<void> getUsers({
    required int limit,
    required int offset,
    String? username,
    int? roleId,
  }) async {
    state = const AsyncValue.loading();
    try {
      final team = await ref.read(teamServiceProvider.notifier).getUsers(
        TeamRequest(limit: limit, offset: offset, username: username, roleId: roleId),
      );
      state = AsyncValue.data(team);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// 添加团队成员
  Future<String> addUsers(List<AddTeamUserRequest> users) async {
    try {
      final res = await ref.read(teamServiceProvider.notifier).addUsers(users);

      final currentTeam = state.value;
      if (currentTeam != null) {
        await getUsers(limit: currentTeam.total, offset: 0);
      }
      return res;
    } catch (error) {
      rethrow;
    }
  }

  /// 更新团队成员
  Future<void> updateUsers(List<UpdateTeamUserRequest> users, int pageSize, int offset) async {
    try {
      await ref.read(teamServiceProvider.notifier).updateUsers(users);
      final currentTeam = state.value;
      if (currentTeam != null) {
        getUsers(limit: pageSize, offset: offset);
      }
    } catch (error) {
      rethrow;
    }
  }
}
