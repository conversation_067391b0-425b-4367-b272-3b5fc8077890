import 'package:flutter/material.dart';
import 'package:frontend_re/features/team/team_manage/controllers/team_manage_controller.dart';
import 'package:frontend_re/domain/models/role_model.dart';
import 'package:frontend_re/features/team/team_manage/views/components/role_add_dialog.dart';
import 'package:frontend_re/core/utils/ui/snack_bar_util.dart';
import 'package:frontend_re/widgets/confirm_dialog.dart';
import 'package:frontend_re/widgets/custom_edit_button.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class RoleTable extends ConsumerWidget {
  const RoleTable({super.key, required this.data});

  final List<RoleModel> data;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      children: [
        ...data.map((e) => _buildRoleItem(e, context, ref)),
      ],
    );
  }

  Widget _buildRoleItem(RoleModel role, BuildContext context, WidgetRef ref) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          hoverColor: Theme.of(context).hoverColor,
          borderRadius: BorderRadius.circular(4),
          onTap: () {}, // 可以根据需要添加点击事件
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 4),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(role.name),
                Row(
                  spacing: 6,
                  children: [
                    CustomEditButton(
                      onPressed: () {
                        showDialog(
                          context: context,
                          builder: (context) => RoleAddDialog(role: role),
                        );
                      },
                      iconPath: 'assets/svg/edit.svg',
                      borderRadius: BorderRadius.circular(50),
                    ),
                    CustomEditButton(
                      onPressed: () {
                        showDialog<bool>(
                          context: context,
                          builder: (context) =>
                            ConfirmDialog(
                              content: '确定要删除该角色吗？',
                              onConfirm: () async {
                                try {
                                  // 等待删除操作完成
                                  await ref.read(roleControllerProvider.notifier).deleteRole(role.id);
                                  // 删除成功后刷新角色列表
                                  ref.invalidate(roleControllerProvider);
                                  if (context.mounted) {
                                    SnackBarUtil().showSuccess(context, '删除成功');
                                  }
                                } catch (e) {
                                  if (context.mounted) {
                                    SnackBarUtil().showError(context, '删除失败: ${e.toString()}');
                                  }
                                }
                              },
                            ),
                        );
                      },
                      iconPath: 'assets/svg/delete.svg',
                      borderRadius: BorderRadius.circular(50),
                    ),
                  ],
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
