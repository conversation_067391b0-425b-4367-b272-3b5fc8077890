import 'package:flutter/material.dart';
import 'package:frontend_re/core/utils/ui/snack_bar_util.dart';
import 'package:frontend_re/core/utils/permission_utils.dart';
import 'package:frontend_re/domain/models/role_model.dart';
import 'package:frontend_re/features/team/team_manage/controllers/team_manage_controller.dart';
import 'package:frontend_re/features/team/team_manage/views/components/permission_selector.dart';
import 'package:frontend_re/widgets/custom_text_field.dart';
import 'package:frontend_re/widgets/primary_botton.dart';
import 'package:frontend_re/widgets/resizable_dialog.dart';
import 'package:frontend_re/widgets/s_switch.dart';
import 'package:frontend_re/widgets/secondary_botton.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

class RoleAddDialog extends HookConsumerWidget {
  const RoleAddDialog({super.key, this.role});

  final RoleModel? role;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 创建角色名称输入控制器
    final roleNameController = useTextEditingController();
    // 使用 useState 管理选中的权限集合
    final selectedPermissions = useState<Map<String, bool>>({});
    // 安全登录标识
    final isSecure = useState<bool>(false);

    // 初始化值
    useEffect(() {
      if (role != null) {
        roleNameController.text = role!.name;
        selectedPermissions.value = PermissionUtils.parsePermissions(role!.permissions);
        isSecure.value = role!.secure ?? false;
      }
      return null;
    }, []);



    // 验证表单
    bool isFormValid() {
      return roleNameController.text.trim().isNotEmpty &&
             PermissionUtils.isPermissionValid(selectedPermissions.value);
    }

    // 提交表单
    void submitForm() async {
      if (!isFormValid()) {
        SnackBarUtil().showError(context, '请填写完整的角色信息');
        return;
      }

      try {
        final roleName = roleNameController.text.trim();
        final permissions = PermissionUtils.convertPermissionsToBackendFormat(
          selectedPermissions.value,
        );

        print('permissions: $permissions');
        
        if (role != null) {
          // 更新角色
          await ref.read(roleControllerProvider.notifier).updateRole(
            role!.id,
            roleName,
            permissions,
            isSecure.value,
          );
        } else {
          // 添加角色
          await ref.read(roleControllerProvider.notifier).addRole(
            roleName,
            permissions,
            isSecure.value,
          );
        }

        // 重新加载角色列表
        await ref.read(roleControllerProvider.notifier).loadRoles();
        
        if (context.mounted) {
          Navigator.of(context).pop();
          SnackBarUtil().showSuccess(context, role != null ? '角色更新成功' : '角色添加成功');
        }
      } catch (e) {
        if (context.mounted) {
          SnackBarUtil().showError(context, '操作失败: ${e.toString()}');
        }
      }
    }

    return ResizableDialog(
      initialHeight: 650,
      minHeight: 650,
      child: Column(
        children: [
          // 标题栏
          Container(
            padding: const EdgeInsets.all(24),
            child: Row(
              children: [
                Container(
                  width: 16,
                  height: 16,
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary,
                    borderRadius: BorderRadius.circular(50),
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  role != null ? '编辑角色' : '添加角色',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ],
            ),
          ),
          
          // 内容区域（可滚动）
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 角色名称输入框
                  _buildFormField(
                    context,
                    label: '角色名称',
                    required: true,
                    child: CustomTextField(
                      controller: roleNameController,
                      hint: '请输入角色名称',
                    ),
                  ),
                  const SizedBox(height: 24),
                  
                  // 权限配置
                  _buildFormField(
                    context,
                    label: '权限配置',
                    required: true,
                    child: PermissionSelector(
                      selectedPermissions: selectedPermissions.value,
                      onPermissionsChanged: (newPermissions) {
                        selectedPermissions.value = newPermissions;
                      },
                    ),
                  ),
                  const SizedBox(height: 24),
                  
                  // 安全登录设置
                  _buildFormField(
                    context,
                    label: '安全设置',
                    required: false,
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surface,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                        ),
                      ),
                      child: Row(
                        children: [
                          Transform.scale(
                            scale: 0.8,
                            child: SSwitch(
                              value: isSecure.value,
                              onChanged: (value) => null,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  '启用安全登录',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                    color: Theme.of(context).colorScheme.onSurface,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  '启用后该角色需要更严格的身份验证',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),
                ],
              ),
            ),
          ),
          
          // 底部按钮栏
          Container(
            padding: const EdgeInsets.all(24),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                SecondaryButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('取消'),
                ),
                const SizedBox(width: 12),
                PrimaryButton(
                  onPressed: submitForm,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        role != null ? Icons.save : Icons.add,
                        size: 18,
                        color: Theme.of(context).colorScheme.onPrimary,
                      ),
                      const SizedBox(width: 8),
                      Text(role != null ? '保存' : '添加'),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFormField(
    BuildContext context, {
    required String label,
    required bool required,
    required Widget child,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            if (required) ...[
              const SizedBox(width: 4),
              Text(
                '*',
                style: TextStyle(
                  fontSize: 14,
                  color: Theme.of(context).colorScheme.error,
                ),
              ),
            ],
          ],
        ),
        const SizedBox(height: 8),
        child,
      ],
    );
  }
}
