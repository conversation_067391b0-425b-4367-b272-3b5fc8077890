import 'package:flutter/material.dart';
import 'package:dio/dio.dart';
import 'package:frontend_re/repositories/team_repository.dart';
import 'package:frontend_re/domain/models/team_model.dart';
import 'package:frontend_re/features/team/team_manage/controllers/team_manage_controller.dart';
import 'package:frontend_re/core/utils/ui/snack_bar_util.dart';
import 'package:frontend_re/widgets/custom_dropdown_menu.dart';
import 'package:frontend_re/widgets/custom_text_field.dart';
import 'package:frontend_re/widgets/primary_botton.dart';
import 'package:frontend_re/widgets/s_switch.dart';
import 'package:frontend_re/widgets/secondary_botton.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';


class UserAddDialog extends HookConsumerWidget {
  const UserAddDialog({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 监听角色列表状态
    final rolesState = ref.watch(roleControllerProvider);

    // 用户名和密码的输入控制器
    final usernameController = useTextEditingController();
    final passwordController = useTextEditingController();

    // 当前选中的角色 ID
    final selectedRoleId = useState<int?>(null);
    final selectedRoleName = useState<String?>(null);
    final isActive = useState<bool>(true);

    // 引入 TeamController
    final teamController = ref.read(teamControllerProvider.notifier);

    return AlertDialog(
      backgroundColor: Theme.of(context).colorScheme.surfaceContainer,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      title: Row(
        children: [
          Container(
            width: 16,
            height: 16,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
              borderRadius: BorderRadius.circular(50),
            ),
          ),
          const SizedBox(width: 12),
          const Text('添加员工', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),),
        ],
      ),
      content: SizedBox(
        width: 400,
        child: Column(
          spacing: 16,
          mainAxisSize: MainAxisSize.min,
          children: [
            // 用户名输入框
            CustomTextField(
              controller: usernameController,
              hint: '请输入用户名',
            ),
            // 密码输入框
            CustomTextField(
              controller: passwordController,
              hint: '请输入密码',
              obscureText: true,
            ),
            // 是否启用的开关
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(12),
                // border: Border.all(
                //   color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                // ),
              ),
              child: Row(
                children: [
                  Transform.scale(
                    scale: 0.8, // 缩小到80%大小
                    child: SSwitch(
                      value: isActive.value,
                      onChanged: (value) => isActive.value = value,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '启用员工',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Theme.of(context).colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '新员工默认启用，可后续修改状态',
                          style: TextStyle(
                            fontSize: 12,
                            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            rolesState.when(
              data: (roles) => CustomDropdownMenu(
                height: 40,
                hintText: '请选择部门',
                items: roles.map((role) => {'name': role.name}).toList(),
                value: selectedRoleName.value,
                onChanged: (value) {
                  selectedRoleName.value = value;
                  selectedRoleId.value = roles.firstWhere((role) => role.name == value).id;
                }, 
              ),
              loading: () => const Center(
                child: CircularProgressIndicator(),
              ),
              error: (error, stackTrace) => Center(
                child: Text('加载角色失败: $error'),
              ),
            )
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
          },
          child: const Text('取消'),
        ),
        ElevatedButton(
          onPressed: () async {
            final username = usernameController.text.trim();
            final password = passwordController.text.trim();

            if (username.isEmpty || password.isEmpty || selectedRoleId.value == null) {
              SnackBarUtil().showInfo(context, '请填写所有信息');
              return;
            }

            try {
              // 调用添加用户接口
              await teamController.addUsers([
                AddTeamUserRequest(
                  userName: username,
                  password: password,
                  roleId: selectedRoleId.value!,
                  isActive: isActive.value,
                ),
              ]);
              // 添加成功后刷新员工列表
              await teamController.getUsers(limit: 10, offset: 0);
              if (context.mounted) {
                SnackBarUtil().showSuccess(context, '员工添加成功');
              }
            } catch (error) {
              if (context.mounted) {
                // 处理 DioException 错误，提取真正的错误信息
                String errorMessage = '系统错误';
                if (error is DioException && error.error != null) {
                  errorMessage = error.error.toString();
                } else if (error is Exception) {
                  errorMessage = error.toString().replaceFirst('Exception: ', '');
                } else {
                  errorMessage = error.toString();
                }
                SnackBarUtil().showError(context, errorMessage);
              }
            }
          },
          child: const Text('添加'),
        ),
      ],
    );
  }
}

class EditUserDialog extends HookConsumerWidget {
  final String username;
  final int id;
  final bool isActive;
  final bool isTwoFactorEnabled;

  const EditUserDialog({
    super.key,
    required this.username,
    required this.id,
    required this.isActive,
    required this.isTwoFactorEnabled,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 监听角色列表状态
    final rolesState = ref.watch(roleControllerProvider);

    // 输入控制器
    final usernameController = useTextEditingController(text: username);
    final passwordController = useTextEditingController();

    // 当前选中的角色 ID
    final selectedRoleId = useState<int?>(null);

    // 当前启用状态
    final isUserActive = useState<bool>(isActive);
    useState<bool>(isTwoFactorEnabled);

    return AlertDialog(
      backgroundColor: Theme.of(context).colorScheme.surfaceContainer,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      title: const Text('编辑用户',style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),),
      content: SizedBox(
        width: 400,
        child: Column(
          spacing: 16,
          mainAxisSize: MainAxisSize.min,
          children: [
            // 用户名输入框
            CustomTextField(
              controller: usernameController,
              hint: '请输入用户名',
            ),
            // 密码输入框
            CustomTextField(
              controller: passwordController,
              hint: '留空则不修改密码',
              obscureText: true,
            ),
            // 角色下拉选择框
            rolesState.when(
              data: (roles) {
                // 添加"不修改"选项到角色列表
                final rolesWithDefault = [
                  {'name': '不修改'},
                  ...roles.map((role) => {'name': role.name}).toList(),
                ];
                
                return CustomDropdownMenu(
                  height: 40,
                  hintText: '请选择部门（不修改则保持为空）',
                  items: rolesWithDefault,
                  value: selectedRoleId.value != null 
                      ? roles.firstWhere((role) => role.id == selectedRoleId.value).name 
                      : null,
                  onChanged: (value) {
                    if (value == '不修改') {
                      selectedRoleId.value = null;
                    } else {
                      selectedRoleId.value = roles.firstWhere((role) => role.name == value).id;
                    }
                  },
                );
              },
              loading: () => const Center(
                child: CircularProgressIndicator(),
              ),
              error: (error, stackTrace) => Center(
                child: Text('加载角色失败: $error'),
              ),
            ),
            // 是否启用的开关
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(12),
                // border: Border.all(
                //   color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                // ),
              ),
              child: Row(
                children: [
                  Transform.scale(
                    scale: 0.8,
                    child: SSwitch(
                      value: isUserActive.value,
                      onChanged: (value) => isUserActive.value = value,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '启用用户',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Theme.of(context).colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '控制用户是否可以登录系统',
                          style: TextStyle(
                            fontSize: 12,
                            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      actions: [
        SecondaryButton(
          onPressed: () {
            Navigator.of(context).pop();
          },
          child: const Text('取消'),
        ),
        PrimaryButton(
          onPressed: () async {
            final updatedUsername = usernameController.text.trim();
            final updatedPassword = passwordController.text.trim();

            if (updatedUsername.isEmpty) {
              SnackBarUtil().showInfo(context, '用户名不能为空');
              return;
            }

            // 构建请求数据
            final updateRequest = UpdateTeamUserRequest(
              id: id,
              userName: updatedUsername,
              password: updatedPassword.isNotEmpty ? updatedPassword : null,
              roleId: selectedRoleId.value, // 如果未选择则为 null
              isActive: isUserActive.value,
            );

            try {
              await ref.read(teamRepositoryProvider.notifier).updateUsers([updateRequest]);
              if (context.mounted) {
                SnackBarUtil().showSuccess(context, '用户更新成功');
              }
              // 刷新用户列表
              await ref.read(teamControllerProvider.notifier).getUsers(limit: 10, offset: 0);
              if (context.mounted) {
                Navigator.of(context).pop();
              }
            } catch (error) {
              if (context.mounted) {
                // 处理 DioException 错误，提取真正的错误信息
                String errorMessage = '更新用户失败';
                if (error is DioException && error.error != null) {
                  errorMessage = error.error.toString();
                } else if (error is Exception) {
                  errorMessage = error.toString().replaceFirst('Exception: ', '');
                } else {
                  errorMessage = error.toString();
                }
                SnackBarUtil().showError(context, errorMessage);
              }
            }
          },
          child: const Text('确定'),
        ),
      ],
    );
  }
}

class ConfirmDeleteUserDialog extends HookConsumerWidget {
  final String username;
  final int id;

  const ConfirmDeleteUserDialog({
    required this.username,
    required this.id,
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return AlertDialog(
      title: const Text('确认删除'),
      content: Text('确定要删除用户 "$username" 吗？'),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
          },
          child: const Text('取消'),
        ),
        ElevatedButton(
          onPressed: () async {
            try {
              await ref.read(teamRepositoryProvider.notifier).deleteUsers([id]);
              if (context.mounted) {
                SnackBarUtil().showSuccess(context, '用户删除成功');
              }
              // 刷新用户列表
              await ref
                  .read(teamControllerProvider.notifier)
                  .getUsers(limit: 10, offset: 0);
            } catch (error) {
              if (context.mounted) {
                // 处理 DioException 错误，提取真正的错误信息
                String errorMessage = '删除用户失败';
                if (error is DioException && error.error != null) {
                  errorMessage = error.error.toString();
                } else if (error is Exception) {
                  errorMessage = error.toString().replaceFirst('Exception: ', '');
                } else {
                  errorMessage = error.toString();
                }
                SnackBarUtil().showError(context, errorMessage);
              }
            }
            if (context.mounted) {
              Navigator.of(context).pop();
            }
          },
          child: const Text('删除'),
        ),
      ],
    );
  }
}
