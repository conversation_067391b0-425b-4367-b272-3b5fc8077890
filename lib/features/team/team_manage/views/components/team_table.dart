import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:frontend_re/features/team/team_manage/controllers/team_manage_controller.dart';
import 'package:frontend_re/domain/models/team_model.dart';
import 'package:frontend_re/features/team/team_manage/views/components/user_add_dialog.dart';
import 'package:frontend_re/repositories/team_repository.dart';
import 'package:frontend_re/core/utils/ui/snack_bar_util.dart';
import 'package:frontend_re/widgets/confirm_dialog.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:frontend_re/widgets/data_table_widget.dart';

final pageSizeProvider = StateProvider<int>((ref) => 10);
final offsetProvider = StateProvider<int>((ref) => 0);

class TeamTable extends ConsumerWidget {
  const TeamTable({super.key, required this.data});

  final TeamModelResponse data;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: LayoutBuilder(
        builder: (context, constraints) {
          return Column(
            children: [
              Expanded(
                child: DataTableWidget(
                  headingTextStyle: const TextStyle(fontSize: 14, color: Color(0xFF333333)),
                  dataTextStyle: const TextStyle(fontSize: 14, color: Color(0xFF8D8E93)),
                  minWidth: 100,
                  showCheckbox: false,
                  columnSpacing: 0,
                  enablePagination: true,
                  totalCount: data.total,
                  pageSize: 10,
                  currentPage: (ref.read(offsetProvider) / ref.read(pageSizeProvider)).floor() + 1,
                  onPageChanged: (page, pageSize) {
                    final offset = (page - 1) * pageSize;
                    ref.read(teamControllerProvider.notifier).getUsers(limit: pageSize, offset: offset);
                    ref.read(pageSizeProvider.notifier).state = pageSize;
                    ref.read(offsetProvider.notifier).state = offset;
                  },
                  noDataImagePath: 'assets/images/proxy/NoData.png',
                  noDataText: '暂无数据',
                  columns: const [
                    DataColumn2(label: Text('用户名'), size: ColumnSize.M),
                    DataColumn2(label: Text('角色'), size: ColumnSize.L),
                    DataColumn2(label: Text('状态'), size: ColumnSize.M),
                    DataColumn2(label: Text('操作'), size: ColumnSize.S, fixedWidth: 60),
                  ],
                  data: data.users,
                  rowBuilder: (user, index, isHovered) {
                    final member = user;
                    final pageSize = ref.read(pageSizeProvider);
                    final offset = ref.read(offsetProvider);
                    
                    return DataRow2(cells: [
                      DataCell(Text(member.username)),
                      DataCell(Text(member.roleName)),
                      DataCell(_buildStatus(member, context, ref, pageSize, offset)),
                      DataCell(_buildEditButton(context, member, ref)),
                    ]);
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildStatus(TeamUserResponse member, BuildContext context, WidgetRef ref, int pageSize, int offset) {
    return HookConsumer(
      builder: (context, ref, child) {
        final isHovered = useState(false);
        
        return GestureDetector(
          onTap: () {
            showDialog(
              context: context,
              builder: (context) => ConfirmDialog(
                content: '确定要切换状态吗？',
                onConfirm: () {
                  ref.read(teamControllerProvider.notifier).updateUsers(
                    [UpdateTeamUserRequest(id: member.id, isActive: !member.isActive)],
                    pageSize,
                    offset);
                },
              ),
            );
          },
          child: MouseRegion(
            onEnter: (_) => isHovered.value = true,
            onExit: (_) => isHovered.value = false,
            cursor: SystemMouseCursors.click,
            child: FittedBox(
              fit: BoxFit.none,
              child: ConstrainedBox(
                constraints: const BoxConstraints(
                  minWidth: 70,
                  maxWidth: 70,
                ),
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: member.isActive
                        ? Theme.of(context).colorScheme.primary.withValues(alpha: isHovered.value ? 0.2 : 0.1)
                        : Colors.grey.withValues(alpha: isHovered.value ? 0.2 : 0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: isHovered.value
                        ? Border.all(
                            color: member.isActive
                                ? Theme.of(context).colorScheme.primary
                                : Colors.grey,
                            width: 1,
                          )
                        : null,
                  ),
                  child: Text(
                    member.isActive ? '已激活' : '已关闭',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: member.isActive
                          ? Theme.of(context).colorScheme.primary
                          : Colors.grey,
                      fontSize: 12,
                      fontWeight: isHovered.value ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildEditButton(BuildContext context, TeamUserResponse member, WidgetRef ref) {
    return DropdownButtonHideUnderline(
      child: DropdownButton2<String>(
        customButton: Padding(
          padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 4.0),
          child: SvgPicture.asset(
            'assets/svg/More.svg',
            width: 5,
            height: 5,
            colorFilter: ColorFilter.mode(Theme.of(context).colorScheme.onTertiary, BlendMode.srcIn),
          ),
        ),
        items: const [
          DropdownMenuItem<String>(
            value: 'edit',
            child: Text('编辑', style: TextStyle(fontSize: 12),),
          ),
          DropdownMenuItem<String>(
            value: 'delete',
            child: Text('删除', style: TextStyle(fontSize: 12),),
          ),
        ],
        onChanged: (String? value) {
          if (value == 'delete') {
            showDialog(
              context: context,
              builder: (BuildContext dialogContext) {
                return ConfirmDialog(
                  title: '确认删除',
                  content: '确定要删除用户 "${member.username}" 吗？',
                  onConfirm: () async {
                    try {
                      await ref.read(teamRepositoryProvider.notifier).deleteUsers([member.id]);
                      if (context.mounted) {
                        SnackBarUtil().showSuccess(context, '删除用户成功');
                      }
                      final pageSize = ref.read(pageSizeProvider);
                      final offset = ref.read(offsetProvider);
                      await ref.read(teamControllerProvider.notifier).getUsers(limit: pageSize, offset: offset);
                    } catch (e) {
                      if (context.mounted) {
                        SnackBarUtil().showError(context, '删除用户失败: $e');
                      }
                    }
                  },
                );
              },
            );
          } else if (value == 'edit') {
            showDialog(
              context: context,
              builder: (BuildContext dialogContext) => EditUserDialog(username: member.username, id: member.id, isActive: member.isActive, isTwoFactorEnabled: member.isTwoFactorEnabled),
            );
          }
        },
        dropdownStyleData: DropdownStyleData(
          width: 60,
          padding: EdgeInsets.zero,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              )
            ]
          ),
          offset: const Offset(0, 8),
        ),
        menuItemStyleData: const MenuItemStyleData(
          height: 40,
        ),
      ),
    );
  }
}

