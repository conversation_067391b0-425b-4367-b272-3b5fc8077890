import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:frontend_re/core/constants/permission_constants.dart';
import 'package:frontend_re/core/utils/permission_utils.dart';

class PermissionSelector extends HookWidget {
  const PermissionSelector({
    super.key,
    required this.selectedPermissions,
    required this.onPermissionsChanged,
  });

  final Map<String, bool> selectedPermissions;
  final ValueChanged<Map<String, bool>> onPermissionsChanged;

  @override
  Widget build(BuildContext context) {
    // 当前选中的模块
    final selectedModule = useState<String?>(null);
    
    // 如果没有选中模块，默认选择第一个
    useEffect(() {
      if (selectedModule.value == null && PermissionConstants.permissionModules.isNotEmpty) {
        selectedModule.value = PermissionConstants.permissionModules.keys.first;
      }
      return null;
    }, []);

    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.0),
        ),
      ),
      child: Row(
        children: [
          // 左侧模块列表
          Expanded(
            flex: 2,
            child: _buildModuleList(context, selectedModule),
          ),
          
          // 分割线
          Container(
            width: 1,
            height: 120,
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
          ),
          
          // 右侧权限列表
          Expanded(
            flex: 3,
            child: _buildPermissionList(context, selectedModule.value),
          ),
        ],
      ),
    );
  }

  /// 构建左侧模块列表
  Widget _buildModuleList(BuildContext context, ValueNotifier<String?> selectedModule) {
    return Container(
      color: Theme.of(context).colorScheme.surfaceContainer.withValues(alpha: 0.3),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [          
          // 模块列表
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              itemCount: PermissionConstants.permissionModules.length,
              itemBuilder: (context, index) {
                final module = PermissionConstants.permissionModules.keys.elementAt(index);
                final moduleData = PermissionConstants.permissionModules[module]!;
                final label = moduleData['label'] as String;
                
                final isSelected = selectedModule.value == module;
                final checkboxState = PermissionUtils.getModuleCheckboxState(module, selectedPermissions);
                
                return Container(
                  margin: const EdgeInsets.symmetric(vertical: 4),
                  decoration: BoxDecoration(
                    color: isSelected 
                        ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.1)
                        : Colors.transparent,
                    borderRadius: BorderRadius.circular(50),
                  ),
                  child: InkWell(
                    borderRadius: BorderRadius.circular(8),
                    // hoverColor: Colors.transparent,
                    onTap: () => selectedModule.value = module,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 0),
                      child: Row(
                        children: [
                          // 模块选择checkbox
                          Transform.scale(
                            scale: 0.8,
                            child: Checkbox(
                              value: checkboxState,
                              tristate: true,
                              onChanged: (value) => _handleModuleToggle(module, value),
                              activeColor: Theme.of(context).colorScheme.primary,
                              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                              side: BorderSide(
                                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.5),
                              ),
                            ),
                          ),

                          const SizedBox(width: 8),
                          
                          // 模块名称
                          Expanded(
                            child: Text(
                              label,
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                                color: isSelected 
                                    ? Theme.of(context).colorScheme.primary
                                    : const Color(0xFF999999),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// 构建右侧权限列表
  Widget _buildPermissionList(BuildContext context, String? selectedModule) {
    if (selectedModule == null) {
      return Center(
        child: Text(
          '请选择左侧功能模块',
          style: TextStyle(
            fontSize: 14,
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
          ),
        ),
      );
    }

    final moduleData = PermissionConstants.permissionModules[selectedModule]!;
    // final label = moduleData['label'] as String;
    // final icon = moduleData['icon'] as IconData;
    final permissions = moduleData['permissions'] as List<String>;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 权限列表
        Expanded(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: permissions.isEmpty 
                ? Center(
                    child: Text(
                      '该模块暂无可配置权限',
                      style: TextStyle(
                        fontSize: 14,
                        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                    ),
                  )
                                : SingleChildScrollView(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: Wrap(
                      spacing: 8, // 水平间距
                      runSpacing: 8, // 垂直间距
                      children: permissions.map((permission) {
                        final isSelected = PermissionUtils.isSpecificPermissionSelected(
                          selectedModule,
                          permission,
                          selectedPermissions,
                        );
                        final permissionLabel = PermissionConstants.permissionLabels[permission] ?? permission;
                        
                        return InkWell(
                          borderRadius: BorderRadius.circular(8),
                          onTap: () => _handleSpecificPermissionToggle(selectedModule, permission, !isSelected),
                          child: Container(
                            height: 32, // 固定高度
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 0),
                            decoration: BoxDecoration(
                              color: isSelected
                                  ? Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.6)
                                  : Theme.of(context).colorScheme.surfaceContainer.withValues(alpha: 0.3),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: isSelected
                                    ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.5)
                                    : Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min, // 重要：让Row宽度自适应内容
                              children: [
                                Transform.scale(
                                  scale: 0.8,
                                  child: Checkbox(
                                    value: isSelected,
                                    onChanged: (value) => _handleSpecificPermissionToggle(
                                      selectedModule, 
                                      permission, 
                                      value ?? false
                                    ),
                                    activeColor: Theme.of(context).colorScheme.primary,
                                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                                    side: BorderSide(
                                      color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.5),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 4),
                                Flexible(
                                  child: Text(
                                    permissionLabel,
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: isSelected
                                          ? Theme.of(context).colorScheme.primary
                                          : const Color(0xFF999999),
                                      fontWeight: isSelected ? FontWeight.w500 : FontWeight.w400,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ),
          ),
        ),
        
        const SizedBox(height: 16),
      ],
    );
  }

  /// 处理模块权限切换
  void _handleModuleToggle(String module, bool? value) {
    final currentState = PermissionUtils.getModuleCheckboxState(module, selectedPermissions);
    bool newValue;
    
    if (currentState == null) {
      // 当前是部分选中，点击后全选
      newValue = true;
    } else if (currentState == true) {
      // 当前是全选，点击后取消全选
      newValue = false;
    } else {
      // 当前是未选中，点击后全选
      newValue = true;
    }
    
    final newPermissions = PermissionUtils.toggleModulePermissions(
      module,
      newValue,
      selectedPermissions,
    );
    onPermissionsChanged(newPermissions);
  }

  /// 处理具体权限切换
  void _handleSpecificPermissionToggle(String module, String permission, bool value) {
    var newPermissions = PermissionUtils.toggleSpecificPermission(
      module,
      permission,
      value,
      selectedPermissions,
    );
    
    // 检查是否所有权限都被选中，如果是则转换为模块全选
    if (value) {
      final modulePermissions = PermissionConstants.permissionModules[module]?['permissions'] as List<String>?;
      if (modulePermissions != null) {
        final allSelected = modulePermissions.every((p) => newPermissions.containsKey('$module:$p'));
        if (allSelected) {
          // 转换为模块全选
          newPermissions[module] = true;
          // 移除具体权限
          for (final p in modulePermissions) {
            newPermissions.remove('$module:$p');
          }
        }
      }
    }
    
    onPermissionsChanged(newPermissions);
  }
} 
