import 'package:flutter/material.dart';
import 'package:frontend_re/features/team/team_manage/controllers/team_manage_controller.dart';
import 'package:frontend_re/features/team/team_manage/views/components/role_add_dialog.dart';
import 'package:frontend_re/features/team/team_manage/views/components/role_table.dart';
import 'package:frontend_re/features/team/team_manage/views/components/team_table.dart';
import 'package:frontend_re/features/team/team_manage/views/components/user_add_dialog.dart';
import 'package:frontend_re/widgets/custom_border_button.dart';
import 'package:frontend_re/widgets/custom_solid_button.dart';
import 'package:frontend_re/widgets/error_window.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';


/// 团队协作主页面，用于嵌套显示子路由内容
class TeamMembersPage extends StatelessWidget {

  const TeamMembersPage({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Row(
        spacing: 16,
        children: [
          // 团队成员左侧
          SizedBox(
            width: 320,
            child: Column(
              spacing: 16,
              children: [
                // 团队成员头部
                Row(
                  children: [
                    CustomSolidButton(
                      width: 320,
                      isCenter: true,
                      iconBackgroundColor: Colors.transparent,
                      backgroundColor: Colors.white,
                      iconColor: Colors.black,
                      textColor: Colors.black,
                      iconPath: 'assets/svg/add.svg',
                      text: '添加员工',
                      onPressed: () {
                        showDialog(
                          context: context,
                          builder: (context) => const UserAddDialog(),
                        );
                      },
                    ),
                  ],
                ),
                Expanded(
                  child: Container(
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(20),
                        topRight: Radius.circular(20),
                      ),
                    ),
                    child: Column(
                      children: [
                        // 新建部门按钮
                        Padding(
                          padding: const EdgeInsets.all(16),
                          child: CustomSolidButton(
                            width: 320,
                            isCenter: true,
                            iconBackgroundColor: Colors.transparent,
                            iconColor: Colors.white,
                            textColor: Colors.white,
                            iconPath: 'assets/svg/add.svg',
                            text: '新建角色',
                            onPressed: () {
                              showDialog(
                                context: context,
                                barrierDismissible: false,
                                builder: (context) => const RoleAddDialog(),
                              );
                            },
                          ),
                        ),
                        // 部门列表 标题
                        Column(
                          children: [
                            const Padding(
                              padding: EdgeInsets.only(left: 16, right: 0, top: 8, bottom: 8),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Text('角色列表'),
                                ],
                              ),
                            ),
                            // 分割线
                            Container(
                              height: 1,
                              margin: const EdgeInsets.symmetric(horizontal: 16),
                              color: Colors.grey.withValues(alpha: 0.3),
                            ),
                          ],
                        ),
                        SingleChildScrollView(
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            child: Consumer(builder: (context, ref, child) {
                              final teamController = ref.watch(roleControllerProvider);
                            
                              return teamController.when(
                                data: (data) => RoleTable(data: data),
                                error: (error, stackTrace) => Text(error.toString()),
                                loading: () => const CircularProgressIndicator(),
                              );
                            }),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          // 团队成员右侧
          Expanded(
            child: Column(
              spacing: 16,
              children: [
                // 搜索框和按钮
                Row(
                  spacing: 16,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Container(
                        height: 42,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(50),
                        ),
                        child: const Center(child: Text('')),
                      ),
                    ),
                    CustomBorderButton(
                      width: 44,
                      height: 42,
                      borderColor: const Color(0xFFFFFFFF),
                      iconPath: 'assets/svg/delete.svg',
                      backgroundColor: const Color(0xFFFFFFFF),
                      iconColor: const Color(0xFF8D8E93),
                      horizontalPadding: 12,
                      verticalPadding: 12,
                      iconSize: 18,
                      onPressed: () {
                        print('快捷创建');
                      },
                    ),
                  ],
                ),
                // 成员表格
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Consumer(builder: (context, ref, child) {
                      final teamController = ref.watch(teamControllerProvider);

                      return teamController.when(
                        data: (data) => TeamTable(data: data),
                        error: (error, stackTrace) => ErrorWindow(error: error.toString()),
                        loading: () => const Center(
                          child: CircularProgressIndicator(),
                        ),
                      );
                    }),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
