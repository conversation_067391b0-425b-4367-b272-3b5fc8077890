import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'package:vector_math/vector_math_64.dart' hide Colors;

// 组件类型枚举
enum ComponentType {
  button,
  text,
  image,
  container,
  textField,
}

// 可拖拽组件数据类
class DraggableComponent {
  final String id;
  final ComponentType type;
  Offset position;

  DraggableComponent({
    required this.id,
    required this.type,
    required this.position,
  });
}

// 组件连线数据类
class ComponentConnection {
  final String sourceId;
  final String targetId;
  
  ComponentConnection({
    required this.sourceId,
    required this.targetId,
  });
}

// 连线绘制器
class ConnectionPainter extends CustomPainter {
  final List<ComponentConnection> connections;
  final List<DraggableComponent> components;
  final DraggableComponent? selectedSource;
  final Offset? tempEnd;
  
  ConnectionPainter({
    required this.connections,
    required this.components,
    this.selectedSource,
    this.tempEnd,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.blue
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;
      
    final arrowPaint = Paint()
      ..color = Colors.blue
      ..style = PaintingStyle.fill;
    
    // 绘制现有连线
    for (final connection in connections) {
      if (components.isNotEmpty) {
        final sourceComponent = components.firstWhere(
          (comp) => comp.id == connection.sourceId,
          orElse: () => components.first,
        );
        final targetComponent = components.firstWhere(
          (comp) => comp.id == connection.targetId,
          orElse: () => components.first,
        );
        
        if (components.contains(sourceComponent) && components.contains(targetComponent)) {
          final startPoint = Offset(
            sourceComponent.position.dx + 60, // 组件中心
            sourceComponent.position.dy + 30,
          );
          final endPoint = Offset(
            targetComponent.position.dx + 60,
            targetComponent.position.dy + 30,
          );
          
          _drawArrowLine(canvas, startPoint, endPoint, paint, arrowPaint);
        }
      }
    }
    
    // 绘制临时连线（拖拽中）
    if (selectedSource != null && tempEnd != null) {
      final tempPaint = Paint()
        ..color = Colors.red.withOpacity(0.7)
        ..strokeWidth = 2
        ..style = PaintingStyle.stroke;
        
      final tempArrowPaint = Paint()
        ..color = Colors.red.withOpacity(0.7)
        ..style = PaintingStyle.fill;
        
      final startPoint = Offset(
        selectedSource!.position.dx + 60,
        selectedSource!.position.dy + 30,
      );
      
      _drawArrowLine(canvas, startPoint, tempEnd!, tempPaint, tempArrowPaint);
    }
  }
  
  // 绘制带箭头的线
  void _drawArrowLine(Canvas canvas, Offset start, Offset end, Paint linePaint, Paint arrowPaint) {
    // 绘制线条
    canvas.drawLine(start, end, linePaint);
    
    // 计算箭头
    final direction = (end - start).direction;
    final arrowLength = 12.0;
    final arrowAngle = 0.5; // 箭头角度
    
    final arrowPoint1 = Offset(
      end.dx - arrowLength * math.cos(direction - arrowAngle),
      end.dy - arrowLength * math.sin(direction - arrowAngle),
    );
    
    final arrowPoint2 = Offset(
      end.dx - arrowLength * math.cos(direction + arrowAngle),
      end.dy - arrowLength * math.sin(direction + arrowAngle),
    );
    
    // 绘制箭头
    final arrowPath = Path()
      ..moveTo(end.dx, end.dy)
      ..lineTo(arrowPoint1.dx, arrowPoint1.dy)
      ..lineTo(arrowPoint2.dx, arrowPoint2.dy)
      ..close();
      
    canvas.drawPath(arrowPath, arrowPaint);
  }
  
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

// 网格背景绘制器
class GridPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.grey.withOpacity(0.2)
      ..strokeWidth = 1;

    const gridSize = 20.0;

    // 绘制垂直线
    for (double x = 0; x < size.width; x += gridSize) {
      canvas.drawLine(Offset(x, 0), Offset(x, size.height), paint);
    }

    // 绘制水平线
    for (double y = 0; y < size.height; y += gridSize) {
      canvas.drawLine(Offset(0, y), Offset(size.width, y), paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class LibraryPage extends StatefulWidget {
  const LibraryPage({super.key});

  @override
  State<LibraryPage> createState() => _LibraryPageState();
}

class _LibraryPageState extends State<LibraryPage> {
  // 画布上的组件列表
  List<DraggableComponent> canvasComponents = [];
  
  // 连线列表
  List<ComponentConnection> connections = [];
  
  // 组件计数器，用于生成唯一ID
  int componentCounter = 0;
  
  // 连线模式状态
  bool isConnectionMode = false;
  DraggableComponent? selectedSourceComponent;
  
  // 临时连线（拖拽中的连线）
  Offset? tempConnectionEnd;
  
  // 画布变换控制器
  final TransformationController _transformationController = TransformationController();
  
  @override
  void dispose() {
    _transformationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      body: Row(
        children: [
          // 左侧组件库
          // _buildComponentLibrary(),
          // 右侧画布
          // _buildCanvas(),
        ],
      ),
    );
  }

  // 构建左侧组件库
  Widget _buildComponentLibrary() {
    return Container(
      width: 250,
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(2, 0),
          ),
        ],
      ),
      child: Column(
        children: [
          // 标题栏
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              color: Colors.blue,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: const Row(
              children: [
                Icon(Icons.widgets, color: Colors.white),
                SizedBox(width: 8),
                Text(
                  '组件库',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          // 连线模式切换
          Container(
            padding: const EdgeInsets.all(12),
            child: Row(
              children: [
                Icon(
                  isConnectionMode ? Icons.polyline : Icons.touch_app,
                  color: isConnectionMode ? Colors.red : Colors.blue,
                ),
                const SizedBox(width: 8),
                Text(
                  isConnectionMode ? '连线模式' : '拖拽模式',
                  style: TextStyle(
                    color: isConnectionMode ? Colors.red : Colors.blue,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Switch(
                  value: isConnectionMode,
                  onChanged: (value) {
                    setState(() {
                      isConnectionMode = value;
                      selectedSourceComponent = null;
                      tempConnectionEnd = null;
                    });
                  },
                  activeColor: Colors.red,
                ),
              ],
            ),
          ),
          const Divider(),
          // 组件列表
          Expanded(
            child: ListView(
              padding: const EdgeInsets.all(16),
              children: [
                _buildLibraryComponent(ComponentType.button, '按钮', Colors.blue),
                const SizedBox(height: 12),
                _buildLibraryComponent(ComponentType.text, '文本', Colors.green),
                const SizedBox(height: 12),
                _buildLibraryComponent(ComponentType.image, '图像', Colors.orange),
                const SizedBox(height: 12),
                _buildLibraryComponent(ComponentType.container, '容器', Colors.purple),
                const SizedBox(height: 12),
                _buildLibraryComponent(ComponentType.textField, '输入框', Colors.teal),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 构建组件库中的单个组件
  Widget _buildLibraryComponent(ComponentType type, String name, Color color) {
    return Draggable<ComponentType>(
      data: type,
      feedback: Material(
        elevation: 6,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: color.withOpacity(0.8),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            name,
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
      childWhenDragging: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color.withOpacity(0.3),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(name),
      ),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          border: Border.all(color: color),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(
              _getComponentIcon(type),
              color: color,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              name,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 获取组件图标
  IconData _getComponentIcon(ComponentType type) {
    switch (type) {
      case ComponentType.button:
        return Icons.smart_button;
      case ComponentType.text:
        return Icons.text_fields;
      case ComponentType.image:
        return Icons.image;
      case ComponentType.container:
        return Icons.crop_square;
      case ComponentType.textField:
        return Icons.input;
    }
  }

  // 构建右侧画布
  Widget _buildCanvas() {
    return Expanded(
      child: Container(
        margin: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.2),
              spreadRadius: 1,
              blurRadius: 5,
            ),
          ],
        ),
        child: Column(
          children: [
            // 画布标题栏
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: const BoxDecoration(
                color: Colors.grey,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Row(
                children: [
                  const Text(
                    '设计画布',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  // 缩放控制按钮
                  Row(
                    children: [
                      IconButton(
                        icon: const Icon(Icons.zoom_in),
                        onPressed: () {
                          final matrix = Matrix4.copy(_transformationController.value);
                          matrix.scale(1.2);
                          _transformationController.value = matrix;
                        },
                        tooltip: '放大',
                        iconSize: 20,
                      ),
                      IconButton(
                        icon: const Icon(Icons.zoom_out),
                        onPressed: () {
                          final matrix = Matrix4.copy(_transformationController.value);
                          matrix.scale(0.8);
                          _transformationController.value = matrix;
                        },
                        tooltip: '缩小',
                        iconSize: 20,
                      ),
                      IconButton(
                        icon: const Icon(Icons.center_focus_strong),
                        onPressed: () {
                          _transformationController.value = Matrix4.identity();
                        },
                        tooltip: '重置视图',
                        iconSize: 20,
                      ),
                      const SizedBox(width: 8),
                    ],
                  ),
                  // 清空连线按钮
                  IconButton(
                    icon: const Icon(Icons.timeline),
                    onPressed: () {
                      setState(() {
                        connections.clear();
                      });
                    },
                    tooltip: '清空连线',
                  ),
                  IconButton(
                    icon: const Icon(Icons.clear_all),
                    onPressed: () {
                      setState(() {
                        canvasComponents.clear();
                        connections.clear();
                      });
                    },
                    tooltip: '清空画布',
                  ),
                ],
              ),
            ),
            // 画布区域
            Expanded(
              child: Builder(
                builder: (canvasContext) {
                  return DragTarget<ComponentType>(
                    onAcceptWithDetails: (DragTargetDetails<ComponentType> details) {
                      if (!isConnectionMode) {
                        setState(() {
                          // 获取画布区域的RenderBox来计算准确位置
                          final RenderBox? canvasRenderBox = canvasContext.findRenderObject() as RenderBox?;
                          Offset dropPosition = const Offset(100, 100); // 默认位置
                          
                          if (canvasRenderBox != null) {
                            // 将全局坐标转换为画布容器的本地坐标
                            final localPosition = canvasRenderBox.globalToLocal(details.offset);
                            // 应用变换矩阵的逆变换，得到画布坐标系中的真实位置
                            final transformedPosition = _transformGlobalToLocal(localPosition);
                            dropPosition = Offset(
                              transformedPosition.dx.clamp(10.0, 1950.0),
                              transformedPosition.dy.clamp(10.0, 1950.0),
                            );
                          }
                          
                          canvasComponents.add(
                            DraggableComponent(
                              id: 'component_${componentCounter++}',
                              type: details.data,
                              position: dropPosition,
                            ),
                          );
                        });
                      }
                    },
                    onWillAccept: (data) => data != null && !isConnectionMode,
                    builder: (context, candidateData, rejectedData) {
                      return InteractiveViewer(
                        transformationController: _transformationController,
                        boundaryMargin: const EdgeInsets.all(100),
                        minScale: 0.1,
                        maxScale: 3.0,
                        constrained: false,
                        child: Container(
                          width: 2000,
                          height: 2000,
                          decoration: BoxDecoration(
                            color: candidateData.isNotEmpty 
                                ? Colors.blue.withOpacity(0.1) 
                                : Colors.grey[50],
                            borderRadius: const BorderRadius.only(
                              bottomLeft: Radius.circular(12),
                              bottomRight: Radius.circular(12),
                            ),
                          ),
                          child: Stack(
                            children: [
                              // 背景网格
                              CustomPaint(
                                size: const Size(2000, 2000),
                                painter: GridPainter(),
                              ),
                              // 连线层
                              CustomPaint(
                                size: const Size(2000, 2000),
                                painter: ConnectionPainter(
                                  connections: connections,
                                  components: canvasComponents,
                                  selectedSource: selectedSourceComponent,
                                  tempEnd: tempConnectionEnd,
                                ),
                              ),
                              // 画布组件
                              ...canvasComponents.map((component) =>
                                _buildCanvasComponent(component),
                              ),
                              // 提示文字
                              if (canvasComponents.isEmpty)
                                Center(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        isConnectionMode ? Icons.polyline : Icons.drag_indicator,
                                        size: 64,
                                        color: Colors.grey,
                                      ),
                                      const SizedBox(height: 16),
                                      Text(
                                        isConnectionMode 
                                          ? '点击组件进行连线'
                                          : '从左侧拖拽组件到此处开始设计',
                                        style: const TextStyle(
                                          fontSize: 16,
                                          color: Colors.grey,
                                        ),
                                      ),
                                      const SizedBox(height: 8),
                                      const Text(
                                        '鼠标滚轮缩放 • 拖拽移动画布',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.grey,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                            ],
                          ),
                        ),
                      );
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建画布上的组件
  Widget _buildCanvasComponent(DraggableComponent component) {
    bool isSelected = selectedSourceComponent?.id == component.id;
    
    return Positioned(
      left: component.position.dx,
      top: component.position.dy,
      child: Builder(
        builder: (componentContext) {
          return Draggable(
            feedback: _buildComponentWidget(component.type, true),
            childWhenDragging: Container(),
            onDragEnd: isConnectionMode ? null : (details) {
              setState(() {
                // 获取InteractiveViewer容器的RenderBox
                final RenderBox? canvasRenderBox = componentContext.findAncestorRenderObjectOfType<RenderBox>();
                if (canvasRenderBox != null) {
                  // 将全局坐标转换为画布容器的本地坐标
                  final localPosition = canvasRenderBox.globalToLocal(details.offset);
                  // 应用变换矩阵的逆变换，得到画布坐标系中的真实位置
                  final transformedPosition = _transformGlobalToLocal(localPosition);
                  component.position = Offset(
                    transformedPosition.dx.clamp(0.0, 1950.0), // 限制在画布范围内
                    transformedPosition.dy.clamp(0.0, 1950.0),
                  );
                }
              });
            },
            child: GestureDetector(
              onTap: () {
                if (isConnectionMode) {
                  _handleComponentTapForConnection(component);
                } else {
                  _showComponentProperties(component);
                }
              },
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(
                    color: isSelected 
                        ? Colors.red.withOpacity(0.8)
                        : Colors.blue.withOpacity(0.5),
                    width: isSelected ? 3 : 1,
                  ),
                  borderRadius: BorderRadius.circular(4),
                  boxShadow: isSelected ? [
                    BoxShadow(
                      color: Colors.red.withOpacity(0.3),
                      blurRadius: 8,
                      spreadRadius: 2,
                    ),
                  ] : null,
                ),
                child: _buildComponentWidget(component.type, false),
              ),
            ),
          );
        },
      ),
    );
  }

  // 将坐标转换为画布本地坐标（考虑缩放和平移）
  Offset _transformGlobalToLocal(Offset globalPosition) {
    final matrix = Matrix4.copy(_transformationController.value);
    matrix.invert();
    final vector = Vector4(globalPosition.dx, globalPosition.dy, 0, 1);
    final transformed = matrix.transform(vector);
    return Offset(transformed.x, transformed.y);
  }

  // 处理组件点击连线逻辑
  void _handleComponentTapForConnection(DraggableComponent component) {
    setState(() {
      if (selectedSourceComponent == null) {
        selectedSourceComponent = component;
      } else if (selectedSourceComponent!.id != component.id) {
        connections.add(ComponentConnection(
          sourceId: selectedSourceComponent!.id,
          targetId: component.id,
        ));
        selectedSourceComponent = null;
      } else {
        selectedSourceComponent = null;
      }
    });
  }

  // 构建具体的组件Widget
  Widget _buildComponentWidget(ComponentType type, bool isFeedback) {
    final opacity = isFeedback ? 0.8 : 1.0;
    
    switch (type) {
      case ComponentType.button:
        return Opacity(
          opacity: opacity,
          child: ElevatedButton(
            onPressed: isFeedback ? null : () {},
            child: const Text('按钮'),
          ),
        );
      case ComponentType.text:
        return Opacity(
          opacity: opacity,
          child: Container(
            padding: const EdgeInsets.all(8),
            child: const Text(
              '文本内容',
              style: TextStyle(fontSize: 16),
            ),
          ),
        );
      case ComponentType.image:
        return Opacity(
          opacity: opacity,
          child: Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(4),
            ),
            child: const Icon(
              Icons.image,
              size: 48,
              color: Colors.grey,
            ),
          ),
        );
      case ComponentType.container:
        return Opacity(
          opacity: opacity,
          child: Container(
            width: 120,
            height: 80,
            decoration: BoxDecoration(
              color: Colors.purple.withOpacity(0.2),
              border: Border.all(color: Colors.purple),
              borderRadius: BorderRadius.circular(4),
            ),
            child: const Center(
              child: Text('容器'),
            ),
          ),
        );
      case ComponentType.textField:
        return Opacity(
          opacity: opacity,
          child: SizedBox(
            width: 150,
            child: TextField(
              enabled: !isFeedback,
              decoration: const InputDecoration(
                hintText: '输入文本',
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              ),
            ),
          ),
        );
    }
  }

  // 显示组件属性对话框
  void _showComponentProperties(DraggableComponent component) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('${component.type.name} 属性'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('ID: ${component.id}'),
            Text('位置: (${component.position.dx.toInt()}, ${component.position.dy.toInt()})'),
            const SizedBox(height: 16),
            const Text('连线信息:', style: TextStyle(fontWeight: FontWeight.bold)),
            ...connections.where((conn) => 
              conn.sourceId == component.id || conn.targetId == component.id
            ).map((conn) {
              if (conn.sourceId == component.id) {
                return Text('→ ${conn.targetId}');
              } else {
                return Text('← ${conn.sourceId}');
              }
            }),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                connections.removeWhere((conn) => 
                  conn.sourceId == component.id || conn.targetId == component.id
                );
                canvasComponents.remove(component);
              });
              Navigator.of(context).pop();
            },
            child: const Text('删除'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }
}
