// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'log_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

LogFilterState _$LogFilterStateFromJson(Map<String, dynamic> json) {
  return _LogFilterState.fromJson(json);
}

/// @nodoc
mixin _$LogFilterState {
  String? get username => throw _privateConstructorUsedError;
  DateTime? get startTime => throw _privateConstructorUsedError;
  DateTime? get endTime => throw _privateConstructorUsedError;
  int get currentPage => throw _privateConstructorUsedError;
  int get pageSize => throw _privateConstructorUsedError;

  /// Serializes this LogFilterState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of LogFilterState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $LogFilterStateCopyWith<LogFilterState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LogFilterStateCopyWith<$Res> {
  factory $LogFilterStateCopyWith(
          LogFilterState value, $Res Function(LogFilterState) then) =
      _$LogFilterStateCopyWithImpl<$Res, LogFilterState>;
  @useResult
  $Res call(
      {String? username,
      DateTime? startTime,
      DateTime? endTime,
      int currentPage,
      int pageSize});
}

/// @nodoc
class _$LogFilterStateCopyWithImpl<$Res, $Val extends LogFilterState>
    implements $LogFilterStateCopyWith<$Res> {
  _$LogFilterStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LogFilterState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? username = freezed,
    Object? startTime = freezed,
    Object? endTime = freezed,
    Object? currentPage = null,
    Object? pageSize = null,
  }) {
    return _then(_value.copyWith(
      username: freezed == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String?,
      startTime: freezed == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      endTime: freezed == endTime
          ? _value.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      currentPage: null == currentPage
          ? _value.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LogFilterStateImplCopyWith<$Res>
    implements $LogFilterStateCopyWith<$Res> {
  factory _$$LogFilterStateImplCopyWith(_$LogFilterStateImpl value,
          $Res Function(_$LogFilterStateImpl) then) =
      __$$LogFilterStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? username,
      DateTime? startTime,
      DateTime? endTime,
      int currentPage,
      int pageSize});
}

/// @nodoc
class __$$LogFilterStateImplCopyWithImpl<$Res>
    extends _$LogFilterStateCopyWithImpl<$Res, _$LogFilterStateImpl>
    implements _$$LogFilterStateImplCopyWith<$Res> {
  __$$LogFilterStateImplCopyWithImpl(
      _$LogFilterStateImpl _value, $Res Function(_$LogFilterStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of LogFilterState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? username = freezed,
    Object? startTime = freezed,
    Object? endTime = freezed,
    Object? currentPage = null,
    Object? pageSize = null,
  }) {
    return _then(_$LogFilterStateImpl(
      username: freezed == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String?,
      startTime: freezed == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      endTime: freezed == endTime
          ? _value.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      currentPage: null == currentPage
          ? _value.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$LogFilterStateImpl implements _LogFilterState {
  const _$LogFilterStateImpl(
      {this.username,
      this.startTime,
      this.endTime,
      this.currentPage = 1,
      this.pageSize = 10});

  factory _$LogFilterStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$LogFilterStateImplFromJson(json);

  @override
  final String? username;
  @override
  final DateTime? startTime;
  @override
  final DateTime? endTime;
  @override
  @JsonKey()
  final int currentPage;
  @override
  @JsonKey()
  final int pageSize;

  @override
  String toString() {
    return 'LogFilterState(username: $username, startTime: $startTime, endTime: $endTime, currentPage: $currentPage, pageSize: $pageSize)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LogFilterStateImpl &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.startTime, startTime) ||
                other.startTime == startTime) &&
            (identical(other.endTime, endTime) || other.endTime == endTime) &&
            (identical(other.currentPage, currentPage) ||
                other.currentPage == currentPage) &&
            (identical(other.pageSize, pageSize) ||
                other.pageSize == pageSize));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, username, startTime, endTime, currentPage, pageSize);

  /// Create a copy of LogFilterState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LogFilterStateImplCopyWith<_$LogFilterStateImpl> get copyWith =>
      __$$LogFilterStateImplCopyWithImpl<_$LogFilterStateImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LogFilterStateImplToJson(
      this,
    );
  }
}

abstract class _LogFilterState implements LogFilterState {
  const factory _LogFilterState(
      {final String? username,
      final DateTime? startTime,
      final DateTime? endTime,
      final int currentPage,
      final int pageSize}) = _$LogFilterStateImpl;

  factory _LogFilterState.fromJson(Map<String, dynamic> json) =
      _$LogFilterStateImpl.fromJson;

  @override
  String? get username;
  @override
  DateTime? get startTime;
  @override
  DateTime? get endTime;
  @override
  int get currentPage;
  @override
  int get pageSize;

  /// Create a copy of LogFilterState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LogFilterStateImplCopyWith<_$LogFilterStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

OperationLogFilterState _$OperationLogFilterStateFromJson(
    Map<String, dynamic> json) {
  return _OperationLogFilterState.fromJson(json);
}

/// @nodoc
mixin _$OperationLogFilterState {
  int? get userId => throw _privateConstructorUsedError;
  int? get action => throw _privateConstructorUsedError;
  int? get category => throw _privateConstructorUsedError;
  String? get target => throw _privateConstructorUsedError;
  DateTime? get startTime => throw _privateConstructorUsedError;
  DateTime? get endTime => throw _privateConstructorUsedError;
  int get currentPage => throw _privateConstructorUsedError;
  int get pageSize => throw _privateConstructorUsedError;

  /// Serializes this OperationLogFilterState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of OperationLogFilterState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OperationLogFilterStateCopyWith<OperationLogFilterState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OperationLogFilterStateCopyWith<$Res> {
  factory $OperationLogFilterStateCopyWith(OperationLogFilterState value,
          $Res Function(OperationLogFilterState) then) =
      _$OperationLogFilterStateCopyWithImpl<$Res, OperationLogFilterState>;
  @useResult
  $Res call(
      {int? userId,
      int? action,
      int? category,
      String? target,
      DateTime? startTime,
      DateTime? endTime,
      int currentPage,
      int pageSize});
}

/// @nodoc
class _$OperationLogFilterStateCopyWithImpl<$Res,
        $Val extends OperationLogFilterState>
    implements $OperationLogFilterStateCopyWith<$Res> {
  _$OperationLogFilterStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OperationLogFilterState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = freezed,
    Object? action = freezed,
    Object? category = freezed,
    Object? target = freezed,
    Object? startTime = freezed,
    Object? endTime = freezed,
    Object? currentPage = null,
    Object? pageSize = null,
  }) {
    return _then(_value.copyWith(
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int?,
      action: freezed == action
          ? _value.action
          : action // ignore: cast_nullable_to_non_nullable
              as int?,
      category: freezed == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as int?,
      target: freezed == target
          ? _value.target
          : target // ignore: cast_nullable_to_non_nullable
              as String?,
      startTime: freezed == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      endTime: freezed == endTime
          ? _value.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      currentPage: null == currentPage
          ? _value.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OperationLogFilterStateImplCopyWith<$Res>
    implements $OperationLogFilterStateCopyWith<$Res> {
  factory _$$OperationLogFilterStateImplCopyWith(
          _$OperationLogFilterStateImpl value,
          $Res Function(_$OperationLogFilterStateImpl) then) =
      __$$OperationLogFilterStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? userId,
      int? action,
      int? category,
      String? target,
      DateTime? startTime,
      DateTime? endTime,
      int currentPage,
      int pageSize});
}

/// @nodoc
class __$$OperationLogFilterStateImplCopyWithImpl<$Res>
    extends _$OperationLogFilterStateCopyWithImpl<$Res,
        _$OperationLogFilterStateImpl>
    implements _$$OperationLogFilterStateImplCopyWith<$Res> {
  __$$OperationLogFilterStateImplCopyWithImpl(
      _$OperationLogFilterStateImpl _value,
      $Res Function(_$OperationLogFilterStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of OperationLogFilterState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = freezed,
    Object? action = freezed,
    Object? category = freezed,
    Object? target = freezed,
    Object? startTime = freezed,
    Object? endTime = freezed,
    Object? currentPage = null,
    Object? pageSize = null,
  }) {
    return _then(_$OperationLogFilterStateImpl(
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int?,
      action: freezed == action
          ? _value.action
          : action // ignore: cast_nullable_to_non_nullable
              as int?,
      category: freezed == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as int?,
      target: freezed == target
          ? _value.target
          : target // ignore: cast_nullable_to_non_nullable
              as String?,
      startTime: freezed == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      endTime: freezed == endTime
          ? _value.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      currentPage: null == currentPage
          ? _value.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OperationLogFilterStateImpl implements _OperationLogFilterState {
  const _$OperationLogFilterStateImpl(
      {this.userId,
      this.action,
      this.category,
      this.target,
      this.startTime,
      this.endTime,
      this.currentPage = 1,
      this.pageSize = 10});

  factory _$OperationLogFilterStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$OperationLogFilterStateImplFromJson(json);

  @override
  final int? userId;
  @override
  final int? action;
  @override
  final int? category;
  @override
  final String? target;
  @override
  final DateTime? startTime;
  @override
  final DateTime? endTime;
  @override
  @JsonKey()
  final int currentPage;
  @override
  @JsonKey()
  final int pageSize;

  @override
  String toString() {
    return 'OperationLogFilterState(userId: $userId, action: $action, category: $category, target: $target, startTime: $startTime, endTime: $endTime, currentPage: $currentPage, pageSize: $pageSize)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OperationLogFilterStateImpl &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.action, action) || other.action == action) &&
            (identical(other.category, category) ||
                other.category == category) &&
            (identical(other.target, target) || other.target == target) &&
            (identical(other.startTime, startTime) ||
                other.startTime == startTime) &&
            (identical(other.endTime, endTime) || other.endTime == endTime) &&
            (identical(other.currentPage, currentPage) ||
                other.currentPage == currentPage) &&
            (identical(other.pageSize, pageSize) ||
                other.pageSize == pageSize));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, userId, action, category, target,
      startTime, endTime, currentPage, pageSize);

  /// Create a copy of OperationLogFilterState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OperationLogFilterStateImplCopyWith<_$OperationLogFilterStateImpl>
      get copyWith => __$$OperationLogFilterStateImplCopyWithImpl<
          _$OperationLogFilterStateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OperationLogFilterStateImplToJson(
      this,
    );
  }
}

abstract class _OperationLogFilterState implements OperationLogFilterState {
  const factory _OperationLogFilterState(
      {final int? userId,
      final int? action,
      final int? category,
      final String? target,
      final DateTime? startTime,
      final DateTime? endTime,
      final int currentPage,
      final int pageSize}) = _$OperationLogFilterStateImpl;

  factory _OperationLogFilterState.fromJson(Map<String, dynamic> json) =
      _$OperationLogFilterStateImpl.fromJson;

  @override
  int? get userId;
  @override
  int? get action;
  @override
  int? get category;
  @override
  String? get target;
  @override
  DateTime? get startTime;
  @override
  DateTime? get endTime;
  @override
  int get currentPage;
  @override
  int get pageSize;

  /// Create a copy of OperationLogFilterState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OperationLogFilterStateImplCopyWith<_$OperationLogFilterStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
