import 'package:freezed_annotation/freezed_annotation.dart';

part 'log_model.freezed.dart';
part 'log_model.g.dart';

@freezed
class LogFilterState with _$LogFilterState {
  const factory LogFilterState({
    String? username,
    DateTime? startTime,
    DateTime? endTime,
    @Default(1) int currentPage,
    @Default(10) int pageSize,
  }) = _LogFilterState;

  factory LogFilterState.fromJson(Map<String, dynamic> json) =>
      _$LogFilterStateFromJson(json);
}

/// 操作日志过滤状态
@freezed
class OperationLogFilterState with _$OperationLogFilterState {
  const factory OperationLogFilterState({
    int? userId,
    int? action,
    int? category,
    String? target,
    DateTime? startTime,
    DateTime? endTime,
    @Default(1) int currentPage,
    @Default(10) int pageSize,
  }) = _OperationLogFilterState;

  factory OperationLogFilterState.fromJson(Map<String, dynamic> json) =>
      _$OperationLogFilterStateFromJson(json);
}
