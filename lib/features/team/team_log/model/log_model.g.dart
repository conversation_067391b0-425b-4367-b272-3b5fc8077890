// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'log_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$LogFilterStateImpl _$$LogFilterStateImplFromJson(Map<String, dynamic> json) =>
    _$LogFilterStateImpl(
      username: json['username'] as String?,
      startTime: json['startTime'] == null
          ? null
          : DateTime.parse(json['startTime'] as String),
      endTime: json['endTime'] == null
          ? null
          : DateTime.parse(json['endTime'] as String),
      currentPage: (json['currentPage'] as num?)?.toInt() ?? 1,
      pageSize: (json['pageSize'] as num?)?.toInt() ?? 10,
    );

Map<String, dynamic> _$$LogFilterStateImplToJson(
        _$LogFilterStateImpl instance) =>
    <String, dynamic>{
      'username': instance.username,
      'startTime': instance.startTime?.toIso8601String(),
      'endTime': instance.endTime?.toIso8601String(),
      'currentPage': instance.currentPage,
      'pageSize': instance.pageSize,
    };

_$OperationLogFilterStateImpl _$$OperationLogFilterStateImplFromJson(
        Map<String, dynamic> json) =>
    _$OperationLogFilterStateImpl(
      userId: (json['userId'] as num?)?.toInt(),
      action: (json['action'] as num?)?.toInt(),
      category: (json['category'] as num?)?.toInt(),
      target: json['target'] as String?,
      startTime: json['startTime'] == null
          ? null
          : DateTime.parse(json['startTime'] as String),
      endTime: json['endTime'] == null
          ? null
          : DateTime.parse(json['endTime'] as String),
      currentPage: (json['currentPage'] as num?)?.toInt() ?? 1,
      pageSize: (json['pageSize'] as num?)?.toInt() ?? 10,
    );

Map<String, dynamic> _$$OperationLogFilterStateImplToJson(
        _$OperationLogFilterStateImpl instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'action': instance.action,
      'category': instance.category,
      'target': instance.target,
      'startTime': instance.startTime?.toIso8601String(),
      'endTime': instance.endTime?.toIso8601String(),
      'currentPage': instance.currentPage,
      'pageSize': instance.pageSize,
    };
