import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:frontend_re/domain/models/team_model.dart';
import 'package:frontend_re/features/team/team_log/controllers/log_controller.dart';
import 'package:frontend_re/features/team/team_log/controllers/operation_log_controller.dart';
import 'package:frontend_re/widgets/custom_border_button.dart';
import 'package:frontend_re/widgets/custom_date_picker.dart';
import 'package:frontend_re/widgets/data_table_widget.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class OperationLogPage extends HookConsumerWidget {
  const OperationLogPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    //监听log窗口数据
    final operationLogState = ref.watch(operationLogControllerProvider);
    final controller = ref.read(operationLogControllerProvider.notifier);
    
    // 监听过滤状态
    final filterState = ref.watch(operationLogFilterControllerProvider);
    final startTime = filterState.startTime;
    final endTime = filterState.endTime;
    final currentPage = filterState.currentPage;
    final pageSize = filterState.pageSize;

    return Scaffold(
      backgroundColor: const Color.fromARGB(0, 210, 149, 149),
      body: Column(
        spacing: 20,
        children: [
          // 添加过滤条件行
          Row(
            spacing: 20,
            children: [
              // 使用日期范围选择器
              CustomDateRangePicker(
                startDate: startTime,
                endDate: endTime,
                onStartDateSelected: (date) => ref
                    .read(operationLogFilterControllerProvider.notifier)
                    .updateStartTime(date),
                onEndDateSelected: (date) => ref
                    .read(operationLogFilterControllerProvider.notifier)
                    .updateEndTime(date),
                height: 42,
              ),
              CustomBorderButton(
                verticalPadding: 10,
                horizontalPadding: 20,
                text: '查询',
                iconPath: 'assets/svg/inquiry.svg',
                backgroundColor: const Color(0xFFFFFFFF),
                borderColor: Colors.transparent,
                onPressed: () {
                  // 重置页码到第一页
                  ref.read(operationLogFilterControllerProvider.notifier).updateCurrentPage(1);
                  
                  controller.fetchLogs(
                    OperationLogRequest(
                      startTime: startTime != null
                          ? '${startTime.toIso8601String().substring(0, 19)}Z'
                          : null,
                      endTime: endTime != null
                          ? '${endTime.toIso8601String().substring(0, 19)}Z'
                          : null,
                      offset: 0, // 从第一页开始
                      limit: pageSize,
                    ),
                  );
                },
              )
            ],
          ),
          Expanded(
            child: operationLogState.when(
              data: (windows) => DataTableWidget(
                headingTextStyle: const TextStyle(fontSize: 14, color: Color(0xFF333333)),
                dataTextStyle: const TextStyle(fontSize: 14, color: Color(0xFF8D8E93)),
                minWidth: 100,
                showCheckbox: false,
                columnSpacing: 0,
                enablePagination: true,
                totalCount: windows.total,
                pageSize: pageSize,
                currentPage: currentPage,
                onPageChanged: (page, newPageSize) {
                  // 更新分页状态
                  ref.read(operationLogFilterControllerProvider.notifier).updateCurrentPage(page);
                  if (newPageSize != pageSize) {
                    ref.read(operationLogFilterControllerProvider.notifier).updatePageSize(newPageSize);
                  }
                  
                  // 获取数据时保留当前的过滤条件
                  controller.fetchLogs(OperationLogRequest(
                    startTime: startTime != null
                        ? '${startTime.toIso8601String().substring(0, 19)}Z'
                        : null,
                    endTime: endTime != null
                        ? '${endTime.toIso8601String().substring(0, 19)}Z'
                        : null,
                    offset: (page - 1) * newPageSize,
                    limit: newPageSize,
                  ));
                },
                noDataImagePath: 'assets/images/proxy/NoData.png',
                noDataText: '暂无数据',
                noDataButtonText: '刷新',
                noDataButtonEvent: () {
                  ref.read(operationLogControllerProvider.notifier).fetchLogs(const OperationLogRequest());
                },
                columns: const [
                  DataColumn2(label: Text('用户ID'), size: ColumnSize.M),
                  DataColumn2(label: Text('操作'), size: ColumnSize.M),
                  DataColumn2(label: Text('分类'), size: ColumnSize.M),
                  DataColumn2(label: Text('目标'), size: ColumnSize.M),
                  DataColumn2(label: Text('创建时间'), size: ColumnSize.L),
                ],
                data: windows.logs,
                rowBuilder: (log, index, isHovered) {
                  return DataRow2(cells: [
                    DataCell(Text(log.userId.toString())),
                    DataCell(Text(log.action.toString())),
                    DataCell(Text(log.category.toString())),
                    DataCell(Text(log.target)),
                    DataCell(Text(log.createdAt)),
                  ]);
                },
              ),
              loading: () => const Center(
                child: CircularProgressIndicator(),
              ),
              error: (error, stackTrace) => DataTableWidget(
                headingTextStyle: const TextStyle(fontSize: 14, color: Color(0xFF333333)),
                dataTextStyle: const TextStyle(fontSize: 14, color: Color(0xFF8D8E93)),
                minWidth: 100,
                showCheckbox: true,
                columnSpacing: 0,
                enablePagination: true,
                noDataImagePath: 'assets/images/proxy/NoData.png',
                noDataText: error.toString(),
                columns: const [
                  DataColumn2(label: Text('用户ID'), size: ColumnSize.S),
                  DataColumn2(label: Text('操作'), size: ColumnSize.M),
                  DataColumn2(label: Text('分类'), size: ColumnSize.M),
                  DataColumn2(label: Text('目标'), size: ColumnSize.M),
                  DataColumn2(label: Text('创建时间'), size: ColumnSize.L),
                ],
                data: const [],
                rowBuilder: (log, index, isHovered) {
                  return DataRow2(cells: [
                    DataCell(Text(log.userId.toString())),
                    DataCell(Text(log.action.toString())),
                    DataCell(Text(log.category.toString())),
                    DataCell(Text(log.target)),
                    DataCell(Text(log.createdAt)),
                  ]);
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
