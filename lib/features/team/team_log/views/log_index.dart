import 'package:flutter/material.dart';
import 'package:frontend_re/features/team/team_log/views/widgets/log_tap_btn.dart';
// import 'package:go_router/go_router.dart'; // 不再需要导入 go_router

class LogIndex extends StatelessWidget {
  // 将 child 改为必需参数，并移除 navigationShell
  const LogIndex({super.key, required this.child});
  
  final Widget child;
  // final StatefulNavigationShell? navigationShell; // 移除 navigationShell

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        spacing: 16,
        children: [
          const LogTapBtn(),
          // 使用传入的 child 参数
          Expanded(child: child),
        ],
      )
    );
  }
}
