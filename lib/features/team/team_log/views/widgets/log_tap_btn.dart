import 'package:flutter/material.dart';
import 'package:frontend_re/core/utils/ui/snack_bar_util.dart';
import 'package:frontend_re/features/team/team_log/controllers/log_controller.dart';
import 'package:frontend_re/widgets/custom_solid_button.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class LogTapBtn extends HookConsumerWidget {
  const LogTapBtn({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final index = ref.watch(logPageIndexProvider);
    return Row(
      spacing: 16,
      children: [
        // 登录日志按钮
        CustomSolidButton(
          iconPath: 'assets/svg/platform_proxy.svg',
          backgroundColor: index == 0 ? const Color(0xFF1E1C1D) : const Color(0xFFFFFFFF),
          iconColor: index == 0 ? const Color(0xFF1E1C1D) : const Color(0xFFFFFFFF),
          iconBackgroundColor: index == 0 ? const Color(0xFFFFFFFF) : const Color(0xFF1E1C1D),
          iconSize: 14,
          textColor: index == 0 ? const Color(0xFFFFFFFF) : const Color(0xFF8D8E93),
          text: '登录日志',
          onPressed: () {
            ref.read(logPageIndexProvider.notifier).setIndex(0);
            // 使用路径导航，更直观
            context.goNamed('loginLog');
          },
        ),
        // 操作日志按钮
        CustomSolidButton(
          iconPath: 'assets/svg/self_proxy.svg',
          backgroundColor: index == 1 ? const Color(0xFF1E1C1D) : const Color(0xFFFFFFFF),
          iconColor: index == 1 ? const Color(0xFF1E1C1D) : const Color(0xFFFFFFFF),
          iconBackgroundColor: index == 1 ? const Color(0xFFFFFFFF) : const Color(0xFF1E1C1D),
          iconSize: 14,
          textColor: index == 1 ? const Color(0xFFFFFFFF) : const Color(0xFF8D8E93),
          text: '操作日志',
          // textDirection: TextDirection.rtl,
          onPressed: () {
            // 使用路径导航，更直观
            // context.push('/addBrowser');
            ref.read(logPageIndexProvider.notifier).setIndex(1);
            context.goNamed('operationLog');
          },
        ),
        // 权限日志按钮
        CustomSolidButton(
          iconPath: 'assets/svg/proxy_api.svg',
          backgroundColor: index == 2 ? const Color(0xFF1E1C1D) : const Color(0xFFFFFFFF),
          iconColor: index == 2 ? const Color(0xFF1E1C1D) : const Color(0xFFFFFFFF),
          iconBackgroundColor: index == 2 ? const Color(0xFFFFFFFF) : const Color(0xFF1E1C1D),
          iconSize: 14,
          textColor: index == 2 ? const Color(0xFFFFFFFF) : const Color(0xFF8D8E93),
          text: '权限日志',
          // textDirection: TextDirection.rtl,
          onPressed: () {
            // 使用路径导航，更直观
            // context.push('/addBrowser');
            // ref.read(logPageIndexProvider.notifier).setIndex(2);
            // context.goNamed('permissionLog');
            SnackBarUtil().showInfo(context, '暂未开放');
          },
        ),
      ],
    );
  }
}
