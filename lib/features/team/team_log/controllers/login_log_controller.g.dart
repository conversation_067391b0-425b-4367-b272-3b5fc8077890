// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'login_log_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$loginLogControllerHash() =>
    r'3042e80df25c8d78d36848d52f3f7f252f3c74b2';

/// See also [LoginLogController].
@ProviderFor(LoginLogController)
final loginLogControllerProvider = AutoDisposeAsyncNotifierProvider<
    LoginLogController, LoginLogModel>.internal(
  LoginLogController.new,
  name: r'loginLog<PERSON>ontrollerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$loginLogControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$LoginLogController = AutoDisposeAsyncNotifier<LoginLogModel>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
