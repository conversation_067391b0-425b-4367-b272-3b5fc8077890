import 'package:frontend_re/repositories/team_repository.dart';
import 'package:frontend_re/domain/models/team_model.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'operation_log_controller.g.dart';

@riverpod
class OperationLogController extends _$OperationLogController {
  @override
  Future<OperationLogModel> build() async {
    return await fetchLogs(const OperationLogRequest());
  }

  // 合并后的获取日志方法
  Future<OperationLogModel> fetchLogs(OperationLogRequest request) async {
    // 设置加载状态
    state = const AsyncValue.loading();
    
    try {
      // 直接调用仓库获取数据
      final repository = ref.read(operationLogRepositoryProvider.notifier);
      final operationLogModel = await repository.getOperationLogs(request);
      
      // 更新状态
      state = AsyncValue.data(operationLogModel);
      return operationLogModel;
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
      rethrow;
    }
  }
}
