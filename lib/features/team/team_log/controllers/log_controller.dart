// 日志页面索引
import 'package:frontend_re/features/team/team_log/model/log_model.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'log_controller.g.dart';

@Riverpod(keepAlive: true)
class LogPageIndex extends _$LogPageIndex {
  @override
  int build() {
    return 0;
  }

  void setIndex(int index) {
    state = index;
  }
}

/// 日期选择器状态
@riverpod
class LogFilterController extends _$LogFilterController {
  @override
  LogFilterState build() {
    // 初始状态
    return const LogFilterState();
  }

  // 更新开始时间
  void updateStartTime(DateTime? date) {
    state = state.copyWith(startTime: date);
  }

  // 更新结束时间
  void updateEndTime(DateTime? date) {
    state = state.copyWith(endTime: date);
  }

  // 更新用户名
  void updateUsername(String username) {
    state = state.copyWith(username: username);
  }

  // 更新当前页码
  void updateCurrentPage(int page) {
    state = state.copyWith(currentPage: page);
  }

  // 更新页面大小
  void updatePageSize(int pageSize) {
    state = state.copyWith(pageSize: pageSize, currentPage: 1); // 重置页码
  }

  // 重置过滤条件
  void resetFilters() {
    state = const LogFilterState();
  }
}

/// 操作日志过滤控制器
@riverpod
class OperationLogFilterController extends _$OperationLogFilterController {
  @override
  OperationLogFilterState build() {
    // 初始状态
    return const OperationLogFilterState();
  }

  // 更新用户ID
  void updateUserId(int? userId) {
    state = state.copyWith(userId: userId);
  }

  // 更新操作类型
  void updateAction(int? action) {
    state = state.copyWith(action: action);
  }

  // 更新操作分类
  void updateCategory(int? category) {
    state = state.copyWith(category: category);
  }

  // 更新操作目标
  void updateTarget(String? target) {
    state = state.copyWith(target: target);
  }

  // 更新开始时间
  void updateStartTime(DateTime? date) {
    state = state.copyWith(startTime: date);
  }

  // 更新结束时间
  void updateEndTime(DateTime? date) {
    state = state.copyWith(endTime: date);
  }

  // 更新当前页码
  void updateCurrentPage(int page) {
    state = state.copyWith(currentPage: page);
  }

  // 更新页面大小
  void updatePageSize(int pageSize) {
    state = state.copyWith(pageSize: pageSize, currentPage: 1); // 重置页码
  }

  // 重置过滤条件
  void resetFilters() {
    state = const OperationLogFilterState();
  }
}
