// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'operation_log_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$operationLogControllerHash() =>
    r'dfbe67ae4a9b28ad5fde3286eea46fc6066feb78';

/// See also [OperationLogController].
@ProviderFor(OperationLogController)
final operationLogControllerProvider = AutoDisposeAsyncNotifierProvider<
    OperationLogController, OperationLogModel>.internal(
  OperationLogController.new,
  name: r'operationLogControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$operationLogControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$OperationLogController = AutoDisposeAsyncNotifier<OperationLogModel>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
