import 'package:frontend_re/repositories/team_repository.dart';
import 'package:frontend_re/domain/models/team_model.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'login_log_controller.g.dart';

@riverpod
class LoginLogController extends _$LoginLogController {
  @override
  Future<LoginLogModel> build() async {
    return await fetchLogs(const LoginLogRequest());
  }

  // 合并后的获取日志方法
  Future<LoginLogModel> fetchLogs(LoginLogRequest request) async {
    // 设置加载状态
    state = const AsyncValue.loading();
    
    try {
      // 直接调用仓库获取数据
      final repository = ref.read(loginLogRepositoryProvider.notifier);
      final loginLogModel = await repository.getLoginLogs(request);
      
      // 更新状态
      state = AsyncValue.data(loginLogModel);
      return loginLogModel;
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
      rethrow;
    }
  }
}
