// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'log_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$logPageIndexHash() => r'0340ff06cd42f50228c7efbc9266dc07ad7d17f8';

/// See also [LogPageIndex].
@ProviderFor(LogPageIndex)
final logPageIndexProvider = NotifierProvider<LogPageIndex, int>.internal(
  LogPageIndex.new,
  name: r'logPageIndexProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$logPageIndexHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$LogPageIndex = Notifier<int>;
String _$logFilterControllerHash() =>
    r'388699a3eb8ea403d8fe7c5a3200554171a93ddd';

/// 日期选择器状态
///
/// Copied from [LogFilterController].
@ProviderFor(LogFilterController)
final logFilterControllerProvider =
    AutoDisposeNotifierProvider<LogFilterController, LogFilterState>.internal(
  LogFilterController.new,
  name: r'logFilterControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$logFilterControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$LogFilterController = AutoDisposeNotifier<LogFilterState>;
String _$operationLogFilterControllerHash() =>
    r'60aaee74662b0d479cae4b2b38307d99a191bc33';

/// 操作日志过滤控制器
///
/// Copied from [OperationLogFilterController].
@ProviderFor(OperationLogFilterController)
final operationLogFilterControllerProvider = AutoDisposeNotifierProvider<
    OperationLogFilterController, OperationLogFilterState>.internal(
  OperationLogFilterController.new,
  name: r'operationLogFilterControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$operationLogFilterControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$OperationLogFilterController
    = AutoDisposeNotifier<OperationLogFilterState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
