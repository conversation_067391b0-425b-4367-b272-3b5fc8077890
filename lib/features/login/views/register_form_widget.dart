import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_svg/svg.dart';
import 'package:frontend_re/features/login/controllers/login_index_notifier.dart';
import 'package:frontend_re/features/login/controllers/auth_controller.dart';
import 'package:frontend_re/features/login/views/widgets/auth_button_widget.dart';
import 'package:frontend_re/features/login/views/widgets/auth_input_custom_widget.dart';
import 'package:frontend_re/features/login/views/widgets/captcha_dialog_widget.dart';
import 'package:frontend_re/core/utils/ui/snack_bar_util.dart';
import 'package:frontend_re/widgets/custom_card.dart';
import 'package:frontend_re/widgets/custom_text_button.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class RegisterFormWidget extends HookConsumerWidget {
  const RegisterFormWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 创建一个表单 key
    final formKey = useMemoized(() => GlobalKey<FormState>());

    // 创建控制器
    final usernameController = useTextEditingController();
    final phoneController = useTextEditingController();
    final emailController = useTextEditingController();
    final passwordController = useTextEditingController();
    final emailCodeController = useTextEditingController();
    final inviteCodeController = useTextEditingController();

    // 状态变量声明
    final isVerifying = useState(false);
    final isRegister = useState(false);
    final isAgreed = useState(false);

    ref.listen<AsyncValue>(authControllerProvider, (previous, next) {
      // 只在状态从加载变为数据或错误时显示提示
      if (previous?.isLoading == true && !next.isLoading) {
        next.when(
          data: (res) {
            if (res != null) {
              SnackBarUtil().showSuccess(context, res);
            }
          }, 
          error: (error, stack) {
            if (error is DioException) {
              SnackBarUtil().showError(context, error.response?.data['error'] ?? error.toString());
            } else {
              SnackBarUtil().showError(context, error.toString());
            }
            // 重置校验状态
            isVerifying.value = false;
          }, 
          loading: () {}
        );
      }
    });

    return Form(
      key: formKey,
      child: SizedBox(
        width: 340,
        height: 600, // 调整高度
        child: Center(
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('欢迎注册', style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),),
                const SizedBox(height: 20),
                // 用户名输入框
                AuthInputCustomWidget(
                  controller: usernameController,
                  labelText: '用户名（必填）',
                  prefixIcon: SvgPicture.asset('assets/svg/user_login.svg'),
                  maxLength: 20,
                ),
                const SizedBox(height: 20),
                // 密码输入框
                AuthInputCustomWidget(
                  controller: passwordController,
                  labelText: '密码（必填）',
                  isPassword: true,
                  prefixIcon: SvgPicture.asset('assets/svg/pwd_login.svg'),
                ),
                const SizedBox(height: 20),
                // 手机号输入框（可选）
                // AuthInputCustomWidget(
                //   controller: phoneController,
                //   labelText: '手机号（可选）',
                //   keyboardType: TextInputType.phone,
                //   maxLength: 20,
                //   prefixIcon: const Icon(Icons.phone,color: Color(0xFF7BB4FF),size: 20,),
                //   inputFormatters: [
                //     FilteringTextInputFormatter.digitsOnly,
                //   ],
                // ),
                // const SizedBox(height: 20),
                // 邮箱输入框（可选）
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    AuthInputCustomWidget(
                      width: 260,
                      controller: emailController,
                      labelText: '邮箱（必填）',
                      keyboardType: TextInputType.emailAddress,
                      prefixIcon: SvgPicture.asset('assets/svg/email.svg'),
                    ),
                    isVerifying.value ? const Padding(
                      padding: EdgeInsets.all(8.0),
                      child: Text('已校验',style: TextStyle(fontSize: 14, color: Color(0xFF7BB4FF)),),
                    ) : CustomTextButton(
                        text: '校验',
                        onPressed: () async {
                          // 检查邮箱
                          if (emailController.text.isEmpty) {
                            SnackBarUtil().showWarning(context, '请输入邮箱');
                            return;
                          }
                          // 检查邮箱格式
                          if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(emailController.text)) {
                            SnackBarUtil().showWarning(context, '请输入有效的邮箱地址');
                            return;
                          }
                          // 打开验证码弹窗
                          showDialog(
                            context: context,
                            barrierDismissible: false,
                            builder: (context) => CaptchaDialogWidget(
                              email: emailController.text,
                              onVerifySuccess: (bool success) {
                                if (success) {
                                  // 验证成功后设置可注册状态为true
                                  isRegister.value = true;
                                  // 校验状态为true
                                  isVerifying.value = true;
                                }
                              },
                            ),
                          );                     
                        },
                        // style: TextButton.styleFrom(
                        //   // 设置最小高度和宽度
                        //   minimumSize: const Size(60, 50), // 宽度80，高度40
                        //   // 设置圆角
                        //   shape: RoundedRectangleBorder(
                        //     borderRadius: BorderRadius.circular(50), // 圆角半径8
                        //   ),
                        //   // 其他样式
                        //   backgroundColor: Colors.transparent, // 背景透明
                        //   padding: EdgeInsets.zero, // 内边距为0
                        // ),
                        // child: const Text(
                        //   '校验',
                        //   style: TextStyle(
                        //     fontSize: 14, 
                        //     color: Color(0xFF7BB4FF),
                        //   ),
                        // ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                // 邮箱验证码输入框
                AuthInputCustomWidget(
                  controller: emailCodeController,
                  labelText: '验证码',
                  prefixIcon: SvgPicture.asset('assets/svg/email.svg'),
                ),
                const SizedBox(height: 20),
                // 邀请码输入框
                AuthInputCustomWidget(
                  controller: inviteCodeController,
                  labelText: '邀请码（可选）',
                  prefixIcon: SvgPicture.asset('assets/svg/invite_code.svg'),
                ),
          
                const SizedBox(height: 10),
          
                // 隐私协议
                Row(
                  children: [
                    Transform.scale(
                      scale: 1,
                      child: Checkbox(
                        value: isAgreed.value,
                        onChanged: (value) {
                          isAgreed.value = value ?? false;
                        },
                        activeColor: const Color(0xFF2F64E8),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(6),
                        ),
                        side: const BorderSide(
                          color: Color(0xFF2F64E8), // 未选中时的边框颜色
                          width: 1.5,
                        ),
                      ),
                    ),
                    Expanded(
                      child: Wrap(
                        children: [
                          const Text(
                            '我已阅读并同意',
                            style: TextStyle(fontSize: 14, color: Color(0xFF8D8E93)),
                          ),
                          MouseRegion(
                            cursor: SystemMouseCursors.click,
                            child: GestureDetector(
                              onTap: () {
                                // TODO: 打开用户协议页面
                                showDialog(
                                  context: context,
                                  builder: (context) => const Dialog(
                                    child: SizedBox(
                                      width: 500,
                                      height: 600,
                                      child: CustomCard(
                                        title: '用户协议',
                                        description: '',
                                        child: Text('用户协议'),
                                      ),
                                    ),
                                  ),
                                );
                              },
                              child: const Text(
                                '用户协议',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Color(0xFF2F64E8),
                                ),
                              ),
                            ),
                          ),
                          const Text(
                            '和',
                            style: TextStyle(fontSize: 14, color: Color(0xFF8D8E93)),
                          ),
                          MouseRegion(
                            cursor: SystemMouseCursors.click,
                            child: GestureDetector(
                              onTap: () {
                                // TODO: 打开隐私政策页面
                                showDialog(
                                  context: context,
                                  builder: (context) => const Dialog(
                                    child: SizedBox(
                                      width: 500,
                                      height: 600,
                                      child: CustomCard(
                                        title: '隐私政策',
                                        description: '',
                                        child: Text('隐私政策'),
                                      ),
                                    ),
                                  ),
                                );
                              },
                              child: const Text(
                                '隐私政策',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Color(0xFF2F64E8),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 10),
                
                // 注册按钮
                AuthButtonWidget(
                  text: '注册',
                  onPressed: () {
                    // 检查是否校验邮箱
                    if (!isVerifying.value) {
                      SnackBarUtil().showWarning(context, '请先校验邮箱');
                      return;
                    }

                    // 检查是否同意协议
                    if (!isAgreed.value) {
                      SnackBarUtil().showWarning(context, '请先同意用户协议和隐私政策');
                      return;
                    }
                    
                    // 检查是否可注册
                    if (!isRegister.value) {
                      SnackBarUtil().showWarning(context, '请先校验邮箱');
                      return;
                    }
                    
                    // 检查必填字段
                    if (usernameController.text.isEmpty || passwordController.text.isEmpty) {
                      SnackBarUtil().showWarning(context, '请填写必填字段');
                      return;
                    }
                    
                    ref.read(authControllerProvider.notifier).register(
                      username: usernameController.text,
                      password: passwordController.text,
                      email: emailController.text.isNotEmpty ? emailController.text : null,
                      phone: phoneController.text.isNotEmpty ? phoneController.text : null,
                      emailCode: emailCodeController.text.isNotEmpty ? emailCodeController.text : null,
                      inviteCode: inviteCodeController.text.isNotEmpty ? inviteCodeController.text : null,
                    );
                  },
                ),
                const SizedBox(height: 10),
                //有账号
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Text('已有账号？', style: TextStyle(fontSize: 14, color: Color(0xFF8D8E93)),),
                    TextButton(onPressed: () {
                      ref.read(loginIndexNotifierProvider.notifier).setIndex(0);
                    }, child: const Text('立即登录', style: TextStyle(fontSize: 14, color: Color(0xFF2F64E8)),),),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
