import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

class ImageCarousel extends HookWidget {
  final List<String> imagePaths;
  final List<String> titles; // 每张图片对应的文字

  const ImageCarousel({
    super.key,
    required this.imagePaths,
    required this.titles,
  });

  @override
  Widget build(BuildContext context) {
    // 自动轮播逻辑
    final pageController = usePageController();
    final currentPage = useState(0); // 当前页面索引
    useEffect(() {
      final timer = Timer.periodic(const Duration(seconds: 5), (timer) {
        if (currentPage.value == imagePaths.length - 1) {
          pageController.animateToPage(
            0,
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeInOut,
          );
        } else {
          pageController.nextPage(
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeInOut,
          );
        }
      });
      return () => timer.cancel(); // 清理定时器
    }, []);

    return Stack(
      children: [
        PageView.builder(
          controller: pageController, // 添加controller
          itemCount: imagePaths.length, // 图片数量
          itemBuilder: (context, index) {
            return Image.asset(
              imagePaths[index],
              fit: BoxFit.cover, // 图片填充整个容器
            );
          },
          onPageChanged: (index) {
            currentPage.value = index; // 更新当前页面索引
          },
        ),
        // 文字和指示器
        Positioned(
          bottom: 50, // 调整位置，留出空间给文字
          left: 0,
          right: 0,
          child: Column(
            children: [
              // 第一行文字
              Text(
                titles[currentPage.value].split('\n')[0], // 获取第一行文字
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 36,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 10), // 文字和指示器之间的间距
              // 第二行文字
              Text(
                titles[currentPage.value].split('\n')[1], // 获取第二行文字
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 20), // 文字和指示器之间的间距
              // 小点提示（指示器）
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(imagePaths.length, (index) {
                  return GestureDetector(
                    onTap: () {
                      pageController.animateToPage(
                        index,
                        duration: const Duration(milliseconds: 500),
                        curve: Curves.easeInOut,
                      );
                    },
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 300),
                      margin: const EdgeInsets.symmetric(horizontal: 4),
                      width: currentPage.value == index ? 12 : 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: currentPage.value == index
                            ? Colors.white
                            : Colors.white.withOpacity(0.5),
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  );
                }),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
