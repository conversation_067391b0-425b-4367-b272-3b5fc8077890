import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

/// 自定义登录按钮组件
/// 提供卡片式样式和悬停效果
class AuthButtonWidget extends HookConsumerWidget {
  /// 按钮文本
  final String text;

  /// 点击回调
  final VoidCallback? onPressed;

  /// 自定义按钮高度
  final double? height;

  /// 自定义按钮宽度
  final double? width;

  /// 是否启用
  final bool enabled;

  /// 是否正在加载
  final bool isLoading;

  const AuthButtonWidget({
    super.key,
    required this.text,
    this.onPressed,
    this.height,
    this.width,
    this.enabled = true,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return MouseRegion(
      cursor: enabled && !isLoading ? SystemMouseCursors.click : SystemMouseCursors.forbidden,
      child: GestureDetector(
        onTap: enabled && !isLoading ? onPressed : null,
        child: Container(
          // 设置按钮容器的基本尺寸
          height: height ?? 56, // 默认高度56，与输入框一致
          width: width ?? double.infinity, // 默认宽度撑满父容器

          // 容器装饰样式
          decoration: BoxDecoration(
            // 渐变背景色
            color: enabled ? const Color(0xFF0C75F8) : const Color(0xFFF3F4F8),
            borderRadius: BorderRadius.circular(50),
          ),

          // 使用 Material 实现水波纹效果
          child: Material(
            color: Colors.transparent, // 透明背景
            child: InkWell(
              borderRadius: BorderRadius.circular(50),
              onTap: enabled && !isLoading ? onPressed : null,
              hoverColor: enabled
                  ? Colors.purple.shade900.withValues(alpha: 0.1) // 悬停时的颜色
                  : Colors.transparent,
              splashColor: enabled
                  ? Colors.purple.shade800.withValues(alpha: 0.2) // 点击时的水波纹颜色
                  : Colors.transparent,
              child: Center(
                child: isLoading
                    ? const CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      )
                    : Text(
                        text,
                        style: TextStyle(
                          color: enabled ? Colors.white : Colors.grey.shade600, // 文字颜色
                          fontSize: 18, // 字体大小
                          fontWeight: FontWeight.w600, // 字体粗细
                        ),
                      ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
