import 'dart:convert';
import 'dart:isolate';
import 'package:crypto/crypto.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:frontend_re/features/login/controllers/auth_controller.dart';
import 'package:frontend_re/domain/models/auth_model.dart';
import 'package:frontend_re/core/utils/ui/snack_bar_util.dart';

class CaptchaDialogWidget extends HookConsumerWidget {
  final String email;
  final Function(bool success) onVerifySuccess;

  const CaptchaDialogWidget({
    super.key,
    required this.email,
    required this.onVerifySuccess,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final captchaController = useTextEditingController();
    final isLoading = useState(false);
    final captchaData = useState<CaptchaResponse?>(null);
    final isCalculatingPoW = useState(false);

    // 获取验证码
    useEffect(() {
      Future<void> fetchCaptcha() async {
        isLoading.value = true;
        try {
          final response = await ref.read(authControllerProvider.notifier).generateCaptcha();
          captchaData.value = response;
        } catch (e) {
          if (context.mounted) {
            SnackBarUtil().showError(context, e.toString());
            Navigator.of(context).pop();
          }
        } finally {
          isLoading.value = false;
        }
      }
      
      fetchCaptcha();
      return null;
    }, []);

    // 刷新验证码
    Future<void> refreshCaptcha() async {
      isLoading.value = true;
      captchaController.clear();
      try {
        final response = await ref.read(authControllerProvider.notifier).generateCaptcha();
        captchaData.value = response;
      } catch (e) {
        if (context.mounted) {
          SnackBarUtil().showError(context, e.toString());
        }
      } finally {
        isLoading.value = false;
      }
    }

    // PoW计算（在isolate中进行）
    Future<String?> calculatePoW(String seed, String target) async {
      final receivePort = ReceivePort();
      
      await Isolate.spawn(_powWorker, {
        'sendPort': receivePort.sendPort,
        'seed': seed,
        'target': target,
      });

      await for (final message in receivePort) {
        if (message is Map) {
          // 处理最终结果
          if (message['success'] == true) {
            receivePort.close();
            return message['nonce'] as String;
          } else {
            // 失败情况
            receivePort.close();
            return null;
          }
        }
      }
      
      return null;
    }

    // 提交验证码
    Future<void> submitCaptcha() async {
      if (captchaController.text.isEmpty) {
        SnackBarUtil().showWarning(context, '请输入验证码');
        return;
      }

      if (captchaData.value == null) {
        SnackBarUtil().showError(context, '验证码数据异常，请刷新重试');
        return;
      }

      try {
        isCalculatingPoW.value = true;
        
        // 计算PoW
        final nonce = await calculatePoW(
          captchaData.value!.seed,
          captchaData.value!.target,
        );

        if (nonce == null) {
          if (context.mounted) {
            SnackBarUtil().showError(context, 'PoW计算失败，请重试');
          }
          return;
        }

        isCalculatingPoW.value = false;
        isLoading.value = true;

        // 发送注册验证邮件
        final request = SendRegisterEmailRequest(
          answer: captchaController.text,
          email: email,
          nonce: nonce,
          seed: captchaData.value!.seed,
          target: captchaData.value!.target,
        );

        final response = await ref.read(authControllerProvider.notifier).sendRegisterEmail(request);
        
        if (response.sent) {
          if (context.mounted) {
            SnackBarUtil().showSuccess(context, response.message);
          }
          // 向父组件发送验证成功信号
          onVerifySuccess(true);
          if (context.mounted) {
            Navigator.of(context).pop();
          }
        } else {
          if (context.mounted) {
            SnackBarUtil().showError(context, response.message);
          }
        }
      } catch (e) {
        if (context.mounted && e is DioException) {
          SnackBarUtil().showError(context, e.response?.data['error'] ?? e.toString());
        } else if (context.mounted) {
          SnackBarUtil().showError(context, e.toString());
        }
      } finally {
        isLoading.value = false;
        isCalculatingPoW.value = false;
      }
    }

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Container(
        width: 400,
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题
            Row(
              children: [
                const Icon(Icons.security, color: Color(0xFF7BB4FF)),
                const SizedBox(width: 8),
                const Text(
                  '安全验证',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close, color: Color(0xFF999999)),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // 说明文字
            Text(
              '为了确保账户安全，请完成以下验证：',
              style: TextStyle(fontSize: 14, color: Colors.grey[600]),
            ),
            const SizedBox(height: 20),

            // 验证码图片区域
            Container(
              width: double.infinity,
              height: 120,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey[300]!),
                borderRadius: BorderRadius.circular(8),
              ),
              child: isLoading.value || captchaData.value == null
                  ? const Center(
                      child: CircularProgressIndicator(
                        color: Color(0xFF7BB4FF),
                      ),
                    )
                  : Stack(
                      children: [
                        Center(
                          child: Image.memory(
                            base64Decode(captchaData.value!.captchaImage.split(',').last),
                            fit: BoxFit.contain,
                          ),
                        ),
                        Positioned(
                          top: 8,
                          right: 8,
                          child: IconButton(
                            onPressed: refreshCaptcha,
                            icon: const Icon(Icons.refresh, color: Color(0xFF7BB4FF)),
                            tooltip: '刷新验证码',
                          ),
                        ),
                      ],
                    ),
            ),
            const SizedBox(height: 16),

            // 验证码输入框
            TextField(
              controller: captchaController,
              decoration: const InputDecoration(
                labelText: '验证码',
                hintText: '请输入图片中的验证码',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.keyboard, color: Color(0xFF7BB4FF)),
              ),
              enabled: !isLoading.value && !isCalculatingPoW.value,
              onSubmitted: (_) => submitCaptcha(),
            ),
            const SizedBox(height: 20),

            // 进度指示器
            if (isCalculatingPoW.value)
              Column(
                children: [
                  const LinearProgressIndicator(
                    color: Color(0xFF7BB4FF),
                    backgroundColor: Color(0xFFE5F1FF),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '正在进行安全计算，请稍候...',
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
                  const SizedBox(height: 16),
                ],
              ),

            // 按钮区域
            Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text(
                      '取消',
                      style: TextStyle(color: Color(0xFF999999)),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: (isLoading.value || isCalculatingPoW.value || captchaData.value == null)
                        ? null
                        : submitCaptcha,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF7BB4FF),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: isLoading.value
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: Colors.white,
                            ),
                          )
                        : const Text('确认'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

// PoW工作线程
void _powWorker(Map<String, dynamic> params) {
  final sendPort = params['sendPort'] as SendPort;
  final seed = params['seed'] as String;
  final target = params['target'] as String;

  try {
    // 将target转换为BigInt进行比较
    final targetInt = BigInt.parse(target, radix: 16);
    
    // 尝试计算PoW，最多500万次
    for (int nonce = 0; nonce < 5000000; nonce++) {
      final input = seed + nonce.toString();
      final bytes = utf8.encode(input);
      final digest = sha256.convert(bytes);
      
      // 将hash转换为BigInt
      final hashHex = digest.toString();
      final hashInt = BigInt.parse(hashHex, radix: 16);
      
      // 如果hash小于target，则找到了有效的nonce
      if (hashInt < targetInt) {
        sendPort.send({
          'success': true,
          'nonce': nonce.toString(),
          'attempts': nonce + 1,
        });
        return;
      }
    }
    
    // 如果没有找到有效的nonce
    sendPort.send({
      'success': false,
      'attempts': 5000000,
    });
  } catch (e) {
    sendPort.send({
      'success': false,
      'error': e.toString(),
    });
  }
} 
