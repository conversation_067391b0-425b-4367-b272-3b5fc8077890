import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

/// 自定义验证输入框组件
/// 提供卡片式样式和自定义验证功能
class AuthInputCustomWidget extends HookConsumerWidget {
  /// 输入框标签文本
  final String labelText;

  /// 是否是密码输入框
  final bool isPassword;

  /// 输入框前缀图标
  final Widget? prefixIcon;

  /// 输入框后缀图标
  final Widget? suffixIcon;

  /// 输入框控制器
  final TextEditingController? controller;

  /// 自定义验证函数
  final String? Function(String?)? validator;

  /// 输入内容变化回调
  final Function(String)? onChanged;

  /// 输入框获取焦点回调
  final Function()? onFocus;

  /// 输入框失去焦点回调
  final Function()? onBlur;

  /// 键盘类型
  final TextInputType? keyboardType;

  /// 最大输入长度
  final int? maxLength;

  /// 自定义输入格式
  final List<TextInputFormatter>? inputFormatters;

  /// 是否自动获取焦点
  final bool autofocus;

  /// 是否启用输入框
  final bool enabled;

  /// 自定义输入框高度
  final double? height;

  /// 自定义输入框宽度
  final double? width;

  const AuthInputCustomWidget({
    super.key,
    required this.labelText,
    this.isPassword = false,
    this.prefixIcon,
    this.suffixIcon,
    this.controller,
    this.validator,
    this.onChanged,
    this.onFocus,
    this.onBlur,
    this.keyboardType,
    this.maxLength,
    this.inputFormatters,
    this.autofocus = false,
    this.enabled = true,
    this.height = 50,
    this.width = double.infinity,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 使用 useState 管理输入框焦点状态
    final isFocused = useState(false);
    // 使用 useState 管理密码显示状态
    final isPasswordVisible = useState(false);
    // 创建焦点节点
    final focusNode = useFocusNode();

    // 监听焦点变化
    useEffect(() {
      void listener() {
        isFocused.value = focusNode.hasFocus;
        if (focusNode.hasFocus) {
          onFocus?.call();
        } else {
          onBlur?.call();
        }
      }

      focusNode.addListener(listener);
      // 只移除监听器，不要dispose focusNode
      return () => focusNode.removeListener(listener);
    }, [focusNode]);

    return Container(
      // 设置输入框容器的基本尺寸
      height: height, // 默认高度56
      width: width, // 默认宽度撑满父容器

      // 容器装饰样式
      decoration: BoxDecoration(
        color: const Color(0xFFF3F4F8), // 白色背景
        borderRadius: BorderRadius.circular(50),
      ),

      child: Center(
        child: TextFormField(
          // 输入框基本配置
          controller: controller, // 控制器
          focusNode: focusNode, // 焦点节点
          obscureText: isPassword && !isPasswordVisible.value, // 密码模式时遮罩文字
          keyboardType: keyboardType, // 键盘类型
          maxLength: maxLength, // 最大输入长度
          inputFormatters: inputFormatters, // 输入格式化
          autofocus: autofocus, // 自动获取焦点
          enabled: enabled, // 是否可用

          // 输入文字样式
          style: const TextStyle(
            fontSize: 14, // 字体大小16
            color: Colors.black87, // 文字颜色，87%黑
          ),

          // 输入框装饰
          decoration: InputDecoration(
            floatingLabelBehavior: FloatingLabelBehavior.never, // 禁止浮动提示文本
            hoverColor: Colors.transparent, // 去除焦点的颜色
            labelText: labelText, // 标签文本
            labelStyle: const TextStyle(color: Color(0xFFBDBCC1)),// 标签文本颜色
            hintStyle: const TextStyle(color: Color(0xFFBDBCC1)),// 提示文本颜色
            hintFadeDuration: const Duration(milliseconds: 200), // 提示文本淡入淡出动画持续时间
            counterText: '', // 隐藏字符计数器
            iconColor: Colors.purple, // 图标颜色

            // 前缀图标
            prefixIcon: Padding(
              padding: const EdgeInsets.all(16.0),
              child: prefixIcon,
            ), // 前缀图标
            prefixIconConstraints: const BoxConstraints(
              minWidth: 60,
              minHeight: 60,
            ),

            // 后缀图标，密码模式显示切换按钮，否则显示自定义后缀
            suffixIcon: isPassword
                ? IconButton(
                    icon: Icon(
                      isPasswordVisible.value
                          ? Icons.visibility // 显示密码图标
                          : Icons.visibility_off, // 隐藏密码图标
                      color: const Color(0xFF7BB4FF), // 图标颜色
                    ),
                    onPressed: () {
                      isPasswordVisible.value = !isPasswordVisible.value;
                    },
                  )
                : suffixIcon,
            suffixIconConstraints: const BoxConstraints(
              minWidth: 60,
              minHeight: 60,
            ),
            // 内容内边距
            contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16), // 内容内边距
            isDense: true, // 减少垂直间距
            // 默认边框样式
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16), // 16像素圆角
              borderSide: BorderSide.none, // 无边框
            ),
            // 未获取焦点时的边框样式
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: BorderSide.none,
            ),
            // 获取焦点时的边框样式
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: BorderSide.none,
            ),

            filled: true, // 启用填充
            fillColor: Colors.transparent, // 填充颜色透明
          ),
          onChanged: onChanged, // 内容变化回调
        ),
      ),
    );
  }
}
