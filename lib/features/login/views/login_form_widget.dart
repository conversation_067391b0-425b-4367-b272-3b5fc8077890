import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:frontend_re/features/login/controllers/login_index_notifier.dart';
import 'package:frontend_re/features/login/controllers/auth_controller.dart';
import 'package:frontend_re/core/storage/auth_storage.dart';
import 'package:frontend_re/features/login/views/widgets/auth_button_widget.dart';
import 'package:frontend_re/features/login/views/widgets/auth_input_custom_widget.dart';
import 'package:frontend_re/core/utils/ui/snack_bar_util.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';


class LoginFormWidget extends HookConsumerWidget {
  const LoginFormWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isAutoLogin = useState(false);

    // 创建控制器
    final identifierController = useTextEditingController();
    final passwordController = useTextEditingController();

    // 创建一个表单 key
    final formKey = useMemoized(() => GlobalKey<FormState>());

    ref.listen<AsyncValue>(authControllerProvider, (previous, next) {
      // 只在状态从加载变为数据或错误时显示提示
      if (previous?.isLoading == true && !next.isLoading) {
        next.when(
          data: (res) {
            if (res != null) {
              SnackBarUtil().showSuccess(context, res);
            }
          }, 
          error: (error, stack) {
            SnackBarUtil().showError(context, error.toString());
          }, 
          loading: () {}
        );
      }
    });

    // 首次构建时，获取自动登录状态
    useEffect(() {
      AuthStorage.getAutoLogin().then((value) {
        isAutoLogin.value = value ?? false;
      });
      return null;
    }, []);

    return Form(
      key: formKey,
      child: SizedBox(
        width: 340,
        height: 500,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('欢迎登录', style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),),
            const SizedBox(height: 40),
            // 用户输入框
            const Text('邮箱或手机号', style: TextStyle(fontSize: 14, color: Color(0xFF8D8E93)),),
            const SizedBox(height: 10),
            AuthInputCustomWidget(
              controller: identifierController,
              labelText: '用户名/手机号/邮箱',
              prefixIcon: SvgPicture.asset('assets/svg/user_login.svg'),
              maxLength: 30,
            ),
            const SizedBox(height: 20),
            // 密码输入框
            const Text('密码', style: TextStyle(fontSize: 14, color: Color(0xFF8D8E93)),),
            const SizedBox(height: 10),
            AuthInputCustomWidget(
              controller: passwordController,
              labelText: '密码',
              isPassword: true,
              prefixIcon: SvgPicture.asset('assets/svg/pwd_login.svg'),
            ),
            const SizedBox(height: 20),
            const SizedBox(height: 20),
            // 登录按钮
            AuthButtonWidget(
              text: '登录',
              isLoading: ref.watch(authControllerProvider).isLoading,
              onPressed: () {
                ref.read(authControllerProvider.notifier).login(identifierController.text, passwordController.text);
              },
            ),
            const SizedBox(height: 20),
            // 注册
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text('没有PrismBrowse账户？', style: TextStyle(fontSize: 14, color: Color(0xFF8D8E93)),),
                TextButton(onPressed: () {
                  ref.read(loginIndexNotifierProvider.notifier).setIndex(1);
                }, child: const Text('立即注册', style: TextStyle(fontSize: 14, color: Color(0xFF7BB4FF)),),),
              ],
            )
          ],
        ),
      ),
    );
  }
}
