import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:frontend_re/core/utils/ui/window_manager_util.dart';
import 'package:frontend_re/features/login/controllers/login_index_notifier.dart';
import 'package:frontend_re/domain/services/locale_service.dart';
import 'package:frontend_re/features/login/views/login_form_widget.dart';
import 'package:frontend_re/features/login/views/register_form_widget.dart';
import 'package:frontend_re/features/login/views/widgets/image_carousel.dart';
import 'package:frontend_re/widgets/custom_icon_text_button.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:window_manager/window_manager.dart';

class LoginScreen extends HookConsumerWidget {
  const LoginScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 添加初始化代码
    useEffect(() {
      // 在 Mac 上隐藏红绿灯按钮
      windowManager.setTitleBarStyle(TitleBarStyle.hidden);
      return null;
    }, []);

    final loginIndex = ref.watch(loginIndexNotifierProvider);

    // 使用 DragToMoveArea 使整个窗口可拖动
    return Scaffold(
      body: Stack(
        children: [
          Row(
            children: [
              // 左边轮播图
              const Expanded(
                flex: 1, // 设置flex为1，表示平分空间
                child: ImageCarousel(
                  imagePaths: [
                    'assets/images/login/bk1.png',
                    'assets/images/login/bk2.png',
                    'assets/images/login/bk3.png',
                  ],
                  titles: [
                    '隐身无痕 指纹清零\n访问自动生成虚拟身份，真实信息0残留。',
                    '一屏百面 分身无界\n独立浏览器环境隔离，多账号同步操作不关联。',
                    '密钥隐形 登录无痕\n动态指纹加密技术，杜绝账号盗用风险',
                  ],
                ),
              ),
              // 右边 登录注册表单
              Expanded(
                flex: 1, // 设置flex为1，表示平分空间
                child: Container(
                  decoration: BoxDecoration(
                    color: const Color.fromARGB(255, 255, 255, 255).withValues(alpha: 1),
                    borderRadius: BorderRadius.circular(0),
                  ),
                  child: Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(top: 40, bottom: 0, left: 20, right: 20),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            // logo
                            Row(
                              spacing: 10,
                              children: [
                                SvgPicture.asset(
                                  'assets/svg/logo.svg',
                                  width: 26,
                                  height: 26,
                                ),
                                const Text('PrismBrowse', style: TextStyle(fontSize: 26, fontWeight: FontWeight.bold)),
                              ],
                            ),
                            // 按钮
                            Row(
                              children: [
                                // 帮助中心
                                CustomIconTextButton(
                                  label: '帮助中心',
                                  iconPath: 'assets/svg/help.svg',
                                  onPressed: () {
                                    // 处理帮助中心按钮点击事件
                                  },
                                ),
                                // 语言切换
                                // Consumer(
                                //   builder: (context, ref, child) {
                                //     final locale = ref.watch(localeServiceProvider);

                                //     return PopupMenuButton<String>(
                                //       icon: Row(
                                //         children: [
                                //           SvgPicture.asset(
                                //             'assets/svg/local.svg',
                                //             width: 20,
                                //             height: 20,
                                //           ),
                                //           const SizedBox(width: 8), // 添加间距
                                //           Text(locale.value?.languageCode.toUpperCase() ?? 'ZH', style: const TextStyle(color: Color(0xFF8D8E93)),),
                                //         ],
                                //       ),
                                //       tooltip: '',
                                //       onSelected: (String value) {
                                //         // 处理语言切换
                                //         ref.read(localeServiceProvider.notifier).changeLanguage(value);
                                //       },
                                //       itemBuilder: (BuildContext context) {
                                //         final supportedLanguages = ref.read(localeServiceProvider.notifier).getSupportedLanguages();
                                //         return supportedLanguages.entries.map((entry) => PopupMenuItem<String>(
                                //           value: entry.key.languageCode,
                                //           child: Text(entry.value),
                                //         )).toList();
                                //       },
                                //     );
                                //   }
                                // ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      Expanded(
                        child: Center(
                          child: switch (loginIndex) {
                            0 => const LoginFormWidget(),
                            1 => const RegisterFormWidget(),
                            // 2 => const RegisterFormWidget(),
                            _ => const SizedBox.shrink(),
                          },
                        ),
                      )
                    ],
                  ),
                ),
              ),
            ],
          ),
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: GestureDetector(
              onDoubleTap: () async {
                await WindowManagerUtil.handleTitleBarDoubleClick();
              },
              onPanStart: (details) {
                windowManager.startDragging();
              },
              child: _buildTitleBar(),
            ),
          ),
        ],
      ),
    );
  }

    /// 构建标题栏
  Widget _buildTitleBar() {
    return Container(
      height: 24,
      color: Colors.transparent,
      child: Row(
        children: [
          // 中间可拖拽区域
          const Expanded(
            child: SizedBox(),
          ),
                     // 版本号
           const Text('v0.1.0-alpha', style: TextStyle(fontSize: 12, color: Color(0xFF8C8C8C))),
           const SizedBox(width: 20),
          // 右侧窗口控制按钮
          Platform.isWindows ? _buildWindowControls() : const SizedBox.shrink(),
        ],
      ),
    );
  }

    /// 构建窗口控制按钮组
  Widget _buildWindowControls() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildWindowButton(
          icon: Icons.remove,
          onPressed: () => WindowManagerUtil().minimize(),
          hoverColor: const Color(0x1A000000),
        ),
        _buildWindowButton(
          icon: Icons.crop_square,
          onPressed: () => WindowManagerUtil.handleTitleBarDoubleClick(),
          hoverColor: const Color(0x1A000000),
        ),
        _buildWindowButton(
          icon: Icons.close,
          onPressed: () async {
            // 在关闭前保存窗口状态
            await WindowManagerUtil.saveCurrentWindowState();
            await WindowManagerUtil.close();
          },
          hoverColor: const Color.fromARGB(255, 255, 116, 127),
          iconColor: Colors.black,
        ),
      ],
    );
  }

    /// 构建单个窗口控制按钮
  Widget _buildWindowButton({
    required IconData icon,
    required VoidCallback onPressed,
    required Color hoverColor,
    Color? iconColor,
    Offset? iconOffset,
  }) {
    return SizedBox(
      width: 46,
      height: 24,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          hoverColor: hoverColor,
          child: Center(
            child: Transform.translate(
              offset: iconOffset ?? Offset.zero,
              child: Icon(
                icon,
                size: 10,
                color: iconColor ?? const Color(0xFF000000),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
