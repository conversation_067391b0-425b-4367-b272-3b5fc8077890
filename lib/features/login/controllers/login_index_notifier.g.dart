// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'login_index_notifier.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$loginIndexNotifierHash() =>
    r'2ec38dba427151ec03df2f8ef12d77af7cb6a4cb';

/// See also [LoginIndexNotifier].
@ProviderFor(LoginIndexNotifier)
final loginIndexNotifierProvider =
    AutoDisposeNotifierProvider<LoginIndexNotifier, int>.internal(
  LoginIndexNotifier.new,
  name: r'loginIndexNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$loginIndexNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$LoginIndexNotifier = AutoDisposeNotifier<int>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
