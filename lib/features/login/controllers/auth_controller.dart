import 'package:dio/dio.dart';
import 'package:frontend_re/domain/services/auth_service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:frontend_re/domain/models/auth_model.dart';
import 'package:frontend_re/router/go_router_provider.dart';
import 'package:frontend_re/core/providers/auth_state_provider.dart';

part 'auth_controller.g.dart';

class LoginModel {
  String? message;
  String? token;
  User? user;

  LoginModel({this.message, this.token, this.user});
}

@riverpod
class AuthController extends _$AuthController {
  @override
  Future<String?> build() async {
    // 初始化为null，不获取任何用户信息
    return null;
  }

  /// 用户登录
  Future<String> login(String identifier, String password) async {
    state = const AsyncValue.loading();

    try {
      final repository = ref.read(authServiceProvider.notifier);
      final request = LoginRequest(
        identifier: identifier,
        password: password,
      );
      
      final response = await repository.login(request);
      
      // 更新认证状态
      final authState = ref.read(authStateProvider.notifier);
      authState.setAuthenticated(true);
      
      // 登录成功，更新状态并跳转
      state = AsyncValue.data(response.message);
      
      // 跳转到工作台
      final goRouter = ref.read(goRouterProvider);
      goRouter.goNamed('workbench');
      
      return response.message;
    } on DioException catch (e) {
      String errorMessage = '登录失败';
      if (e.error != null) {
        errorMessage = e.error.toString();
      }
      state = AsyncValue.error(errorMessage, StackTrace.current);
      return errorMessage;
    } catch (e) {
      String errorMessage = '登录失败，请稍后重试';

      // 如果是Service的业务异常，使用异常消息
      if (e is Exception) {
        errorMessage = e.toString().replaceFirst('Exception: ', '');
      }

      state = AsyncValue.error(errorMessage, StackTrace.current);
      return errorMessage;
    }
  }

  /// 用户注册
  Future<String> register({
    required String username,
    required String password,
    String? email,
    String? phone,
    String? emailCode,
    String? inviteCode,
  }) async {
    state = const AsyncValue.loading();

    try {
      final repository = ref.read(authServiceProvider.notifier);
      final request = RegisterRequest(
        username: username,
        password: password,
        email: email,
        telephone: phone,
        emailCode: emailCode,
        inviteCode: inviteCode,
      );
      
      // 执行注册
      final response = await repository.register(request);
      
      // 注册成功后自动登录
      final loginRequest = LoginRequest(
        identifier: username,
        password: password,
      );
      
      await repository.login(loginRequest);
      
      // 更新认证状态
      final authState = ref.read(authStateProvider.notifier);
      authState.setAuthenticated(true);
      
      state = AsyncValue.data(response.message);
      
      // 跳转到工作台
      final goRouter = ref.read(goRouterProvider);
      goRouter.goNamed('workbench');
      
      return response.message;
    } on DioException catch (e) {
      String errorMessage = '注册失败';
      // 根据记忆，应该从error字段获取错误信息
      if (e.error != null) {
        errorMessage = e.error.toString();
      } else if (e.response?.data != null && e.response!.data['message'] != null) {
        errorMessage = e.response!.data['message'];
      } else if (e.message != null) {
        errorMessage = e.message!;
      }
      state = AsyncValue.error(errorMessage, StackTrace.current);
      return errorMessage;
    } catch (e) {
      String errorMessage = '注册失败，请稍后重试';

      // 如果是Service的业务异常，使用异常消息
      if (e is Exception) {
        errorMessage = e.toString().replaceFirst('Exception: ', '');
      }

      state = AsyncValue.error(errorMessage, StackTrace.current);
      return errorMessage;
    }
  }
  
  /// 生成验证码
  Future<CaptchaResponse> generateCaptcha() async {
    try {
      final authService = ref.read(authServiceProvider.notifier);
      return await authService.generateCaptcha();
    } on DioException catch (e) {
      String errorMessage = '获取验证码失败';
      if (e.response?.data != null && e.response!.data['message'] != null) {
        errorMessage = e.response!.data['message'];
      } else if (e.message != null) {
        errorMessage = e.message!;
      }
      throw Exception(errorMessage);
    } catch (e) {
      String errorMessage = '获取验证码失败，请稍后重试';
      if (e is Exception) {
        errorMessage = e.toString().replaceFirst('Exception: ', '');
      }
      throw Exception(errorMessage);
    }
  }

  /// 发送注册验证邮件
  Future<SendRegisterEmailResponse> sendRegisterEmail(SendRegisterEmailRequest request) async {
    final repository = ref.read(authServiceProvider.notifier);
    final response = await repository.sendRegisterEmail(request);
    return response;
  }
} 
