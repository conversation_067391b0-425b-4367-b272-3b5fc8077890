import 'package:flutter/material.dart';
import 'package:frontend_re/features/proxy/proxy_platform/views/components/platform_proxy_table.dart';
import 'package:frontend_re/widgets/custom_border_button.dart';
import 'package:frontend_re/widgets/custom_solid_button.dart';

class PlatformProxyPage extends StatelessWidget {
  const PlatformProxyPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        spacing: 16,
        children: [
          // const ProxyTapBtn(),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // 左 筛选 搜索 删除
              Row(
                children: [
                  CustomSolidButton(
                    iconPath: 'assets/svg/screening.svg',
                    backgroundColor: const Color(0xFFFFFFFF),
                    iconColor: const Color(0xFFFFFFFF),
                    iconBackgroundColor: const Color(0xFF1E1C1D),
                    iconSize: 14,
                    textColor: const Color(0xFF8D8E93),
                    text: '筛选',
                    // textDirection: TextDirection.rtl,
                    onPressed: () {
                      // 使用路径导航，更直观
                      //context.push('/addBrowser');
                    },
                  ),
                  // CustomBorderButton(
                  //   borderColor: const Color(0xFFFFFFFF),
                  //   iconPath: 'assets/svg/delete.svg',
                  //   backgroundColor: const Color(0xFFFFFFFF),
                  //   iconColor: const Color(0xFF8D8E93),
                  //   horizontalPadding: 12,
                  //   verticalPadding: 12,
                  //   iconSize: 20,
                  //   onPressed: () {
                  //     print('快捷创建');
                  //   },
                  // ),
                ],
              ),
              // 右 表单功能按钮 设置和刷新
              Row(
                children: [
                  CustomBorderButton(
                    borderColor: const Color(0xFFFFFFFF),
                    iconPath: 'assets/svg/setting_btn.svg',
                    backgroundColor: const Color(0xFFFFFFFF),
                    iconColor: const Color(0xFF8D8E93),
                    horizontalPadding: 12,
                    verticalPadding: 12,
                    iconSize: 20,
                    onPressed: () {
                      print('快捷创建');
                    },
                  ),
                  CustomBorderButton(
                    borderColor: const Color(0xFFFFFFFF),
                    iconPath: 'assets/svg/refresh.svg',
                    backgroundColor: const Color(0xFFFFFFFF),
                    iconColor: const Color(0xFF8D8E93),
                    horizontalPadding: 12,
                    verticalPadding: 12,
                    iconSize: 20,
                    onPressed: () {
                      print('快捷创建');
                    },
                  ),
                ],
              )
            ],
          ),
          const Expanded(child: PlatformProxyTable())
        ],
      ),
    );
  }
}
