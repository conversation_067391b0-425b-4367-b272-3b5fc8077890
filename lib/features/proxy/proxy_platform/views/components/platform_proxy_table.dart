import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:frontend_re/features/proxy/proxy_platform/controllers/proxy_platform_controller.dart';
import 'package:frontend_re/widgets/data_table_widget.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class PlatformProxyTable extends HookConsumerWidget {
  const PlatformProxyTable({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 监听代理数据状态
    final proxyState = ref.watch(platformProxyControllerProvider);

    return Scaffold(
      backgroundColor: Colors.transparent, // 设置背景色
      body: LayoutBuilder(
        builder: (context, constraints) {
          return Column(
            children: [
              Expanded(
                child: proxyState.when(
                  data: (proxies) => DataTableWidget(
                    headingTextStyle: const TextStyle(fontSize: 14, color: Color(0xFF333333)),
                    dataTextStyle: const TextStyle(fontSize: 14, color: Color(0xFF8D8E93)),
                    minWidth: 100,
                    showCheckbox: true,
                    columnSpacing: 0,
                    // onSelectionChanged: (selectedRows) {
                    //   // selectedRows 是选中行的索引集合 (Set<int>)
                    //   final selectedIds = selectedRows
                    //       .where((index) => index < proxies.proxies.length) // 确保索引在范围内
                    //       .map((index) => proxies.proxies[index].id)
                    //       .toList();
                    // },
                    enablePagination: true,
                    totalCount: proxies.total,
                    pageSize: 10,
                    currentPage: 1,
                    onPageChanged: (page, pageSize) {
                      // controller.getProxies(
                      //   ProxyListRequest(
                      //     offset: (page - 1) * pageSize,
                      //     limit: pageSize,
                      //   ),
                      // );
                    },
                    noDataImagePath: 'assets/images/proxy/NoData.png',
                    noDataText: '暂无代理数据',
                    // noDataButtonText: '添加平台代理',
                    // noDataButtonEvent: () {
                    // 
                    // },
                    columns: const [
                      DataColumn2(label: Text('名称'), size: ColumnSize.S),
                      DataColumn2(label: Text('IP地址'), size: ColumnSize.L),
                      DataColumn2(label: Text('环境'), size: ColumnSize.S),
                      DataColumn2(label: Text('地区'), size: ColumnSize.S),
                      DataColumn2(label: Text('是否无限制'), size: ColumnSize.S),
                      DataColumn2(label: Text('是否自动续期'), size: ColumnSize.S),
                      DataColumn2(label: Text('过期时间'), size: ColumnSize.L),
                    ],
                    data: proxies.proxies,
                    rowBuilder: (proxy, index, isHovered) {
                      return DataRow2(cells: [
                        DataCell(Text(proxy.name)),
                        DataCell(Text(proxy.ipAddress)),
                        DataCell(Text(proxy.environment)),
                        DataCell(Text(proxy.region)),
                        DataCell(Text(proxy.isUnlimited ? '是' : '否')),
                        DataCell(Text(proxy.autoRenew ? '是' : '否')),
                        DataCell(Text(proxy.expiresAt)),
                      ]);
                    },
                  ),
                  loading: () => const Center(
                    child: CircularProgressIndicator(),
                  ),
                  error: (error, stack) => Center(
                    child: Text('加载失败: $error'),
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
