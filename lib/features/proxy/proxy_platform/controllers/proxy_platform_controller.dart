import 'package:frontend_re/domain/models/proxy_model.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';


part 'proxy_platform_controller.g.dart';

// 平台代理状态
@Riverpod(keepAlive: false)
class PlatformProxyController extends _$PlatformProxyController {
  @override
  FutureOr<PlatformProxyModel> build() {
    // 初始化时加载代理列表
    // return getProxies(const ProxyListRequest(limit: 20, offset: 0));
    return const PlatformProxyModel(proxies: [], total: 0);
  }

  /// 获取代理列表
  // Future<PlatformProxyModel> getProxies(ProxyListRequest request) async {
  //   state = const AsyncValue.loading();
  //   try {
  //     final repository = ref.read(proxyPlatformServiceProvider.notifier);
  //     final proxies = await repository.getPlatformProxies(request);
  //     state = AsyncValue.data(proxies);
  //     return proxies;
  //   } catch (error, stackTrace) {
  //     state = AsyncValue.error(error, stackTrace);
  //     rethrow;
  //   }
  // }
}
