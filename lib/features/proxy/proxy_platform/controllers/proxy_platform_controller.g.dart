// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'proxy_platform_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$platformProxyControllerHash() =>
    r'f3afd2f4a77f6be9bbb304970c56ff1e3c272c7d';

/// See also [PlatformProxyController].
@ProviderFor(PlatformProxyController)
final platformProxyControllerProvider = AutoDisposeAsyncNotifierProvider<
    PlatformProxyController, PlatformProxyModel>.internal(
  PlatformProxyController.new,
  name: r'platformProxyControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$platformProxyControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$PlatformProxyController
    = AutoDisposeAsyncNotifier<PlatformProxyModel>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
