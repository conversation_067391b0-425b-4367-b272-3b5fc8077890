import 'package:flutter/material.dart';
import 'package:frontend_re/features/proxy/views/components/proxy_tapbtn.dart';
// import 'package:go_router/go_router.dart'; // 不再需要导入 go_router

class ProxyIndex extends StatelessWidget {
  // 将 child 改为必需参数，并移除 navigationShell
  const ProxyIndex({super.key, required this.child});
  
  final Widget child;
  // final StatefulNavigationShell? navigationShell; // 移除 navigationShell

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: const EdgeInsets.only(bottom: 0),
        child: Column(
          spacing: 16,
          children: [
            const ProxyTapBtn(),
            // 使用传入的 child 参数
            Expanded(child: child),
          ],
        ),
      )
    );
  }
}
