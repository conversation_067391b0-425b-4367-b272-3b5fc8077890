import 'package:flutter/material.dart';
import 'package:frontend_re/core/utils/ui/snack_bar_util.dart';
import 'package:frontend_re/domain/services/proxy_service.dart';
import 'package:frontend_re/widgets/custom_solid_button.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class ProxyTapBtn extends HookConsumerWidget {
  const ProxyTapBtn({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final index = ref.watch(proxyPageIndexProvider);
    return Row(
      spacing: 16,
      children: [
        // 平台代理按钮
        CustomSolidButton(
          iconPath: 'assets/svg/platform_proxy.svg',
          backgroundColor: index == 0 ? const Color(0xFF1E1C1D) : const Color(0xFFFFFFFF),
          iconColor: index == 0 ? const Color(0xFF1E1C1D) : const Color(0xFFFFFFFF),
          iconBackgroundColor: index == 0 ? const Color(0xFFFFFFFF) : const Color(0xFF1E1C1D),
          iconSize: 14,
          textColor: index == 0 ? const Color(0xFFFFFFFF) : const Color(0xFF8D8E93),
          text: '平台代理',
          onPressed: () {
            ref.read(proxyPageIndexProvider.notifier).setIndex(0);
            // 使用路径导航，更直观
            context.goNamed('proxy');
          },
        ),
        // 自有代理代理按钮
        CustomSolidButton(
          iconPath: 'assets/svg/self_proxy.svg',
          backgroundColor: index == 1 ? const Color(0xFF1E1C1D) : const Color(0xFFFFFFFF),
          iconColor: index == 1 ? const Color(0xFF1E1C1D) : const Color(0xFFFFFFFF),
          iconBackgroundColor: index == 1 ? const Color(0xFFFFFFFF) : const Color(0xFF1E1C1D),
          iconSize: 14,
          textColor: index == 1 ? const Color(0xFFFFFFFF) : const Color(0xFF8D8E93),
          text: '自有代理',
          // textDirection: TextDirection.rtl,
          onPressed: () {
            // 使用路径导航，更直观
            // context.push('/addBrowser');
            ref.read(proxyPageIndexProvider.notifier).setIndex(1);
            context.goNamed('selfProxy');
          },
        ),
        // 代理API按钮
        CustomSolidButton(
          iconPath: 'assets/svg/proxy_api.svg',
          backgroundColor: index == 2 ? const Color(0xFF1E1C1D) : const Color(0xFFFFFFFF),
          iconColor: index == 2 ? const Color(0xFF1E1C1D) : const Color(0xFFFFFFFF),
          iconBackgroundColor: index == 2 ? const Color(0xFFFFFFFF) : const Color(0xFF1E1C1D),
          iconSize: 14,
          textColor: index == 2 ? const Color(0xFFFFFFFF) : const Color(0xFF8D8E93),
          text: '代理API',
          // textDirection: TextDirection.rtl,
          onPressed: () {
            // 使用路径导航，更直观
            // context.push('/addBrowser');
            // ref.read(proxyPageIndexProvider.notifier).setIndex(2);
            // context.goNamed('proxyApi');
            SnackBarUtil().showInfo(context, '暂未开放');
          },
        ),
        // 购买代理按钮
        CustomSolidButton(
          iconPath: 'assets/svg/buy_proxy.svg',
          backgroundColor: index == 3 ? const Color(0xFF1E1C1D) : const Color(0xFFFFFFFF),
          iconColor: index == 3 ? const Color(0xFF1E1C1D) : const Color(0xFFFFFFFF),
          iconBackgroundColor: index == 3 ? const Color(0xFFFFFFFF) : const Color(0xFF1E1C1D),
          iconSize: 14,
          textColor: index == 3 ? const Color(0xFFFFFFFF) : const Color(0xFF8D8E93),
          text: '购买代理',
          // textDirection: TextDirection.rtl,
          onPressed: () {
            // 使用路径导航，更直观
            // context.push('/addBrowser');
            // ref.read(proxyPageIndexProvider.notifier).setIndex(3);
            // context.goNamed('buyProxy');
            SnackBarUtil().showInfo(context, '暂未开放');
          },
        ),
        // 购物车
        CustomSolidButton(
          iconPath: 'assets/svg/shopping_cart.svg',
          backgroundColor: index == 4 ? const Color(0xFF1E1C1D) : const Color(0xFFFFFFFF),
          iconColor: index == 4 ? const Color(0xFF1E1C1D) : const Color(0xFFFFFFFF),
          iconBackgroundColor: index == 4 ? const Color(0xFFFFFFFF) : const Color(0xFF1E1C1D),
          iconSize: 14,
          textColor: index == 4 ? const Color(0xFFFFFFFF) : const Color(0xFF8D8E93),
          text: '购物车',
          // textDirection: TextDirection.rtl,
          onPressed: () {
            // 使用路径导航，更直观
            // context.push('/addBrowser');
            // ref.read(proxyPageIndexProvider.notifier).setIndex(4);
            // context.goNamed('cart');
            SnackBarUtil().showInfo(context, '暂未开放');
          },
        ),
      ],
    );
  }
}
