// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'proxy_select_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ProxySelectModel _$ProxySelectModelFromJson(Map<String, dynamic> json) {
  return _ProxySelectModel.fromJson(json);
}

/// @nodoc
mixin _$ProxySelectModel {
  List<SelfProxyItem> get proxies => throw _privateConstructorUsedError;
  List<int> get selectedProxyIds => throw _privateConstructorUsedError;

  /// Serializes this ProxySelectModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProxySelectModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProxySelectModelCopyWith<ProxySelectModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProxySelectModelCopyWith<$Res> {
  factory $ProxySelectModelCopyWith(
          ProxySelectModel value, $Res Function(ProxySelectModel) then) =
      _$ProxySelectModelCopyWithImpl<$Res, ProxySelectModel>;
  @useResult
  $Res call({List<SelfProxyItem> proxies, List<int> selectedProxyIds});
}

/// @nodoc
class _$ProxySelectModelCopyWithImpl<$Res, $Val extends ProxySelectModel>
    implements $ProxySelectModelCopyWith<$Res> {
  _$ProxySelectModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProxySelectModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? proxies = null,
    Object? selectedProxyIds = null,
  }) {
    return _then(_value.copyWith(
      proxies: null == proxies
          ? _value.proxies
          : proxies // ignore: cast_nullable_to_non_nullable
              as List<SelfProxyItem>,
      selectedProxyIds: null == selectedProxyIds
          ? _value.selectedProxyIds
          : selectedProxyIds // ignore: cast_nullable_to_non_nullable
              as List<int>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProxySelectModelImplCopyWith<$Res>
    implements $ProxySelectModelCopyWith<$Res> {
  factory _$$ProxySelectModelImplCopyWith(_$ProxySelectModelImpl value,
          $Res Function(_$ProxySelectModelImpl) then) =
      __$$ProxySelectModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<SelfProxyItem> proxies, List<int> selectedProxyIds});
}

/// @nodoc
class __$$ProxySelectModelImplCopyWithImpl<$Res>
    extends _$ProxySelectModelCopyWithImpl<$Res, _$ProxySelectModelImpl>
    implements _$$ProxySelectModelImplCopyWith<$Res> {
  __$$ProxySelectModelImplCopyWithImpl(_$ProxySelectModelImpl _value,
      $Res Function(_$ProxySelectModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProxySelectModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? proxies = null,
    Object? selectedProxyIds = null,
  }) {
    return _then(_$ProxySelectModelImpl(
      proxies: null == proxies
          ? _value._proxies
          : proxies // ignore: cast_nullable_to_non_nullable
              as List<SelfProxyItem>,
      selectedProxyIds: null == selectedProxyIds
          ? _value._selectedProxyIds
          : selectedProxyIds // ignore: cast_nullable_to_non_nullable
              as List<int>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ProxySelectModelImpl implements _ProxySelectModel {
  const _$ProxySelectModelImpl(
      {required final List<SelfProxyItem> proxies,
      final List<int> selectedProxyIds = const []})
      : _proxies = proxies,
        _selectedProxyIds = selectedProxyIds;

  factory _$ProxySelectModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProxySelectModelImplFromJson(json);

  final List<SelfProxyItem> _proxies;
  @override
  List<SelfProxyItem> get proxies {
    if (_proxies is EqualUnmodifiableListView) return _proxies;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_proxies);
  }

  final List<int> _selectedProxyIds;
  @override
  @JsonKey()
  List<int> get selectedProxyIds {
    if (_selectedProxyIds is EqualUnmodifiableListView)
      return _selectedProxyIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_selectedProxyIds);
  }

  @override
  String toString() {
    return 'ProxySelectModel(proxies: $proxies, selectedProxyIds: $selectedProxyIds)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProxySelectModelImpl &&
            const DeepCollectionEquality().equals(other._proxies, _proxies) &&
            const DeepCollectionEquality()
                .equals(other._selectedProxyIds, _selectedProxyIds));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_proxies),
      const DeepCollectionEquality().hash(_selectedProxyIds));

  /// Create a copy of ProxySelectModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProxySelectModelImplCopyWith<_$ProxySelectModelImpl> get copyWith =>
      __$$ProxySelectModelImplCopyWithImpl<_$ProxySelectModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProxySelectModelImplToJson(
      this,
    );
  }
}

abstract class _ProxySelectModel implements ProxySelectModel {
  const factory _ProxySelectModel(
      {required final List<SelfProxyItem> proxies,
      final List<int> selectedProxyIds}) = _$ProxySelectModelImpl;

  factory _ProxySelectModel.fromJson(Map<String, dynamic> json) =
      _$ProxySelectModelImpl.fromJson;

  @override
  List<SelfProxyItem> get proxies;
  @override
  List<int> get selectedProxyIds;

  /// Create a copy of ProxySelectModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProxySelectModelImplCopyWith<_$ProxySelectModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
