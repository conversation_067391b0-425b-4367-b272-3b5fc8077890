import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:frontend_re/domain/models/proxy_model.dart';

part 'proxy_select_model.freezed.dart';
part 'proxy_select_model.g.dart';

@freezed
class ProxySelectModel with _$ProxySelectModel {
  const factory ProxySelectModel({
    required List<SelfProxyItem> proxies,
    @Default([]) List<int> selectedProxyIds,
  }) = _ProxySelectModel;

  factory ProxySelectModel.fromJson(Map<String, dynamic> json) => _$ProxySelectModelFromJson(json);
} 
