// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'proxy_select_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ProxySelectModelImpl _$$ProxySelectModelImplFromJson(
        Map<String, dynamic> json) =>
    _$ProxySelectModelImpl(
      proxies: (json['proxies'] as List<dynamic>)
          .map((e) => SelfProxyItem.fromJson(e as Map<String, dynamic>))
          .toList(),
      selectedProxyIds: (json['selectedProxyIds'] as List<dynamic>?)
              ?.map((e) => (e as num).toInt())
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$ProxySelectModelImplToJson(
        _$ProxySelectModelImpl instance) =>
    <String, dynamic>{
      'proxies': instance.proxies,
      'selectedProxyIds': instance.selectedProxyIds,
    };
