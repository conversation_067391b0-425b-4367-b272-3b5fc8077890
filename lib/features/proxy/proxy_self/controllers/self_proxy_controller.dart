import 'package:frontend_re/core/utils/random.dart';
import 'package:frontend_re/domain/models/proxy_model.dart';
import 'package:frontend_re/domain/services/proxy_service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:frontend_re/features/proxy/proxy_self/models/proxy_select_model.dart';

part 'self_proxy_controller.g.dart';

@riverpod
class ProxyController extends _$ProxyController {
  @override
  FutureOr<String?> build() {
    return null; // 初始状态为null
  }
  
  /// 添加单个代理
  Future<void> addSingleProxy({
    required String name,
    required String type,
    required String host,
    required String port,
    required String username,
    required String password,
  }) async {
    state = const AsyncLoading();
    
    try {
      // 校验数据
      if (name.isEmpty || host.isEmpty || port.isEmpty) {
        state = AsyncError('请注意必填项是否填入！', StackTrace.current);
        return;
      }
      
      final portInt = int.tryParse(port);
      if (portInt == null || portInt < 1 || portInt > 65535) {
        state = AsyncError('端口号必须是 1 到 65535 的数字', StackTrace.current);
        return;
      }

      // 数据校验通过，构建请求
      final request = SelfProxyCreateRequest(
        name: name,
        type: type == 'HTTP' ? 1 : type == 'HTTPS' ? 2 : 3,
        host: host,
        port: portInt,
        username: username.isNotEmpty ? username : null,
        password: password.isNotEmpty ? password : null,
        environmentId: null,
      );

      // 调用域名层服务
      final service = ref.read(selfProxyProvider.notifier);
      await service.addProxy(request);
      
      // 直接传递成功消息
      state = const AsyncData('代理创建成功');
    } catch (e) {
      // 更新状态为失败
      state = AsyncError('创建失败: $e', StackTrace.current);
    }
  }
  
  /// 批量添加代理
  Future<void> addBatchProxy(String multiple) async {
    state = const AsyncLoading();
    
    try {
      if (multiple.trim().isEmpty) {
        state = AsyncError('批量添加内容不能为空', StackTrace.current);
        return;
      }

      // 分割每一行
      final lines = multiple.split('\n').where((line) => line.trim().isNotEmpty).toList();
      final List<SelfProxyCreateRequest> validRequests = [];
      final List<String> failedLines = [];

      // 处理每一行，收集有效的请求
      for (final line in lines) {
        final trimmedLine = line.trim();
        if (trimmedLine.isEmpty) continue;

        try {
          // 解析代理信息
          final result = _parseProxyLine(trimmedLine);
          
          if (result['error'] != null) {
            failedLines.add('$trimmedLine - ${result['error']}');
            continue;
          }

          // 验证必要字段
          if (result['host']?.isEmpty ?? true) {
            failedLines.add('$trimmedLine - 代理地址或端口为空');
            continue;
          }

          // 验证端口
          final portInt = int.tryParse(result['port']!);
          if (portInt == null || portInt < 1 || portInt > 65535) {
            failedLines.add('$trimmedLine - 端口号必须是 1 到 65535 的数字');
            continue;
          }

          // 生成代理名称
          final String finalName = result['name']?.isNotEmpty == true 
              ? result['name']! 
              : '批量代理-${generateRandomString(6)}';

          // 创建代理请求
          final request = SelfProxyCreateRequest(
            name: finalName,
            type: result['protocol'] == 'HTTP' ? 1 : result['protocol'] == 'HTTPS' ? 2 : 3,
            host: result['host']!,
            port: portInt,
            username: result['username']?.isNotEmpty == true ? result['username'] : null,
            password: result['password']?.isNotEmpty == true ? result['password'] : null,
            environmentId: result['environmentId'],
          );

          validRequests.add(request);
          
        } catch (e) {
          failedLines.add('$trimmedLine - 处理失败: $e');
        }
      }

      // 如果有有效的请求，进行批量添加
      if (validRequests.isNotEmpty) {
        try {
          final service = ref.read(selfProxyProvider.notifier);
          await service.addBatchProxy(validRequests);
          
          // 构建成功消息
          final successCount = validRequests.length;
          final message = failedLines.isEmpty 
              ? '批量添加成功，共添加 $successCount 个代理' 
              : '批量添加部分成功，失败 ${failedLines.length} 个，成功 $successCount 个';
          
          state = AsyncData(message);
        } catch (e) {
          // 批量添加失败
          state = AsyncError('批量添加失败: $e', StackTrace.current);
        }
      } else {
        // 没有有效的请求
        state = AsyncError(
          failedLines.isNotEmpty ? failedLines.first : '没有有效的代理配置', 
          StackTrace.current
        );
      }
    } catch (e) {
      // 更新状态为失败
      state = AsyncError('批量添加失败: $e', StackTrace.current);
    }
  }

  /// 删除单个代理
  Future<void> deleteProxy(int id, String name) async {
    state = const AsyncLoading();
    
    try {
      // 调用域名层服务
      final service = ref.read(selfProxyProvider.notifier);
      await service.deleteProxy([id]);
      
      // 直接传递成功消息
      state = const AsyncData('代理删除成功');
    } catch (e) {
      // 更新状态为失败
      state = AsyncError('删除失败: $e', StackTrace.current);
    }
  }

  /// 更新单个代理
  Future<void> updateProxy(SelfProxyUpdateRequest request) async {
    state = const AsyncLoading();
    
    try {
      // 校验数据
      if (request.name.isEmpty || request.host.isEmpty) {
        state = AsyncError('请注意必填项是否填入！', StackTrace.current);
        return;
      }
      
      if (request.port < 1 || request.port > 65535) {
        state = AsyncError('端口号必须是 1 到 65535 的数字', StackTrace.current);
        return;
      }

      // 调用域名层服务
      final service = ref.read(selfProxyProvider.notifier);
      await service.updateProxy(request);
      
      // 直接传递成功消息
      state = const AsyncData('代理更新成功');
    } catch (e) {
      // 更新状态为失败
      state = AsyncError('更新失败: $e', StackTrace.current);
    }
  }
}

/// 解析代理配置行，支持多种格式
/// 格式：{协议}{代理IP}:{端口}:{用户名}:{密码}:{环境ID}:{自定义名称}
/// 支持的格式示例：
/// • 基础格式：*************:8080
/// • 带认证：*************:8080:user123:pass456
/// • 带协议：http://*************:8080:user123:pass456
/// • 绑定环境：http://*************:8080:user123:pass456:3
/// • 完整配置：http://*************:8080:user123:pass456:3:我的代理
/// • SOCKS5：socks5://proxy.com:1080:::0:SOCKS代理
Map<String, dynamic> _parseProxyLine(String line) {
  final result = <String, dynamic>{
    'protocol': 'HTTP', // 默认协议
    'host': '',
    'port': '',
    'username': '',
    'password': '',
    'environmentId': null,
    'name': '',
    'error': null,
  };

  try {
    String workingLine = line.trim();
    
    // 第一步：提取协议
    if (workingLine.contains('://')) {
      final protocolMatch = RegExp(r'^(http|https|socks5)://', caseSensitive: false).firstMatch(workingLine);
      if (protocolMatch != null) {
        final protocol = protocolMatch.group(1)!.toLowerCase();
        // 根据协议设置正确的值
        switch (protocol) {
          case 'http':
            result['protocol'] = 'HTTP';
            break;
          case 'https':
            result['protocol'] = 'HTTPS';
            break;
          case 'socks5':
            result['protocol'] = 'SOCKS5';
            break;
        }
        workingLine = workingLine.substring(protocolMatch.end);
      } else {
        result['error'] = '不支持的协议类型，支持 http/https/socks5';
        return result;
      }
    }

    // 第二步：按冒号分割剩余部分
    final parts = workingLine.split(':');
    
    if (parts.length < 2) {
      result['error'] = '格式错误，至少需要包含 IP:端口';
      return result;
    }

    // 第三步：解析各个部分
    result['host'] = parts[0].trim();
    result['port'] = parts[1].trim();

    // 验证必填字段
    if (result['host'].isEmpty) {
      result['error'] = '代理IP或域名不能为空';
      return result;
    }

    if (result['port'].isEmpty) {
      result['error'] = '端口不能为空';
      return result;
    }

    // 解析可选参数：用户名、密码、环境ID、名称
    // 用户名（第3个参数）
    if (parts.length >= 3) {
      final username = parts[2].trim();
      if (username.isNotEmpty) {
        result['username'] = username;
      }
    }

    // 密码（第4个参数）
    if (parts.length >= 4) {
      final password = parts[3].trim();
      if (password.isNotEmpty) {
        result['password'] = password;
      }
    }

    // 环境ID（第5个参数）
    if (parts.length >= 5) {
      final envIdStr = parts[4].trim();
      if (envIdStr.isNotEmpty && envIdStr != '0') {
        final envId = int.tryParse(envIdStr);
        if (envId != null && envId > 0) {
          result['environmentId'] = envId;
        } else {
          result['error'] = '环境ID必须是大于0的数字，填0或留空表示不绑定环境';
          return result;
        }
      }
    }

    // 自定义名称（第6个参数）
    if (parts.length >= 6) {
      final name = parts[5].trim();
      if (name.isNotEmpty) {
        result['name'] = name;
      }
    }

  } catch (e) {
    result['error'] = '解析失败: $e';
  }

  return result;
}

@riverpod
class ProxyTableController extends _$ProxyTableController {
  @override
  ProxySelectModel build() {
    // 表格选中的代理项
    return const ProxySelectModel(proxies: []);
  }

  bool get hasSelectedIds => state.selectedProxyIds.isNotEmpty;
  List<int> get selectedIds => state.selectedProxyIds;
  
  // 更新选中的代理ID
  void updateSelectedProxyIds(List<int> ids) {
    state = state.copyWith(selectedProxyIds: ids);
  }

  // 清空状态
  void clearSelectedProxyIds() {
    state = state.copyWith(selectedProxyIds: []);
  }

  // 删除选中的代理
  Future<String> deleteSelectedProxies() async {
    // 删除选中的代理
    // 这里需要调用删除代理的服务方法
    // final res = await ref.read(proxySelfProvider.notifier).deleteProxies(ids);
    
    // 删除后清空选中的ids
    clearSelectedProxyIds();
    return "删除成功"; // 临时返回值
  }
}
