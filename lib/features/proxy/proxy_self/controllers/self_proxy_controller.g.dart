// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'self_proxy_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$proxyControllerHash() => r'0ec391f4cfdd0aa128d79897f11bf787253e43fb';

/// See also [ProxyController].
@ProviderFor(ProxyController)
final proxyControllerProvider =
    AutoDisposeAsyncNotifierProvider<ProxyController, String?>.internal(
  ProxyController.new,
  name: r'proxyControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$proxyControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ProxyController = AutoDisposeAsyncNotifier<String?>;
String _$proxyTableControllerHash() =>
    r'57cd4cf354c1842e3db98898592d09ab3a374392';

/// See also [ProxyTableController].
@ProviderFor(ProxyTableController)
final proxyTableControllerProvider = AutoDisposeNotifierProvider<
    ProxyTableController, ProxySelectModel>.internal(
  ProxyTableController.new,
  name: r'proxyTableControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$proxyTableControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ProxyTableController = AutoDisposeNotifier<ProxySelectModel>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
