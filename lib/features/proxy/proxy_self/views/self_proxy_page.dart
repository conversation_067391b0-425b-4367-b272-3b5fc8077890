import 'package:flutter/material.dart';
import 'package:frontend_re/core/utils/ui/snack_bar_util.dart';
import 'package:frontend_re/domain/services/proxy_service.dart';
import 'package:frontend_re/features/proxy/proxy_self/controllers/self_proxy_controller.dart';
import 'package:frontend_re/features/proxy/proxy_self/views/components/proxy_add_dialog.dart';
import 'package:frontend_re/features/proxy/proxy_self/views/components/self_proxy_table.dart';

import 'package:frontend_re/widgets/custom_solid_button.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class SelfProxyPage extends ConsumerWidget {
  const SelfProxyPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 监听 provider 以保持其状态存活，并确保在状态变化时页面可以响应（如果需要）
    ref.watch(proxyTableControllerProvider);

    return Scaffold(
      body: Column(
        spacing: 16,
        children: [
          Row(
            spacing: 16,
            children: [
              // 添加
              CustomSolidButton(
                iconPath: 'assets/svg/add.svg',
                backgroundColor: const Color(0xFFFFFFFF),
                iconColor: const Color(0xFFFFFFFF),
                iconBackgroundColor: const Color(0xFF1E1C1D),
                iconSize: 14,
                textColor: const Color(0xFF8D8E93),
                text: '添加',
                // textDirection: TextDirection.rtl,
                onPressed: () {
                  _showAddSelfProxyDialog(context);
                },
              ),
              // 批量删除
              CustomSolidButton(
                iconPath: 'assets/svg/delete.svg',
                backgroundColor: const Color(0xFFFFFFFF),
                iconColor: const Color(0xFFFFFFFF),
                iconBackgroundColor: const Color(0xFF1E1C1D),
                iconSize: 14,
                textColor: const Color(0xFF8D8E93),
                text: '批量删除',
                // textDirection: TextDirection.rtl,
                onPressed: () async {
                  final selectedIds = ref.read(proxyTableControllerProvider).selectedProxyIds;
                  print(selectedIds);
                  if (selectedIds.isEmpty) {
                    if (context.mounted) SnackBarUtil().showInfo(context, '请选择要删除的窗口');
                    return;
                  }

                  // 显示确认删除对话框
                  final bool? confirmed = await showDialog<bool>(
                    context: context,
                    builder: (BuildContext dialogContext) {
                      return AlertDialog(
                        title: const Text('确认删除'),
                        content: Text('您确定要删除选中的 ${selectedIds.length} 个窗口吗？'),
                        actions: <Widget>[
                          TextButton(
                            child: const Text('取消'),
                            onPressed: () {
                              Navigator.of(dialogContext).pop(false); // 用户取消
                            },
                          ),
                          TextButton(
                            child: const Text('确认'),
                            onPressed: () {
                              ref.read(selfProxyProvider.notifier).deleteProxy(selectedIds);
                              Navigator.of(dialogContext).pop(true); // 用户确认
                            },
                          ),
                        ],
                      );
                    },
                  );

                  // 如果用户确认删除
                  if (confirmed == true) {
                    try {
                      final res = await ref.read(proxyTableControllerProvider.notifier).deleteSelectedProxies();
                      if (context.mounted) SnackBarUtil().showSuccess(context, res);
                    } catch (e) {
                      if (context.mounted) SnackBarUtil().showError(context, e.toString());
                    }
                  }
                },
              ),
            ],
          ),
          const Expanded(child: SelfProxyTable())
        ],
      ),
    );
  }

  /// 添加自有代理弹窗
  void _showAddSelfProxyDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: ProxyAddDialog(),
      ),
    );
  }
}
