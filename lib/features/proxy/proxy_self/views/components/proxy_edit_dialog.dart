import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:frontend_re/domain/models/proxy_model.dart';
import 'package:frontend_re/core/utils/ui/snack_bar_util.dart';
import 'package:frontend_re/features/proxy/proxy_self/controllers/self_proxy_controller.dart';
import 'package:frontend_re/domain/services/proxy_service.dart';
import 'package:frontend_re/widgets/custom_dropdown_menu.dart';
import 'package:frontend_re/widgets/custom_text_field.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

// 代理类型编辑状态提供器
final proxyTypeEditProvider = StateProvider.autoDispose<String>((ref) => "HTTP");

class SelfProxyEditDialog extends ConsumerStatefulWidget {
  final int id;

  const SelfProxyEditDialog({
    super.key,
    required this.id,
  });

  @override
  ConsumerState<SelfProxyEditDialog> createState() => _SelfProxyEditDialogState();
}

class _SelfProxyEditDialogState extends ConsumerState<SelfProxyEditDialog> {
  late final TextEditingController nameController;
  late final TextEditingController hostController;
  late final TextEditingController portController;
  late final TextEditingController usernameController;
  late final TextEditingController passwordController;
  
  String? nameError;
  String? hostError;
  String? portError;
  bool isLoading = false;
  bool isInitializing = true;
  String? errorMessage;
  
  @override
  void initState() {
    super.initState();
    nameController = TextEditingController();
    hostController = TextEditingController();
    portController = TextEditingController();
    usernameController = TextEditingController();
    passwordController = TextEditingController();
    
    // 在初始化时加载代理详情
    _loadProxyDetails();
  }
  
  // 加载代理详情
  Future<void> _loadProxyDetails() async {
    setState(() {
      isInitializing = true;
    });
    
    try {
      // 调用服务获取代理详情
      final proxyService = ref.read(selfProxyProvider.notifier);
      final proxyDetail = await proxyService.getProxy(widget.id);
      
      // 设置控制器的值
      nameController.text = proxyDetail!.name ?? '';
      hostController.text = proxyDetail.host ?? '';
      portController.text = proxyDetail.port.toString();
      usernameController.text = proxyDetail.username ?? '';
      passwordController.text = proxyDetail.password ?? '';
      
      // 设置代理类型
      WidgetsBinding.instance.addPostFrameCallback((_) {
        String proxyType;
        switch (proxyDetail.type) {
          case 1:
            proxyType = 'HTTP';
            break;
          case 2:
            proxyType = 'HTTPS';
            break;
          case 3:
            proxyType = 'SOCKS5';
            break;
          default:
            proxyType = 'HTTP';
        }
        ref.read(proxyTypeEditProvider.notifier).state = proxyType;
      });
      
    } catch (e) {
      setState(() {
        errorMessage = '加载代理详情失败: $e';
      });
    } finally {
      setState(() {
        isInitializing = false;
      });
    }
  }
  
  @override
  void dispose() {
    nameController.dispose();
    hostController.dispose();
    portController.dispose();
    usernameController.dispose();
    passwordController.dispose();
    super.dispose();
  }

  void submitProxyData() {
    // 直接构建请求对象并传递给控制器
    final proxyType = ref.read(proxyTypeEditProvider);
    
    // 构建请求
    final request = SelfProxyUpdateRequest(
      id: widget.id,
      name: nameController.text,
      type: proxyType == 'HTTP' ? 1 : proxyType == 'HTTPS' ? 2 : 3,
      host: hostController.text,
      port: int.tryParse(portController.text) ?? 0,
      username: usernameController.text,
      password: passwordController.text,
    );

    // 调用控制器更新代理，验证逻辑由控制器处理
    ref.read(proxyControllerProvider.notifier).updateProxy(request);
  }

  @override
  Widget build(BuildContext context) {
    // 监听代理操作状态
    ref.listen<AsyncValue<String?>>(
      proxyControllerProvider, 
      (previous, current) {
        // 处理加载状态
        setState(() {
          isLoading = current.isLoading;
        });
        
        // 处理错误状态
        if (current.hasError) {
          // 显示错误信息，可以根据错误类型设置对应的字段错误
          final errorMessage = current.error.toString();
          SnackBarUtil().showError(context, errorMessage);
          
          // 根据错误消息设置对应的字段错误
          setState(() {
            if (errorMessage.contains('名称')) {
              nameError = errorMessage;
            } else {
              nameError = null;
            }
            
            if (errorMessage.contains('地址') || errorMessage.contains('域名')) {
              hostError = errorMessage;
            } else {
              hostError = null;
            }
            
            if (errorMessage.contains('端口')) {
              portError = errorMessage;
            } else {
              portError = null;
            }
          });
          
          return;
        }
        
        // 处理成功状态 - 简化逻辑
        if (current.hasValue && current.value != null) {
          final message = current.value!;
          SnackBarUtil().showSuccess(context, message);
          Navigator.of(context).pop(); // 成功后关闭对话框
        }
      },
    );
    
    // 从provider获取当前代理类型
    final proxyType = ref.watch(proxyTypeEditProvider);

    // 显示加载中或错误状态
    if (isInitializing) {
      return AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        content: const SizedBox(
          height: 100,
          width: 100,
          child: Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );
    }
    
    if (errorMessage != null) {
      return AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: const Text('加载失败'),
        content: Text(errorMessage!),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('关闭'),
          ),
        ],
      );
    }

    return AlertDialog(
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20), // 圆角
      ),
      contentPadding: const EdgeInsets.all(30),
      content: SizedBox(
        width: 600,
        child: SingleChildScrollView(
          child: Column(
            spacing: 16,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              // 添加自有代理标题
              Row(
                children: [
                  Container(
                    width: 16,
                    height: 16,
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary,
                      borderRadius: BorderRadius.circular(50),
                    ),
                  ),
                  const SizedBox(width: 8),
                  const Text('编辑自有代理', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),),
                ],
              ),
              // 代理名称
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Expanded(
                    flex: 3,
                    child: Text('代理名称'),
                  ),
                  Expanded(
                    flex: 7,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomTextField(
                          controller: nameController,
                          hint: '请输入（必填）',
                        ),
                        if (nameError != null)
                          Padding(
                            padding: const EdgeInsets.only(top: 4.0, left: 8.0),
                            child: Text(
                              nameError!,
                              style: const TextStyle(color: Colors.red, fontSize: 12),
                            ),
                          ),
                      ],
                    ),
                  ),
                ],
              ),
              // 代理类型 - 使用Riverpod状态
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Expanded(
                    flex: 3,
                    child: Text('代理类型'),
                  ),
                  Expanded(
                    flex: 7,
                    child: CustomDropdownMenu(
                      value: proxyType,
                      items: const [
                        {'name': 'HTTP'},
                        {'name': 'HTTPS'},
                        {'name': 'SOCKS5'},
                      ],
                      onChanged: (value) {
                        if (value != null) {
                          ref.read(proxyTypeEditProvider.notifier).state = value;
                        }
                      },
                    ),
                  ),
                ],
              ),
              // 代理地址和端口
              Row(
                children: [
                  const Expanded(
                    flex: 3,
                    child: Text('代理地址和端口'),
                  ),
                  Expanded(
                    flex: 7,
                    child: Row(
                      spacing: 8,
                      children: [
                        Expanded(
                          flex: 6,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CustomTextField(
                                controller: hostController,
                                hint: '域名',
                              ),
                              if (hostError != null)
                                Padding(
                                  padding: const EdgeInsets.only(top: 4.0, left: 8.0),
                                  child: Text(
                                    hostError!,
                                    style: const TextStyle(color: Colors.red, fontSize: 12),
                                  ),
                                ),
                            ],
                          ),
                        ),
                        Expanded(
                          flex: 3,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CustomTextField(
                                controller: portController,
                                hint: '端口',
                                keyboardType: TextInputType.number,
                                inputFormatters: [
                                  FilteringTextInputFormatter.digitsOnly,
                                  LengthLimitingTextInputFormatter(5), // 端口号最大65535，限制5位数
                                ],
                              ),
                              if (portError != null)
                                Padding(
                                  padding: const EdgeInsets.only(top: 4.0, left: 8.0),
                                  child: Text(
                                    portError!,
                                    style: const TextStyle(color: Colors.red, fontSize: 12),
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              // 登录账号
              Row(
                children: [
                  const Expanded(
                    flex: 3,
                    child: Text('登录账号'),
                  ),
                  Expanded(
                    flex: 7,
                    child: CustomTextField(
                      controller: usernameController,
                      hint: '请输入',
                    ),
                  ),
                ],
              ),
              // 登录密码
              Row(
                children: [
                  const Expanded(
                    flex: 3,
                    child: Text('登录密码'),
                  ),
                  Expanded(
                    flex: 7,
                    child: CustomTextField(
                      controller: passwordController,
                      hint: '请输入',
                      obscureText: true,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: isLoading ? null : () {
            Navigator.of(context).pop();
          },
          child: const Text('取消'),
        ),
        ElevatedButton(
          onPressed: isLoading ? null : submitProxyData,
          child: isLoading 
              ? const SizedBox(
                  width: 20, 
                  height: 20, 
                  child: CircularProgressIndicator(strokeWidth: 2)
                ) 
              : const Text('提交'),
        ),
      ],
    );
  }
} 
