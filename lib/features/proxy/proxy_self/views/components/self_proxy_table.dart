import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:frontend_re/domain/models/proxy_model.dart';
import 'package:frontend_re/domain/services/proxy_service.dart';
import 'package:frontend_re/features/proxy/proxy_self/controllers/self_proxy_controller.dart';
import 'package:frontend_re/features/proxy/proxy_self/views/components/proxy_add_dialog.dart';
import 'package:frontend_re/features/proxy/proxy_self/views/components/proxy_delete_dialog.dart';
import 'package:frontend_re/features/proxy/proxy_self/views/components/proxy_edit_dialog.dart';
import 'package:frontend_re/widgets/custom_edit_button.dart';
import 'package:frontend_re/widgets/data_table_widget.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class SelfProxyTable extends HookConsumerWidget {
  const SelfProxyTable({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 监听代理数据状态
    final proxyState = ref.watch(selfProxyProvider);
    final controller = ref.read(selfProxyProvider.notifier);

    return Scaffold(
      backgroundColor: Colors.transparent, // 设置背景色
      body: LayoutBuilder(
        builder: (context, constraints) {
          return Column(
            children: [
              Expanded(
                child: proxyState.when(
                  data: (proxyModel) => DataTableWidget(
                    headingTextStyle: const TextStyle(fontSize: 14, color: Color(0xFF333333)),
                    dataTextStyle: const TextStyle(fontSize: 14, color: Color(0xFF8D8E93)),
                    minWidth: 100,
                    showCheckbox: true,
                    columnSpacing: 0,
                    onSelectionChanged: (selectedRows) {
                      // selectedRows 是选中行的索引集合 (Set<int>)
                      // 我们需要根据这些索引从数据源获取对应的 id
                      final selectedIds = selectedRows
                          .where((index) => index < proxyModel.proxies.length) // 确保索引在范围内
                          .map((index) => proxyModel.proxies[index].id)
                          .toList();
                      
                      // 更新控制器中的选中状态
                      ref.read(proxyTableControllerProvider.notifier).updateSelectedProxyIds(selectedIds);
                      print(selectedIds);
                    },
                    enablePagination: true,
                    totalCount: proxyModel.total,
                    pageSize: 10,
                    currentPage: 1,
                    onPageChanged: (page, pageSize) {
                      controller.getProxies(
                        ProxyListRequest(
                          offset: page,
                          limit: pageSize,
                        ),
                      );
                    },
                    noDataImagePath: 'assets/images/proxy/NoData.png',
                    noDataText: '暂无代理数据',
                    noDataButtonText: '添加自建代理',
                    noDataButtonEvent: () {
                      _showAddSelfProxyDialog(context);
                    },
                    columns: const [
                      DataColumn2(label: Text('名称'), size: ColumnSize.M),
                      DataColumn2(label: Text('所属环境'), size: ColumnSize.M),
                      DataColumn2(label: Text('类型'), size: ColumnSize.M),
                      DataColumn2(label: Text('代理信息'), size: ColumnSize.L),
                      DataColumn2(label: Text('操作'), size: ColumnSize.S, fixedWidth: 120),
                    ],
                    data: proxyModel.proxies,
                    rowBuilder: (proxy, index, isHovered) {
                      final selfProxy = proxy;
                      return DataRow2(cells: [
                        DataCell(Text(selfProxy.name)),
                        DataCell(Text(selfProxy.environmentName)),
                        DataCell(_buildProxyTypeChip(selfProxy.type)),
                        DataCell(Text('${selfProxy.host}:${selfProxy.port}')),
                        DataCell(_buildActionButtons(context, selfProxy)),
                      ]);
                    },
                  ),
                  loading: () => const Center(
                    child: CircularProgressIndicator(),
                  ),
                  error: (error, stack) => Center(
                    child: Text('加载失败: $error'),
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

    /// 添加自有代理弹窗
  void _showAddSelfProxyDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const Center(
        child: ProxyAddDialog(),
      ),
    );
  }

  // 构建代理类型标签
  Widget _buildProxyTypeChip(int type) {
    String typeText;
    Color chipColor;
    Color textColor;
    
    switch (type) {
      case 1: // HTTP
        typeText = 'HTTP';
        chipColor = const Color(0xFFE3F2FD);
        textColor = const Color(0xFF1976D2);
        break;
      case 2: // HTTPS
        typeText = 'HTTPS';
        chipColor = const Color(0xFFE8F5E8);
        textColor = const Color(0xFF2E7D32);
        break;
      case 3: // SOCKS5
        typeText = 'SOCKS5';
        chipColor = const Color(0xFFFFF3E0);
        textColor = const Color(0xFFE65100);
        break;
      default:
        typeText = 'Unknown';
        chipColor = const Color(0xFFF5F5F5);
        textColor = const Color(0xFF666666);
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: chipColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        typeText,
        style: TextStyle(
          color: textColor,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  // 操作按钮
  Widget _buildActionButtons(BuildContext context, SelfProxyItem proxy) {
    return Row(
      spacing: 6,
      children: [
        // 编辑代理按钮
        CustomEditButton(
          onPressed: () {
            showDialog(
              context: context,
              builder: (context) => SelfProxyEditDialog(
                id: proxy.id,
              ),
            );
          },
          iconPath: 'assets/svg/open.svg',
          iconWidth: 16,
          iconHeight: 16,
          tooltip: '编辑代理',
        ),
        // 删除代理按钮
        CustomEditButton(
          onPressed: () {
            showDialog(
              context: context,
              builder: (context) => ProxyDeleteDialog(
                id: proxy.id,
                name: proxy.name,
              ),
            );
          }, 
          iconPath: 'assets/svg/delete.svg',
          iconWidth: 16,
          iconHeight: 16,
          tooltip: '删除代理',
        ),
      ],
    );
  }
}
