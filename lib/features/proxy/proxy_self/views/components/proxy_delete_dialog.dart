import 'package:flutter/material.dart';
import 'package:frontend_re/core/utils/ui/snack_bar_util.dart';
import 'package:frontend_re/features/proxy/proxy_self/controllers/self_proxy_controller.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class ProxyDeleteDialog extends ConsumerStatefulWidget {
  final int id;
  final String name;

  const ProxyDeleteDialog({
    super.key,
    required this.id,
    required this.name,
  });

  @override
  ConsumerState<ProxyDeleteDialog> createState() => _ProxyDeleteDialogState();
}

class _ProxyDeleteDialogState extends ConsumerState<ProxyDeleteDialog> {
  bool isLoading = false;

  @override
  Widget build(BuildContext context) {
    // 监听代理操作状态
    ref.listen<AsyncValue<String?>>(
      proxyControllerProvider, 
      (previous, current) {
        // 处理加载状态
        setState(() {
          isLoading = current.isLoading;
        });
        
        // 处理错误状态
        if (current.hasError) {
          SnackBarUtil().showError(context, current.error.toString());
          return;
        }
        
        // 处理成功状态
        if (current.hasValue && current.value != null) {
          final message = current.value!;
          SnackBarUtil().showSuccess(context, message);
          Navigator.of(context).pop(); // 成功后关闭对话框
        }
      },
    );

    return AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8), // 圆角
      ),
      title: const Text(
        '确认删除',
        style: TextStyle(fontWeight: FontWeight.bold),
      ),
      content: Text('您确定要删除代理 "${widget.name}" 吗？'),
      actions: [
        TextButton(
          onPressed: isLoading ? null : () {
            Navigator.of(context).pop();
          },
          child: const Text('取消'),
        ),
        ElevatedButton(
          onPressed: isLoading ? null : () {
            ref.read(proxyControllerProvider.notifier).deleteProxy(widget.id, widget.name);
          },
          child: isLoading 
              ? const SizedBox(
                  width: 20, 
                  height: 20, 
                  child: CircularProgressIndicator(strokeWidth: 2)
                ) 
              : const Text('删除'),
        ),
      ],
    );
  }
} 
