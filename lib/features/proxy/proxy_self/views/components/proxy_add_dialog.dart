import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:frontend_re/core/utils/random.dart';
import 'package:frontend_re/core/utils/ui/snack_bar_util.dart';
import 'package:frontend_re/features/proxy/proxy_self/controllers/self_proxy_controller.dart';
import 'package:frontend_re/widgets/custom_dropdown_menu.dart';
import 'package:frontend_re/widgets/custom_text_field.dart';
import 'package:frontend_re/widgets/primary_botton.dart';
import 'package:frontend_re/widgets/secondary_botton.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class ProxyAddDialog extends ConsumerStatefulWidget {
  const ProxyAddDialog({super.key});

  @override
  ConsumerState<ProxyAddDialog> createState() => _ProxyAddDialogState();
}

class _ProxyAddDialogState extends ConsumerState<ProxyAddDialog> {
  final nameController = TextEditingController();
  final hostController = TextEditingController();
  final portController = TextEditingController();
  final usernameController = TextEditingController();
  final passwordController = TextEditingController();
  final multipleController = TextEditingController();

  String dropdownValue = 'HTTP'; // 默认选项
  bool isLoading = false;

  @override
  void dispose() {
    nameController.dispose();
    hostController.dispose();
    portController.dispose();
    usernameController.dispose();
    passwordController.dispose();
    multipleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 监听代理操作状态
    ref.listen<AsyncValue<String?>>(
      proxyControllerProvider, 
      (previous, current) {
        // 处理加载状态
        setState(() {
          isLoading = current.isLoading;
        });
        
        // 处理错误状态
        if (current.hasError) {
          SnackBarUtil().showError(context, current.error.toString());
          return;
        }
        
        // 处理成功状态
        if (current.hasValue && current.value != null) {
          final message = current.value!;
          
          // 根据消息内容判断是否为批量操作
          if (message.contains('批量添加部分成功')) {
            SnackBarUtil().showWarning(context, message);
            // 部分成功时不关闭对话框
          } else {
            SnackBarUtil().showSuccess(context, message);
            Navigator.of(context).pop(); // 成功后关闭对话框
          }
        }
      },
    );

    return AlertDialog(
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16), // 圆角
      ),
      contentPadding: const EdgeInsets.all(0),
      content: SizedBox(
        width: 600,
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(30.0),
            child: Column(
              spacing: 8,
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // 代理名称
                Row(
                  children: [
                    const Expanded(
                      flex: 3,
                      child: Row(
                        children: [
                          Text( '* ',style: TextStyle(color: Colors.red),),
                          Text('代理名称',style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),),
                        ],
                      ),
                    ),
                    Expanded(
                      flex: 7,
                      child: CustomTextField(
                        controller: nameController,
                        hint: '请输入',
                      ),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton(
                      onPressed: () {
                        String random = generateRandomString(6);
                        nameController.text = '自建代理-$random';
                      },
                      child: const Text('自动生成'),
                    ),
                  ],
                ),
                // 代理类型
                const Row(
                  children: [
                    Text('* ',style: TextStyle(color: Colors.red)),
                    Text('代理类型', style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold)),
                  ],
                ),
                CustomDropdownMenu(
                  value: dropdownValue,
                  items: const [
                    {'name': 'HTTP'},
                    {'name': 'HTTPS'},
                    {'name': 'SOCKS5'},
                  ],
                  onChanged: (value) {
                    setState(() {
                      dropdownValue = value!;
                    });
                  },
                ),
            
                // 代理地址和端口
                const Row(
                  children: [
                    Text('* ', style: TextStyle(color: Colors.red)),
                    Text('代理地址或域名', style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold)),
                  ],
                ),
            
                Row(
                  children: [
                    Expanded(
                      flex: 6,
                      child: CustomTextField(
                        controller: hostController,
                        hint: '域名',
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      flex: 3,
                      child: CustomTextField(
                        controller: portController,
                        hint: '端口',
                        keyboardType: TextInputType.number,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly,
                          LengthLimitingTextInputFormatter(5), // 端口号最大65535，限制5位数
                        ],
                      ),
                    ),
                  ],
                ),
            
                // 登录账号
                const Text(
                  '代理账号',
                  style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                ),
             
                CustomTextField(
                  controller: usernameController,
                  hint: '请输入',
                ),
            
                // 登录密码
                const Text(
                  '代理密码',
                  style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                ),
            
                CustomTextField(
                  controller: passwordController,
                  hint: '请输入',
                  obscureText: true,
                ),
            
                const Text(
                  '批量添加',
                  style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                ),
                TextField(
                  maxLines: 10,
                  minLines: 10,
                  controller: multipleController,
                  style: const TextStyle(fontSize: 12.0),
                  decoration: InputDecoration(
                    hintText:"批量代理配置格式说明：\n{协议}{代理IP}:{端口}:{用户名}:{密码}:{环境ID}:{自定义名称}\n\n✨ 支持的格式示例：\n• 基础格式：*************:8080\n• 带认证：*************:8080:user123:pass456\n• 带协议：http://*************:8080:user123:pass456\n• 绑定环境：http://*************:8080:user123:pass456:3\n• 完整配置：http://*************:8080:user123:pass456:3:我的代理\n• SOCKS5：socks5://proxy.com:1080:::0:SOCKS代理\n\n📋 字段说明：\n• 协议：支持http/https/socks5，不填默认HTTP\n• IP端口：必填项，格式为 IP:端口\n• 用户名密码：可选，留空表示无认证\n• 环境ID：可选，填0或留空表示不绑定环境\n• 自定义名称：可选，不填会自动生成\n\n⚠️ 注意：每行一个代理，按顺序填写，可选字段留空即可",
                    hintStyle: const TextStyle(fontSize: 12, color: Color(0xFF8D8E93)),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(color: Colors.black),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(color: Colors.transparent),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(color: Color(0xFF0C75F8)),
                    ),
                  ),
                )
              ],
            ),
          ),
        ),
      ),
      actionsPadding: const EdgeInsets.only(top: 10, right: 30, bottom: 10),
      actions: [
        SecondaryButton(
          onPressed: isLoading ? () {} : () {
            Navigator.of(context).pop();
          },
          child: const Text('取消'),
        ),
        PrimaryButton(
          onPressed: isLoading ? () {} : () {
            ref.read(proxyControllerProvider.notifier).addSingleProxy(
              name: nameController.text,
              type: dropdownValue,
              host: hostController.text,
              port: portController.text,
              username: usernameController.text,
              password: passwordController.text,
            );
          },
          child: isLoading 
              ? const SizedBox(
                  width: 20, 
                  height: 20, 
                  child: CircularProgressIndicator(strokeWidth: 2)
                ) 
              : const Text('添加'),
        ),
        PrimaryButton(
          onPressed: isLoading ? () {} : () {
            ref.read(proxyControllerProvider.notifier).addBatchProxy(
              multipleController.text,
            );
          },
          child: isLoading 
              ? const SizedBox(
                  width: 20, 
                  height: 20, 
                  child: CircularProgressIndicator(strokeWidth: 2)
                ) 
              : const Text('批量添加'),
        ),
      ],
    );
  }
} 
