import 'package:flutter/material.dart';
import 'package:frontend_re/features/proxy/proxy_store/views/components/proxy_params_selector.dart';

// 自定义 ScrollBehavior，不显示默认滚动条
class _NoThumbScrollBehavior extends ScrollBehavior {
  @override
  Widget buildScrollbar(
      BuildContext context, Widget child, ScrollableDetails details) {
    // 直接返回 child，不构建额外的滚动条
    return child;
  }

  @override
  Widget buildOverscrollIndicator(
      BuildContext context, Widget child, ScrollableDetails details) {
    // 直接返回 child，不构建额外的越界指示器
    return child;
  }
}

class BuyProxyPage extends StatelessWidget {
  const BuyProxyPage({super.key});

  @override
  Widget build(BuildContext context) {
    // 创建滚动控制器
    final ScrollController scrollController = ScrollController();
    return Scaffold(
      body: ScrollConfiguration(
        behavior: _NoThumbScrollBehavior(), // 应用自定义行为
        child: Scrollbar(
          // 设置滚动控制器
          controller: scrollController,
          // 设置滚动条始终可见
          thumbVisibility: true,
          // 设置滚动条厚度
          thickness: 6,
          // 设置滚动条圆角
          radius: const Radius.circular(10),
          // 设置滚动条颜色
          child: ClipRRect(
            borderRadius: const BorderRadius.only(topLeft: Radius.circular(20), topRight: Radius.circular(20)), // 设置滚动区域圆角
            child: SingleChildScrollView(
              // 关联滚动控制器
              controller: scrollController,
              child: const Padding(
                padding: EdgeInsets.only(bottom: 16),
                child: Column(
                  children: [
                    // 代理参数选择
                    ProxyParamsSelector(),
                  ],
                )
              ),
            ),
          ),
        ),
      ),
    );
  }
}
