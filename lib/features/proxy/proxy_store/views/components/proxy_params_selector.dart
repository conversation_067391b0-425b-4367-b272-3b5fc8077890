import 'package:flutter/material.dart';
import 'package:frontend_re/widgets/custom_switch_button.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class ProxyParamsSelector extends ConsumerWidget {
  const ProxyParamsSelector({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      width: double.infinity,
      height: 800,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(20)),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 24.0),
        child: Column(
          children: [
            Row(
              children: [
                Text('代理类型:'),
                Row(
                  children: [
                    CustomSwitchButton(
                      options: ['全球家庭住宅', '全球数据中心', '全球移动网络'],
                      selectedIndex: 0,
                      onChanged: (index) {},
                    ),
                  ],
                )
              ],
            )
          ],
        ),
      ),
    );
  }
} 
