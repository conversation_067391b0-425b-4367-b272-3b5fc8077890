import 'package:flutter/material.dart';
import 'package:frontend_re/features/proxy/proxy_self/views/components/self_proxy_table.dart';
import 'package:frontend_re/widgets/custom_solid_button.dart';

class ProxyApiPage extends StatelessWidget {
  const ProxyApiPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        spacing: 16,
        children: [
          Row(
            children: [
              CustomSolidButton(
                iconPath: 'assets/svg/screening.svg',
                backgroundColor: const Color(0xFFFFFFFF),
                iconColor: const Color(0xFFFFFFFF),
                iconBackgroundColor: const Color(0xFF1E1C1D),
                iconSize: 14,
                textColor: const Color(0xFF8D8E93),
                text: '筛选',
                // textDirection: TextDirection.rtl,
                onPressed: () {
                  // 使用路径导航，更直观
                  //context.push('/addBrowser');
                },
              ),
              Text('自有代理'),
              Text('代理API'),
              Text('购买代理'),
            ],
          ),
          const Expanded(child: SelfProxyTable())
        ],
      ),
    );
  }
}
