import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:frontend_re/router/navigation_history_provider.dart';
import 'package:frontend_re/widgets/custom_card.dart';
import 'package:frontend_re/widgets/s_back_button.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

class AppSettingPage extends HookConsumerWidget {
  const AppSettingPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      body: Column(
        spacing: 20,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 使用新的返回按钮组件
          SBackButton(
            onPressed: () {
              NavigationHelper.safeGoBack(context, ref);
            },
          ),
          const Center(
            child: CustomCard(
              title: '缓存设置',
              description: '',
              child: Text(
                'App Setting',
                style: TextStyle(fontSize: 14, color: Color(0xFF8D8E93))
              )),
          ),
        ],
      ),
    );
  }
}
