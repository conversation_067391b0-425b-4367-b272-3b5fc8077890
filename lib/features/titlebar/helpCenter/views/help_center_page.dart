import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:frontend_re/router/navigation_history_provider.dart';
import 'package:frontend_re/widgets/custom_border_button.dart';
import 'package:frontend_re/widgets/s_back_button.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

// 帮助中心搜索结果数据模型
class HelpSearchResult {
  final String title;
  final String content;
  final String category;
  final IconData icon;
  final Color color;

  const HelpSearchResult({
    required this.title,
    required this.content,
    required this.category,
    required this.icon,
    required this.color,
  });
}

// 模拟帮助数据
final helpSearchProvider = StateProvider<List<HelpSearchResult>>((ref) => []);

// 模拟帮助数据源
const List<HelpSearchResult> _helpData = [
  HelpSearchResult(
    title: '如何创建浏览器窗口',
    content: '在工作台页面点击新建按钮，填写窗口配置信息...',
    category: '基础操作',
    icon: Icons.web,
    color: Color(0xFF4A90E2),
  ),
  HelpSearchResult(
    title: '代理设置说明',
    content: '支持HTTP、HTTPS、SOCKS5等多种代理类型...',
    category: '代理配置',
    icon: Icons.security,
    color: Color(0xFF7ED321),
  ),
  HelpSearchResult(
    title: '浏览器指纹配置',
    content: '可自定义User-Agent、WebGL、Canvas等指纹信息...',
    category: '高级设置',
    icon: Icons.fingerprint,
    color: Color(0xFFFF6B35),
  ),
  HelpSearchResult(
    title: '账号密码管理',
    content: '为每个窗口配置独立的登录凭据...',
    category: '账号管理',
    icon: Icons.account_circle,
    color: Color(0xFF9013FE),
  ),
  HelpSearchResult(
    title: '批量操作指南',
    content: '支持批量打开、删除浏览器窗口...',
    category: '批量操作',
    icon: Icons.select_all,
    color: Color(0xFFFF9800),
  ),
];

class HelpCenterPage extends HookConsumerWidget {
  const HelpCenterPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final searchController = useTextEditingController();
    final searchResults = ref.watch(helpSearchProvider);
    final isSearching = useState(false);

    // 搜索逻辑
    void performSearch(String query) {
      if (query.isEmpty) {
        ref.read(helpSearchProvider.notifier).state = [];
        isSearching.value = false;
        return;
      }

      isSearching.value = true;
      final results = _helpData.where((item) {
        return item.title.toLowerCase().contains(query.toLowerCase()) ||
               item.content.toLowerCase().contains(query.toLowerCase()) ||
               item.category.toLowerCase().contains(query.toLowerCase());
      }).toList();

      ref.read(helpSearchProvider.notifier).state = results;
    }

    return Scaffold(
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 返回按钮
          SBackButton(
            onPressed: () {
              NavigationHelper.safeGoBack(context, ref);
            },
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // 标题
                  const Text(
                    '请问需要什么帮助？',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF333333),
                    ),
                  ),
                  const SizedBox(height: 32),
                  
                  // 搜索框
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: 400,
                        height: 40,  // 固定高度为40
                        child: TextField(
                          controller: searchController,
                          style: const TextStyle(
                            fontSize: 14,
                            color: Color(0xFF666666),
                          ),
                          onChanged: performSearch,
                          decoration: InputDecoration(
                            hintText: '搜索帮助内容...',
                            hintStyle: const TextStyle(
                              color: Color(0xFFBBBBBB),
                              fontSize: 14,
                            ),
                            prefixIcon: const Icon(
                              Icons.search,
                              color: Color(0xFFBBBBBB),
                              size: 20,
                            ),
                            suffixIcon: searchController.text.isNotEmpty
                                ? IconButton(
                                    icon: const Icon(
                                      Icons.clear,
                                      color: Color(0xFFBBBBBB),
                                      size: 18,
                                    ),
                                    onPressed: () {
                                      searchController.clear();
                                      performSearch('');
                                    },
                                  )
                                : null,
                            filled: true,
                            fillColor: Colors.white,
                            isDense: true,  // 使用更紧凑的布局
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(100),
                              borderSide: const BorderSide(
                                color: Color(0x00EBEBEB),
                                width: 1,
                              ),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(100),
                              borderSide: const BorderSide(
                                color: Color(0xFF4A90E2),
                                width: 1,
                              ),
                            ),
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 8,  // 减少垂直内边距来降低高度
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 10),
                      // 按钮
                      CustomBorderButton(
                        borderColor: const Color(0x000C75F8),
                        backgroundColor: const Color(0xFFFFFFFF),
                        iconPath: 'assets/svg/chat.svg',
                        text: '咨询客服',
                        horizontalPadding: 50,
                        verticalPadding: 9,
                        onPressed: () {},
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // 常见问题
                  Flexible(
                    child: _buildQuestionsCard(context),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuestionsCard(BuildContext context) {
    // 热门问题数据
    final List<Map<String, dynamic>> hotQuestions = [
      {
        'title': '如何新建账号？',
        'questions': [
          '如何新建设备？',
          '账号如何删除设备、解绑设备、移...',
          '如何管理内部成员',
          '如何启动账号',
        ],
      },
      {
        'title': '如何新建账号？',
        'questions': [
          '如何新建设备？',
          '账号如何删除设备、解绑设备、移...',
          '如何管理内部成员',
          '如何启动账号',
        ],
      },
      {
        'title': '如何新建账号？',
        'questions': [
          '如何新建设备？',
          '账号如何删除设备、解绑设备、移...',
          '如何管理内部成员',
          '如何启动账号',
        ],
      },
      {
        'title': '如何新建账号？',
        'questions': [
          '如何新建设备？',
          '账号如何删除设备、解绑设备、移...',
          '如何管理内部成员',
          '如何启动账号',
        ],
      },
      {
        'title': '如何新建账号？',
        'questions': [
          '如何新建设备？',
          '账号如何删除设备、解绑设备、移...',
          '如何管理内部成员',
          '如何启动账号',
        ],
      },
      {
        'title': '如何新建账号？',
        'questions': [
          '如何新建设备？',
          '账号如何删除设备、解绑设备、移...',
          '如何管理内部成员',
          '如何启动账号',
        ],
      },
    ];

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Wrap(
        spacing: 16, // 水平间距
        runSpacing: 16, // 垂直间距
        children: hotQuestions.map((question) {
          return SizedBox(
            width: 280, // 固定卡片宽度
            height: 200, // 固定卡片高度
            child: _buildSingleQuestionCard(context, question),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildSingleQuestionCard(BuildContext context, Map<String, dynamic> questionData) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题行
            Row(
              children: [
                Container(
                  width: 16,
                  height: 16,
                  decoration: BoxDecoration(
                    // color: const Color(0xFF3B82F6),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: SvgPicture.asset('assets/svg/question.svg'),
                ),
                const SizedBox(width: 8),
                const Expanded(
                  child: Text(
                    '热门问题',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF1F2937),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            // 问题列表
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  for (int i = 0; i < questionData['questions'].length; i++) ...[
                    if (i > 0) const SizedBox(height: 8),
                    InkWell(
                      onTap: () {
                        // 点击问题的处理逻辑
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('点击了：${questionData['questions'][i]}'),
                          ),
                        );
                      },
                      child: Text(
                        questionData['questions'][i],
                        style: const TextStyle(
                          fontSize: 12,
                          color: Color(0xFF6B7280),
                          height: 1.4,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
