// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sync_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$syncControllerHash() => r'b690711f1796f3a8ef3493fe275c12302ec8df55';

/// See also [SyncController].
@ProviderFor(SyncController)
final syncControllerProvider =
    AsyncNotifierProvider<SyncController, void>.internal(
  SyncController.new,
  name: r'syncControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$syncControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SyncController = AsyncNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
