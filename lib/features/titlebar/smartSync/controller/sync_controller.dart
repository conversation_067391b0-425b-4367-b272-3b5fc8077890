import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:synchronizer/synchronizer.dart';
import 'package:frontend_re/features/workbench/controllers/simple_browser_manager.dart';

part 'sync_controller.g.dart';

@Riverpod(keepAlive: true)
class SyncController extends _$SyncController {

  final _synchronizerPlugin = Synchronizer();

  @override
  FutureOr<void> build() {
    // TODO: implement build
  }

  // 保存的状态值
  SimpleBrowserInstance? _selectedControlBrowser;
  List<Map<String, dynamic>> _selectedTargetBrowserTags = [];
  String _xCoordinate = '0';
  String _yCoordinate = '0';
  String _width = '800';
  String _height = '600';

  // Getters
  SimpleBrowserInstance? get selectedControlBrowser => _selectedControlBrowser;
  List<Map<String, dynamic>> get selectedTargetBrowserTags => _selectedTargetBrowserTags;
  String get xCoordinate => _xCoordinate;
  String get yCoordinate => _yCoordinate;
  String get width => _width;
  String get height => _height;

  // Setters
  void setSelectedControlBrowser(SimpleBrowserInstance? browser) {
    _selectedControlBrowser = browser;
  }

  void setSelectedTargetBrowserTags(List<Map<String, dynamic>> tags) {
    _selectedTargetBrowserTags = tags;
  }

  void setWindowPosition(String x, String y, String width, String height) {
    _xCoordinate = x;
    _yCoordinate = y;
    _width = width;
    _height = height;
  }

  /// 获取屏幕尺寸，return {width: xxx, height: xxx}
  Future<Map<String, dynamic>> getScreenSize() async {
    final result = await _synchronizerPlugin.getScreenSize();
    return result;
  }

  /// 设置窗口位置尺寸，return "success"
  Future<String> arrangeWindow(int pid, int x, int y, int width, int height) async {
    final result = await _synchronizerPlugin.arrangeWindow(
      pid: pid,
      x: x,
      y: y,
      width: width,
      height: height,
    );

    return result;
  }

  /// 启动同步，return "success"
  Future<String> startSynchronization(int sourcePid, List<int> targetPids) async {
    print('startSynchronization: $sourcePid, $targetPids');
    final result = await _synchronizerPlugin.startSynchronization(
      sourcePID: sourcePid,
      targetPIDs: targetPids,
    );

    return result;
  }

  /// 停止同步，return "success"
  Future<String> stopSynchronization() async {
    final result = await _synchronizerPlugin.stopSynchronization();
    return result;
  }

  /// 检查同步状态
  Future<bool> checkSyncStatus() async {
    final isRunning = await _synchronizerPlugin.isSynchronizationRunning();
    return isRunning;
  }

  /// 聚焦指定PID的窗口
  Future<bool> focusWindow(int pid) async {
    try {
      final result = await _synchronizerPlugin.focusWindow(pid: pid);
      return result == "success";
    } catch (e) {
      print('聚焦窗口失败 (PID: $pid): $e');
      return false;
    }
  }
}
