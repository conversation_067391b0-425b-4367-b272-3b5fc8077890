import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:frontend_re/core/utils/ui/snack_bar_util.dart';
import 'package:frontend_re/core/utils/ui/window_manager_util.dart';
import 'package:frontend_re/features/titlebar/smartSync/controller/sync_controller.dart';
import 'package:frontend_re/features/workbench/controllers/simple_browser_manager.dart';
import 'package:frontend_re/widgets/custom_text_field.dart';
import 'package:frontend_re/widgets/enhanced_dropdown_menu.dart';
import 'package:frontend_re/widgets/primary_botton.dart';
import 'package:frontend_re/widgets/secondary_botton.dart';
import 'package:frontend_re/widgets/tag_selector_widget.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class SyncDialog extends HookConsumerWidget {
  const SyncDialog({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final syncController = ref.read(syncControllerProvider.notifier);
    
    // 浏览器列表状态
    final browserRunningList = useState<List<SimpleBrowserInstance>>([]);
    // 所选的控制浏览器（只能选一个）
    final selectedControlBrowser = useState<SimpleBrowserInstance?>(null);
    // 已选择的被控制浏览器标签（可以选多个）
    final selectedTargetBrowserTags = useState<List<Map<String, dynamic>>>([]);

    // 窗口排列选中的浏览器实例
    final selectedWindowArrangementBrowser = useState<SimpleBrowserInstance?>(null);
    // 窗口排列输入框控制器
    final xCoordinateController = useTextEditingController();
    final yCoordinateController = useTextEditingController();
    final widthController = useTextEditingController();
    final heightController = useTextEditingController();

    // 获取屏幕尺寸
    final screenSizeFuture = ref.read(syncControllerProvider.notifier).getScreenSize();

    // 检查同步状态
    final syncStatus = useState<bool>(false);

    // 按钮锁
    final buttonLock = useState<bool>(false);
    
    useEffect(() {
      // 初始化浏览器列表
      browserRunningList.value = SimpleBrowserManager.instance.runningBrowsers;
      
      // 恢复保存的状态
      selectedControlBrowser.value = syncController.selectedControlBrowser;
      selectedTargetBrowserTags.value = List.from(syncController.selectedTargetBrowserTags);
      
      // 恢复输入框的值
      xCoordinateController.text = syncController.xCoordinate;
      yCoordinateController.text = syncController.yCoordinate;
      widthController.text = syncController.width;
      heightController.text = syncController.height;
      
      // 添加浏览器状态变化监听器
      void onBrowserStatusChanged(SimpleBrowserInstance browser) {
        browserRunningList.value = SimpleBrowserManager.instance.runningBrowsers;
      }
      
      SimpleBrowserManager.instance.addListener(onBrowserStatusChanged);
      
      // 检查同步状态
      ref.read(syncControllerProvider.notifier).checkSyncStatus().then((status) {
        syncStatus.value = status;
      });
      
      return () {
        SimpleBrowserManager.instance.removeListener(onBrowserStatusChanged);
      };
    }, []);

    return Scaffold(
      backgroundColor: Colors.black.withValues(alpha: 0.5),
      body: Center(
        child: LayoutBuilder(
          builder: (context, constraints) {
            final screenHeight = MediaQuery.of(context).size.height;
            final maxHeight = screenHeight * 0.8;
            final minHeight = maxHeight < 640 ? maxHeight : 640.0;

            return ConstrainedBox(
              constraints: BoxConstraints(
                minHeight: minHeight,
                maxHeight: maxHeight,
              ),
              child: Container(
                width: 700,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                  child: Column(
                    children: [
                      Expanded(
                        child: SingleChildScrollView(
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                            child: Column(
                              spacing: 10,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // 标题
                                Row(
                                  children: [
                                    Container(
                                      width: 16,
                                      height: 16,
                                      decoration: BoxDecoration(
                                        color: const Color(0xFF0C75F8),
                                        borderRadius: BorderRadius.circular(50),
                                      ),
                                    ),
                                    const SizedBox(width: 10),
                                    const Text('智能同步', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
                                    const Spacer(),
                                    // 关闭按钮
                                    InkWell(
                                      onTap: () {
                                        Navigator.of(context).pop();
                                      },
                                      child: Container(
                                        padding: const EdgeInsets.all(4),
                                        decoration: BoxDecoration(
                                          color: Colors.black.withValues(alpha: 0.3),
                                          borderRadius: BorderRadius.circular(50),
                                        ),
                                        child: const Icon(
                                          Icons.close,
                                          color: Colors.white,
                                          size: 16,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                // 分割线
                                const Divider(
                                  color: Color(0xFFE2E8FF),
                                  height: 1,
                                ),
                                const Text('同步控制', style: TextStyle(color: Color(0xFF999999),fontSize: 14, fontWeight: FontWeight.bold)),
                                // 控制浏览器选择
                                Container(
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(color: const Color(0xFFE2E8FF)),
                                  ),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      // const Text('同步控制', style: TextStyle(color: Color(0xFF999999),fontSize: 14, fontWeight: FontWeight.bold)),
                                      // const SizedBox(height: 10),
                                      Row(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          SizedBox(
                                            height: 40,
                                            width: 200,
                                            child: EnhancedDropdownMenu(
                                              height: 40,
                                              hintText: '请选择控制的浏览器',
                                              items: browserRunningList.value.where((browser) {
                                                // 排除已经在被控制列表中的浏览器
                                                final selectedTargetPids = selectedTargetBrowserTags.value
                                                    .map((tag) => tag['value'])
                                                    .toSet();
                                                return !selectedTargetPids.contains(browser.pid.toString());
                                              }).map((e) => {'label': e.name, 'value': e.pid.toString()}).toList(),
                                              value: selectedControlBrowser.value?.pid?.toString(),
                                              onChanged: (value) {
                                                if (value != null) {
                                                  final pid = int.parse(value);
                                                  final newControlBrowser = browserRunningList.value.firstWhere(
                                                    (browser) => browser.pid == pid,
                                                  );
                                                  selectedControlBrowser.value = newControlBrowser;
                                                  
                                                  // 如果新选择的控制浏览器已经在被控制列表中，需要移除
                                                  selectedTargetBrowserTags.value = selectedTargetBrowserTags.value
                                                      .where((tag) => tag['value'] != newControlBrowser.pid.toString())
                                                      .toList();
                                                  
                                                  // 保存状态到controller
                                                  syncController.setSelectedControlBrowser(newControlBrowser);
                                                  syncController.setSelectedTargetBrowserTags(selectedTargetBrowserTags.value);
                                                }
                                              },
                                            ),
                                          ),
                                          const SizedBox(width: 16),
                                          Expanded(
                                            child: TagSelectorWidget(
                                              height: null, // 设置为null让其自适应高度
                                              hintText: '请选择被控制的浏览器',
                                              showSelectAllButton: true, // 启用内置全选按钮
                                              items: browserRunningList.value.where((browser) => 
                                                browser.pid != selectedControlBrowser.value?.pid
                                              ).map((e) => {'label': e.name, 'value': e.pid.toString()}).toList(),
                                              selectedTags: selectedTargetBrowserTags.value,
                                              onTagsChanged: (tags) {
                                                selectedTargetBrowserTags.value = tags;
                                                
                                                // 如果被控制列表中包含了当前的控制浏览器，需要清空控制浏览器
                                                if (selectedControlBrowser.value != null) {
                                                  final controlBrowserInTargets = tags.any(
                                                    (tag) => tag['value'] == selectedControlBrowser.value!.pid.toString()
                                                  );
                                                  if (controlBrowserInTargets) {
                                                    selectedControlBrowser.value = null;
                                                    syncController.setSelectedControlBrowser(null);
                                                  }
                                                }
                                                
                                                // 保存状态到controller
                                                syncController.setSelectedTargetBrowserTags(tags);
                                              },
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),  
                                const SizedBox(height: 10),
                                const Text('窗口排列', style: TextStyle(color: Color(0xFF999999),fontSize: 14, fontWeight: FontWeight.bold)),
                                // 窗口排列
                                Container(
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(color: const Color(0xFFE2E8FF)),
                                  ),
                                  child: Column(
                                    children: [
                                      EnhancedDropdownMenu(
                                        height: 40,
                                        hintText: '请选择窗口排列的浏览器',
                                        items: browserRunningList.value.map((e) => {'label': e.name, 'value': e.pid.toString()}).toList(),
                                        value: selectedWindowArrangementBrowser.value?.pid?.toString(),
                                        onChanged: (value) {
                                          if (value != null) {
                                            final pid = int.parse(value);
                                            final newBrowser = browserRunningList.value.firstWhere(
                                              (browser) => browser.pid == pid,
                                            );
                                            selectedWindowArrangementBrowser.value = newBrowser;
                                          }
                                        },
                                      ),
                                      const SizedBox(height: 10),
                                      Row(
                                        children: [
                                          Expanded(
                                            child: _compactTextField('X坐标', xCoordinateController, () {
                                              syncController.setWindowPosition(
                                                xCoordinateController.text,
                                                yCoordinateController.text,
                                                widthController.text,
                                                heightController.text,
                                              );
                                            }),
                                          ),
                                          const SizedBox(width: 8),
                                          Expanded(
                                            child: _compactTextField('Y坐标', yCoordinateController, () {
                                              syncController.setWindowPosition(
                                                xCoordinateController.text,
                                                yCoordinateController.text,
                                                widthController.text,
                                                heightController.text,
                                              );
                                            }),
                                          ),
                                          const SizedBox(width: 8),
                                          Expanded(
                                            child: _compactTextField('宽度', widthController, () {
                                              syncController.setWindowPosition(
                                                xCoordinateController.text,
                                                yCoordinateController.text,
                                                widthController.text,
                                                heightController.text,
                                              );
                                            }),
                                          ),
                                          const SizedBox(width: 8),
                                          Expanded(
                                            child: _compactTextField('高度', heightController, () {
                                              syncController.setWindowPosition(
                                                xCoordinateController.text,
                                                yCoordinateController.text,
                                                widthController.text,
                                                heightController.text,
                                              );
                                            }),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 20),
                                      Row(
                                        children: [
                                          SecondaryButton(child: const Text('单个应用'), onPressed: () async {
                                            if (selectedWindowArrangementBrowser.value == null) {
                                              if (context.mounted) {
                                                SnackBarUtil().showError(context, '请先选择要排列的浏览器');
                                              }
                                              return;
                                            }
                                            
                                            try {
                                              await ref.read(syncControllerProvider.notifier).arrangeWindow(
                                                selectedWindowArrangementBrowser.value!.pid!,
                                                int.parse(xCoordinateController.text),
                                                int.parse(yCoordinateController.text),
                                                int.parse(widthController.text),
                                                int.parse(heightController.text),
                                              );
                                              
                                              if (context.mounted) {
                                                SnackBarUtil().showSuccess(context, '窗口排列成功');
                                              }
                                            } catch (e) {
                                              if (context.mounted) {
                                                SnackBarUtil().showError(context, '窗口排列失败: $e');
                                              }
                                            }
                                          }),
                                          const SizedBox(width: 8),
                                          SecondaryButton(child: const Text('全部应用'), onPressed: () async {
                                            // 获取所有运行中的浏览器
                                            final runningBrowsers = SimpleBrowserManager.instance.runningBrowsers;
                                            if (runningBrowsers.isEmpty) {
                                              if (context.mounted) {
                                                SnackBarUtil().showError(context, '没有运行中的浏览器');
                                              }
                                              return;
                                            }
                            
                                            try {
                                              // 解析输入的坐标和尺寸
                                              final x = int.parse(xCoordinateController.text);
                                              final y = int.parse(yCoordinateController.text);
                                              final width = int.parse(widthController.text);
                                              final height = int.parse(heightController.text);
                                              
                                              int successCount = 0;
                                              final failedBrowsers = <String>[];
                            
                                              // 逐个应用到所有浏览器
                                              for (final browser in runningBrowsers) {
                                                if (browser.pid != null) {
                                                  try {
                                                    await ref.read(syncControllerProvider.notifier).arrangeWindow(
                                                      browser.pid!,
                                                      x,
                                                      y,
                                                      width,
                                                      height,
                                                    );
                                                    successCount++;
                                                    // 添加短暂延迟，避免同时操作太多窗口
                                                    await Future.delayed(const Duration(milliseconds: 200));
                                                  } catch (e) {
                                                    failedBrowsers.add('${browser.name}: $e');
                                                    debugPrint('排列窗口失败: ${browser.name} (PID: ${browser.pid}) - $e');
                                                  }
                                                }
                                              }
                            
                                              if (context.mounted) {
                                                String message = '已应用到${successCount}个浏览器';
                                                if (failedBrowsers.isNotEmpty) {
                                                  message += '，${failedBrowsers.length}个失败';
                                                  if (failedBrowsers.length <= 2) {
                                                    message += '：${failedBrowsers.join('、')}';
                                                  }
                                                }
                                                
                                                if (successCount > 0) {
                                                  SnackBarUtil().showSuccess(context, message);
                                                } else {
                                                  SnackBarUtil().showError(context, '排列失败：$message');
                                                }
                                              }
                                            } catch (e) {
                                              if (context.mounted) {
                                                SnackBarUtil().showError(context, '应用失败: $e');
                                              }
                                            }
                                          }),
                                          const SizedBox(width: 8),
                                          SecondaryButton(child: const Text('自动排列'), onPressed: () async {
                                            // 获取需要排列的浏览器列表
                                            final List<SimpleBrowserInstance> browsersToArrange = [];
                                            
                                            // 添加控制浏览器
                                            if (selectedControlBrowser.value != null) {
                                              browsersToArrange.add(selectedControlBrowser.value!);
                                            }
                                            
                                            // 添加被控制浏览器
                                            for (final tag in selectedTargetBrowserTags.value) {
                                              final pid = int.parse(tag['value']);
                                              final browser = browserRunningList.value.firstWhere(
                                                (b) => b.pid == pid,
                                                orElse: () => throw Exception('Browser not found'),
                                              );
                                              browsersToArrange.add(browser);
                                            }
                                            
                                            if (browsersToArrange.isEmpty) {
                                              if (context.mounted) {
                                                SnackBarUtil().showError(context, '请先选择需要排列的浏览器');
                                              }
                                              return;
                                            }
                                            
                                            try {
                                              // 获取屏幕尺寸
                                              final screenSize = await ref.read(syncControllerProvider.notifier).getScreenSize();
                                              final screenWidth = screenSize['width'] as int;
                                              final screenHeight = screenSize['height'] as int;
                                              
                                              final browserCount = browsersToArrange.length;
                                              
                                              // 计算排列方式
                                              List<Map<String, int>> positions = _calculatePositions(browserCount, screenWidth, screenHeight);
                                              
                                              // 过滤仍然运行中的浏览器，避免操作已关闭的窗口
                                              final currentRunningBrowsers = SimpleBrowserManager.instance.runningBrowsers;
                                              final validBrowsers = <SimpleBrowserInstance>[];
                                              
                                              for (final browser in browsersToArrange) {
                                                final stillRunning = currentRunningBrowsers.any((running) => running.pid == browser.pid);
                                                if (stillRunning) {
                                                  validBrowsers.add(browser);
                                                } else {
                                                  debugPrint('跳过已关闭的浏览器: ${browser.name} (PID: ${browser.pid})');
                                                }
                                              }
                                              
                                              if (validBrowsers.isEmpty) {
                                                if (context.mounted) {
                                                  SnackBarUtil().showError(context, '所选浏览器都已关闭，请重新选择');
                                                }
                                                return;
                                              }
                                              
                                              // 应用排列到有效的浏览器
                                              int successCount = 0;
                                              final failedBrowsers = <String>[];
                                              
                                              for (int i = 0; i < validBrowsers.length && i < positions.length; i++) {
                                                final browser = validBrowsers[i];
                                                final position = positions[i];

                                                print('arrangeWindow: ${browser.pid}, ${position['x']}, ${position['y']}, ${position['width']}, ${position['height']}');
                                                
                                                if (browser.pid != null) {
                                                  try {
                                                    await ref.read(syncControllerProvider.notifier).arrangeWindow(
                                                      browser.pid!,
                                                      position['x']!,
                                                      position['y']!,
                                                      position['width']!,
                                                      position['height']!,
                                                    );
                                                    successCount++;
                                                    // 添加延迟避免同时操作
                                                    await Future.delayed(const Duration(milliseconds: 200));
                                                  } catch (e) {
                                                    failedBrowsers.add('${browser.name}: $e');
                                                    debugPrint('排列窗口失败: ${browser.name} (PID: ${browser.pid}) - $e');
                                                  }
                                                }
                                              }
                                              
                                              if (context.mounted) {
                                                String message = '自动排列完成，成功排列${successCount}个浏览器';
                                                if (failedBrowsers.isNotEmpty) {
                                                  message += '，${failedBrowsers.length}个失败';
                                                  if (failedBrowsers.length <= 2) {
                                                    message += '：${failedBrowsers.join('、')}';
                                                  }
                                                }
                                                
                                                if (successCount > 0) {
                                                  SnackBarUtil().showSuccess(context, message);
                                                } else {
                                                  SnackBarUtil().showError(context, '排列失败：$message');
                                                }
                                              }
                                            } catch (e) {
                                              if (context.mounted) {
                                                SnackBarUtil().showError(context, '自动排列失败: $e');
                                              }
                                            }
                                          }),
                                        ],
                                      )
                                    ],
                                  ),
                                ),
                                const SizedBox(height: 10),
                                // 系统信息
                                const Text('系统信息', style: TextStyle(color: Color(0xFF999999),fontSize: 14, fontWeight: FontWeight.bold)),
                                Container(
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(color: const Color(0xFFE2E8FF)),
                                  ),
                                  child: Column(
                                    children: [
                                      Row(
                                        children: [
                                          const Text('屏幕尺寸', style: TextStyle(color: Color(0xFF999999),fontSize: 12, fontWeight: FontWeight.w500)),
                                          const SizedBox(width: 10),
                                          FutureBuilder<Map<String, dynamic>>(
                                            future: screenSizeFuture,
                                            builder: (context, snapshot) {
                                              if (snapshot.hasData) {
                                                final screenSize = snapshot.data!;
                                                return Text('${screenSize['width']}*${screenSize['height']}', style: TextStyle(color: Color(0xFF999999),fontSize: 12, fontWeight: FontWeight.w500));
                                              }
                                              if (snapshot.hasError) {
                                                return const Text('获取失败', style: TextStyle(color: Color(0xFF999999),fontSize: 12, fontWeight: FontWeight.w500));
                                              }
                                              return const Text('获取中...', style: TextStyle(color: Color(0xFF999999),fontSize: 12, fontWeight: FontWeight.w500));
                                            },
                                          ),
                                        ],
                                      )
                                    ],
                                  ),
                                ),
                                const SizedBox(height: 10),
                              ],
                            ),
                          ),
                        ),
                      ),
                      // 操作按钮（固定在底部）
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          TextButton(
                            onPressed: () {
                              Navigator.of(context).pop();
                            },
                            child: const Text('取消'),
                          ),
                          const SizedBox(width: 10),
                          ElevatedButton(
                            onPressed: (syncStatus.value == true && !buttonLock.value) ? () async {
                              try {
                                buttonLock.value = true;
                                await ref.read(syncControllerProvider.notifier).stopSynchronization();
                                syncStatus.value = false;
                              } catch (e) {
                                if(context.mounted) SnackBarUtil().showError(context, '停止同步失败,$e');
                              } finally {
                                buttonLock.value = false;
                              }
                            } : null,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF016CFA),
                              foregroundColor: Colors.white,
                            ),
                            child: const Text('停止同步'),
                          ),
                          const SizedBox(width: 10),
                          ElevatedButton(
                            onPressed: (selectedControlBrowser.value != null && selectedTargetBrowserTags.value.isNotEmpty && syncStatus.value == false && !buttonLock.value) ? () async {
                              try {
                                buttonLock.value = true;

                                final result = await ref.read(syncControllerProvider.notifier).startSynchronization(
                                  selectedControlBrowser.value!.pid!,
                                  selectedTargetBrowserTags.value.map((tag) => int.parse(tag['value'])).toList(),
                                );

                                if (result == 'success') {
                                  // 启动后更新状态
                                  await Future.delayed(const Duration(milliseconds: 500));
                                  final status = await ref.read(syncControllerProvider.notifier).checkSyncStatus();
                                  syncStatus.value = status;
                                  if(context.mounted) SnackBarUtil().showSuccess(context, '已开启同步');

                                  // 聚焦控制浏览器
                                  await ref.read(syncControllerProvider.notifier).focusWindow(selectedControlBrowser.value!.pid!);
                                  // 缩小应用窗口
                                  WindowManagerUtil().minimize();
                                }
                              } catch (e) {
                                if(context.mounted) SnackBarUtil().showError(context, '开启同步失败,$e');
                              } finally {
                                buttonLock.value = false;
                              }

                            } : null,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF016CFA),
                              foregroundColor: Colors.white,
                            ),
                            child: const Text('开始同步'),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ) 
      ),
    );
  }

     /// 计算浏览器窗口排列位置
   List<Map<String, int>> _calculatePositions(int browserCount, int screenWidth, int screenHeight) {
     List<Map<String, int>> positions = [];

    //  screenHeight = screenHeight - 100;
     
     if (browserCount == 1) {
       // 单个浏览器，全屏
       positions.add({
         'x': 0,
         'y': 0,
         'width': screenWidth,
         'height': screenHeight,
       });
     } else if (browserCount == 2) {
       // 两个浏览器，左右分屏
       final halfWidth = screenWidth ~/ 2;
       
       // 左边浏览器
       positions.add({
         'x': 0,
         'y': 0,
         'width': halfWidth,
         'height': screenHeight,
       });
       
       // 右边浏览器
       positions.add({
         'x': halfWidth,
         'y': 0,
         'width': halfWidth,
         'height': screenHeight,
       });
     } else if (browserCount <= 4) {
       // 3-4个浏览器，四宫格
       final halfWidth = screenWidth ~/ 2;
       final halfHeight = screenHeight ~/ 2;
       
       // 左上
       positions.add({
         'x': 0,
         'y': 0,
         'width': halfWidth,
         'height': halfHeight,
       });
       
       // 右上
       positions.add({
         'x': halfWidth,
         'y': 0,
         'width': halfWidth,
         'height': halfHeight,
       });
       
       // 如果有第3个，放在左下
       if (browserCount >= 3) {
         positions.add({
           'x': 0,
           'y': halfHeight,
           'width': halfWidth,
           'height': halfHeight,
         });
       }
       
       // 如果有第4个，放在右下
       if (browserCount >= 4) {
         positions.add({
           'x': halfWidth,
           'y': halfHeight,
           'width': halfWidth,
           'height': halfHeight,
         });
       }
     } else if (browserCount <= 6) {
       // 5-6个浏览器，3×2排列
       final cols = 3;
       final rows = 2;
       final windowWidth = screenWidth ~/ cols;
       final windowHeight = screenHeight ~/ rows;
       
       for (int i = 0; i < browserCount; i++) {
         final row = i ~/ cols;
         final col = i % cols;
         
         positions.add({
           'x': col * windowWidth,
           'y': row * windowHeight,
           'width': windowWidth,
           'height': windowHeight,
         });
       }
     } else {
       // 7个及以上浏览器，3×3排列
       final cols = 3;
       final rows = (browserCount / cols).ceil();
       final windowWidth = screenWidth ~/ cols;
       final windowHeight = screenHeight ~/ rows;
       
       for (int i = 0; i < browserCount; i++) {
         final row = i ~/ cols;
         final col = i % cols;
         
         positions.add({
           'x': col * windowWidth,
           'y': row * windowHeight,
           'width': windowWidth,
           'height': windowHeight,
         });
       }
     }
     
     return positions;
   }

   Widget _compactTextField(String label, TextEditingController controller, VoidCallback onSave) {
     return Column(
       crossAxisAlignment: CrossAxisAlignment.center,
       children: [
         Text(
           label,
           style: const TextStyle( color: Color(0xFF999999),fontSize: 12, fontWeight: FontWeight.w500),
         ),
         const SizedBox(height: 4),
         SizedBox(
           height: 36,
           child: CustomTextField(
             height: 10,
             controller: controller,
             hint: label,
             lines: 1,
             onChanged: (value) {
               // 延迟保存，让controller都更新完
               Future.delayed(const Duration(milliseconds: 100), onSave);
             },
           ),
         ),
       ],
     );
   }
}
