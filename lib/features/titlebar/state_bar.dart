import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:frontend_re/features/titlebar/smartSync/views/sync_dialog.dart';
import 'package:frontend_re/features/titlebar/welcome/welcome_page.dart';
import 'package:frontend_re/router/navigation_history_provider.dart';
import 'package:frontend_re/domain/services/user_info_service.dart';
import 'package:frontend_re/widgets/custom_border_button.dart';
import 'package:frontend_re/widgets/global_search_widget.dart';
import 'package:go_router/go_router.dart';
import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class StateBar extends HookConsumerWidget {
  const StateBar({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 控制菜单展开状态
    final isOpen = useState(false);

    final userInfo = ref.watch(userInfoNotifierProvider);

    return Container(
      height: 50,
      width: double.infinity,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainer,
        borderRadius: BorderRadius.circular(50),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 左侧
          Row(
            spacing: 16,
            children: [
              const SizedBox.shrink(),
              Container(
                width: 30,
                height: 30,
                decoration: const BoxDecoration(
                  color: Color(0xFF0C75F8),
                  shape: BoxShape.circle,
                ),
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: SvgPicture.asset(
                    'assets/svg/logo.svg',
                    colorFilter: const ColorFilter.mode(Colors.white, BlendMode.srcIn),
                  ),
                ),
              ),
              CustomBorderButton(
                iconPath: 'assets/svg/sync_btn.svg',
                text: '智能同步',
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (context) => const SyncDialog(),
                  );
                },
              ),
              CustomBorderButton(
                iconPath: 'assets/svg/store_btn.svg',
                text: '插件商店',
                onPressed: () {
                  print('智能同步按钮被点击');
                },
              ),
            ]
          ),

          // 中间 - 全局搜索
          const GlobalSearchWidget(),

          // 右侧
          Row(
            spacing: 16,
            children: [
              // 推广按钮
              CustomBorderButton(
                isImage: true,
                iconPath: 'assets/images/common/money.png',
                horizontalPadding: 4,
                verticalPadding: 4,
                iconSize: 24,
                backgroundColor: const Color(0xFFFF7A46),
                onPressed: () {
                  // 使用导航助手跳转到用户设置，自动保存当前位置
                  NavigationHelper.goToPublicRoute(context, ref, 'promotion');
                },
              ),
              // 设置按钮
              CustomBorderButton(
                iconPath: 'assets/svg/setting_btn.svg',
                horizontalPadding: 8,
                verticalPadding: 8,
                onPressed: () {
                  // 使用导航助手跳转到用户设置，自动保存当前位置
                  NavigationHelper.goToPublicRoute(context, ref, 'appSetting');
                },
              ),
              // 帮助按钮
              CustomBorderButton(
                horizontalPadding: 8,
                verticalPadding: 8,
                iconPath: 'assets/svg/help_btn.svg',
                onPressed: () {
                  NavigationHelper.goToPublicRoute(context, ref, 'helpCenter');
                },
              ),
              // 头像
              Tooltip(
                message: userInfo.value?.username ?? 'dddd',
                preferBelow: false,
                verticalOffset: 20,
                child: MouseRegion(
                  cursor: SystemMouseCursors.click,
                  child: GestureDetector(
                    onTap: () {
                      NavigationHelper.goToUserSettings(context, ref);
                    },
                    child: Container(
                      width: 34,
                      height: 34,
                      decoration: const BoxDecoration(
                        color: Color(0xFFE0E7EF),
                        shape: BoxShape.circle,
                      ),
                      child: ClipOval(
                        child: Image.asset(
                          'assets/images/user.png',
                          width: 30,
                          height: 30,
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              // 更多按钮改为下拉菜单
              Container(
                width: 30,
                height: 30,
                decoration: BoxDecoration(
                  color: const Color(0xFF1E1C1D),
                  borderRadius: BorderRadius.circular(50),
                ),
                child: DropdownButton2<String>(
                  isExpanded: true,
                  // 下拉菜单样式配置
                  dropdownStyleData: DropdownStyleData(
                    // 设置弹出菜单的最大高度
                    maxHeight: 300,
                    // 设置弹出菜单的宽度
                    width: 120,
                    padding: EdgeInsets.zero, // 移除内边距
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      color: Colors.white,
                    ),
                    // 菜单位置配置
                    offset: const Offset(0, 8),
                  ),
                  // 按钮样式配置
                  buttonStyleData: const ButtonStyleData(
                    padding: EdgeInsets.zero,
                    height: 30,
                    width: 30,
                  ),
                  // 菜单项样式配置
                  menuItemStyleData: const MenuItemStyleData(
                    height: 36,
                    padding: EdgeInsets.symmetric(horizontal: 12),
                  ),
                  underline: Container(), // 移除下划线
                  onMenuStateChange: (isOpenState) {
                    // 监听菜单状态
                    isOpen.value = isOpenState;
                  },
                  onChanged: (value) {
                    if (value == 'modifyPassword') {
                      print('修改密码选项被点击');
                      // 这里添加修改密码的逻辑
                    } else if (value == 'logout') {
                      ref.read(userInfoNotifierProvider.notifier).clearUserInfo();
                      context.goNamed('login');
                      // 这里添加退出登录的逻辑
                    }
                  },
                  // 自定义按钮
                  customButton: Container(
                    width: 30,
                    height: 30,
                    decoration: BoxDecoration(
                      color: const Color(0xFF1E1C1D),
                      borderRadius: BorderRadius.circular(50),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(8),
                      child: SvgPicture.asset(
                        'assets/svg/right_default.svg',
                        colorFilter: const ColorFilter.mode(
                          Color(0xFFFFFFFF),
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                  ),
                  // 菜单项
                  items: const [
                    DropdownMenuItem<String>(
                      value: 'modifyPassword',
                      child: Row(
                        children: [
                          Icon(Icons.lock_outline, size: 16, color: Color(0xFF8D8E93)),
                          SizedBox(width: 8),
                          Text(
                            '修改密码',
                            style: TextStyle(fontSize: 14, color: Color(0xFF8D8E93)),
                          ),
                        ],
                      ),
                    ),
                    DropdownMenuItem<String>(
                      value: 'logout',
                      child: Row(
                        children: [
                          Icon(Icons.exit_to_app, size: 16, color: Color(0xFF8D8E93)),
                          SizedBox(width: 8),
                          Text(
                            '退出登录',
                            style: TextStyle(fontSize: 14, color: Color(0xFF8D8E93)),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox.shrink(),
            ]
          ),
        ],
      ),
    );
  }
}
