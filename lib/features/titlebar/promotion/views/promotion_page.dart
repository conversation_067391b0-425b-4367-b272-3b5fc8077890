import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:frontend_re/router/navigation_history_provider.dart';
import 'package:frontend_re/widgets/custom_solid_button.dart';
import 'package:frontend_re/widgets/painters/index.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

// NotchedPanelClipper 已移动到 lib/widgets/painters/notched_panel_painters.dart

class PromotionPage extends HookConsumerWidget {
  const PromotionPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Column(
          spacing: 20,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomPaint(
              painter: NotchedPanelPainter(),
              child: Stack(
                children: [
                  Container(
                  height: 400,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    // color: const Color.fromARGB(255, 255, 255, 255),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(20.0),
                    child: Column(
                      spacing: 6,
                      children: [
                        // 宣传图片
                        SizedBox(
                          width: double.infinity,
                          height: 200,
                          child: Stack(
                            children: [
                              ClipPath(
                                clipper: NotchedPanelClipper(),
                                child: Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(12),
                                    image: const DecorationImage(
                                      image: AssetImage('assets/images/common/bk.png'),
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),
                              ),
                              // 图片
                              Positioned(
                                right: 0,
                                top: 0,
                                bottom: 0,
                                child: Image.asset('assets/images/common/money2.png',scale: 3,),
                              ),
                              // 描述
                              Positioned(
                                top: 0,
                                bottom: 0,
                                left: 50,
                                right: 100,
                                child: Column(
                                  spacing: 6,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    const Text('推广达人招募令',style: TextStyle(fontSize: 18,fontWeight: FontWeight.bold,color: Color(0xFF016CFA)),),
                                    const Text('奖励规则',style: TextStyle(fontSize: 32,color: Color(0xFF333333)),),
                                    Flexible(
                                      child: RichText(
                                        text: const TextSpan(
                                          children: [
                                            TextSpan(text: '在未来',style: TextStyle(fontSize: 14,color: Color(0xFF333333))),
                                            TextSpan(text: '24',style: TextStyle(fontSize: 14,color: Color(0xFF016CFA))),
                                            TextSpan(text: '个月里，你可以获得受邀用户',style: TextStyle(fontSize: 14,color: Color(0xFF333333))),
                                            TextSpan(text: '10%',style: TextStyle(fontSize: 14,color: Color(0xFF016CFA))),
                                            TextSpan(text: '推广佣金',style: TextStyle(fontSize: 14,color: Color(0xFF333333))),
                                          ],
                                        ),
                                        overflow: TextOverflow.ellipsis,
                                        maxLines: 2,
                                      ),
                                    ),
                                  ],
                                ),
                              )
                            ],
                          ),
                        ),
                        
                        // 邀请方式
                        Flexible(
                          child: SizedBox(
                            width: double.infinity,
                            // color: Colors.red,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const SizedBox(height: 20),
                                Row(
                                  children: [
                                    Container(
                                      width: 16,
                                      height: 16,
                                      decoration: BoxDecoration(
                                        color: const Color(0xFF016CFA),
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    const Text('邀请方式',style: TextStyle(fontSize: 16,color: Color(0xFF333333)),),
                                    const SizedBox(width: 8),
                                    TextButton(onPressed: (){}, child: const Text('受邀记录',style: TextStyle(fontSize: 14,color: Color(0xFF016CFA)),)),
                                    const Spacer(),
                                    CustomSolidButton(
                                      textSize: 14,
                                      width: 100,
                                      height: 32,
                                      iconSize: 16,
                                      iconTextSpacing: 0,
                                      text: '自定义',
                                      iconPath: 'assets/svg/add.svg',
                                      iconColor: Colors.white,
                                      iconColorHover: Colors.white,
                                      iconBackgroundColor: const Color(0xFF016CFA),
                                      iconBackgroundColorHover: const Color(0xFF4A90E2),
                                      backgroundColor: const Color(0xFF016CFA),
                                      backgroundColorHover: const Color(0xFF4A90E2),
                                      isCenter: true,
                                      onPressed: () {
                                        print('复制邀请码');
                                      },
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 10),
                                const Text(
                                  '分享邀请链接或邀请码，推荐用户使用产品将获得奖励！',
                                  style: TextStyle(fontSize: 12,color: Color(0xFF8D8E93)),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                const Spacer(),
                                Row(
                                  children: [
                                    SizedBox(
                                      width: 300,
                                      child: Container(
                                        height: 40,
                                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                                        decoration: BoxDecoration(
                                          borderRadius: BorderRadius.circular(50),
                                          color: const Color(0xFFF3F4F8), // 浅灰色背景表示只读
                                        ),
                                        child: const Align(
                                          alignment: Alignment.centerLeft,
                                          child: SelectableText(
                                            'https://www.baidu.com',
                                            style: TextStyle(
                                              fontSize: 14,
                                              color: Color(0xFF666666), // 稍微暗一点的颜色表示只读
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    CustomSolidButton(
                                      textSize: 14,
                                      width: 100,
                                      height: 40,
                                      iconSize: 16,
                                      iconTextSpacing: 0,
                                      text: '复制',
                                      iconPath: 'assets/svg/copy.svg',
                                      iconColor: Colors.white,
                                      iconColorHover: Colors.white,
                                      iconBackgroundColor: const Color(0xFF016CFA),
                                      iconBackgroundColorHover: const Color(0xFF4A90E2),
                                      backgroundColor: const Color(0xFF016CFA),
                                      backgroundColorHover: const Color(0xFF4A90E2),
                                      isCenter: true,
                                      onPressed: () {
                                        print('复制邀请码');
                                      },
                                    ),
                                    const SizedBox(width: 40),
                                    SizedBox(
                                      width: 200,
                                      child: Container(
                                        height: 40,
                                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                                        decoration: BoxDecoration(
                                          borderRadius: BorderRadius.circular(50),
                                          color: const Color(0xFFF3F4F8), // 浅灰色背景表示只读
                                        ),
                                        child: const Align(
                                          alignment: Alignment.centerLeft,
                                          child: Row(
                                            children: [
                                              Text('邀请码：',style: TextStyle(fontSize: 14,color: Color(0xFF666666)),),
                                              SelectableText(
                                                '123456',
                                                style: TextStyle(
                                                  fontSize: 14,
                                                  color: Color(0xFF666666), // 稍微暗一点的颜色表示只读
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    CustomSolidButton(
                                      textSize: 14,
                                      width: 100,
                                      height: 40,
                                      iconSize: 16,
                                      iconTextSpacing: 0,
                                      text: '复制',
                                      iconPath: 'assets/svg/copy.svg',
                                      iconColor: Colors.white,
                                      iconColorHover: Colors.white,
                                      iconBackgroundColor: const Color(0xFF016CFA),
                                      iconBackgroundColorHover: const Color(0xFF4A90E2),
                                      backgroundColor: const Color(0xFF016CFA),
                                      backgroundColorHover: const Color(0xFF4A90E2),
                                      isCenter: true,
                                      onPressed: () {
                                        print('复制邀请码');
                                      },
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        )
                      ],
                    ),
                    ),
                  ),
                  // 返回按钮 - 浮动在左上角
                  Positioned(
                    top: 0,
                    left: 0,
                    child: Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: const Color(0xFFFFFFFF),
                        borderRadius: BorderRadius.circular(24),
                      ),
                      child: IconButton(
                        onPressed: () {
                          NavigationHelper.safeGoBack(context, ref);
                        },
                        icon: const Icon(Icons.arrow_back),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Container(
              // height: 400,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: const Color.fromARGB(255, 255, 255, 255),
              ),
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Container(
                          width: 16,
                          height: 16,
                          decoration: BoxDecoration(
                            color: const Color(0xFF016CFA),
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        const SizedBox(width: 8),
                        const Text('数据摘要',style: TextStyle(fontSize: 16,color: Color(0xFF333333)),),
                        const SizedBox(width: 8),
                        TextButton(onPressed: (){}, child: const Text('体现记录',style: TextStyle(fontSize: 14,color: Color(0xFF016CFA)),)),
                      ],
                    ),
                    
                    const SizedBox(height: 10),

                    Row(
                      children: [
                        // 图标
                        Container(
                          width: 60,
                          height: 60,
                          decoration: BoxDecoration(
                            color: const Color(0xFFEAF2FC),
                            borderRadius: BorderRadius.circular(50),
                          ),
                          child: Center(
                            child: SvgPicture.asset(
                              'assets/svg/wallet.svg',
                              width: 30,
                              height: 30,
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        const SizedBox(
                          height: 60,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text('可提现余额',style: TextStyle(fontSize: 14,color: Color(0xFF333333)),),
                              Text('￥${1000}',style: TextStyle(fontSize: 14,color: Color(0xFF016CFA)),),
                            ],
                          ),
                        ),
                        const SizedBox(width: 32),
                        CustomSolidButton(
                          textSize: 14,
                          // width: 100,
                          height: 32,
                          iconSize: 16,
                          iconTextSpacing: 0,
                          text: '现在提现',
                          iconPath: 'assets/svg/withdrawal.svg',
                          iconColor: Colors.white,
                          iconColorHover: Colors.white,
                          iconBackgroundColor: const Color(0xFF016CFA),
                          iconBackgroundColorHover: const Color(0xFF4A90E2),
                          backgroundColor: const Color(0xFF016CFA),
                          backgroundColorHover: const Color(0xFF4A90E2),
                          isCenter: true,
                          onPressed: () {
                            print('复制邀请码');
                          },
                        ),
                      ],
                    ),
                  
                    const SizedBox(height: 32),
                     
                                         // 统计数据卡片 - 5个独立的方块
                     Row(
                       children: [
                         // 点击数
                         _buildStatCard(
                           title: '点击数(个)',
                           value: '0',
                           valueColor: const Color(0xFF016CFA),
                         ),
                         const SizedBox(width: 12),
                         // 注册数
                         _buildStatCard(
                           title: '注册数(个)',
                           value: '0',
                           valueColor: const Color(0xFF016CFA),
                         ),
                         const SizedBox(width: 12),
                         // 交易金额
                         _buildStatCard(
                           title: '交易金额(\$)',
                           value: '0.00',
                           valueColor: const Color(0xFF016CFA),
                         ),
                         const SizedBox(width: 12),
                         // 交易总金额
                         _buildStatCard(
                           title: '交易总金额(\$)',
                           value: '0.00',
                           valueColor: const Color(0xFF016CFA),
                         ),
                         const SizedBox(width: 12),
                         // 已到账佣金
                         _buildStatCard(
                           title: '已到账佣金(\$)',
                           value: '0.00',
                           valueColor: const Color(0xFF016CFA),
                         ),
                       ],
                     )
                  ],
                ),
              ),
            ),
          
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  /// 构建独立的统计卡片
  Widget _buildStatCard({
    required String title,
    required String value,
    required Color valueColor,
  }) {
    return Expanded(
      child: Container(
        constraints: const BoxConstraints(
          maxWidth: 200,
          minWidth: 180,
        ),
        height: 120,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: const Color(0xFFF3F4F9),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 12,
                color: Color(0xFF8D8E93),
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            Text(
              value,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: valueColor,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}
