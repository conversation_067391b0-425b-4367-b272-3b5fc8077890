import 'package:flutter/material.dart';
import 'package:frontend_re/core/constants/constant.dart';
import 'package:frontend_re/router/navigation_history_provider.dart';
import 'package:frontend_re/features/titlebar/userSetting/views/widgets/account_binding_card.dart';
import 'package:frontend_re/features/titlebar/userSetting/views/widgets/user_info_form.dart';
import 'package:frontend_re/widgets/s_back_button.dart';
import 'package:frontend_re/widgets/custom_card.dart';
import 'package:frontend_re/widgets/custom_dropdown_menu.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

// 自定义 ScrollBehavior，不显示默认滚动条
class _NoThumbScrollBehavior extends ScrollBehavior {
  @override
  Widget buildScrollbar(
      BuildContext context, Widget child, ScrollableDetails details) {
    // 直接返回 child，不构建额外的滚动条
    return child;
  }

  @override
  Widget buildOverscrollIndicator(
      BuildContext context, Widget child, ScrollableDetails details) {
    // 直接返回 child，不构建额外的越界指示器
    return child;
  }
}

class UserSettings extends ConsumerWidget {
  const UserSettings({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final controller = TextEditingController();
    // 创建滚动控制器
    final ScrollController scrollController = ScrollController();

    return Scaffold(
      body: ScrollConfiguration(
        behavior: _NoThumbScrollBehavior(), // 应用自定义行为
        child: Scrollbar(
          // 设置滚动控制器
          controller: scrollController,
          // 设置滚动条始终可见
          thumbVisibility: true,
          // 设置滚动条厚度
          thickness: 6,
          // 设置滚动条圆角
          radius: const Radius.circular(10),
          // 设置滚动条颜色
          child: ClipRRect(
            borderRadius: const BorderRadius.only(topLeft: Radius.circular(20), topRight: Radius.circular(20)), // 设置滚动区域圆角
            child: SingleChildScrollView(
              // 关联滚动控制器
              controller: scrollController,
              child: Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: Column(
                  spacing: 16,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 返回按钮
                    SBackButton(
                      onPressed: () {
                        NavigationHelper.safeGoBack(context, ref);
                      },
                    ),
                    // 账户信息
                    CustomCard(
                      title: '账户信息',
                      description: '',
                      width: double.infinity,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          Image.asset('assets/images/user_max.png', scale: 2,),
                          UserInfoForm(controller: controller),
                        ],
                      ),
                    ),
                    // 账号关联
                    CustomCard(
                      title: '账号关联',
                      description: '绑定后可通过第三方应用快速登录',
                      width: double.infinity,
                      child: Wrap(
                        spacing: 16,
                        runSpacing: 16,
                        children: [
                          AccountBindingCard(
                            iconPath: 'assets/svg/google.svg',
                            accountName: 'Google账号',
                            statusText: '已绑定',
                            onTap: () {
                              print('点击了Google账号绑定卡片');
                            },
                          ),
                          AccountBindingCard(
                            iconPath: 'assets/svg/facebook.svg',
                            accountName: 'Facebook账号',
                            statusText: '未绑定',
                            onTap: () {
                              print('点击了GitHub账号绑定卡片');
                            },
                          ),
                          AccountBindingCard(
                            iconPath: 'assets/svg/vk.svg',
                            accountName: 'VK账号',
                            statusText: '已绑定',
                            onTap: () {
                              print('点击了VK账号绑定卡片');
                            },
                          ),
                        ],
                      ),
                    ),
                    // 其他
                    CustomCard(
                      title: '其他',
                      description: '',
                      width: double.infinity,
                      child: Column(
                        spacing: 16,
                        children: [
                          // 语言
                          Row(
                            children: [
                              const SizedBox(
                                width: 50,
                                child: Text(
                                '语言',
                                style: TextStyle(
                                    color: Color(0xFF333333), fontSize: 14),
                              )),
                              CustomDropdownMenu(
                                height: 30,
                                items: languageOptions,
                                value: 'zh-CN',
                                onChanged: (value) {}, 
                              )
                            ],
                          ),
                          // 时区
                          Row(
                            children: [
                              const SizedBox(
                                width: 50,
                                child: Text(
                                '时区',
                                style: TextStyle(
                                    color: Color(0xFF333333), fontSize: 14),
                              )),
                              CustomDropdownMenu(
                                height: 30,
                                items: timezoneOptions,
                                value: 'Asia/Shanghai',
                                onChanged: (value) {}, 
                              )    
                            ],
                          ),
                          // 通知
                          Row(
                            children: [
                              const SizedBox(
                                width: 50,
                                child: Text(
                                '通知',
                                style: TextStyle(
                                    color: Color(0xFF333333), fontSize: 14),
                              )),
                              Transform.scale(
                                alignment: Alignment.centerLeft,
                                scale: 0.7,
                                child: Switch(
                                  value: true,
                                  onChanged: (value) {},
                                ),
                              ),
                              const Text('订阅营销通知(邮件/短信)',style: TextStyle(color: Color(0xFF8D8E93), fontSize: 14),)
                            ],
                          ),
                        ],
                      )
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

