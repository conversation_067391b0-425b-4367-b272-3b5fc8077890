import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:frontend_re/domain/services/user_info_service.dart';
import 'package:frontend_re/widgets/custom_border_button.dart';
import 'package:frontend_re/widgets/custom_text_field.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class UserInfoForm extends HookConsumerWidget {
  final TextEditingController controller;
  
  const UserInfoForm({
    super.key, 
    required this.controller,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userInfo = ref.watch(userInfoNotifierProvider);

    final passwordController = useTextEditingController();
    final newPasswordController = useTextEditingController();
    final confirmPasswordController = useTextEditingController();
    
    // 在页面构建时主动获取用户信息
    useEffect(() {
      Future.microtask(() async {
        try {
          await ref.read(userInfoNotifierProvider.notifier).getUserInfo();
        } catch (e) {
          print('获取用户信息失败: $e');
        }
      });
      return null;
    }, []);
    
    return SizedBox(
      width: 400, // 设置容器宽度
      child: userInfo.when(
        data: (user) => Column(
          spacing: 10,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 信息标题
            _buildSectionTitle('信息'),
            
            // 信息区域 - 使用Column和Row组合
            Column(
              spacing: 10,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 用户名 - 只读显示
                _buildReadOnlyField('用户名', user?.username ?? '未知'),
                
                // 手机 - 只读显示
                _buildReadOnlyField('手机', user?.telephone ?? '未知'),
                
                // 邮箱 - 只读显示
                _buildReadOnlyField('邮箱', user?.email ?? '未知'),
                
                // 实名认证
                _buildAuthenticationStatus(user?.realNameType ?? 0),
              ],
            ),
            
            // 分割线
            const Divider(
              height: 1,
              color: Color(0xFFE0E0E0),
            ),
            
            // 更改密码标题
            _buildSectionTitle('更改密码'),
            
            // 密码表单字段
            _buildFormField('原始密码', passwordController),
            _buildFormField('新密码', newPasswordController),
            _buildFormField('确认密码', confirmPasswordController),

            // 确认更改密码按钮
            CustomBorderButton(
              iconPath: "assets/svg/auto_fill.svg",
              text: '确认更改密码',
              onPressed: () {
                //TODO
              },
            ),
          ],
        ),
        loading: () => const Center(
          child: CircularProgressIndicator(),
        ),
        error: (error, stack) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text('加载用户信息失败: $error'),
            ],
          ),
        ),
      ),
    );
  }

  // 构建带圆点图标的标题
  Widget _buildSectionTitle(String title) {
    return Row(
      children: [
        Container(
          width: 16,
          height: 16,
          decoration: const BoxDecoration(
            color: Color(0xFF0C75F8),
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 8),
        Text(
          title,
          style: const TextStyle(
            fontSize: 14,
          ),
        ),
      ],
    );
  }

  // 构建只读显示字段（保持输入框样式）
  Widget _buildReadOnlyField(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(
          width: 80,
          child: Text(
            label,
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF333333),
            ),
          ),
        ),
        const SizedBox(width: 10),
        SizedBox(
          width: 300,
          child: Container(
            height: 40,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(50),
              color: const Color(0xFFF3F4F8), // 浅灰色背景表示只读
            ),
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                value,
                style: const TextStyle(
                  fontSize: 14,
                  color: Color(0xFF666666), // 稍微暗一点的颜色表示只读
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  // 构建表单字段
  Widget _buildFormField(String label, TextEditingController controller) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(
          width: 80,
          child: Text(
            label,
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF333333),
            ),
          ),
        ),
        const SizedBox(width: 10),
        SizedBox(
          width: 300,
          child: CustomTextField(
            controller: controller,
            hint: "选填",
            lines: 1,
            height: 12,
            onChanged: (value) {},
          ),
        ),
      ],
    );
  }

  // 构建实名认证状态
  Widget _buildAuthenticationStatus(int realNameType) {
    final bool isAuthenticated = realNameType > 0;
    
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        const SizedBox(
          width: 80,
          child: Text(
            '实名认证',
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF333333),
            ),
          ),
        ),
        const SizedBox(width: 10),
        Row(
          children: [
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                color: isAuthenticated ? const Color(0xFF4CAF50) : const Color(0xFFFF9800),
                shape: BoxShape.circle,
              ),
              child: Icon(
                isAuthenticated ? Icons.check : Icons.priority_high,
                color: Colors.white,
                size: 16,
              ),
            ),
            const SizedBox(width: 8),
            Text(
              isAuthenticated ? '已认证' : '未认证',
              style: TextStyle(
                fontSize: 14,
                color: isAuthenticated ? const Color(0xFF4CAF50) : const Color(0xFFFF9800),
              ),
            ),
          ],
        ),
      ],
    );
  }
} 

