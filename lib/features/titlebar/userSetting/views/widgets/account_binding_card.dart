import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class AccountBindingCard extends StatefulWidget {
  final String iconPath;
  final String accountName;
  final String statusText;
  final VoidCallback? onTap;
  final double width;
  final double height;
  
  const AccountBindingCard({
    super.key,
    required this.iconPath,
    required this.accountName,
    required this.statusText,
    this.onTap,
    this.width = 200,
    this.height = 30,
  });

  @override
  State<AccountBindingCard> createState() => _AccountBindingCardState();
}

class _AccountBindingCardState extends State<AccountBindingCard> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: widget.onTap,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          width: widget.width,
          height: widget.height,
          padding: const EdgeInsets.symmetric(horizontal: 10),
          decoration: BoxDecoration(
            color: _isHovered 
                ? const Color(0xFF0C75F8) 
                : const Color(0xFFF3F4F8),
            borderRadius: BorderRadius.circular(50),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  // 使用SvgPicture渲染SVG图标，颜色根据悬停状态变化
                  SvgPicture.asset(
                    widget.iconPath,
                    width: 20,
                    height: 20,
                    // 当悬停时，更改SVG图标的颜色为白色
                    colorFilter: _isHovered 
                        ? const ColorFilter.mode(Colors.white, BlendMode.srcIn)
                        : null,
                  ),
                  const SizedBox(width: 10),
                  Text(
                    widget.accountName,
                    style: TextStyle(
                      fontSize: 12,
                      color: _isHovered 
                          ? Colors.white 
                          : const Color(0xFF8D8E93),
                    ),
                  ),
                ],
              ),
              Text(
                widget.statusText,
                style: TextStyle(
                  fontSize: 12,
                  color: _isHovered 
                      ? Colors.white 
                      : const Color(0xFF8D8E93),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
} 
