// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_setting_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$userSettingControllerHash() =>
    r'5eeee5ea71d1f4f7581a963a392498b3030334a0';

/// See also [UserSettingController].
@ProviderFor(UserSettingController)
final userSettingControllerProvider =
    AutoDisposeAsyncNotifierProvider<UserSettingController, void>.internal(
  UserSettingController.new,
  name: r'userSettingControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$userSettingControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$UserSettingController = AutoDisposeAsyncNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
