import 'package:frontend_re/domain/models/user_model.dart';
import 'package:frontend_re/domain/services/user_info_service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'user_setting_controller.g.dart';

@riverpod
class UserSettingController extends _$UserSettingController {
  @override
  FutureOr<void> build() {
    // 初始化逻辑
    
  }

  /// 获取用户信息
  Future<UserModel> getUserInfo() async {
    return ref.read(userInfoNotifierProvider.notifier).getUserInfo();
  }

  /// 更新用户信息
  Future<String> updateUserInfo(UpdateUserInfoRequest user) async {
    return ref.read(userInfoNotifierProvider.notifier).updateUserInfo(user);
  }
}
