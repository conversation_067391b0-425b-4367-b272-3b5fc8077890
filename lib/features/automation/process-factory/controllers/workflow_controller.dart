import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../models/operation.dart';

/// 简化的工作流状态
class WorkflowState {
  final List<Operation> operations;
  final String name;
  final String? description;
  
  const WorkflowState({
    this.operations = const [],
    this.name = '未命名工作流',
    this.description,
  });
  
  WorkflowState copyWith({
    List<Operation>? operations,
    String? name,
    String? description,
  }) {
    return WorkflowState(
      operations: operations ?? this.operations,
      name: name ?? this.name,
      description: description ?? this.description,
    );
  }
}

/// 简化的工作流控制器
class WorkflowController extends StateNotifier<WorkflowState> {
  WorkflowController() : super(const WorkflowState());
  
  /// 添加操作
  void addOperation(Operation operation, [int? index]) {
    final newOperations = [...state.operations];
    if (index != null && index >= 0 && index <= newOperations.length) {
      newOperations.insert(index, operation);
    } else {
      newOperations.add(operation);
    }
    state = state.copyWith(operations: newOperations);
  }
  
  /// 移除操作
  void removeOperation(int index) {
    if (index >= 0 && index < state.operations.length) {
      final newOperations = [...state.operations];
      newOperations.removeAt(index);
      state = state.copyWith(operations: newOperations);
    }
  }
  
  /// 更新操作
  void updateOperation(int index, Operation operation) {
    if (index >= 0 && index < state.operations.length) {
      final newOperations = [...state.operations];
      newOperations[index] = operation;
      state = state.copyWith(operations: newOperations);
    }
  }
  
  /// 重新排序
  void reorderOperations(int oldIndex, int newIndex) {
    if (oldIndex < newIndex) newIndex -= 1;
    
    final newOperations = [...state.operations];
    final operation = newOperations.removeAt(oldIndex);
    newOperations.insert(newIndex, operation);
    state = state.copyWith(operations: newOperations);
  }
  
  /// 清空所有操作
  void clearOperations() {
    state = state.copyWith(operations: []);
  }
  
  /// 更新工作流信息
  void updateWorkflowInfo({String? name, String? description}) {
    state = state.copyWith(name: name, description: description);
  }
  
  /// 验证工作流
  List<String> validateWorkflow() {
    final errors = <String>[];
    
    for (int i = 0; i < state.operations.length; i++) {
      final operation = state.operations[i];
      final unconfigured = operation.unconfiguredParams;
      
      if (unconfigured.isNotEmpty) {
        errors.add('步骤 ${i + 1} (${operation.displayName}) 包含未配置的参数: ${unconfigured.join(", ")}');
      }
    }
    
    return errors;
  }
}

/// Provider 定义
final workflowControllerProvider = 
    StateNotifierProvider<WorkflowController, WorkflowState>((ref) {
  return WorkflowController();
});

/// 便捷访问操作列表
final operationsProvider = Provider<List<Operation>>((ref) {
  return ref.watch(workflowControllerProvider).operations;
});

/// 便捷访问工作流名称
final workflowNameProvider = Provider<String>((ref) {
  return ref.watch(workflowControllerProvider).name;
}); 
