import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../models/operation.dart';

/// 简化的编辑对话框
class EditDialog extends HookConsumerWidget {
  const EditDialog({
    super.key, 
    required this.operation,
    required this.onSave,
  });

  final Operation operation;
  final Function(Operation updatedOperation) onSave;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 解析命令中的参数
    final parameters = _parseParameters(operation.command);
    final paramValues = useState<Map<String, String>>(operation.parameters);

    return Dialog(
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: 500,
        constraints: const BoxConstraints(maxHeight: 600),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题栏
            Container(
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                color: Color(0xFF0C75F8),
                borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
              ),
              child: Row(
                children: [
                  Icon(operation.icon, color: Colors.white, size: 20),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      '配置参数 - ${operation.name}',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close, color: Colors.white),
                  ),
                ],
              ),
            ),
            
            // 内容区域
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 操作描述
                    if (operation.description?.isNotEmpty == true) ...[
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: const Color(0xFFF8F9FA),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          operation.description!,
                          style: const TextStyle(
                            fontSize: 14,
                            color: Color(0xFF666666),
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                    ],
                    
                    // 命令预览
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: const Color(0xFF2D3748),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            '命令模板:',
                            style: TextStyle(
                              fontSize: 12,
                              color: Color(0xFFE2E8F0),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 4),
                          SelectableText(
                            operation.command,
                            style: const TextStyle(
                              fontSize: 11,
                              color: Color(0xFF68D391),
                              fontFamily: 'monospace',
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    // 参数配置
                    if (parameters.isNotEmpty) ...[
                      const SizedBox(height: 20),
                      const Text(
                        '参数配置:',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF333333),
                        ),
                      ),
                      const SizedBox(height: 12),
                      ...parameters.map((param) => _buildParameterField(
                        param,
                        paramValues.value[param] ?? '',
                        (value) {
                          paramValues.value = {
                            ...paramValues.value,
                            param: value,
                          };
                        },
                      )).toList(),
                    ],
                  ],
                ),
              ),
            ),
            
            // 按钮区域
            Container(
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                border: Border(top: BorderSide(color: Color(0xFFE9ECEF))),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('取消'),
                  ),
                  const SizedBox(width: 12),
                  ElevatedButton(
                    onPressed: () {
                      final updatedOperation = operation.updateParameters(paramValues.value);
                      onSave(updatedOperation);
                      Navigator.of(context).pop();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF0C75F8),
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('保存'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 解析命令中的参数
  List<String> _parseParameters(String command) {
    final regex = RegExp(r'\$\{([^}]+)\}');
    return regex.allMatches(command)
        .map((match) => match.group(1)!)
        .toSet()
        .toList()
        ..sort();
  }

  /// 构建参数输入字段
  Widget _buildParameterField(
    String parameter,
    String value,
    Function(String) onChanged,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            parameter,
            style: const TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.w500,
              color: Color(0xFF333333),
            ),
          ),
          const SizedBox(height: 6),
          TextFormField(
            initialValue: value,
            onChanged: onChanged,
            decoration: InputDecoration(
              hintText: _getParameterHint(parameter),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              contentPadding: const EdgeInsets.all(12),
            ),
          ),
        ],
      ),
    );
  }

  /// 获取参数提示
  String _getParameterHint(String parameter) {
    switch (parameter.toUpperCase()) {
      case 'URL':
        return '输入网站地址，如: https://example.com';
      case 'SELECTOR':
        return '输入CSS选择器，如: #id, .class';
      case 'KEY':
        return '输入按键名称，如: Enter, Space';
      case 'KEYS':
        return '输入组合键，如: Ctrl+C';
      case 'TIMEOUT':
        return '输入超时时间，如: 30s';
      case 'TIMES':
        return '输入重复次数，如: 3';
      case 'CONDITION':
        return '输入条件表达式';
      case 'EXPECTED_VALUE':
        return '输入期望值';
      case 'ATTRIBUTE':
        return '输入属性名称，如: href, src';
      default:
        return '请输入${parameter}的值';
    }
  }
} 
