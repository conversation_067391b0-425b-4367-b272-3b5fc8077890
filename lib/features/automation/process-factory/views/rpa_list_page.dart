import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:frontend_re/widgets/primary_botton.dart';
import 'package:frontend_re/widgets/secondary_botton.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class RpaListPage extends HookConsumerWidget {
  const RpaListPage({super.key});

  // 生成示例数据
  List<String> _generateInitialData() {
    return [
      'Amazon商品数据采集',
      '每日销售报表自动生成',
      'Excel数据清洗处理',
      '网站健康监控检查',
      '客户邮件自动回复',
      '订单状态批量更新',
      '商品价格监控爬虫',
      '财务数据自动对账',
      '库存预警通知',
      '用户行为数据分析',
      'PDF文档批量处理',
      '社交媒体内容抓取',
      '竞品价格对比分析',
      '发票数据自动录入',
      '客服工单自动分类',
      '网站SEO数据监控'
    ];
  }

  // 生成更多数据
  List<String> _generateMoreData(int currentLength) {
    final moreItems = [
      '微信群消息监控',
      '淘宝店铺数据统计',
      '员工考勤自动汇总',
      '供应商信息更新',
      '产品评论情感分析',
      '广告投放效果监控',
      '物流轨迹自动追踪',
      '会员积分自动计算',
      '促销活动数据分析',
      '库存周转率计算',
      '客户满意度调研',
      '竞对活动监控',
      '价格策略优化',
      '商品标签自动生成',
      '退款流程自动化',
      '数据备份定时任务',
      '系统日志自动分析',
      '用户画像数据建模',
      '营销邮件发送',
      '订单异常检测',
      '商品推荐算法',
      '客户流失预警',
      '销售漏斗分析',
      '渠道效果评估'
    ];
    
    final startIndex = (currentLength - 16) ~/ 8;
    final endIndex = (startIndex + 8).clamp(0, moreItems.length);
    
    if (startIndex >= moreItems.length) {
      return List.generate(8, (index) => '流程_${currentLength + index + 1}');
    }
    
    return moreItems.sublist(startIndex, endIndex);
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 使用 hooks 管理状态
    final items = useState<List<String>>(_generateInitialData());
    final selectedItems = useState<Set<int>>({});  // 选中项的索引集合
    final isLoading = useState(false);
    final hasMoreData = useState(true);
    final scrollController = useScrollController();

    // 桌面端加载更多数据的函数
    Future<void> loadMoreData() async {
      if (isLoading.value || !hasMoreData.value) return;
      
      isLoading.value = true;
      
      // 模拟网络请求延迟
      await Future.delayed(const Duration(milliseconds: 800));
      
      // 生成更多数据
      final newItems = _generateMoreData(items.value.length);
      items.value = [...items.value, ...newItems];
      
      // 模拟到达数据末尾
      if (items.value.length >= 80) {
        hasMoreData.value = false;
      }
      
      isLoading.value = false;
    }

    // 桌面端滚动监听 - 更精确的触发
    useEffect(() {
      void onScroll() {
        final position = scrollController.position;
        // 桌面端使用更小的阈值，更快触发加载
        if (position.pixels >= position.maxScrollExtent - 100) {
          loadMoreData();
        }
      }
      
      scrollController.addListener(onScroll);
      return () => scrollController.removeListener(onScroll);
    }, [scrollController]);

    // 重置数据（桌面端可以用按钮触发而不是下拉刷新）
    void resetData() {
      items.value = _generateInitialData();
      selectedItems.value = {};
      hasMoreData.value = true;
    }

    // 全选/取消全选
    void toggleSelectAll() {
      if (selectedItems.value.length == items.value.length) {
        // 如果已全选，则取消全选
        selectedItems.value = {};
      } else {
        // 否则全选
        selectedItems.value = Set.from(List.generate(items.value.length, (index) => index));
      }
    }

    // 切换单个项目选中状态
    void toggleItemSelection(int index) {
      final newSelection = Set<int>.from(selectedItems.value);
      if (newSelection.contains(index)) {
        newSelection.remove(index);
      } else {
        newSelection.add(index);
      }
      selectedItems.value = newSelection;
    }

    // 批量删除
    void batchDelete() {
      if (selectedItems.value.isEmpty) return;
      
      final indicesToDelete = selectedItems.value.toList()..sort((a, b) => b.compareTo(a));
      final newItems = List<String>.from(items.value);
      
      for (final index in indicesToDelete) {
        if (index < newItems.length) {
          newItems.removeAt(index);
        }
      }
      
      items.value = newItems;
      selectedItems.value = {};
      
      // 显示删除结果
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('已删除 ${indicesToDelete.length} 个流程'),
          backgroundColor: const Color(0xFF0C75F8),
        ),
      );
    }

    // 批量启动
    void batchStart() {
      if (selectedItems.value.isEmpty) return;
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('已启动 ${selectedItems.value.length} 个流程'),
          backgroundColor: const Color(0xFF4CAF50),
        ),
      );
      selectedItems.value = {};
    }

    // 批量停止
    void batchStop() {
      if (selectedItems.value.isEmpty) return;
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('已停止 ${selectedItems.value.length} 个流程'),
          backgroundColor: const Color(0xFFFF9800),
        ),
      );
      selectedItems.value = {};
    }
    return Scaffold(
      backgroundColor: const Color(0x001A1A1A),
      body: Column(
        spacing: 16,
        children: [
          // 顶部操作栏
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              color: Color(0xFFFFFFFF),
              borderRadius: BorderRadius.all(Radius.circular(16)),
            ),
            child: Row(
              children: [
                // 新建按钮
                Container(
                  decoration: BoxDecoration(
                    color: const Color(0xFF0C75F8),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: IconButton(
                    onPressed: () {
                      // 跳转到新建流程页面
                      context.pushNamed('rpa');
                    },
                    icon: const Icon(Icons.add, color: Colors.white),
                    tooltip: '新建流程',
                  ),
                ),
                const SizedBox(width: 16),
                
                // 全部下拉选择器
                // Container(
                //   padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                //   decoration: BoxDecoration(
                //     color: const Color(0xFF3A3A3A),
                //     borderRadius: BorderRadius.circular(8),
                //     border: Border.all(color: const Color(0xFF4A4A4A)),
                //   ),
                //   child: DropdownButton<String>(
                //     value: '全部',
                //     underline: const SizedBox(),
                //     dropdownColor: const Color(0xFF3A3A3A),
                //     style: const TextStyle(color: Colors.white),
                //     icon: const Icon(Icons.keyboard_arrow_down, color: Colors.white),
                //     items: const [
                //       DropdownMenuItem(value: '全部', child: Text('全部')),
                //       DropdownMenuItem(value: '运行中', child: Text('运行中')),
                //       DropdownMenuItem(value: '已停止', child: Text('已停止')),
                //     ],
                //     onChanged: (value) {},
                //   ),
                // ),
                const SizedBox(width: 16),
                
                // 搜索框
                Expanded(
                  child: SizedBox(
                    height: 40,
                    child: TextField(
                      style: const TextStyle(color: Colors.white),
                      decoration: InputDecoration(
                        hintText: '搜索我的任务流程',
                        hintStyle: TextStyle(color: Colors.grey[400]),
                        prefixIcon: Icon(Icons.search, color: Colors.grey[400]),
                        border: InputBorder.none,
                        contentPadding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                
                // 右侧操作按钮组
                // Row(
                //   children: [
                //     IconButton(
                //       onPressed: () {},
                //       icon: const Icon(Icons.refresh, color: Colors.white),
                //       tooltip: '刷新',
                //     ),
                //     IconButton(
                //       onPressed: () {},
                //       icon: const Icon(Icons.undo, color: Colors.white),
                //       tooltip: '撤销',
                //     ),
                //     IconButton(
                //       onPressed: () {},
                //       icon: const Icon(Icons.edit, color: Colors.white),
                //       tooltip: '编辑',
                //     ),
                //     IconButton(
                //       onPressed: () {},
                //       icon: const Icon(Icons.delete, color: Colors.white),
                //       tooltip: '删除',
                //     ),
                //     const SizedBox(width: 8),
                //     IconButton(
                //       onPressed: () {},
                //       icon: const Icon(Icons.view_list, color: Colors.white),
                //       tooltip: '列表视图',
                //     ),
                //     IconButton(
                //       onPressed: () {},
                //       icon: const Icon(Icons.view_module, color: Colors.white),
                //       tooltip: '网格视图',
                //     ),
                //     IconButton(
                //       onPressed: () {},
                //       icon: const Icon(Icons.settings, color: Colors.white),
                //       tooltip: '设置',
                //     ),
                //   ],
                // ),
              ],
            ),
          ),
          
          // 内容区域
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: const BoxDecoration(
                  color: Color(0xFFFFFFFF),
                  borderRadius: BorderRadius.all(Radius.circular(16)),
                ),
                child: Column(
                  children: [
                    // 全选和批量操作区域
                    SizedBox(
                      height: 40,
                      child: Row(
                        children: [
                          // 全选复选框
                          Checkbox(
                            value: selectedItems.value.isNotEmpty && 
                                   selectedItems.value.length == items.value.length,
                            tristate: true,
                            onChanged: (value) => toggleSelectAll(),
                            fillColor: WidgetStateProperty.resolveWith(
                              (states) => states.contains(WidgetState.selected) 
                                ? const Color(0xFF0C75F8) 
                                : Colors.transparent,
                            ),
                            side: const BorderSide(color: Color(0xFF4A4A4A)),
                          ),
                          Text(
                            selectedItems.value.isEmpty 
                              ? '全选' 
                              : '已选择 ${selectedItems.value.length} 项',
                            style: const TextStyle(
                              color: Color(0xFF333333), 
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          
                          // 批量操作按钮
                          if (selectedItems.value.isNotEmpty) ...[
                            const SizedBox(width: 16),
                            _buildBatchButton(
                              icon: Icons.play_arrow,
                              label: '批量启动',
                              color: const Color(0xFF999999),
                              onPressed: batchStart,
                            ),
                            const SizedBox(width: 8),
                            _buildBatchButton(
                              icon: Icons.stop,
                              label: '批量停止',
                              color: const Color(0xFF999999),
                              onPressed: batchStop,
                            ),
                            const SizedBox(width: 8),
                            _buildBatchButton(
                               icon: Icons.delete,
                               label: '批量删除',
                               color: const Color(0xFF999999),
                               onPressed: () => _showDeleteConfirmDialog(context, selectedItems.value.length, batchDelete),
                             ),
                          ],
                          
                          const Spacer(),
                          
                          // 统计信息
                          RichText(
                            text: TextSpan(
                              children: [
                                const TextSpan(
                                  text: '创建流程数: ',
                                  style: TextStyle(color: Colors.grey),
                                ),
                                TextSpan(
                                  text: '${items.value.length}',
                                  style: const TextStyle(color: Color(0xFF0C75F8)),
                                ),
                                const TextSpan(
                                  text: ' / ',
                                  style: TextStyle(color: Colors.grey),
                                ),
                                const TextSpan(
                                  text: '500',
                                  style: TextStyle(color: Colors.grey),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    // 卡片网格 - 桌面端版本
                    Expanded(
                      child: CustomScrollView(
                        controller: scrollController,
                        slivers: [
                          SliverGrid(
                            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: 4,
                              crossAxisSpacing: 16,
                              mainAxisSpacing: 16,
                              childAspectRatio: 1.2,
                            ),
                            delegate: SliverChildBuilderDelegate(
                              (context, index) {
                                final title = items.value[index];
                                final isSelected = selectedItems.value.contains(index);
                                
                                return Container(
                                  decoration: BoxDecoration(
                                    color: const Color(0xFFF3F4F8),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Column(
                                    children: [
                                      // 卡片头部
                                      Container(
                                        padding: const EdgeInsets.all(16),
                                        child: Row(
                                          children: [
                                            // 选择框
                                            GestureDetector(
                                              onTap: () => toggleItemSelection(index),
                                              child: Container(
                                                width: 16,
                                                height: 16,
                                                decoration: BoxDecoration(
                                                  color: isSelected 
                                                    ? const Color(0xFF0C75F8) 
                                                    : Colors.transparent,
                                                  border: Border.all(
                                                    color: isSelected
                                                      ? const Color(0xFF0C75F8)
                                                      : const Color(0xFF4A90E2),
                                                  ),
                                                  borderRadius: BorderRadius.circular(4),
                                                ),
                                                child: isSelected
                                                  ? const Icon(
                                                      Icons.check,
                                                      size: 12,
                                                      color: Colors.white,
                                                    )
                                                  : null,
                                              ),
                                            ),
                                            const SizedBox(width: 8),
                                            // 标题
                                            Expanded(
                                              child: Text(
                                                title,
                                                style: const TextStyle(
                                                  color: Color(0xFF999999),
                                                  fontSize: 14,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      
                                      // 暂无备注区域
                                      Expanded(
                                        child: Container(
                                          width: double.infinity,
                                          margin: const EdgeInsets.symmetric(horizontal: 16),
                                          decoration: BoxDecoration(
                                            color: const Color(0xFFFFFFFF),
                                            borderRadius: BorderRadius.circular(8),
                                          ),
                                          child: const Center(
                                            child: Text(
                                              '暂无备注',
                                              style: TextStyle(
                                                color: Color(0xFF999999),
                                                fontSize: 12,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                      
                                      // 底部操作按钮
                                      Container(
                                        padding: const EdgeInsets.all(12),
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.end,
                                          children: [
                                            // 启动
                                            Container(
                                              decoration: BoxDecoration(
                                                color: const Color.fromARGB(255, 255, 255, 255),
                                                borderRadius: BorderRadius.circular(50),
                                              ),
                                              child: IconButton(
                                                onPressed: () {},
                                                tooltip: '启动',
                                                icon: const Icon(
                                                  Icons.play_arrow,
                                                  color: Color(0xFF0C75F8),
                                                  size: 16,
                                                ),
                                                constraints: const BoxConstraints(
                                                  minWidth: 24,
                                                  minHeight: 24,
                                                ),
                                                padding: EdgeInsets.zero,
                                              ),
                                            ),
                                            const SizedBox(width: 8),
                                            // 停止
                                            Container(
                                              decoration: BoxDecoration(
                                                color: const Color.fromARGB(255, 255, 255, 255),
                                                borderRadius: BorderRadius.circular(50),
                                              ),
                                              child: IconButton(
                                                onPressed: () {},
                                                tooltip: '停止',
                                                icon: const Icon(
                                                  Icons.stop,
                                                  color: Color(0xFF0C75F8),
                                                  size: 16,
                                                ),
                                                constraints: const BoxConstraints(
                                                  minWidth: 24,
                                                  minHeight: 24,
                                                ),
                                                padding: EdgeInsets.zero,
                                              ),
                                            ),
                                            const SizedBox(width: 8),
                                            // 编辑
                                            Container(
                                              decoration: BoxDecoration(
                                                color: const Color.fromARGB(255, 255, 255, 255),
                                                borderRadius: BorderRadius.circular(50),
                                              ),
                                              child: IconButton(
                                                onPressed: () {},
                                                tooltip: '编辑',
                                                icon: const Icon(
                                                  Icons.edit,
                                                  color: Color(0xFF0C75F8),
                                                  size: 16,
                                                ),
                                                constraints: const BoxConstraints(
                                                  minWidth: 24,
                                                  minHeight: 24,
                                                ),
                                                padding: EdgeInsets.zero,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              },
                              childCount: items.value.length,
                            ),
                          ),
                          // 加载更多指示器
                          if (isLoading.value)
                            const SliverToBoxAdapter(
                              child: Center(
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    SizedBox(
                                      width: 20,
                                      height: 20,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF0C75F8)),
                                      ),
                                    ),
                                    SizedBox(width: 12),
                                    Text(
                                      '正在加载更多流程...',
                                      style: TextStyle(
                                        color: Color(0xFF666666),
                                        fontSize: 14,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          // 到底提示
                          if (!hasMoreData.value && items.value.isNotEmpty)
                            SliverToBoxAdapter(
                              child: Padding(
                                padding: const EdgeInsets.all(20),
                                child: Center(
                                  child: Column(
                                    children: [
                                      const Icon(
                                        Icons.done_all,
                                        color: Color(0xFF999999),
                                        size: 24,
                                      ),
                                      const SizedBox(height: 8),
                                      const Text(
                                        '已加载全部流程',
                                        style: TextStyle(
                                          color: Color(0xFF999999),
                                          fontSize: 14,
                                        ),
                                      ),
                                      const SizedBox(height: 8),
                                      TextButton.icon(
                                        onPressed: resetData,
                                        icon: const Icon(
                                          Icons.refresh,
                                          size: 16,
                                          color: Color(0xFF0C75F8),
                                        ),
                                        label: const Text(
                                          '重新加载',
                                          style: TextStyle(
                                            color: Color(0xFF0C75F8),
                                            fontSize: 12,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 构建批量操作按钮
  Widget _buildBatchButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return Container(
      height: 32,
      padding: const EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(6),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 14, color: color),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: color,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 显示删除确认对话框
  void _showDeleteConfirmDialog(BuildContext context, int selectedCount, VoidCallback onConfirm) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        titleTextStyle: const TextStyle(
          color: Color(0xFF333333),
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
        content: Text('确定要删除选中的 $selectedCount 个流程吗？此操作不可撤销。'),
        backgroundColor: Colors.white,
        actions: [
          SecondaryButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          PrimaryButton(
            onPressed: () {
              Navigator.of(context).pop();
              onConfirm();
            },
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

}
