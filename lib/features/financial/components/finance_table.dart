import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_svg/svg.dart';
import 'package:frontend_re/widgets/custom_solid_button.dart';
import 'package:frontend_re/widgets/data_table_widget.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:frontend_re/widgets/custom_date_picker.dart';

// 财务交易数据模型
class FinanceTransaction {
  final String id;
  final String type;
  final String time;
  final String project;
  final String balance;
  final String amount;
  final String payment;

  FinanceTransaction({
    required this.id,
    required this.type,
    required this.time,
    required this.project,
    required this.balance,
    required this.amount,
    required this.payment,
  });
}

class FinanceTable extends HookConsumerWidget {
  const FinanceTable({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 搜索框控制器
    final searchController = useTextEditingController();
    
    // 日期状态
    final startDate = useState<DateTime?>(null);
    final endDate = useState<DateTime?>(null);

    // 模拟数据
    final transactions = _getAllTransactionData();

    return Container(
      height: 600, // 添加固定高度
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题栏
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // 搜索框
                SizedBox(
                  width: 300,
                  height: 40,
                  child: TextField(
                    controller: searchController,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF666666),
                    ),
                    decoration: InputDecoration(
                      hintText: '搜索交易单号',
                      hintStyle: const TextStyle(
                        color: Color(0xFFBBBBBB),
                        fontSize: 14,
                      ),
                      filled: true,
                      fillColor: const Color(0xFFF6F6F6),
                      isDense: true,
                      prefixIcon: const Icon(
                        Icons.search,
                        color: Color(0xFF8D8E93),
                        size: 20,
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(100),
                        borderSide: const BorderSide(
                          color: Color(0x00EBEBEB),
                          width: 1,
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(100),
                        borderSide: const BorderSide(
                          color: Color(0xFF4A90E2),
                          width: 1,
                        ),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 10,
                      ),
                    ),
                  ),
                ),
              
                const SizedBox(width: 16),
                
                // 时间选择器
                CustomDateRangePicker(
                  startDate: startDate.value,
                  endDate: endDate.value,
                  onStartDateSelected: (date) => startDate.value = date,
                  onEndDateSelected: (date) => endDate.value = date,
                  backgroundColor: const Color(0xFFF6F6F6),
                ),
                const Spacer(),
                // 发票按钮
                const CustomSolidButton(
                  text: '发票',
                  iconPath: 'assets/svg/finance_invoice.svg',
                  iconColor: Colors.white,
                  iconBackgroundColor: Color(0xFF1E1C1D),
                  textColor: Color(0xFF8D8E93),
                  backgroundColor: Color(0xFFF6F6F6),
                ),
              ],
            ),
          ),
          
          // 数据表格
          Expanded(
            child: DataTableWidget<FinanceTransaction>(
              headingTextStyle: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500, color: Color(0xFF6B7280)),
              dataTextStyle: const TextStyle(fontSize: 12, color: Color(0xFF8D8E93)),
              minWidth: 100,
              showCheckbox: true,
              columnSpacing: 0,
              enablePagination: true,
              totalCount: transactions.length,
              pageSize: 10,
              currentPage: 1,
              onPageChanged: (page, newPageSize) {
                // 分页处理逻辑
                print('页码: $page, 每页: $newPageSize');
              },
              noDataImagePath: 'assets/images/proxy/NoData.png',
              noDataText: '暂无交易数据',
              noDataButtonText: '刷新',
              noDataButtonEvent: () {
                print('刷新数据');
              },
              columns: const [
                DataColumn2(label: Text('交易单号'), size: ColumnSize.L),
                DataColumn2(label: Text('交易类型'), size: ColumnSize.S),
                DataColumn2(label: Text('交易时间'), size: ColumnSize.M),
                DataColumn2(label: Text('项目'), size: ColumnSize.S),
                DataColumn2(label: Text('余额'), size: ColumnSize.S),
                DataColumn2(label: Text('交易金额'), size: ColumnSize.S),
                DataColumn2(label: Text('支付方式'), size: ColumnSize.S),
              ],
              data: transactions.take(10).toList(), // 只显示前10条
              rowBuilder: (transaction, index, isHovered) {
                return DataRow2(cells: [
                  DataCell(Text(transaction.id)),
                  DataCell(Text(transaction.type)),
                  DataCell(Text(transaction.time)),
                  DataCell(Text(transaction.project)),
                  DataCell(Text(transaction.balance)),
                  DataCell(Text(transaction.amount)),
                  DataCell(Text(transaction.payment)),
                ]);
              },
            ),
          ),
        ],
      ),
    );
  }

  // 获取所有交易数据（模拟数据）
  List<FinanceTransaction> _getAllTransactionData() {
    return [
      FinanceTransaction(
        id: 'TRX202307150001',
        type: 'recharge',
        time: '2023-07-15 10:30',
        project: '账户充值',
        balance: '¥5200.69',
        amount: '+¥1000.00',
        payment: 'stripe',
      ),
      FinanceTransaction(
        id: 'TRX202307140002',
        type: 'consume',
        time: '2023-07-14 15:45',
        project: '代理购买',
        balance: '¥4200.69',
        amount: '-¥300.00',
        payment: 'balance',
      ),
      FinanceTransaction(
        id: 'TRX202307130003',
        type: 'recharge',
        time: '2023-07-13 09:20',
        project: '账户充值',
        balance: '¥4500.69',
        amount: '+¥500.00',
        payment: 'wechat',
      ),
      FinanceTransaction(
        id: 'TRX202307120004',
        type: 'consume',
        time: '2023-07-12 16:30',
        project: '浏览器配置',
        balance: '¥4000.69',
        amount: '-¥200.00',
        payment: 'balance',
      ),
      FinanceTransaction(
        id: 'TRX202307110005',
        type: 'refund',
        time: '2023-07-11 14:15',
        project: '退款',
        balance: '¥4200.69',
        amount: '+¥150.00',
        payment: 'alipay',
      ),
      FinanceTransaction(
        id: 'TRX202307100006',
        type: 'consume',
        time: '2023-07-10 11:30',
        project: '代理服务',
        balance: '¥4050.69',
        amount: '-¥149.31',
        payment: 'balance',
      ),
      FinanceTransaction(
        id: 'TRX202307090007',
        type: 'recharge',
        time: '2023-07-09 16:45',
        project: '账户充值',
        balance: '¥4200.00',
        amount: '+¥2000.00',
        payment: 'unionpay',
      ),
      FinanceTransaction(
        id: 'TRX202307080008',
        type: 'consume',
        time: '2023-07-08 09:15',
        project: '云服务',
        balance: '¥2200.00',
        amount: '-¥89.99',
        payment: 'balance',
      ),
      FinanceTransaction(
        id: 'TRX202307070009',
        type: 'recharge',
        time: '2023-07-07 14:20',
        project: '账户充值',
        balance: '¥2289.99',
        amount: '+¥800.00',
        payment: 'wechat',
      ),
      FinanceTransaction(
        id: 'TRX202307060010',
        type: 'consume',
        time: '2023-07-06 18:30',
        project: '数据分析',
        balance: '¥1489.99',
        amount: '-¥320.50',
        payment: 'balance',
      ),
      FinanceTransaction(
        id: 'TRX202307050011',
        type: 'refund',
        time: '2023-07-05 12:45',
        project: '服务退款',
        balance: '¥1810.49',
        amount: '+¥280.00',
        payment: 'alipay',
      ),
      FinanceTransaction(
        id: 'TRX202307040012',
        type: 'consume',
        time: '2023-07-04 10:15',
        project: 'API调用',
        balance: '¥1530.49',
        amount: '-¥45.80',
        payment: 'balance',
      ),
      FinanceTransaction(
        id: 'TRX202307030013',
        type: 'recharge',
        time: '2023-07-03 15:30',
        project: '账户充值',
        balance: '¥1576.29',
        amount: '+¥1200.00',
        payment: 'stripe',
      ),
      FinanceTransaction(
        id: 'TRX202307020014',
        type: 'consume',
        time: '2023-07-02 13:45',
        project: '存储服务',
        balance: '¥376.29',
        amount: '-¥156.70',
        payment: 'balance',
      ),
      FinanceTransaction(
        id: 'TRX202307010015',
        type: 'recharge',
        time: '2023-07-01 09:00',
        project: '账户充值',
        balance: '¥532.99',
        amount: '+¥500.00',
        payment: 'unionpay',
      ),
    ];
  }
}
