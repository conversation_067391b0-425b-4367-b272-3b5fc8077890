import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_svg/svg.dart';
import 'package:frontend_re/features/financial/components/finance_table.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class FinancePage extends StatelessWidget {
  const FinancePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Column(
          children: [
            // 财务布局：左边两个卡片，右边一个大卡片
            _buildFinanceLayout(),
            const SizedBox(height: 20),
            // 订单列表
            const FinanceTable(),
          ],
        ),
      ),
    );
  }

  // 财务布局：左边两个卡片，右边一个大卡片
  Widget _buildFinanceLayout() {
    return SizedBox(
      height: 400,
      child: Row(
        spacing: 24,
        children: [
          // 左侧：上下两个卡片
          SizedBox(
            width: 280,
            child: Column(
              spacing: 16,
              children: [
                // 账号余额卡片
                Expanded(
                  child: _buildBalanceCard(),
                ),
                // 待生成账单卡片
                Expanded(
                  child: _buildPendingBillCard(),
                ),
              ],
            ),
          ),
          // 右侧：充值卡片
          Expanded(
            child: _buildRechargeCard(),
          ),
        ],
      ),
    );
  }

  // 账号余额卡片
  Widget _buildBalanceCard() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题行
            Row(
              children: [
                Container(
                  height: 40,
                  decoration: BoxDecoration(
                    color: const Color(0xFF0C75F8),
                    borderRadius: BorderRadius.circular(50),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.only(
                      left: 6,
                      right: 16,
                      top: 6,
                      bottom: 6,
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Container(
                          width: 30,
                          height: 30,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(50),
                          ),
                          child: Center(
                            child: SvgPicture.asset(
                              'assets/svg/finance_circle.svg',
                              height: 16,
                              width: 16,
                              colorFilter: const ColorFilter.mode(
                                Color(0xFF3B82F6),
                                BlendMode.srcIn,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 10),
                        const Padding(
                          padding: EdgeInsets.only(top: 2),
                          child: Text(
                            '账号余额',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 14,
                              height: 1.0,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // 余额显示
            const Row(
              crossAxisAlignment: CrossAxisAlignment.baseline, // 使用baseline对齐
              textBaseline: TextBaseline.alphabetic, // 指定基线类型
              children: [
                Text(
                  '¥',
                  style: TextStyle(
                    fontSize: 16,
                    color: Color(0xFF666666),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  '5200.69',
                  style: TextStyle(
                    fontSize: 32,
                    color: Color(0xFF1F2937),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // 待生成账单卡片
  Widget _buildPendingBillCard() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题行
            Row(
              children: [
                Container(
                  height: 40,
                  decoration: BoxDecoration(
                    color: const Color(0xFF0C75F8),
                    borderRadius: BorderRadius.circular(50),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.only(
                      left: 6,
                      right: 16,
                      top: 6,
                      bottom: 6,
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          width: 30,
                          height: 30,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(50),
                          ),
                          child: Center(
                            child: SvgPicture.asset(
                              'assets/svg/finance_circle.svg',
                              height: 16,
                              width: 16,
                            ),
                          ),
                        ),
                        const SizedBox(width: 10),
                        const Text(
                          '待生成账单',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // 金额显示
            const Row(
              crossAxisAlignment: CrossAxisAlignment.baseline, // 使用baseline对齐
              textBaseline: TextBaseline.alphabetic, // 指定基线类型
              children: [
                Text(
                  '¥',
                  style: TextStyle(
                    fontSize: 16,
                    color: Color(0xFF666666),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  '5200.69',
                  style: TextStyle(
                    fontSize: 32,
                    color: Color(0xFF1F2937),
                  ),
                ),
              ],
            ),
            const Spacer(),
            // 查看详情
            const Text(
              '查看详情 >',
              style: TextStyle(
                fontSize: 12,
                color: Color(0xFF999999),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 充值卡片
  Widget _buildRechargeCard() {
    return HookConsumer(
      builder: (context, ref, child) {
        // 所选择的充值金额
        final selectedAmount = useState<String>('20');
        // 自定义金额
        final customAmount = useState<String>('');
        // 所选择的支付方式
        final selectedPayment = useState<String>('stripe');
        // 搜索框控制器
        final searchController = useTextEditingController();

        return Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
          ),
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 标题行
                Row(
                  spacing: 10,
                  children: [
                    Container(
                      height: 16,
                      width: 16,
                      decoration: BoxDecoration(
                        color: const Color(0xFF0C75F8),
                        borderRadius: BorderRadius.circular(50),
                      ),
                    ),
                    const Text(
                      '在线充值',
                      style: TextStyle(
                        color: Color(0xFF333333),
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                // 分割线
                const Divider(
                  color: Color(0xFFE5E7EB),
                  height: 1,
                ),
                const SizedBox(height: 20),
                // 充值金额标签
                const Text(
                  '充值金额',
                  style: TextStyle(
                    fontSize: 14,
                    color: Color(0xFF6B7280),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 12),
                
                // 金额选择按钮
                Wrap(
                  spacing: 12,
                  runSpacing: 8,
                  children: [
                    _buildAmountButton('20', selectedAmount.value == '20', () {
                      selectedAmount.value = '20';
                    }),
                    _buildAmountButton('50', selectedAmount.value == '50', () {
                      selectedAmount.value = '50';
                    }),
                    _buildAmountButton('100', selectedAmount.value == '100', () {
                      selectedAmount.value = '100';
                    }),
                    _buildAmountButton('500', selectedAmount.value == '500', () {
                      selectedAmount.value = '500';
                    }),
                    _buildAmountButton('1000', selectedAmount.value == '1000', () {
                      selectedAmount.value = '1000';
                    }),
                    // 自定义金额输入框
                    SizedBox(
                      width: 100,
                      height: 40,  // 固定高度为40
                      child: TextField(
                        controller: searchController,
                        textAlign: TextAlign.center, // 文本居中对齐
                        onChanged: (value) {
                          customAmount.value = value;
                          // 自定义金额时，选中金额为空字符串
                          selectedAmount.value = '';
                        },
                        style: const TextStyle(
                          fontSize: 14,
                          color: Color(0xFF666666),
                        ),
                        decoration: InputDecoration(
                          hintText: '自定义',
                          hintStyle: const TextStyle(
                            color: Color(0xFFBBBBBB),
                            fontSize: 14,
                          ),
                          // 设置文本居中对齐
                          alignLabelWithHint: true,
                          filled: true,
                          fillColor: Colors.white,
                          isDense: true,  // 使用更紧凑的布局
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(100),
                            borderSide: const BorderSide(
                              color: Color(0xFFEBEBEB),
                              width: 2,
                            ),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(100),
                            borderSide: const BorderSide(
                              color: Color(0xFF4A90E2),
                              width: 1,
                            ),
                          ),
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 13,  // 减少垂直内边距来降低高度
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 24),
                // 支付方式
                const Text(
                  '支付方式',
                  style: TextStyle(
                    fontSize: 14,
                    color: Color(0xFF6B7280),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 12),
                
                // 支付方式选择
                Row(
                  children: [
                    _buildPaymentMethod('assets/images/common/stripe.png', selectedPayment.value == 'stripe', () {
                      selectedPayment.value = 'stripe';
                    }),
                    const SizedBox(width: 12),
                    _buildPaymentMethod('assets/images/common/wechat.png', selectedPayment.value == 'wechat', () {
                      selectedPayment.value = 'wechat';
                    }),
                    const SizedBox(width: 12),
                    _buildPaymentMethod('assets/images/common/alipay.png', selectedPayment.value == 'alipay', () {
                      selectedPayment.value = 'alipay';
                    }),
                    const SizedBox(width: 12),
                    _buildPaymentMethod('assets/images/common/unionpay.png', selectedPayment.value == 'unionpay', () {
                      selectedPayment.value = 'unionpay';
                    }),
                  ],
                ),
                const Spacer(),
                
                // 提交支付按钮
                SizedBox(
                  width: 120,
                  height: 40,
                  child: ElevatedButton(
                    onPressed: () {
                      // 处理支付提交逻辑
                      final amount = selectedAmount.value;
                      final payment = selectedPayment.value;
                      final custom = customAmount.value;
                      
                      // 这里可以添加实际的支付处理逻辑
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('提交支付：¥$amount，支付方式：$payment，自定义：$custom'),
                          backgroundColor: const Color(0xFF3B82F6),
                        ),
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF3B82F6),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(50),
                      ),
                    ),
                    child: const Text(
                      '提交支付',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      }
    );
  }

  // 金额选择按钮
  Widget _buildAmountButton(String amount, bool isSelected, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(20),
      child: Container(
        width: 80,
        height: 40,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFF3B82F6) : const Color(0xFFF3F4F6),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Center(
          child: Text(
            amount,
            style: TextStyle(
              fontSize: 12,
              color: isSelected ? Colors.white : const Color(0xFF6B7280),
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }

  // 支付方式选择
  Widget _buildPaymentMethod(String iconPath, bool isSelected, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        width: 48,
        height: 48,
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFFEBF5FF) : const Color(0xFFF9FAFB),
          border: Border.all(
            color: isSelected ? const Color(0xFF3B82F6) : const Color(0xFFE5E7EB),
            width: 1.5,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Image.asset(
          iconPath,
          width: 32,
          height: 32,
        ),
      ),
    );
  }
}
