import 'dart:io';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:frontend_re/features/titlebar/state_bar.dart';
import 'package:frontend_re/widgets/glass_card_widget.dart';
import 'package:frontend_re/core/utils/ui/window_manager_util.dart';
import 'package:window_manager/window_manager.dart';

import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart'; // 导入 GoRouter

/// 导航项模型
class NavItem {
  final String title;
  final String iconPath;
  final String iconSelectedPath;
  final String path; // 导航路径 (用于 GoRouter)
  final List<NavItem>? subItems; // 添加子菜单项

  NavItem({
    required this.title,
    required this.iconPath,
    required this.iconSelectedPath,
    required this.path,
    this.subItems,
  });
}

/// 控制菜单展开状态
final expandedMenuProvider = StateProvider<Set<String>>((ref) => {});

class IndexPage extends HookConsumerWidget {
  // 添加navigationShell参数用于处理嵌套路由
  final StatefulNavigationShell? navigationShell;

  const IndexPage({
    super.key,
    this.navigationShell, // 接收 ShellRoute 的 navigationShell
  });


  // 辅助方法：将路径映射到分支索引
  int? getBranchIndexFromPath(String path) {
    // 根据您的路由配置设置映射关系
    final Map<String, int> pathToIndex = {
      '/workbench': 0,
      '/proxy': 1,
      '/team/members': 2,
      '/team': 3,
      '/team/library': 4,
      '/finance': 5,
      '/auto/api': 6,
      '/auto/factory': 7,
      '/auto/market': 8,
      '/auto/schedule': 9,
      '/auto/records': 10,
      // 添加其他分支路径映射
    };
    return pathToIndex[path];
  }

  /// 构建标题栏
  Widget _buildTitleBar() {
    return Container(
      height: 24,
      color: Colors.transparent,
      child: Row(
        children: [
          // 中间可拖拽区域
          const Expanded(
            child: SizedBox(),
          ),
                     // 版本号
           const Text('v0.1.0-alpha', style: TextStyle(fontSize: 12, color: Color(0xFF8C8C8C))),
           const SizedBox(width: 20),
          // 右侧窗口控制按钮
          Platform.isWindows ? _buildWindowControls() : const SizedBox.shrink(),
        ],
      ),
    );
  }

  /// 构建窗口控制按钮组
  Widget _buildWindowControls() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildWindowButton(
          icon: Icons.remove,
          onPressed: () => WindowManagerUtil().minimize(),
          hoverColor: const Color(0x1A000000),
        ),
        _buildWindowButton(
          icon: Icons.crop_square,
          onPressed: () => WindowManagerUtil.handleTitleBarDoubleClick(),
          hoverColor: const Color(0x1A000000),
        ),
        _buildWindowButton(
          icon: Icons.close,
          onPressed: () async {
            // 在关闭前保存窗口状态
            await WindowManagerUtil.saveCurrentWindowState();
            await WindowManagerUtil.close();
          },
          hoverColor: const Color.fromARGB(255, 255, 116, 127),
          iconColor: Colors.black,
        ),
      ],
    );
  }

  /// 构建单个窗口控制按钮
  Widget _buildWindowButton({
    required IconData icon,
    required VoidCallback onPressed,
    required Color hoverColor,
    Color? iconColor,
    Offset? iconOffset,
  }) {
    return SizedBox(
      width: 46,
      height: 24,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          hoverColor: hoverColor,
          child: Center(
            child: Transform.translate(
              offset: iconOffset ?? Offset.zero,
              child: Icon(
                icon,
                size: 10,
                color: iconColor ?? const Color(0xFF000000),
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final expandedMenus = ref.watch(expandedMenuProvider);
    final GoRouterState routerState = GoRouterState.of(context);
    final String currentPath = routerState.uri.toString(); //.split('/').take(2).join('/');
    final scrollController = useScrollController();

    // 导航项列表
    final navItems = useMemoized(
      () => [
        NavItem(
          title: '我的工作台',
          iconPath: 'assets/svg/home.svg',
          iconSelectedPath: 'assets/svg/home_fill.svg',
          path: '/workbench',
        ),
        NavItem(
          title: '代理管理',
          iconPath: 'assets/svg/proxy.svg',
          iconSelectedPath: 'assets/svg/proxy_fill.svg',
          path: '/proxy',
        ),
        NavItem(
          title: '团队协作',
          iconPath: 'assets/svg/team.svg',
          iconSelectedPath: 'assets/svg/team_fill.svg',
          path: '/team', // 一级菜单路径，仅用于识别，不会直接导航
          subItems: [
            NavItem(
              title: '团队成员',
              iconPath: 'assets/svg/auto.svg',
              iconSelectedPath: 'assets/svg/auto_fill.svg',
              path: '/team/members', // 根据go_router配置调整
            ),
            NavItem(
              title: '协作日志',
              iconPath: 'assets/svg/auto.svg',
              iconSelectedPath: 'assets/svg/auto_fill.svg',
              path: '/teamlogs', // 根据go_router配置调整
            ),
            NavItem(
              title: '共享资源库',
              iconPath: 'assets/svg/auto.svg',
              iconSelectedPath: 'assets/svg/auto_fill.svg',
              path: '/team/library', // 根据go_router配置调整
            ),
          ],
        ),
        NavItem(
          title: '财务中心',
          iconPath: 'assets/svg/finance.svg',
          iconSelectedPath: 'assets/svg/finance_fill.svg',
          path: '/finance',
        ),
        NavItem(
          title: '自动化中心',
          iconPath: 'assets/svg/auto.svg',
          iconSelectedPath: 'assets/svg/auto_fill.svg',
          path: '/auto', // 一级菜单路径，仅用于识别，不会直接导航
          subItems: [
            NavItem(
              title: 'API',
              iconPath: 'assets/svg/auto.svg',
              iconSelectedPath: 'assets/svg/auto_fill.svg',
              path: '/auto/api', // 根据go_router配置调整
            ),
            NavItem(
              title: '流程工厂',
              iconPath: 'assets/svg/auto.svg',
              iconSelectedPath: 'assets/svg/auto_fill.svg',
              path: '/auto/rpaList', // 根据go_router配置调整
            ),
            NavItem(
              title: '模板市场',
              iconPath: 'assets/svg/auto.svg',
              iconSelectedPath: 'assets/svg/auto_fill.svg',
              path: '/auto/market', // 根据go_router配置调整
            ),
            NavItem(
              title: '任务调度',
              iconPath: 'assets/svg/auto.svg',
              iconSelectedPath: 'assets/svg/auto_fill.svg',
              path: '/auto/schedule', // 根据go_router配置调整
            ),
            NavItem(
              title: '执行记录',
              iconPath: 'assets/svg/auto.svg',
              iconSelectedPath: 'assets/svg/auto_fill.svg',
              path: '/auto/records', // 根据go_router配置调整
            ),
          ],
        ),
      ],
    );

    // 自动展开当前路径所在的菜单
    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        final newExpandedMenus = {...expandedMenus};
        bool changed = false;
        
        for (var item in navItems) {
          if (item.subItems != null && item.subItems!.any((sub) => currentPath == sub.path)) {
            if (!newExpandedMenus.contains(item.title)) {
              newExpandedMenus.add(item.title);
              changed = true;
            }
          }
        }
        
        if (changed) {
          ref.read(expandedMenuProvider.notifier).state = newExpandedMenus;
        }
      });
      return null;
    }, [currentPath]);

    return Scaffold(
      body: Stack(
        children: [
          Row(
          children: [
            // 侧边导航栏
            Container(
              width: 240,
              color: const Color(0xFF1E1C1D),
              child: Column(
                children: [
                  // Logo区域
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 32.0),
                    child: Row(
                      children: [
                        SvgPicture.asset('assets/svg/logo.svg', width: 20),
                        const SizedBox(width: 10),
                        const Text(
                          'PrismBrowse',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 20),
                  // 菜单列表
                  Expanded(
                    child: Theme(
                      data: Theme.of(context).copyWith(
                        scrollbarTheme: ScrollbarThemeData(
                          thumbVisibility: WidgetStateProperty.all(true),
                          thickness: WidgetStateProperty.all(4.0),
                          thumbColor: WidgetStateProperty.all(Colors.white.withValues(alpha: 0.4)),
                          trackVisibility: WidgetStateProperty.all(false),
                        ),
                      ),
                      child: Scrollbar(
                        controller: scrollController,
                        child: ListView.builder(
                          controller: scrollController,
                          itemCount: navItems.length,
                          itemBuilder: (context, index) => _buildMenuItem(
                            context, ref, navItems[index], currentPath, expandedMenus),
                        ),
                      ),
                    ),
                  ),
                  // vip卡片
                  // GlassCardWidget(
                  //   title: '体验更多功能',
                  //   description: '跨设备同步数据、团队协作等高级功能。',
                  //   buttonText: '升级套餐',
                  //   buttonIconPath: 'assets/svg/up.svg',
                  //   iconPath: 'assets/svg/rocket.svg',
                  //   iconData: Icons.rocket_launch,
                  //   iconTopOffset: -20,
                  //   iconContainerSize: 40,
                  //   onButtonPressed: () {
                  //     // TODO: 升级套餐
                  //   },
                  // ),
                ],
              ),
            ),
            // 内容区域
            Expanded(
              child: Padding(
                padding: const EdgeInsets.only(left: 16, right: 16, top: 24, bottom: 0),
                child: Column(
                  spacing: 20,
                  children: [
                    // Container(height: 24),
                    const StateBar(),
                    Expanded(child: navigationShell ?? const SizedBox.shrink()),
                  ],
                ),
              ),
            ),
            ],
          ),
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: GestureDetector(
              onDoubleTap: () async {
                await WindowManagerUtil.handleTitleBarDoubleClick();
              },
              onPanStart: (details) {
                windowManager.startDragging();
              },
              child: _buildTitleBar(),
            ),
          ),
        ],
      ),
    );
  }

  // 构建菜单项
  Widget _buildMenuItem(BuildContext context, WidgetRef ref, NavItem item, 
      String currentPath, Set<String> expandedMenus) {
    final bool hasSubItems = item.subItems != null && item.subItems!.isNotEmpty;
    final bool isExpanded = hasSubItems && expandedMenus.contains(item.title);
    
    // 判断当前项是否激活
    bool isActive = false;
    if (hasSubItems) {
      // 匹配规则：当前路径是子菜单路径 或 子菜单路径是当前路径的前缀
      isActive = item.subItems!.any((subItem) => 
        currentPath == subItem.path || 
        currentPath.startsWith('${subItem.path}/')
      );
    } else {
      // 对于没有子菜单的项，直接比较路径
      isActive = currentPath == item.path || currentPath.startsWith('${item.path}/');
    }
    
    // 背景色
    final Color bgColor = isActive 
        ? const Color(0xFF0C75F8) 
        : (isExpanded ? Colors.white.withValues(alpha: 0.1) : Colors.transparent);
    
    // 前景色
    final Color fgColor = isActive || isExpanded
        ? Colors.white
        : const Color(0xFF8C8C8C);

    return Column(
      children: [
        // 主菜单项
        InkWell(
          onTap: () {
            if (hasSubItems) {
              // 只切换展开状态，不触发路由跳转
              final newExpandedMenus = {...expandedMenus};
              if (isExpanded) {
                newExpandedMenus.remove(item.title);
              } else {
                newExpandedMenus.add(item.title);
              }
              ref.read(expandedMenuProvider.notifier).state = newExpandedMenus;
            } else {
              // 直接导航
              final branchIndex = getBranchIndexFromPath(item.path);
              if (branchIndex != null) {
                navigationShell?.goBranch(branchIndex);
              } else {
                context.go(item.path);
              }
            }
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: bgColor,
                borderRadius: BorderRadius.circular(50),
              ),
              child: Row(
                children: [
                  SvgPicture.asset(
                    isActive ? item.iconSelectedPath : item.iconPath,
                    colorFilter: ColorFilter.mode(fgColor, BlendMode.srcIn),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      item.title,
                      style: TextStyle(fontSize: 16, color: fgColor),
                    ),
                  ),
                  if (hasSubItems)
                    SvgPicture.asset(
                      isExpanded ? 'assets/svg/bottom.svg' : 'assets/svg/right.svg',
                      colorFilter: ColorFilter.mode(fgColor, BlendMode.srcIn),
                    ),
                ],
              ),
            ),
          ),
        ),
        
        // 子菜单项
        if (isExpanded && hasSubItems)
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
            child: Column(
              children: item.subItems!.map((subItem) {
                // 修改为检查路径前缀匹配
                final bool isSubActive = currentPath == subItem.path || currentPath.startsWith('${subItem.path}/');
                
                return InkWell(
                  onTap: () => context.go(subItem.path),
                  child: Container(
                    margin: const EdgeInsets.only(bottom: 4),
                    decoration: BoxDecoration(
                      color: isSubActive ? Colors.white.withValues(alpha: 0.15) : Colors.transparent,
                      borderRadius: BorderRadius.circular(50),
                    ),
                    padding: const EdgeInsets.only(left: 32, right: 16, top: 10, bottom: 10),
                    child: Row(
                      children: [
                        Container(
                          width: 6,
                          height: 6,
                          decoration: BoxDecoration(
                            color: isSubActive ? const Color(0xFF0C75F8) : const Color(0xFF8C8C8C),
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 10),
                        Expanded(
                          child: Text(
                            subItem.title,
                            style: TextStyle(
                              fontSize: 14,
                              color: isSubActive ? Colors.white : const Color(0xFF8C8C8C),
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
      ],
    );
  }
}
