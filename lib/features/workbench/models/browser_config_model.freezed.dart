// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'browser_config_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

BrowserConfigModel _$BrowserConfigModelFromJson(Map<String, dynamic> json) {
  return _BrowserConfigModel.fromJson(json);
}

/// @nodoc
mixin _$BrowserConfigModel {
  String get name => throw _privateConstructorUsedError;

  /// 账号平台
  String get account => throw _privateConstructorUsedError;

  /// 配置项的用户名和密码
  String get accountUsername => throw _privateConstructorUsedError;
  String get accountPassword => throw _privateConstructorUsedError;

  /// Cookie
  String get cookie => throw _privateConstructorUsedError;

  /// 代理id
  int get proxyId => throw _privateConstructorUsedError;

  /// 代理类型
  int get proxyType => throw _privateConstructorUsedError;

  /// 浏览器版本
  String get chromeSelectedValue => throw _privateConstructorUsedError;

  /// 操作系统版本
  String get oswSelectedValue => throw _privateConstructorUsedError;

  /// UA 环境设置
  String get ua => throw _privateConstructorUsedError; // 语言
  bool get evaluateLanguage => throw _privateConstructorUsedError; // 是否自定义语言
  String get languageSelectedValue =>
      throw _privateConstructorUsedError; // 自定义语言内容
// 界面语言
  bool get localeEvaluateLanguage =>
      throw _privateConstructorUsedError; // 是否自定义界面语言
  String get locale => throw _privateConstructorUsedError; // 自定义界面语言内容
// 时区
  bool get timezone => throw _privateConstructorUsedError; // 是否自定义时区
  String get timezoneSelectedValue =>
      throw _privateConstructorUsedError; // 自定义时区内容
// 地区
  bool get locationByIP => throw _privateConstructorUsedError; // 是否自定义地区
  String get location => throw _privateConstructorUsedError; // 经 纬 海拔 精度
// WebRTC
  String get webrtc => throw _privateConstructorUsedError; // Canvas
  bool get canvas => throw _privateConstructorUsedError; // true 噪音 false 真实
// WebGL
  bool get webGL => throw _privateConstructorUsedError; // WebGL 厂商和渲染器
  String get webGLVendor => throw _privateConstructorUsedError; // 选中的供应商
  String get webGLRender => throw _privateConstructorUsedError; // 随机选择的渲染器
// Plugin
  bool get plugin => throw _privateConstructorUsedError; // 字体
  bool get font => throw _privateConstructorUsedError; // ClientRects
  bool get clientRects => throw _privateConstructorUsedError; // AudioContext
  bool get audioContext => throw _privateConstructorUsedError; // 分辨率
  String get resolution => throw _privateConstructorUsedError; // 设备内存
  String get memory => throw _privateConstructorUsedError; // 设备 CPU
  String get kernel =>
      throw _privateConstructorUsedError; // 以下变量在 BrowserConfig 中不存在，保留原类型
  String get chromeVersion => throw _privateConstructorUsedError;
  String get oswVersion => throw _privateConstructorUsedError;
  bool get editing => throw _privateConstructorUsedError; // 备注
  String get comment => throw _privateConstructorUsedError; // 标签
  String get tag => throw _privateConstructorUsedError; // 分组id
  int get groupId => throw _privateConstructorUsedError;

  /// Serializes this BrowserConfigModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of BrowserConfigModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BrowserConfigModelCopyWith<BrowserConfigModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BrowserConfigModelCopyWith<$Res> {
  factory $BrowserConfigModelCopyWith(
          BrowserConfigModel value, $Res Function(BrowserConfigModel) then) =
      _$BrowserConfigModelCopyWithImpl<$Res, BrowserConfigModel>;
  @useResult
  $Res call(
      {String name,
      String account,
      String accountUsername,
      String accountPassword,
      String cookie,
      int proxyId,
      int proxyType,
      String chromeSelectedValue,
      String oswSelectedValue,
      String ua,
      bool evaluateLanguage,
      String languageSelectedValue,
      bool localeEvaluateLanguage,
      String locale,
      bool timezone,
      String timezoneSelectedValue,
      bool locationByIP,
      String location,
      String webrtc,
      bool canvas,
      bool webGL,
      String webGLVendor,
      String webGLRender,
      bool plugin,
      bool font,
      bool clientRects,
      bool audioContext,
      String resolution,
      String memory,
      String kernel,
      String chromeVersion,
      String oswVersion,
      bool editing,
      String comment,
      String tag,
      int groupId});
}

/// @nodoc
class _$BrowserConfigModelCopyWithImpl<$Res, $Val extends BrowserConfigModel>
    implements $BrowserConfigModelCopyWith<$Res> {
  _$BrowserConfigModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BrowserConfigModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? account = null,
    Object? accountUsername = null,
    Object? accountPassword = null,
    Object? cookie = null,
    Object? proxyId = null,
    Object? proxyType = null,
    Object? chromeSelectedValue = null,
    Object? oswSelectedValue = null,
    Object? ua = null,
    Object? evaluateLanguage = null,
    Object? languageSelectedValue = null,
    Object? localeEvaluateLanguage = null,
    Object? locale = null,
    Object? timezone = null,
    Object? timezoneSelectedValue = null,
    Object? locationByIP = null,
    Object? location = null,
    Object? webrtc = null,
    Object? canvas = null,
    Object? webGL = null,
    Object? webGLVendor = null,
    Object? webGLRender = null,
    Object? plugin = null,
    Object? font = null,
    Object? clientRects = null,
    Object? audioContext = null,
    Object? resolution = null,
    Object? memory = null,
    Object? kernel = null,
    Object? chromeVersion = null,
    Object? oswVersion = null,
    Object? editing = null,
    Object? comment = null,
    Object? tag = null,
    Object? groupId = null,
  }) {
    return _then(_value.copyWith(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      account: null == account
          ? _value.account
          : account // ignore: cast_nullable_to_non_nullable
              as String,
      accountUsername: null == accountUsername
          ? _value.accountUsername
          : accountUsername // ignore: cast_nullable_to_non_nullable
              as String,
      accountPassword: null == accountPassword
          ? _value.accountPassword
          : accountPassword // ignore: cast_nullable_to_non_nullable
              as String,
      cookie: null == cookie
          ? _value.cookie
          : cookie // ignore: cast_nullable_to_non_nullable
              as String,
      proxyId: null == proxyId
          ? _value.proxyId
          : proxyId // ignore: cast_nullable_to_non_nullable
              as int,
      proxyType: null == proxyType
          ? _value.proxyType
          : proxyType // ignore: cast_nullable_to_non_nullable
              as int,
      chromeSelectedValue: null == chromeSelectedValue
          ? _value.chromeSelectedValue
          : chromeSelectedValue // ignore: cast_nullable_to_non_nullable
              as String,
      oswSelectedValue: null == oswSelectedValue
          ? _value.oswSelectedValue
          : oswSelectedValue // ignore: cast_nullable_to_non_nullable
              as String,
      ua: null == ua
          ? _value.ua
          : ua // ignore: cast_nullable_to_non_nullable
              as String,
      evaluateLanguage: null == evaluateLanguage
          ? _value.evaluateLanguage
          : evaluateLanguage // ignore: cast_nullable_to_non_nullable
              as bool,
      languageSelectedValue: null == languageSelectedValue
          ? _value.languageSelectedValue
          : languageSelectedValue // ignore: cast_nullable_to_non_nullable
              as String,
      localeEvaluateLanguage: null == localeEvaluateLanguage
          ? _value.localeEvaluateLanguage
          : localeEvaluateLanguage // ignore: cast_nullable_to_non_nullable
              as bool,
      locale: null == locale
          ? _value.locale
          : locale // ignore: cast_nullable_to_non_nullable
              as String,
      timezone: null == timezone
          ? _value.timezone
          : timezone // ignore: cast_nullable_to_non_nullable
              as bool,
      timezoneSelectedValue: null == timezoneSelectedValue
          ? _value.timezoneSelectedValue
          : timezoneSelectedValue // ignore: cast_nullable_to_non_nullable
              as String,
      locationByIP: null == locationByIP
          ? _value.locationByIP
          : locationByIP // ignore: cast_nullable_to_non_nullable
              as bool,
      location: null == location
          ? _value.location
          : location // ignore: cast_nullable_to_non_nullable
              as String,
      webrtc: null == webrtc
          ? _value.webrtc
          : webrtc // ignore: cast_nullable_to_non_nullable
              as String,
      canvas: null == canvas
          ? _value.canvas
          : canvas // ignore: cast_nullable_to_non_nullable
              as bool,
      webGL: null == webGL
          ? _value.webGL
          : webGL // ignore: cast_nullable_to_non_nullable
              as bool,
      webGLVendor: null == webGLVendor
          ? _value.webGLVendor
          : webGLVendor // ignore: cast_nullable_to_non_nullable
              as String,
      webGLRender: null == webGLRender
          ? _value.webGLRender
          : webGLRender // ignore: cast_nullable_to_non_nullable
              as String,
      plugin: null == plugin
          ? _value.plugin
          : plugin // ignore: cast_nullable_to_non_nullable
              as bool,
      font: null == font
          ? _value.font
          : font // ignore: cast_nullable_to_non_nullable
              as bool,
      clientRects: null == clientRects
          ? _value.clientRects
          : clientRects // ignore: cast_nullable_to_non_nullable
              as bool,
      audioContext: null == audioContext
          ? _value.audioContext
          : audioContext // ignore: cast_nullable_to_non_nullable
              as bool,
      resolution: null == resolution
          ? _value.resolution
          : resolution // ignore: cast_nullable_to_non_nullable
              as String,
      memory: null == memory
          ? _value.memory
          : memory // ignore: cast_nullable_to_non_nullable
              as String,
      kernel: null == kernel
          ? _value.kernel
          : kernel // ignore: cast_nullable_to_non_nullable
              as String,
      chromeVersion: null == chromeVersion
          ? _value.chromeVersion
          : chromeVersion // ignore: cast_nullable_to_non_nullable
              as String,
      oswVersion: null == oswVersion
          ? _value.oswVersion
          : oswVersion // ignore: cast_nullable_to_non_nullable
              as String,
      editing: null == editing
          ? _value.editing
          : editing // ignore: cast_nullable_to_non_nullable
              as bool,
      comment: null == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String,
      tag: null == tag
          ? _value.tag
          : tag // ignore: cast_nullable_to_non_nullable
              as String,
      groupId: null == groupId
          ? _value.groupId
          : groupId // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BrowserConfigModelImplCopyWith<$Res>
    implements $BrowserConfigModelCopyWith<$Res> {
  factory _$$BrowserConfigModelImplCopyWith(_$BrowserConfigModelImpl value,
          $Res Function(_$BrowserConfigModelImpl) then) =
      __$$BrowserConfigModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String name,
      String account,
      String accountUsername,
      String accountPassword,
      String cookie,
      int proxyId,
      int proxyType,
      String chromeSelectedValue,
      String oswSelectedValue,
      String ua,
      bool evaluateLanguage,
      String languageSelectedValue,
      bool localeEvaluateLanguage,
      String locale,
      bool timezone,
      String timezoneSelectedValue,
      bool locationByIP,
      String location,
      String webrtc,
      bool canvas,
      bool webGL,
      String webGLVendor,
      String webGLRender,
      bool plugin,
      bool font,
      bool clientRects,
      bool audioContext,
      String resolution,
      String memory,
      String kernel,
      String chromeVersion,
      String oswVersion,
      bool editing,
      String comment,
      String tag,
      int groupId});
}

/// @nodoc
class __$$BrowserConfigModelImplCopyWithImpl<$Res>
    extends _$BrowserConfigModelCopyWithImpl<$Res, _$BrowserConfigModelImpl>
    implements _$$BrowserConfigModelImplCopyWith<$Res> {
  __$$BrowserConfigModelImplCopyWithImpl(_$BrowserConfigModelImpl _value,
      $Res Function(_$BrowserConfigModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of BrowserConfigModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? account = null,
    Object? accountUsername = null,
    Object? accountPassword = null,
    Object? cookie = null,
    Object? proxyId = null,
    Object? proxyType = null,
    Object? chromeSelectedValue = null,
    Object? oswSelectedValue = null,
    Object? ua = null,
    Object? evaluateLanguage = null,
    Object? languageSelectedValue = null,
    Object? localeEvaluateLanguage = null,
    Object? locale = null,
    Object? timezone = null,
    Object? timezoneSelectedValue = null,
    Object? locationByIP = null,
    Object? location = null,
    Object? webrtc = null,
    Object? canvas = null,
    Object? webGL = null,
    Object? webGLVendor = null,
    Object? webGLRender = null,
    Object? plugin = null,
    Object? font = null,
    Object? clientRects = null,
    Object? audioContext = null,
    Object? resolution = null,
    Object? memory = null,
    Object? kernel = null,
    Object? chromeVersion = null,
    Object? oswVersion = null,
    Object? editing = null,
    Object? comment = null,
    Object? tag = null,
    Object? groupId = null,
  }) {
    return _then(_$BrowserConfigModelImpl(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      account: null == account
          ? _value.account
          : account // ignore: cast_nullable_to_non_nullable
              as String,
      accountUsername: null == accountUsername
          ? _value.accountUsername
          : accountUsername // ignore: cast_nullable_to_non_nullable
              as String,
      accountPassword: null == accountPassword
          ? _value.accountPassword
          : accountPassword // ignore: cast_nullable_to_non_nullable
              as String,
      cookie: null == cookie
          ? _value.cookie
          : cookie // ignore: cast_nullable_to_non_nullable
              as String,
      proxyId: null == proxyId
          ? _value.proxyId
          : proxyId // ignore: cast_nullable_to_non_nullable
              as int,
      proxyType: null == proxyType
          ? _value.proxyType
          : proxyType // ignore: cast_nullable_to_non_nullable
              as int,
      chromeSelectedValue: null == chromeSelectedValue
          ? _value.chromeSelectedValue
          : chromeSelectedValue // ignore: cast_nullable_to_non_nullable
              as String,
      oswSelectedValue: null == oswSelectedValue
          ? _value.oswSelectedValue
          : oswSelectedValue // ignore: cast_nullable_to_non_nullable
              as String,
      ua: null == ua
          ? _value.ua
          : ua // ignore: cast_nullable_to_non_nullable
              as String,
      evaluateLanguage: null == evaluateLanguage
          ? _value.evaluateLanguage
          : evaluateLanguage // ignore: cast_nullable_to_non_nullable
              as bool,
      languageSelectedValue: null == languageSelectedValue
          ? _value.languageSelectedValue
          : languageSelectedValue // ignore: cast_nullable_to_non_nullable
              as String,
      localeEvaluateLanguage: null == localeEvaluateLanguage
          ? _value.localeEvaluateLanguage
          : localeEvaluateLanguage // ignore: cast_nullable_to_non_nullable
              as bool,
      locale: null == locale
          ? _value.locale
          : locale // ignore: cast_nullable_to_non_nullable
              as String,
      timezone: null == timezone
          ? _value.timezone
          : timezone // ignore: cast_nullable_to_non_nullable
              as bool,
      timezoneSelectedValue: null == timezoneSelectedValue
          ? _value.timezoneSelectedValue
          : timezoneSelectedValue // ignore: cast_nullable_to_non_nullable
              as String,
      locationByIP: null == locationByIP
          ? _value.locationByIP
          : locationByIP // ignore: cast_nullable_to_non_nullable
              as bool,
      location: null == location
          ? _value.location
          : location // ignore: cast_nullable_to_non_nullable
              as String,
      webrtc: null == webrtc
          ? _value.webrtc
          : webrtc // ignore: cast_nullable_to_non_nullable
              as String,
      canvas: null == canvas
          ? _value.canvas
          : canvas // ignore: cast_nullable_to_non_nullable
              as bool,
      webGL: null == webGL
          ? _value.webGL
          : webGL // ignore: cast_nullable_to_non_nullable
              as bool,
      webGLVendor: null == webGLVendor
          ? _value.webGLVendor
          : webGLVendor // ignore: cast_nullable_to_non_nullable
              as String,
      webGLRender: null == webGLRender
          ? _value.webGLRender
          : webGLRender // ignore: cast_nullable_to_non_nullable
              as String,
      plugin: null == plugin
          ? _value.plugin
          : plugin // ignore: cast_nullable_to_non_nullable
              as bool,
      font: null == font
          ? _value.font
          : font // ignore: cast_nullable_to_non_nullable
              as bool,
      clientRects: null == clientRects
          ? _value.clientRects
          : clientRects // ignore: cast_nullable_to_non_nullable
              as bool,
      audioContext: null == audioContext
          ? _value.audioContext
          : audioContext // ignore: cast_nullable_to_non_nullable
              as bool,
      resolution: null == resolution
          ? _value.resolution
          : resolution // ignore: cast_nullable_to_non_nullable
              as String,
      memory: null == memory
          ? _value.memory
          : memory // ignore: cast_nullable_to_non_nullable
              as String,
      kernel: null == kernel
          ? _value.kernel
          : kernel // ignore: cast_nullable_to_non_nullable
              as String,
      chromeVersion: null == chromeVersion
          ? _value.chromeVersion
          : chromeVersion // ignore: cast_nullable_to_non_nullable
              as String,
      oswVersion: null == oswVersion
          ? _value.oswVersion
          : oswVersion // ignore: cast_nullable_to_non_nullable
              as String,
      editing: null == editing
          ? _value.editing
          : editing // ignore: cast_nullable_to_non_nullable
              as bool,
      comment: null == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String,
      tag: null == tag
          ? _value.tag
          : tag // ignore: cast_nullable_to_non_nullable
              as String,
      groupId: null == groupId
          ? _value.groupId
          : groupId // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BrowserConfigModelImpl implements _BrowserConfigModel {
  const _$BrowserConfigModelImpl(
      {required this.name,
      required this.account,
      required this.accountUsername,
      required this.accountPassword,
      required this.cookie,
      required this.proxyId,
      required this.proxyType,
      required this.chromeSelectedValue,
      required this.oswSelectedValue,
      required this.ua,
      required this.evaluateLanguage,
      required this.languageSelectedValue,
      required this.localeEvaluateLanguage,
      required this.locale,
      required this.timezone,
      required this.timezoneSelectedValue,
      required this.locationByIP,
      required this.location,
      required this.webrtc,
      required this.canvas,
      required this.webGL,
      required this.webGLVendor,
      required this.webGLRender,
      required this.plugin,
      required this.font,
      required this.clientRects,
      required this.audioContext,
      required this.resolution,
      required this.memory,
      required this.kernel,
      required this.chromeVersion,
      required this.oswVersion,
      required this.editing,
      required this.comment,
      required this.tag,
      required this.groupId});

  factory _$BrowserConfigModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$BrowserConfigModelImplFromJson(json);

  @override
  final String name;

  /// 账号平台
  @override
  final String account;

  /// 配置项的用户名和密码
  @override
  final String accountUsername;
  @override
  final String accountPassword;

  /// Cookie
  @override
  final String cookie;

  /// 代理id
  @override
  final int proxyId;

  /// 代理类型
  @override
  final int proxyType;

  /// 浏览器版本
  @override
  final String chromeSelectedValue;

  /// 操作系统版本
  @override
  final String oswSelectedValue;

  /// UA 环境设置
  @override
  final String ua;
// 语言
  @override
  final bool evaluateLanguage;
// 是否自定义语言
  @override
  final String languageSelectedValue;
// 自定义语言内容
// 界面语言
  @override
  final bool localeEvaluateLanguage;
// 是否自定义界面语言
  @override
  final String locale;
// 自定义界面语言内容
// 时区
  @override
  final bool timezone;
// 是否自定义时区
  @override
  final String timezoneSelectedValue;
// 自定义时区内容
// 地区
  @override
  final bool locationByIP;
// 是否自定义地区
  @override
  final String location;
// 经 纬 海拔 精度
// WebRTC
  @override
  final String webrtc;
// Canvas
  @override
  final bool canvas;
// true 噪音 false 真实
// WebGL
  @override
  final bool webGL;
// WebGL 厂商和渲染器
  @override
  final String webGLVendor;
// 选中的供应商
  @override
  final String webGLRender;
// 随机选择的渲染器
// Plugin
  @override
  final bool plugin;
// 字体
  @override
  final bool font;
// ClientRects
  @override
  final bool clientRects;
// AudioContext
  @override
  final bool audioContext;
// 分辨率
  @override
  final String resolution;
// 设备内存
  @override
  final String memory;
// 设备 CPU
  @override
  final String kernel;
// 以下变量在 BrowserConfig 中不存在，保留原类型
  @override
  final String chromeVersion;
  @override
  final String oswVersion;
  @override
  final bool editing;
// 备注
  @override
  final String comment;
// 标签
  @override
  final String tag;
// 分组id
  @override
  final int groupId;

  @override
  String toString() {
    return 'BrowserConfigModel(name: $name, account: $account, accountUsername: $accountUsername, accountPassword: $accountPassword, cookie: $cookie, proxyId: $proxyId, proxyType: $proxyType, chromeSelectedValue: $chromeSelectedValue, oswSelectedValue: $oswSelectedValue, ua: $ua, evaluateLanguage: $evaluateLanguage, languageSelectedValue: $languageSelectedValue, localeEvaluateLanguage: $localeEvaluateLanguage, locale: $locale, timezone: $timezone, timezoneSelectedValue: $timezoneSelectedValue, locationByIP: $locationByIP, location: $location, webrtc: $webrtc, canvas: $canvas, webGL: $webGL, webGLVendor: $webGLVendor, webGLRender: $webGLRender, plugin: $plugin, font: $font, clientRects: $clientRects, audioContext: $audioContext, resolution: $resolution, memory: $memory, kernel: $kernel, chromeVersion: $chromeVersion, oswVersion: $oswVersion, editing: $editing, comment: $comment, tag: $tag, groupId: $groupId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BrowserConfigModelImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.account, account) || other.account == account) &&
            (identical(other.accountUsername, accountUsername) ||
                other.accountUsername == accountUsername) &&
            (identical(other.accountPassword, accountPassword) ||
                other.accountPassword == accountPassword) &&
            (identical(other.cookie, cookie) || other.cookie == cookie) &&
            (identical(other.proxyId, proxyId) || other.proxyId == proxyId) &&
            (identical(other.proxyType, proxyType) ||
                other.proxyType == proxyType) &&
            (identical(other.chromeSelectedValue, chromeSelectedValue) ||
                other.chromeSelectedValue == chromeSelectedValue) &&
            (identical(other.oswSelectedValue, oswSelectedValue) ||
                other.oswSelectedValue == oswSelectedValue) &&
            (identical(other.ua, ua) || other.ua == ua) &&
            (identical(other.evaluateLanguage, evaluateLanguage) ||
                other.evaluateLanguage == evaluateLanguage) &&
            (identical(other.languageSelectedValue, languageSelectedValue) ||
                other.languageSelectedValue == languageSelectedValue) &&
            (identical(other.localeEvaluateLanguage, localeEvaluateLanguage) ||
                other.localeEvaluateLanguage == localeEvaluateLanguage) &&
            (identical(other.locale, locale) || other.locale == locale) &&
            (identical(other.timezone, timezone) ||
                other.timezone == timezone) &&
            (identical(other.timezoneSelectedValue, timezoneSelectedValue) ||
                other.timezoneSelectedValue == timezoneSelectedValue) &&
            (identical(other.locationByIP, locationByIP) ||
                other.locationByIP == locationByIP) &&
            (identical(other.location, location) ||
                other.location == location) &&
            (identical(other.webrtc, webrtc) || other.webrtc == webrtc) &&
            (identical(other.canvas, canvas) || other.canvas == canvas) &&
            (identical(other.webGL, webGL) || other.webGL == webGL) &&
            (identical(other.webGLVendor, webGLVendor) ||
                other.webGLVendor == webGLVendor) &&
            (identical(other.webGLRender, webGLRender) ||
                other.webGLRender == webGLRender) &&
            (identical(other.plugin, plugin) || other.plugin == plugin) &&
            (identical(other.font, font) || other.font == font) &&
            (identical(other.clientRects, clientRects) ||
                other.clientRects == clientRects) &&
            (identical(other.audioContext, audioContext) ||
                other.audioContext == audioContext) &&
            (identical(other.resolution, resolution) ||
                other.resolution == resolution) &&
            (identical(other.memory, memory) || other.memory == memory) &&
            (identical(other.kernel, kernel) || other.kernel == kernel) &&
            (identical(other.chromeVersion, chromeVersion) ||
                other.chromeVersion == chromeVersion) &&
            (identical(other.oswVersion, oswVersion) ||
                other.oswVersion == oswVersion) &&
            (identical(other.editing, editing) || other.editing == editing) &&
            (identical(other.comment, comment) || other.comment == comment) &&
            (identical(other.tag, tag) || other.tag == tag) &&
            (identical(other.groupId, groupId) || other.groupId == groupId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        name,
        account,
        accountUsername,
        accountPassword,
        cookie,
        proxyId,
        proxyType,
        chromeSelectedValue,
        oswSelectedValue,
        ua,
        evaluateLanguage,
        languageSelectedValue,
        localeEvaluateLanguage,
        locale,
        timezone,
        timezoneSelectedValue,
        locationByIP,
        location,
        webrtc,
        canvas,
        webGL,
        webGLVendor,
        webGLRender,
        plugin,
        font,
        clientRects,
        audioContext,
        resolution,
        memory,
        kernel,
        chromeVersion,
        oswVersion,
        editing,
        comment,
        tag,
        groupId
      ]);

  /// Create a copy of BrowserConfigModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BrowserConfigModelImplCopyWith<_$BrowserConfigModelImpl> get copyWith =>
      __$$BrowserConfigModelImplCopyWithImpl<_$BrowserConfigModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BrowserConfigModelImplToJson(
      this,
    );
  }
}

abstract class _BrowserConfigModel implements BrowserConfigModel {
  const factory _BrowserConfigModel(
      {required final String name,
      required final String account,
      required final String accountUsername,
      required final String accountPassword,
      required final String cookie,
      required final int proxyId,
      required final int proxyType,
      required final String chromeSelectedValue,
      required final String oswSelectedValue,
      required final String ua,
      required final bool evaluateLanguage,
      required final String languageSelectedValue,
      required final bool localeEvaluateLanguage,
      required final String locale,
      required final bool timezone,
      required final String timezoneSelectedValue,
      required final bool locationByIP,
      required final String location,
      required final String webrtc,
      required final bool canvas,
      required final bool webGL,
      required final String webGLVendor,
      required final String webGLRender,
      required final bool plugin,
      required final bool font,
      required final bool clientRects,
      required final bool audioContext,
      required final String resolution,
      required final String memory,
      required final String kernel,
      required final String chromeVersion,
      required final String oswVersion,
      required final bool editing,
      required final String comment,
      required final String tag,
      required final int groupId}) = _$BrowserConfigModelImpl;

  factory _BrowserConfigModel.fromJson(Map<String, dynamic> json) =
      _$BrowserConfigModelImpl.fromJson;

  @override
  String get name;

  /// 账号平台
  @override
  String get account;

  /// 配置项的用户名和密码
  @override
  String get accountUsername;
  @override
  String get accountPassword;

  /// Cookie
  @override
  String get cookie;

  /// 代理id
  @override
  int get proxyId;

  /// 代理类型
  @override
  int get proxyType;

  /// 浏览器版本
  @override
  String get chromeSelectedValue;

  /// 操作系统版本
  @override
  String get oswSelectedValue;

  /// UA 环境设置
  @override
  String get ua; // 语言
  @override
  bool get evaluateLanguage; // 是否自定义语言
  @override
  String get languageSelectedValue; // 自定义语言内容
// 界面语言
  @override
  bool get localeEvaluateLanguage; // 是否自定义界面语言
  @override
  String get locale; // 自定义界面语言内容
// 时区
  @override
  bool get timezone; // 是否自定义时区
  @override
  String get timezoneSelectedValue; // 自定义时区内容
// 地区
  @override
  bool get locationByIP; // 是否自定义地区
  @override
  String get location; // 经 纬 海拔 精度
// WebRTC
  @override
  String get webrtc; // Canvas
  @override
  bool get canvas; // true 噪音 false 真实
// WebGL
  @override
  bool get webGL; // WebGL 厂商和渲染器
  @override
  String get webGLVendor; // 选中的供应商
  @override
  String get webGLRender; // 随机选择的渲染器
// Plugin
  @override
  bool get plugin; // 字体
  @override
  bool get font; // ClientRects
  @override
  bool get clientRects; // AudioContext
  @override
  bool get audioContext; // 分辨率
  @override
  String get resolution; // 设备内存
  @override
  String get memory; // 设备 CPU
  @override
  String get kernel; // 以下变量在 BrowserConfig 中不存在，保留原类型
  @override
  String get chromeVersion;
  @override
  String get oswVersion;
  @override
  bool get editing; // 备注
  @override
  String get comment; // 标签
  @override
  String get tag; // 分组id
  @override
  int get groupId;

  /// Create a copy of BrowserConfigModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BrowserConfigModelImplCopyWith<_$BrowserConfigModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
