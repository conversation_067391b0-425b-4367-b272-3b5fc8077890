import 'package:freezed_annotation/freezed_annotation.dart';

part 'browser_fingerprint_model.freezed.dart';
part 'browser_fingerprint_model.g.dart';

/// 浏览器指纹配置模型
@freezed
class BrowserFingerprintConfig with _$BrowserFingerprintConfig {
  const factory BrowserFingerprintConfig({
    // 基础设置
    @Json<PERSON><PERSON>(name: 'basic') BasicConfig? basic, // 基础配置
    @Json<PERSON>ey(name: 'webrtc') WebRTCConfig? webrtc, // WebRTC内外网指纹
    @<PERSON>son<PERSON><PERSON>(name: 'position') PositionConfig? position, // 地理位置信息
    @<PERSON><PERSON><PERSON><PERSON>(name: 'canvas') CanvasConfig? canvas, // Canvas指纹
    @Json<PERSON><PERSON>(name: 'webgl') WebGLConfig? webgl, // WebGL指纹
    @JsonKey(name: 'webaudio') double? webaudio, // WebAudio噪声
    @Json<PERSON>ey(name: 'clientrect') ClientRectConfig? clientrect, // Client Rect字体位置
    @J<PERSON><PERSON><PERSON>(name: 'gpu') GPUConfig? gpu, // WebGPU配置
    @Json<PERSON>ey(name: 'screen') ScreenConfig? screen, // 屏幕分辨率
    @JsonKey(name: 'mobile') MobileConfig? mobile, // 触摸支持
    @JsonKey(name: 'hardware') HardwareConfig? hardware, // 处理器和内存
    @JsonKey(name: 'clientHint') ClientHintConfig? clientHint, // 客户端提示
    @JsonKey(name: 'languages') LanguagesConfig? languages, // 语言设置
    @JsonKey(name: 'software') SoftwareConfig? software, // 软件开关
    @JsonKey(name: 'portScan') PortScanConfig? portScan, // 端口扫描开关
    @JsonKey(name: 'font') FontConfig? font, // 字体指纹
  }) = _BrowserFingerprintConfig;

  factory BrowserFingerprintConfig.fromJson(Map<String, dynamic> json) =>
      _$BrowserFingerprintConfigFromJson(json);
}

/// 基础配置模型
@freezed
class BasicConfig with _$BasicConfig {
  const factory BasicConfig({
    String? name, // 窗口名称
    int? groupId, // 分组ID
    String? account, // 账号平台
    String? accountUsername, // 账号用户名
    String? accountPassword, // 账号密码
    String? ua, // 用户代理
    String? cookie, // Cookie
  }) = _BasicConfig;

  factory BasicConfig.fromJson(Map<String, dynamic> json) =>
      _$BasicConfigFromJson(json);
}

/// WebRTC内外网指纹配置
@freezed
class WebRTCConfig with _$WebRTCConfig {
  const factory WebRTCConfig({
    @JsonKey(name: 'public') required String public, // 外网IP地址
    @JsonKey(name: 'private') required String private, // 内网IP地址
  }) = _WebRTCConfig;

  factory WebRTCConfig.fromJson(Map<String, dynamic> json) =>
      _$WebRTCConfigFromJson(json);
}

/// 地理位置信息配置
@freezed
class PositionConfig with _$PositionConfig {
  const factory PositionConfig({
    @JsonKey(name: 'longitude') required double longitude, // 经度
    @JsonKey(name: 'latitude') required double latitude, // 纬度
    @JsonKey(name: 'altitude') required double altitude, // 海拔高度
    @JsonKey(name: 'accuracy') required double accuracy, // 精度
  }) = _PositionConfig;

  factory PositionConfig.fromJson(Map<String, dynamic> json) =>
      _$PositionConfigFromJson(json);
}

/// Canvas指纹配置
@freezed
class CanvasConfig with _$CanvasConfig {
  const factory CanvasConfig({
    @JsonKey(name: 'noise') required double noise, // 噪声级别
  }) = _CanvasConfig;

  factory CanvasConfig.fromJson(Map<String, dynamic> json) =>
      _$CanvasConfigFromJson(json);
}

/// WebGL指纹配置
@freezed
class WebGLConfig with _$WebGLConfig {
  const factory WebGLConfig({
    @JsonKey(name: 'vendor') required String vendor, // 供应商信息
    @JsonKey(name: 'renderer') required String renderer, // 渲染器信息
  }) = _WebGLConfig;

  factory WebGLConfig.fromJson(Map<String, dynamic> json) =>
      _$WebGLConfigFromJson(json);
}

/// Client Rect指纹配置
@freezed
class ClientRectConfig with _$ClientRectConfig {
  const factory ClientRectConfig({
    @JsonKey(name: 'x') required double x, // X轴偏移
    @JsonKey(name: 'y') required double y, // Y轴偏移
  }) = _ClientRectConfig;

  factory ClientRectConfig.fromJson(Map<String, dynamic> json) =>
      _$ClientRectConfigFromJson(json);
}

/// WebGPU配置
@freezed
class GPUConfig with _$GPUConfig {
  const factory GPUConfig({
    @JsonKey(name: 'device') required String device, // 设备名称
    @JsonKey(name: 'description') required String description, // 描述信息
  }) = _GPUConfig;

  factory GPUConfig.fromJson(Map<String, dynamic> json) =>
      _$GPUConfigFromJson(json);
}

/// 屏幕分辨率配置
@freezed
class ScreenConfig with _$ScreenConfig {
  const factory ScreenConfig({
    @JsonKey(name: 'width') required double width, // 屏幕宽度
    @JsonKey(name: 'height') required double height, // 屏幕高度
    @JsonKey(name: 'colorDepth') required double colorDepth, // 色彩深度
    @JsonKey(name: 'availWidth') required double availWidth, // 可用宽度
    @JsonKey(name: 'availHeight') required double availHeight, // 可用高度
  }) = _ScreenConfig;

  factory ScreenConfig.fromJson(Map<String, dynamic> json) =>
      _$ScreenConfigFromJson(json);
}

/// 触摸支持配置
@freezed
class MobileConfig with _$MobileConfig {
  const factory MobileConfig({
    @JsonKey(name: 'touchsupport') required double touchsupport, // 触摸点数量
  }) = _MobileConfig;

  factory MobileConfig.fromJson(Map<String, dynamic> json) =>
      _$MobileConfigFromJson(json);
}

/// 硬件信息配置
@freezed
class HardwareConfig with _$HardwareConfig {
  const factory HardwareConfig({
    @JsonKey(name: 'concurrency') required double concurrency, // 处理器核心数
    @JsonKey(name: 'memory') required double memory, // 内存大小(GB)
  }) = _HardwareConfig;

  factory HardwareConfig.fromJson(Map<String, dynamic> json) =>
      _$HardwareConfigFromJson(json);
}

/// 客户端提示配置
@freezed
class ClientHintConfig with _$ClientHintConfig {
  const factory ClientHintConfig({
    @JsonKey(name: 'platform') required String platform, // 平台
    @JsonKey(name: 'navigator.platform') required String navigatorPlatform, // Navigator平台
    @JsonKey(name: 'platform_version') required String platformVersion, // 平台版本
    @JsonKey(name: 'ua_full_version') required String uaFullVersion, // UA完整版本
    @JsonKey(name: 'mobile') required String mobile, // 是否移动设备
    @JsonKey(name: 'architecture') required String architecture, // 架构
    @JsonKey(name: 'bitness') required String bitness, // 位数
  }) = _ClientHintConfig;

  factory ClientHintConfig.fromJson(Map<String, dynamic> json) =>
      _$ClientHintConfigFromJson(json);
}

/// 语言设置配置
@freezed
class LanguagesConfig with _$LanguagesConfig {
  const factory LanguagesConfig({
    @JsonKey(name: 'js') required String js, // JavaScript层语言
    @JsonKey(name: 'http') required String http, // HTTP层语言
  }) = _LanguagesConfig;

  factory LanguagesConfig.fromJson(Map<String, dynamic> json) =>
      _$LanguagesConfigFromJson(json);
}

/// 软件开关配置
@freezed
class SoftwareConfig with _$SoftwareConfig {
  const factory SoftwareConfig({
    @JsonKey(name: 'cookie') required String cookie, // Cookie开关 (yes/no)
    @JsonKey(name: 'java') required String java, // Java开关 (yes/no)
    @JsonKey(name: 'dnt') required String dnt, // DNT开关 (yes/no)
  }) = _SoftwareConfig;

  factory SoftwareConfig.fromJson(Map<String, dynamic> json) =>
      _$SoftwareConfigFromJson(json);
}

/// 端口扫描开关配置
@freezed
class PortScanConfig with _$PortScanConfig {
  const factory PortScanConfig({
    @JsonKey(name: 'enable') required String enable, // 端口扫描开关 (yes/no)
  }) = _PortScanConfig;

  factory PortScanConfig.fromJson(Map<String, dynamic> json) =>
      _$PortScanConfigFromJson(json);
}

/// 字体指纹配置
@freezed
class FontConfig with _$FontConfig {
  const factory FontConfig({
    @JsonKey(name: 'removefont') required String removefont, // 要移除的字体列表（逗号分隔）
  }) = _FontConfig;

  factory FontConfig.fromJson(Map<String, dynamic> json) =>
      _$FontConfigFromJson(json);
}

/// 浏览器启动参数配置
@freezed
class BrowserLaunchConfig with _$BrowserLaunchConfig {
  const factory BrowserLaunchConfig({
    @JsonKey(name: 'fingerprint') required BrowserFingerprintConfig fingerprint, // 指纹配置
    @JsonKey(name: 'userAgent') required String userAgent, // 用户代理
    @JsonKey(name: 'language') required String language, // 系统界面语言
    @JsonKey(name: 'timeZone') required String timeZone, // 时区
    @JsonKey(name: 'userDataDir') required String userDataDir, // 用户数据目录
    @JsonKey(name: 'enableUnsafeWebGPU') @Default(false) bool enableUnsafeWebGPU, // 启用WebGPU
  }) = _BrowserLaunchConfig;

  factory BrowserLaunchConfig.fromJson(Map<String, dynamic> json) =>
      _$BrowserLaunchConfigFromJson(json);
} 
