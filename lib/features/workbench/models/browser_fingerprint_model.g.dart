// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'browser_fingerprint_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$BrowserFingerprintConfigImpl _$$BrowserFingerprintConfigImplFromJson(
        Map<String, dynamic> json) =>
    _$BrowserFingerprintConfigImpl(
      basic: json['basic'] == null
          ? null
          : BasicConfig.fromJson(json['basic'] as Map<String, dynamic>),
      webrtc: json['webrtc'] == null
          ? null
          : WebRTCConfig.fromJson(json['webrtc'] as Map<String, dynamic>),
      position: json['position'] == null
          ? null
          : PositionConfig.fromJson(json['position'] as Map<String, dynamic>),
      canvas: json['canvas'] == null
          ? null
          : CanvasConfig.fromJson(json['canvas'] as Map<String, dynamic>),
      webgl: json['webgl'] == null
          ? null
          : WebGLConfig.fromJson(json['webgl'] as Map<String, dynamic>),
      webaudio: (json['webaudio'] as num?)?.toDouble(),
      clientrect: json['clientrect'] == null
          ? null
          : ClientRectConfig.fromJson(
              json['clientrect'] as Map<String, dynamic>),
      gpu: json['gpu'] == null
          ? null
          : GPUConfig.fromJson(json['gpu'] as Map<String, dynamic>),
      screen: json['screen'] == null
          ? null
          : ScreenConfig.fromJson(json['screen'] as Map<String, dynamic>),
      mobile: json['mobile'] == null
          ? null
          : MobileConfig.fromJson(json['mobile'] as Map<String, dynamic>),
      hardware: json['hardware'] == null
          ? null
          : HardwareConfig.fromJson(json['hardware'] as Map<String, dynamic>),
      clientHint: json['clientHint'] == null
          ? null
          : ClientHintConfig.fromJson(
              json['clientHint'] as Map<String, dynamic>),
      languages: json['languages'] == null
          ? null
          : LanguagesConfig.fromJson(json['languages'] as Map<String, dynamic>),
      software: json['software'] == null
          ? null
          : SoftwareConfig.fromJson(json['software'] as Map<String, dynamic>),
      portScan: json['portScan'] == null
          ? null
          : PortScanConfig.fromJson(json['portScan'] as Map<String, dynamic>),
      font: json['font'] == null
          ? null
          : FontConfig.fromJson(json['font'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$BrowserFingerprintConfigImplToJson(
        _$BrowserFingerprintConfigImpl instance) =>
    <String, dynamic>{
      'basic': instance.basic,
      'webrtc': instance.webrtc,
      'position': instance.position,
      'canvas': instance.canvas,
      'webgl': instance.webgl,
      'webaudio': instance.webaudio,
      'clientrect': instance.clientrect,
      'gpu': instance.gpu,
      'screen': instance.screen,
      'mobile': instance.mobile,
      'hardware': instance.hardware,
      'clientHint': instance.clientHint,
      'languages': instance.languages,
      'software': instance.software,
      'portScan': instance.portScan,
      'font': instance.font,
    };

_$BasicConfigImpl _$$BasicConfigImplFromJson(Map<String, dynamic> json) =>
    _$BasicConfigImpl(
      name: json['name'] as String?,
      groupId: (json['groupId'] as num?)?.toInt(),
      account: json['account'] as String?,
      accountUsername: json['accountUsername'] as String?,
      accountPassword: json['accountPassword'] as String?,
      ua: json['ua'] as String?,
      cookie: json['cookie'] as String?,
    );

Map<String, dynamic> _$$BasicConfigImplToJson(_$BasicConfigImpl instance) =>
    <String, dynamic>{
      'name': instance.name,
      'groupId': instance.groupId,
      'account': instance.account,
      'accountUsername': instance.accountUsername,
      'accountPassword': instance.accountPassword,
      'ua': instance.ua,
      'cookie': instance.cookie,
    };

_$WebRTCConfigImpl _$$WebRTCConfigImplFromJson(Map<String, dynamic> json) =>
    _$WebRTCConfigImpl(
      public: json['public'] as String,
      private: json['private'] as String,
    );

Map<String, dynamic> _$$WebRTCConfigImplToJson(_$WebRTCConfigImpl instance) =>
    <String, dynamic>{
      'public': instance.public,
      'private': instance.private,
    };

_$PositionConfigImpl _$$PositionConfigImplFromJson(Map<String, dynamic> json) =>
    _$PositionConfigImpl(
      longitude: (json['longitude'] as num).toDouble(),
      latitude: (json['latitude'] as num).toDouble(),
      altitude: (json['altitude'] as num).toDouble(),
      accuracy: (json['accuracy'] as num).toDouble(),
    );

Map<String, dynamic> _$$PositionConfigImplToJson(
        _$PositionConfigImpl instance) =>
    <String, dynamic>{
      'longitude': instance.longitude,
      'latitude': instance.latitude,
      'altitude': instance.altitude,
      'accuracy': instance.accuracy,
    };

_$CanvasConfigImpl _$$CanvasConfigImplFromJson(Map<String, dynamic> json) =>
    _$CanvasConfigImpl(
      noise: (json['noise'] as num).toDouble(),
    );

Map<String, dynamic> _$$CanvasConfigImplToJson(_$CanvasConfigImpl instance) =>
    <String, dynamic>{
      'noise': instance.noise,
    };

_$WebGLConfigImpl _$$WebGLConfigImplFromJson(Map<String, dynamic> json) =>
    _$WebGLConfigImpl(
      vendor: json['vendor'] as String,
      renderer: json['renderer'] as String,
    );

Map<String, dynamic> _$$WebGLConfigImplToJson(_$WebGLConfigImpl instance) =>
    <String, dynamic>{
      'vendor': instance.vendor,
      'renderer': instance.renderer,
    };

_$ClientRectConfigImpl _$$ClientRectConfigImplFromJson(
        Map<String, dynamic> json) =>
    _$ClientRectConfigImpl(
      x: (json['x'] as num).toDouble(),
      y: (json['y'] as num).toDouble(),
    );

Map<String, dynamic> _$$ClientRectConfigImplToJson(
        _$ClientRectConfigImpl instance) =>
    <String, dynamic>{
      'x': instance.x,
      'y': instance.y,
    };

_$GPUConfigImpl _$$GPUConfigImplFromJson(Map<String, dynamic> json) =>
    _$GPUConfigImpl(
      device: json['device'] as String,
      description: json['description'] as String,
    );

Map<String, dynamic> _$$GPUConfigImplToJson(_$GPUConfigImpl instance) =>
    <String, dynamic>{
      'device': instance.device,
      'description': instance.description,
    };

_$ScreenConfigImpl _$$ScreenConfigImplFromJson(Map<String, dynamic> json) =>
    _$ScreenConfigImpl(
      width: (json['width'] as num).toDouble(),
      height: (json['height'] as num).toDouble(),
      colorDepth: (json['colorDepth'] as num).toDouble(),
      availWidth: (json['availWidth'] as num).toDouble(),
      availHeight: (json['availHeight'] as num).toDouble(),
    );

Map<String, dynamic> _$$ScreenConfigImplToJson(_$ScreenConfigImpl instance) =>
    <String, dynamic>{
      'width': instance.width,
      'height': instance.height,
      'colorDepth': instance.colorDepth,
      'availWidth': instance.availWidth,
      'availHeight': instance.availHeight,
    };

_$MobileConfigImpl _$$MobileConfigImplFromJson(Map<String, dynamic> json) =>
    _$MobileConfigImpl(
      touchsupport: (json['touchsupport'] as num).toDouble(),
    );

Map<String, dynamic> _$$MobileConfigImplToJson(_$MobileConfigImpl instance) =>
    <String, dynamic>{
      'touchsupport': instance.touchsupport,
    };

_$HardwareConfigImpl _$$HardwareConfigImplFromJson(Map<String, dynamic> json) =>
    _$HardwareConfigImpl(
      concurrency: (json['concurrency'] as num).toDouble(),
      memory: (json['memory'] as num).toDouble(),
    );

Map<String, dynamic> _$$HardwareConfigImplToJson(
        _$HardwareConfigImpl instance) =>
    <String, dynamic>{
      'concurrency': instance.concurrency,
      'memory': instance.memory,
    };

_$ClientHintConfigImpl _$$ClientHintConfigImplFromJson(
        Map<String, dynamic> json) =>
    _$ClientHintConfigImpl(
      platform: json['platform'] as String,
      navigatorPlatform: json['navigator.platform'] as String,
      platformVersion: json['platform_version'] as String,
      uaFullVersion: json['ua_full_version'] as String,
      mobile: json['mobile'] as String,
      architecture: json['architecture'] as String,
      bitness: json['bitness'] as String,
    );

Map<String, dynamic> _$$ClientHintConfigImplToJson(
        _$ClientHintConfigImpl instance) =>
    <String, dynamic>{
      'platform': instance.platform,
      'navigator.platform': instance.navigatorPlatform,
      'platform_version': instance.platformVersion,
      'ua_full_version': instance.uaFullVersion,
      'mobile': instance.mobile,
      'architecture': instance.architecture,
      'bitness': instance.bitness,
    };

_$LanguagesConfigImpl _$$LanguagesConfigImplFromJson(
        Map<String, dynamic> json) =>
    _$LanguagesConfigImpl(
      js: json['js'] as String,
      http: json['http'] as String,
    );

Map<String, dynamic> _$$LanguagesConfigImplToJson(
        _$LanguagesConfigImpl instance) =>
    <String, dynamic>{
      'js': instance.js,
      'http': instance.http,
    };

_$SoftwareConfigImpl _$$SoftwareConfigImplFromJson(Map<String, dynamic> json) =>
    _$SoftwareConfigImpl(
      cookie: json['cookie'] as String,
      java: json['java'] as String,
      dnt: json['dnt'] as String,
    );

Map<String, dynamic> _$$SoftwareConfigImplToJson(
        _$SoftwareConfigImpl instance) =>
    <String, dynamic>{
      'cookie': instance.cookie,
      'java': instance.java,
      'dnt': instance.dnt,
    };

_$PortScanConfigImpl _$$PortScanConfigImplFromJson(Map<String, dynamic> json) =>
    _$PortScanConfigImpl(
      enable: json['enable'] as String,
    );

Map<String, dynamic> _$$PortScanConfigImplToJson(
        _$PortScanConfigImpl instance) =>
    <String, dynamic>{
      'enable': instance.enable,
    };

_$FontConfigImpl _$$FontConfigImplFromJson(Map<String, dynamic> json) =>
    _$FontConfigImpl(
      removefont: json['removefont'] as String,
    );

Map<String, dynamic> _$$FontConfigImplToJson(_$FontConfigImpl instance) =>
    <String, dynamic>{
      'removefont': instance.removefont,
    };

_$BrowserLaunchConfigImpl _$$BrowserLaunchConfigImplFromJson(
        Map<String, dynamic> json) =>
    _$BrowserLaunchConfigImpl(
      fingerprint: BrowserFingerprintConfig.fromJson(
          json['fingerprint'] as Map<String, dynamic>),
      userAgent: json['userAgent'] as String,
      language: json['language'] as String,
      timeZone: json['timeZone'] as String,
      userDataDir: json['userDataDir'] as String,
      enableUnsafeWebGPU: json['enableUnsafeWebGPU'] as bool? ?? false,
    );

Map<String, dynamic> _$$BrowserLaunchConfigImplToJson(
        _$BrowserLaunchConfigImpl instance) =>
    <String, dynamic>{
      'fingerprint': instance.fingerprint,
      'userAgent': instance.userAgent,
      'language': instance.language,
      'timeZone': instance.timeZone,
      'userDataDir': instance.userDataDir,
      'enableUnsafeWebGPU': instance.enableUnsafeWebGPU,
    };
