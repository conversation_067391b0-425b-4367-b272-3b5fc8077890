// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'browser_fingerprint_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

BrowserFingerprintConfig _$BrowserFingerprintConfigFromJson(
    Map<String, dynamic> json) {
  return _BrowserFingerprintConfig.fromJson(json);
}

/// @nodoc
mixin _$BrowserFingerprintConfig {
// 基础设置
  @JsonKey(name: 'basic')
  BasicConfig? get basic => throw _privateConstructorUsedError; // 基础配置
  @JsonKey(name: 'webrtc')
  WebRTCConfig? get webrtc => throw _privateConstructorUsedError; // WebRTC内外网指纹
  @JsonKey(name: 'position')
  PositionConfig? get position => throw _privateConstructorUsedError; // 地理位置信息
  @JsonKey(name: 'canvas')
  CanvasConfig? get canvas => throw _privateConstructorUsedError; // Canvas指纹
  @JsonKey(name: 'webgl')
  WebGLConfig? get webgl => throw _privateConstructorUsedError; // WebGL指纹
  @JsonKey(name: 'webaudio')
  double? get webaudio => throw _privateConstructorUsedError; // WebAudio噪声
  @JsonKey(name: 'clientrect')
  ClientRectConfig? get clientrect =>
      throw _privateConstructorUsedError; // Client Rect字体位置
  @JsonKey(name: 'gpu')
  GPUConfig? get gpu => throw _privateConstructorUsedError; // WebGPU配置
  @JsonKey(name: 'screen')
  ScreenConfig? get screen => throw _privateConstructorUsedError; // 屏幕分辨率
  @JsonKey(name: 'mobile')
  MobileConfig? get mobile => throw _privateConstructorUsedError; // 触摸支持
  @JsonKey(name: 'hardware')
  HardwareConfig? get hardware => throw _privateConstructorUsedError; // 处理器和内存
  @JsonKey(name: 'clientHint')
  ClientHintConfig? get clientHint =>
      throw _privateConstructorUsedError; // 客户端提示
  @JsonKey(name: 'languages')
  LanguagesConfig? get languages => throw _privateConstructorUsedError; // 语言设置
  @JsonKey(name: 'software')
  SoftwareConfig? get software => throw _privateConstructorUsedError; // 软件开关
  @JsonKey(name: 'portScan')
  PortScanConfig? get portScan => throw _privateConstructorUsedError; // 端口扫描开关
  @JsonKey(name: 'font')
  FontConfig? get font => throw _privateConstructorUsedError;

  /// Serializes this BrowserFingerprintConfig to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of BrowserFingerprintConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BrowserFingerprintConfigCopyWith<BrowserFingerprintConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BrowserFingerprintConfigCopyWith<$Res> {
  factory $BrowserFingerprintConfigCopyWith(BrowserFingerprintConfig value,
          $Res Function(BrowserFingerprintConfig) then) =
      _$BrowserFingerprintConfigCopyWithImpl<$Res, BrowserFingerprintConfig>;
  @useResult
  $Res call(
      {@JsonKey(name: 'basic') BasicConfig? basic,
      @JsonKey(name: 'webrtc') WebRTCConfig? webrtc,
      @JsonKey(name: 'position') PositionConfig? position,
      @JsonKey(name: 'canvas') CanvasConfig? canvas,
      @JsonKey(name: 'webgl') WebGLConfig? webgl,
      @JsonKey(name: 'webaudio') double? webaudio,
      @JsonKey(name: 'clientrect') ClientRectConfig? clientrect,
      @JsonKey(name: 'gpu') GPUConfig? gpu,
      @JsonKey(name: 'screen') ScreenConfig? screen,
      @JsonKey(name: 'mobile') MobileConfig? mobile,
      @JsonKey(name: 'hardware') HardwareConfig? hardware,
      @JsonKey(name: 'clientHint') ClientHintConfig? clientHint,
      @JsonKey(name: 'languages') LanguagesConfig? languages,
      @JsonKey(name: 'software') SoftwareConfig? software,
      @JsonKey(name: 'portScan') PortScanConfig? portScan,
      @JsonKey(name: 'font') FontConfig? font});

  $BasicConfigCopyWith<$Res>? get basic;
  $WebRTCConfigCopyWith<$Res>? get webrtc;
  $PositionConfigCopyWith<$Res>? get position;
  $CanvasConfigCopyWith<$Res>? get canvas;
  $WebGLConfigCopyWith<$Res>? get webgl;
  $ClientRectConfigCopyWith<$Res>? get clientrect;
  $GPUConfigCopyWith<$Res>? get gpu;
  $ScreenConfigCopyWith<$Res>? get screen;
  $MobileConfigCopyWith<$Res>? get mobile;
  $HardwareConfigCopyWith<$Res>? get hardware;
  $ClientHintConfigCopyWith<$Res>? get clientHint;
  $LanguagesConfigCopyWith<$Res>? get languages;
  $SoftwareConfigCopyWith<$Res>? get software;
  $PortScanConfigCopyWith<$Res>? get portScan;
  $FontConfigCopyWith<$Res>? get font;
}

/// @nodoc
class _$BrowserFingerprintConfigCopyWithImpl<$Res,
        $Val extends BrowserFingerprintConfig>
    implements $BrowserFingerprintConfigCopyWith<$Res> {
  _$BrowserFingerprintConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BrowserFingerprintConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? basic = freezed,
    Object? webrtc = freezed,
    Object? position = freezed,
    Object? canvas = freezed,
    Object? webgl = freezed,
    Object? webaudio = freezed,
    Object? clientrect = freezed,
    Object? gpu = freezed,
    Object? screen = freezed,
    Object? mobile = freezed,
    Object? hardware = freezed,
    Object? clientHint = freezed,
    Object? languages = freezed,
    Object? software = freezed,
    Object? portScan = freezed,
    Object? font = freezed,
  }) {
    return _then(_value.copyWith(
      basic: freezed == basic
          ? _value.basic
          : basic // ignore: cast_nullable_to_non_nullable
              as BasicConfig?,
      webrtc: freezed == webrtc
          ? _value.webrtc
          : webrtc // ignore: cast_nullable_to_non_nullable
              as WebRTCConfig?,
      position: freezed == position
          ? _value.position
          : position // ignore: cast_nullable_to_non_nullable
              as PositionConfig?,
      canvas: freezed == canvas
          ? _value.canvas
          : canvas // ignore: cast_nullable_to_non_nullable
              as CanvasConfig?,
      webgl: freezed == webgl
          ? _value.webgl
          : webgl // ignore: cast_nullable_to_non_nullable
              as WebGLConfig?,
      webaudio: freezed == webaudio
          ? _value.webaudio
          : webaudio // ignore: cast_nullable_to_non_nullable
              as double?,
      clientrect: freezed == clientrect
          ? _value.clientrect
          : clientrect // ignore: cast_nullable_to_non_nullable
              as ClientRectConfig?,
      gpu: freezed == gpu
          ? _value.gpu
          : gpu // ignore: cast_nullable_to_non_nullable
              as GPUConfig?,
      screen: freezed == screen
          ? _value.screen
          : screen // ignore: cast_nullable_to_non_nullable
              as ScreenConfig?,
      mobile: freezed == mobile
          ? _value.mobile
          : mobile // ignore: cast_nullable_to_non_nullable
              as MobileConfig?,
      hardware: freezed == hardware
          ? _value.hardware
          : hardware // ignore: cast_nullable_to_non_nullable
              as HardwareConfig?,
      clientHint: freezed == clientHint
          ? _value.clientHint
          : clientHint // ignore: cast_nullable_to_non_nullable
              as ClientHintConfig?,
      languages: freezed == languages
          ? _value.languages
          : languages // ignore: cast_nullable_to_non_nullable
              as LanguagesConfig?,
      software: freezed == software
          ? _value.software
          : software // ignore: cast_nullable_to_non_nullable
              as SoftwareConfig?,
      portScan: freezed == portScan
          ? _value.portScan
          : portScan // ignore: cast_nullable_to_non_nullable
              as PortScanConfig?,
      font: freezed == font
          ? _value.font
          : font // ignore: cast_nullable_to_non_nullable
              as FontConfig?,
    ) as $Val);
  }

  /// Create a copy of BrowserFingerprintConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $BasicConfigCopyWith<$Res>? get basic {
    if (_value.basic == null) {
      return null;
    }

    return $BasicConfigCopyWith<$Res>(_value.basic!, (value) {
      return _then(_value.copyWith(basic: value) as $Val);
    });
  }

  /// Create a copy of BrowserFingerprintConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $WebRTCConfigCopyWith<$Res>? get webrtc {
    if (_value.webrtc == null) {
      return null;
    }

    return $WebRTCConfigCopyWith<$Res>(_value.webrtc!, (value) {
      return _then(_value.copyWith(webrtc: value) as $Val);
    });
  }

  /// Create a copy of BrowserFingerprintConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PositionConfigCopyWith<$Res>? get position {
    if (_value.position == null) {
      return null;
    }

    return $PositionConfigCopyWith<$Res>(_value.position!, (value) {
      return _then(_value.copyWith(position: value) as $Val);
    });
  }

  /// Create a copy of BrowserFingerprintConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CanvasConfigCopyWith<$Res>? get canvas {
    if (_value.canvas == null) {
      return null;
    }

    return $CanvasConfigCopyWith<$Res>(_value.canvas!, (value) {
      return _then(_value.copyWith(canvas: value) as $Val);
    });
  }

  /// Create a copy of BrowserFingerprintConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $WebGLConfigCopyWith<$Res>? get webgl {
    if (_value.webgl == null) {
      return null;
    }

    return $WebGLConfigCopyWith<$Res>(_value.webgl!, (value) {
      return _then(_value.copyWith(webgl: value) as $Val);
    });
  }

  /// Create a copy of BrowserFingerprintConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ClientRectConfigCopyWith<$Res>? get clientrect {
    if (_value.clientrect == null) {
      return null;
    }

    return $ClientRectConfigCopyWith<$Res>(_value.clientrect!, (value) {
      return _then(_value.copyWith(clientrect: value) as $Val);
    });
  }

  /// Create a copy of BrowserFingerprintConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $GPUConfigCopyWith<$Res>? get gpu {
    if (_value.gpu == null) {
      return null;
    }

    return $GPUConfigCopyWith<$Res>(_value.gpu!, (value) {
      return _then(_value.copyWith(gpu: value) as $Val);
    });
  }

  /// Create a copy of BrowserFingerprintConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ScreenConfigCopyWith<$Res>? get screen {
    if (_value.screen == null) {
      return null;
    }

    return $ScreenConfigCopyWith<$Res>(_value.screen!, (value) {
      return _then(_value.copyWith(screen: value) as $Val);
    });
  }

  /// Create a copy of BrowserFingerprintConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MobileConfigCopyWith<$Res>? get mobile {
    if (_value.mobile == null) {
      return null;
    }

    return $MobileConfigCopyWith<$Res>(_value.mobile!, (value) {
      return _then(_value.copyWith(mobile: value) as $Val);
    });
  }

  /// Create a copy of BrowserFingerprintConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $HardwareConfigCopyWith<$Res>? get hardware {
    if (_value.hardware == null) {
      return null;
    }

    return $HardwareConfigCopyWith<$Res>(_value.hardware!, (value) {
      return _then(_value.copyWith(hardware: value) as $Val);
    });
  }

  /// Create a copy of BrowserFingerprintConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ClientHintConfigCopyWith<$Res>? get clientHint {
    if (_value.clientHint == null) {
      return null;
    }

    return $ClientHintConfigCopyWith<$Res>(_value.clientHint!, (value) {
      return _then(_value.copyWith(clientHint: value) as $Val);
    });
  }

  /// Create a copy of BrowserFingerprintConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LanguagesConfigCopyWith<$Res>? get languages {
    if (_value.languages == null) {
      return null;
    }

    return $LanguagesConfigCopyWith<$Res>(_value.languages!, (value) {
      return _then(_value.copyWith(languages: value) as $Val);
    });
  }

  /// Create a copy of BrowserFingerprintConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SoftwareConfigCopyWith<$Res>? get software {
    if (_value.software == null) {
      return null;
    }

    return $SoftwareConfigCopyWith<$Res>(_value.software!, (value) {
      return _then(_value.copyWith(software: value) as $Val);
    });
  }

  /// Create a copy of BrowserFingerprintConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PortScanConfigCopyWith<$Res>? get portScan {
    if (_value.portScan == null) {
      return null;
    }

    return $PortScanConfigCopyWith<$Res>(_value.portScan!, (value) {
      return _then(_value.copyWith(portScan: value) as $Val);
    });
  }

  /// Create a copy of BrowserFingerprintConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $FontConfigCopyWith<$Res>? get font {
    if (_value.font == null) {
      return null;
    }

    return $FontConfigCopyWith<$Res>(_value.font!, (value) {
      return _then(_value.copyWith(font: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$BrowserFingerprintConfigImplCopyWith<$Res>
    implements $BrowserFingerprintConfigCopyWith<$Res> {
  factory _$$BrowserFingerprintConfigImplCopyWith(
          _$BrowserFingerprintConfigImpl value,
          $Res Function(_$BrowserFingerprintConfigImpl) then) =
      __$$BrowserFingerprintConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'basic') BasicConfig? basic,
      @JsonKey(name: 'webrtc') WebRTCConfig? webrtc,
      @JsonKey(name: 'position') PositionConfig? position,
      @JsonKey(name: 'canvas') CanvasConfig? canvas,
      @JsonKey(name: 'webgl') WebGLConfig? webgl,
      @JsonKey(name: 'webaudio') double? webaudio,
      @JsonKey(name: 'clientrect') ClientRectConfig? clientrect,
      @JsonKey(name: 'gpu') GPUConfig? gpu,
      @JsonKey(name: 'screen') ScreenConfig? screen,
      @JsonKey(name: 'mobile') MobileConfig? mobile,
      @JsonKey(name: 'hardware') HardwareConfig? hardware,
      @JsonKey(name: 'clientHint') ClientHintConfig? clientHint,
      @JsonKey(name: 'languages') LanguagesConfig? languages,
      @JsonKey(name: 'software') SoftwareConfig? software,
      @JsonKey(name: 'portScan') PortScanConfig? portScan,
      @JsonKey(name: 'font') FontConfig? font});

  @override
  $BasicConfigCopyWith<$Res>? get basic;
  @override
  $WebRTCConfigCopyWith<$Res>? get webrtc;
  @override
  $PositionConfigCopyWith<$Res>? get position;
  @override
  $CanvasConfigCopyWith<$Res>? get canvas;
  @override
  $WebGLConfigCopyWith<$Res>? get webgl;
  @override
  $ClientRectConfigCopyWith<$Res>? get clientrect;
  @override
  $GPUConfigCopyWith<$Res>? get gpu;
  @override
  $ScreenConfigCopyWith<$Res>? get screen;
  @override
  $MobileConfigCopyWith<$Res>? get mobile;
  @override
  $HardwareConfigCopyWith<$Res>? get hardware;
  @override
  $ClientHintConfigCopyWith<$Res>? get clientHint;
  @override
  $LanguagesConfigCopyWith<$Res>? get languages;
  @override
  $SoftwareConfigCopyWith<$Res>? get software;
  @override
  $PortScanConfigCopyWith<$Res>? get portScan;
  @override
  $FontConfigCopyWith<$Res>? get font;
}

/// @nodoc
class __$$BrowserFingerprintConfigImplCopyWithImpl<$Res>
    extends _$BrowserFingerprintConfigCopyWithImpl<$Res,
        _$BrowserFingerprintConfigImpl>
    implements _$$BrowserFingerprintConfigImplCopyWith<$Res> {
  __$$BrowserFingerprintConfigImplCopyWithImpl(
      _$BrowserFingerprintConfigImpl _value,
      $Res Function(_$BrowserFingerprintConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of BrowserFingerprintConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? basic = freezed,
    Object? webrtc = freezed,
    Object? position = freezed,
    Object? canvas = freezed,
    Object? webgl = freezed,
    Object? webaudio = freezed,
    Object? clientrect = freezed,
    Object? gpu = freezed,
    Object? screen = freezed,
    Object? mobile = freezed,
    Object? hardware = freezed,
    Object? clientHint = freezed,
    Object? languages = freezed,
    Object? software = freezed,
    Object? portScan = freezed,
    Object? font = freezed,
  }) {
    return _then(_$BrowserFingerprintConfigImpl(
      basic: freezed == basic
          ? _value.basic
          : basic // ignore: cast_nullable_to_non_nullable
              as BasicConfig?,
      webrtc: freezed == webrtc
          ? _value.webrtc
          : webrtc // ignore: cast_nullable_to_non_nullable
              as WebRTCConfig?,
      position: freezed == position
          ? _value.position
          : position // ignore: cast_nullable_to_non_nullable
              as PositionConfig?,
      canvas: freezed == canvas
          ? _value.canvas
          : canvas // ignore: cast_nullable_to_non_nullable
              as CanvasConfig?,
      webgl: freezed == webgl
          ? _value.webgl
          : webgl // ignore: cast_nullable_to_non_nullable
              as WebGLConfig?,
      webaudio: freezed == webaudio
          ? _value.webaudio
          : webaudio // ignore: cast_nullable_to_non_nullable
              as double?,
      clientrect: freezed == clientrect
          ? _value.clientrect
          : clientrect // ignore: cast_nullable_to_non_nullable
              as ClientRectConfig?,
      gpu: freezed == gpu
          ? _value.gpu
          : gpu // ignore: cast_nullable_to_non_nullable
              as GPUConfig?,
      screen: freezed == screen
          ? _value.screen
          : screen // ignore: cast_nullable_to_non_nullable
              as ScreenConfig?,
      mobile: freezed == mobile
          ? _value.mobile
          : mobile // ignore: cast_nullable_to_non_nullable
              as MobileConfig?,
      hardware: freezed == hardware
          ? _value.hardware
          : hardware // ignore: cast_nullable_to_non_nullable
              as HardwareConfig?,
      clientHint: freezed == clientHint
          ? _value.clientHint
          : clientHint // ignore: cast_nullable_to_non_nullable
              as ClientHintConfig?,
      languages: freezed == languages
          ? _value.languages
          : languages // ignore: cast_nullable_to_non_nullable
              as LanguagesConfig?,
      software: freezed == software
          ? _value.software
          : software // ignore: cast_nullable_to_non_nullable
              as SoftwareConfig?,
      portScan: freezed == portScan
          ? _value.portScan
          : portScan // ignore: cast_nullable_to_non_nullable
              as PortScanConfig?,
      font: freezed == font
          ? _value.font
          : font // ignore: cast_nullable_to_non_nullable
              as FontConfig?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BrowserFingerprintConfigImpl implements _BrowserFingerprintConfig {
  const _$BrowserFingerprintConfigImpl(
      {@JsonKey(name: 'basic') this.basic,
      @JsonKey(name: 'webrtc') this.webrtc,
      @JsonKey(name: 'position') this.position,
      @JsonKey(name: 'canvas') this.canvas,
      @JsonKey(name: 'webgl') this.webgl,
      @JsonKey(name: 'webaudio') this.webaudio,
      @JsonKey(name: 'clientrect') this.clientrect,
      @JsonKey(name: 'gpu') this.gpu,
      @JsonKey(name: 'screen') this.screen,
      @JsonKey(name: 'mobile') this.mobile,
      @JsonKey(name: 'hardware') this.hardware,
      @JsonKey(name: 'clientHint') this.clientHint,
      @JsonKey(name: 'languages') this.languages,
      @JsonKey(name: 'software') this.software,
      @JsonKey(name: 'portScan') this.portScan,
      @JsonKey(name: 'font') this.font});

  factory _$BrowserFingerprintConfigImpl.fromJson(Map<String, dynamic> json) =>
      _$$BrowserFingerprintConfigImplFromJson(json);

// 基础设置
  @override
  @JsonKey(name: 'basic')
  final BasicConfig? basic;
// 基础配置
  @override
  @JsonKey(name: 'webrtc')
  final WebRTCConfig? webrtc;
// WebRTC内外网指纹
  @override
  @JsonKey(name: 'position')
  final PositionConfig? position;
// 地理位置信息
  @override
  @JsonKey(name: 'canvas')
  final CanvasConfig? canvas;
// Canvas指纹
  @override
  @JsonKey(name: 'webgl')
  final WebGLConfig? webgl;
// WebGL指纹
  @override
  @JsonKey(name: 'webaudio')
  final double? webaudio;
// WebAudio噪声
  @override
  @JsonKey(name: 'clientrect')
  final ClientRectConfig? clientrect;
// Client Rect字体位置
  @override
  @JsonKey(name: 'gpu')
  final GPUConfig? gpu;
// WebGPU配置
  @override
  @JsonKey(name: 'screen')
  final ScreenConfig? screen;
// 屏幕分辨率
  @override
  @JsonKey(name: 'mobile')
  final MobileConfig? mobile;
// 触摸支持
  @override
  @JsonKey(name: 'hardware')
  final HardwareConfig? hardware;
// 处理器和内存
  @override
  @JsonKey(name: 'clientHint')
  final ClientHintConfig? clientHint;
// 客户端提示
  @override
  @JsonKey(name: 'languages')
  final LanguagesConfig? languages;
// 语言设置
  @override
  @JsonKey(name: 'software')
  final SoftwareConfig? software;
// 软件开关
  @override
  @JsonKey(name: 'portScan')
  final PortScanConfig? portScan;
// 端口扫描开关
  @override
  @JsonKey(name: 'font')
  final FontConfig? font;

  @override
  String toString() {
    return 'BrowserFingerprintConfig(basic: $basic, webrtc: $webrtc, position: $position, canvas: $canvas, webgl: $webgl, webaudio: $webaudio, clientrect: $clientrect, gpu: $gpu, screen: $screen, mobile: $mobile, hardware: $hardware, clientHint: $clientHint, languages: $languages, software: $software, portScan: $portScan, font: $font)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BrowserFingerprintConfigImpl &&
            (identical(other.basic, basic) || other.basic == basic) &&
            (identical(other.webrtc, webrtc) || other.webrtc == webrtc) &&
            (identical(other.position, position) ||
                other.position == position) &&
            (identical(other.canvas, canvas) || other.canvas == canvas) &&
            (identical(other.webgl, webgl) || other.webgl == webgl) &&
            (identical(other.webaudio, webaudio) ||
                other.webaudio == webaudio) &&
            (identical(other.clientrect, clientrect) ||
                other.clientrect == clientrect) &&
            (identical(other.gpu, gpu) || other.gpu == gpu) &&
            (identical(other.screen, screen) || other.screen == screen) &&
            (identical(other.mobile, mobile) || other.mobile == mobile) &&
            (identical(other.hardware, hardware) ||
                other.hardware == hardware) &&
            (identical(other.clientHint, clientHint) ||
                other.clientHint == clientHint) &&
            (identical(other.languages, languages) ||
                other.languages == languages) &&
            (identical(other.software, software) ||
                other.software == software) &&
            (identical(other.portScan, portScan) ||
                other.portScan == portScan) &&
            (identical(other.font, font) || other.font == font));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      basic,
      webrtc,
      position,
      canvas,
      webgl,
      webaudio,
      clientrect,
      gpu,
      screen,
      mobile,
      hardware,
      clientHint,
      languages,
      software,
      portScan,
      font);

  /// Create a copy of BrowserFingerprintConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BrowserFingerprintConfigImplCopyWith<_$BrowserFingerprintConfigImpl>
      get copyWith => __$$BrowserFingerprintConfigImplCopyWithImpl<
          _$BrowserFingerprintConfigImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BrowserFingerprintConfigImplToJson(
      this,
    );
  }
}

abstract class _BrowserFingerprintConfig implements BrowserFingerprintConfig {
  const factory _BrowserFingerprintConfig(
          {@JsonKey(name: 'basic') final BasicConfig? basic,
          @JsonKey(name: 'webrtc') final WebRTCConfig? webrtc,
          @JsonKey(name: 'position') final PositionConfig? position,
          @JsonKey(name: 'canvas') final CanvasConfig? canvas,
          @JsonKey(name: 'webgl') final WebGLConfig? webgl,
          @JsonKey(name: 'webaudio') final double? webaudio,
          @JsonKey(name: 'clientrect') final ClientRectConfig? clientrect,
          @JsonKey(name: 'gpu') final GPUConfig? gpu,
          @JsonKey(name: 'screen') final ScreenConfig? screen,
          @JsonKey(name: 'mobile') final MobileConfig? mobile,
          @JsonKey(name: 'hardware') final HardwareConfig? hardware,
          @JsonKey(name: 'clientHint') final ClientHintConfig? clientHint,
          @JsonKey(name: 'languages') final LanguagesConfig? languages,
          @JsonKey(name: 'software') final SoftwareConfig? software,
          @JsonKey(name: 'portScan') final PortScanConfig? portScan,
          @JsonKey(name: 'font') final FontConfig? font}) =
      _$BrowserFingerprintConfigImpl;

  factory _BrowserFingerprintConfig.fromJson(Map<String, dynamic> json) =
      _$BrowserFingerprintConfigImpl.fromJson;

// 基础设置
  @override
  @JsonKey(name: 'basic')
  BasicConfig? get basic; // 基础配置
  @override
  @JsonKey(name: 'webrtc')
  WebRTCConfig? get webrtc; // WebRTC内外网指纹
  @override
  @JsonKey(name: 'position')
  PositionConfig? get position; // 地理位置信息
  @override
  @JsonKey(name: 'canvas')
  CanvasConfig? get canvas; // Canvas指纹
  @override
  @JsonKey(name: 'webgl')
  WebGLConfig? get webgl; // WebGL指纹
  @override
  @JsonKey(name: 'webaudio')
  double? get webaudio; // WebAudio噪声
  @override
  @JsonKey(name: 'clientrect')
  ClientRectConfig? get clientrect; // Client Rect字体位置
  @override
  @JsonKey(name: 'gpu')
  GPUConfig? get gpu; // WebGPU配置
  @override
  @JsonKey(name: 'screen')
  ScreenConfig? get screen; // 屏幕分辨率
  @override
  @JsonKey(name: 'mobile')
  MobileConfig? get mobile; // 触摸支持
  @override
  @JsonKey(name: 'hardware')
  HardwareConfig? get hardware; // 处理器和内存
  @override
  @JsonKey(name: 'clientHint')
  ClientHintConfig? get clientHint; // 客户端提示
  @override
  @JsonKey(name: 'languages')
  LanguagesConfig? get languages; // 语言设置
  @override
  @JsonKey(name: 'software')
  SoftwareConfig? get software; // 软件开关
  @override
  @JsonKey(name: 'portScan')
  PortScanConfig? get portScan; // 端口扫描开关
  @override
  @JsonKey(name: 'font')
  FontConfig? get font;

  /// Create a copy of BrowserFingerprintConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BrowserFingerprintConfigImplCopyWith<_$BrowserFingerprintConfigImpl>
      get copyWith => throw _privateConstructorUsedError;
}

BasicConfig _$BasicConfigFromJson(Map<String, dynamic> json) {
  return _BasicConfig.fromJson(json);
}

/// @nodoc
mixin _$BasicConfig {
  String? get name => throw _privateConstructorUsedError; // 窗口名称
  int? get groupId => throw _privateConstructorUsedError; // 分组ID
  String? get account => throw _privateConstructorUsedError; // 账号平台
  String? get accountUsername => throw _privateConstructorUsedError; // 账号用户名
  String? get accountPassword => throw _privateConstructorUsedError; // 账号密码
  String? get ua => throw _privateConstructorUsedError; // 用户代理
  String? get cookie => throw _privateConstructorUsedError;

  /// Serializes this BasicConfig to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of BasicConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BasicConfigCopyWith<BasicConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BasicConfigCopyWith<$Res> {
  factory $BasicConfigCopyWith(
          BasicConfig value, $Res Function(BasicConfig) then) =
      _$BasicConfigCopyWithImpl<$Res, BasicConfig>;
  @useResult
  $Res call(
      {String? name,
      int? groupId,
      String? account,
      String? accountUsername,
      String? accountPassword,
      String? ua,
      String? cookie});
}

/// @nodoc
class _$BasicConfigCopyWithImpl<$Res, $Val extends BasicConfig>
    implements $BasicConfigCopyWith<$Res> {
  _$BasicConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BasicConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = freezed,
    Object? groupId = freezed,
    Object? account = freezed,
    Object? accountUsername = freezed,
    Object? accountPassword = freezed,
    Object? ua = freezed,
    Object? cookie = freezed,
  }) {
    return _then(_value.copyWith(
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      groupId: freezed == groupId
          ? _value.groupId
          : groupId // ignore: cast_nullable_to_non_nullable
              as int?,
      account: freezed == account
          ? _value.account
          : account // ignore: cast_nullable_to_non_nullable
              as String?,
      accountUsername: freezed == accountUsername
          ? _value.accountUsername
          : accountUsername // ignore: cast_nullable_to_non_nullable
              as String?,
      accountPassword: freezed == accountPassword
          ? _value.accountPassword
          : accountPassword // ignore: cast_nullable_to_non_nullable
              as String?,
      ua: freezed == ua
          ? _value.ua
          : ua // ignore: cast_nullable_to_non_nullable
              as String?,
      cookie: freezed == cookie
          ? _value.cookie
          : cookie // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BasicConfigImplCopyWith<$Res>
    implements $BasicConfigCopyWith<$Res> {
  factory _$$BasicConfigImplCopyWith(
          _$BasicConfigImpl value, $Res Function(_$BasicConfigImpl) then) =
      __$$BasicConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? name,
      int? groupId,
      String? account,
      String? accountUsername,
      String? accountPassword,
      String? ua,
      String? cookie});
}

/// @nodoc
class __$$BasicConfigImplCopyWithImpl<$Res>
    extends _$BasicConfigCopyWithImpl<$Res, _$BasicConfigImpl>
    implements _$$BasicConfigImplCopyWith<$Res> {
  __$$BasicConfigImplCopyWithImpl(
      _$BasicConfigImpl _value, $Res Function(_$BasicConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of BasicConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = freezed,
    Object? groupId = freezed,
    Object? account = freezed,
    Object? accountUsername = freezed,
    Object? accountPassword = freezed,
    Object? ua = freezed,
    Object? cookie = freezed,
  }) {
    return _then(_$BasicConfigImpl(
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      groupId: freezed == groupId
          ? _value.groupId
          : groupId // ignore: cast_nullable_to_non_nullable
              as int?,
      account: freezed == account
          ? _value.account
          : account // ignore: cast_nullable_to_non_nullable
              as String?,
      accountUsername: freezed == accountUsername
          ? _value.accountUsername
          : accountUsername // ignore: cast_nullable_to_non_nullable
              as String?,
      accountPassword: freezed == accountPassword
          ? _value.accountPassword
          : accountPassword // ignore: cast_nullable_to_non_nullable
              as String?,
      ua: freezed == ua
          ? _value.ua
          : ua // ignore: cast_nullable_to_non_nullable
              as String?,
      cookie: freezed == cookie
          ? _value.cookie
          : cookie // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BasicConfigImpl implements _BasicConfig {
  const _$BasicConfigImpl(
      {this.name,
      this.groupId,
      this.account,
      this.accountUsername,
      this.accountPassword,
      this.ua,
      this.cookie});

  factory _$BasicConfigImpl.fromJson(Map<String, dynamic> json) =>
      _$$BasicConfigImplFromJson(json);

  @override
  final String? name;
// 窗口名称
  @override
  final int? groupId;
// 分组ID
  @override
  final String? account;
// 账号平台
  @override
  final String? accountUsername;
// 账号用户名
  @override
  final String? accountPassword;
// 账号密码
  @override
  final String? ua;
// 用户代理
  @override
  final String? cookie;

  @override
  String toString() {
    return 'BasicConfig(name: $name, groupId: $groupId, account: $account, accountUsername: $accountUsername, accountPassword: $accountPassword, ua: $ua, cookie: $cookie)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BasicConfigImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.groupId, groupId) || other.groupId == groupId) &&
            (identical(other.account, account) || other.account == account) &&
            (identical(other.accountUsername, accountUsername) ||
                other.accountUsername == accountUsername) &&
            (identical(other.accountPassword, accountPassword) ||
                other.accountPassword == accountPassword) &&
            (identical(other.ua, ua) || other.ua == ua) &&
            (identical(other.cookie, cookie) || other.cookie == cookie));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, name, groupId, account,
      accountUsername, accountPassword, ua, cookie);

  /// Create a copy of BasicConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BasicConfigImplCopyWith<_$BasicConfigImpl> get copyWith =>
      __$$BasicConfigImplCopyWithImpl<_$BasicConfigImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BasicConfigImplToJson(
      this,
    );
  }
}

abstract class _BasicConfig implements BasicConfig {
  const factory _BasicConfig(
      {final String? name,
      final int? groupId,
      final String? account,
      final String? accountUsername,
      final String? accountPassword,
      final String? ua,
      final String? cookie}) = _$BasicConfigImpl;

  factory _BasicConfig.fromJson(Map<String, dynamic> json) =
      _$BasicConfigImpl.fromJson;

  @override
  String? get name; // 窗口名称
  @override
  int? get groupId; // 分组ID
  @override
  String? get account; // 账号平台
  @override
  String? get accountUsername; // 账号用户名
  @override
  String? get accountPassword; // 账号密码
  @override
  String? get ua; // 用户代理
  @override
  String? get cookie;

  /// Create a copy of BasicConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BasicConfigImplCopyWith<_$BasicConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

WebRTCConfig _$WebRTCConfigFromJson(Map<String, dynamic> json) {
  return _WebRTCConfig.fromJson(json);
}

/// @nodoc
mixin _$WebRTCConfig {
  @JsonKey(name: 'public')
  String get public => throw _privateConstructorUsedError; // 外网IP地址
  @JsonKey(name: 'private')
  String get private => throw _privateConstructorUsedError;

  /// Serializes this WebRTCConfig to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of WebRTCConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WebRTCConfigCopyWith<WebRTCConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WebRTCConfigCopyWith<$Res> {
  factory $WebRTCConfigCopyWith(
          WebRTCConfig value, $Res Function(WebRTCConfig) then) =
      _$WebRTCConfigCopyWithImpl<$Res, WebRTCConfig>;
  @useResult
  $Res call(
      {@JsonKey(name: 'public') String public,
      @JsonKey(name: 'private') String private});
}

/// @nodoc
class _$WebRTCConfigCopyWithImpl<$Res, $Val extends WebRTCConfig>
    implements $WebRTCConfigCopyWith<$Res> {
  _$WebRTCConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WebRTCConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? public = null,
    Object? private = null,
  }) {
    return _then(_value.copyWith(
      public: null == public
          ? _value.public
          : public // ignore: cast_nullable_to_non_nullable
              as String,
      private: null == private
          ? _value.private
          : private // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WebRTCConfigImplCopyWith<$Res>
    implements $WebRTCConfigCopyWith<$Res> {
  factory _$$WebRTCConfigImplCopyWith(
          _$WebRTCConfigImpl value, $Res Function(_$WebRTCConfigImpl) then) =
      __$$WebRTCConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'public') String public,
      @JsonKey(name: 'private') String private});
}

/// @nodoc
class __$$WebRTCConfigImplCopyWithImpl<$Res>
    extends _$WebRTCConfigCopyWithImpl<$Res, _$WebRTCConfigImpl>
    implements _$$WebRTCConfigImplCopyWith<$Res> {
  __$$WebRTCConfigImplCopyWithImpl(
      _$WebRTCConfigImpl _value, $Res Function(_$WebRTCConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of WebRTCConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? public = null,
    Object? private = null,
  }) {
    return _then(_$WebRTCConfigImpl(
      public: null == public
          ? _value.public
          : public // ignore: cast_nullable_to_non_nullable
              as String,
      private: null == private
          ? _value.private
          : private // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$WebRTCConfigImpl implements _WebRTCConfig {
  const _$WebRTCConfigImpl(
      {@JsonKey(name: 'public') required this.public,
      @JsonKey(name: 'private') required this.private});

  factory _$WebRTCConfigImpl.fromJson(Map<String, dynamic> json) =>
      _$$WebRTCConfigImplFromJson(json);

  @override
  @JsonKey(name: 'public')
  final String public;
// 外网IP地址
  @override
  @JsonKey(name: 'private')
  final String private;

  @override
  String toString() {
    return 'WebRTCConfig(public: $public, private: $private)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WebRTCConfigImpl &&
            (identical(other.public, public) || other.public == public) &&
            (identical(other.private, private) || other.private == private));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, public, private);

  /// Create a copy of WebRTCConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WebRTCConfigImplCopyWith<_$WebRTCConfigImpl> get copyWith =>
      __$$WebRTCConfigImplCopyWithImpl<_$WebRTCConfigImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WebRTCConfigImplToJson(
      this,
    );
  }
}

abstract class _WebRTCConfig implements WebRTCConfig {
  const factory _WebRTCConfig(
          {@JsonKey(name: 'public') required final String public,
          @JsonKey(name: 'private') required final String private}) =
      _$WebRTCConfigImpl;

  factory _WebRTCConfig.fromJson(Map<String, dynamic> json) =
      _$WebRTCConfigImpl.fromJson;

  @override
  @JsonKey(name: 'public')
  String get public; // 外网IP地址
  @override
  @JsonKey(name: 'private')
  String get private;

  /// Create a copy of WebRTCConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WebRTCConfigImplCopyWith<_$WebRTCConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PositionConfig _$PositionConfigFromJson(Map<String, dynamic> json) {
  return _PositionConfig.fromJson(json);
}

/// @nodoc
mixin _$PositionConfig {
  @JsonKey(name: 'longitude')
  double get longitude => throw _privateConstructorUsedError; // 经度
  @JsonKey(name: 'latitude')
  double get latitude => throw _privateConstructorUsedError; // 纬度
  @JsonKey(name: 'altitude')
  double get altitude => throw _privateConstructorUsedError; // 海拔高度
  @JsonKey(name: 'accuracy')
  double get accuracy => throw _privateConstructorUsedError;

  /// Serializes this PositionConfig to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PositionConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PositionConfigCopyWith<PositionConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PositionConfigCopyWith<$Res> {
  factory $PositionConfigCopyWith(
          PositionConfig value, $Res Function(PositionConfig) then) =
      _$PositionConfigCopyWithImpl<$Res, PositionConfig>;
  @useResult
  $Res call(
      {@JsonKey(name: 'longitude') double longitude,
      @JsonKey(name: 'latitude') double latitude,
      @JsonKey(name: 'altitude') double altitude,
      @JsonKey(name: 'accuracy') double accuracy});
}

/// @nodoc
class _$PositionConfigCopyWithImpl<$Res, $Val extends PositionConfig>
    implements $PositionConfigCopyWith<$Res> {
  _$PositionConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PositionConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? longitude = null,
    Object? latitude = null,
    Object? altitude = null,
    Object? accuracy = null,
  }) {
    return _then(_value.copyWith(
      longitude: null == longitude
          ? _value.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as double,
      latitude: null == latitude
          ? _value.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as double,
      altitude: null == altitude
          ? _value.altitude
          : altitude // ignore: cast_nullable_to_non_nullable
              as double,
      accuracy: null == accuracy
          ? _value.accuracy
          : accuracy // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PositionConfigImplCopyWith<$Res>
    implements $PositionConfigCopyWith<$Res> {
  factory _$$PositionConfigImplCopyWith(_$PositionConfigImpl value,
          $Res Function(_$PositionConfigImpl) then) =
      __$$PositionConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'longitude') double longitude,
      @JsonKey(name: 'latitude') double latitude,
      @JsonKey(name: 'altitude') double altitude,
      @JsonKey(name: 'accuracy') double accuracy});
}

/// @nodoc
class __$$PositionConfigImplCopyWithImpl<$Res>
    extends _$PositionConfigCopyWithImpl<$Res, _$PositionConfigImpl>
    implements _$$PositionConfigImplCopyWith<$Res> {
  __$$PositionConfigImplCopyWithImpl(
      _$PositionConfigImpl _value, $Res Function(_$PositionConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of PositionConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? longitude = null,
    Object? latitude = null,
    Object? altitude = null,
    Object? accuracy = null,
  }) {
    return _then(_$PositionConfigImpl(
      longitude: null == longitude
          ? _value.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as double,
      latitude: null == latitude
          ? _value.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as double,
      altitude: null == altitude
          ? _value.altitude
          : altitude // ignore: cast_nullable_to_non_nullable
              as double,
      accuracy: null == accuracy
          ? _value.accuracy
          : accuracy // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PositionConfigImpl implements _PositionConfig {
  const _$PositionConfigImpl(
      {@JsonKey(name: 'longitude') required this.longitude,
      @JsonKey(name: 'latitude') required this.latitude,
      @JsonKey(name: 'altitude') required this.altitude,
      @JsonKey(name: 'accuracy') required this.accuracy});

  factory _$PositionConfigImpl.fromJson(Map<String, dynamic> json) =>
      _$$PositionConfigImplFromJson(json);

  @override
  @JsonKey(name: 'longitude')
  final double longitude;
// 经度
  @override
  @JsonKey(name: 'latitude')
  final double latitude;
// 纬度
  @override
  @JsonKey(name: 'altitude')
  final double altitude;
// 海拔高度
  @override
  @JsonKey(name: 'accuracy')
  final double accuracy;

  @override
  String toString() {
    return 'PositionConfig(longitude: $longitude, latitude: $latitude, altitude: $altitude, accuracy: $accuracy)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PositionConfigImpl &&
            (identical(other.longitude, longitude) ||
                other.longitude == longitude) &&
            (identical(other.latitude, latitude) ||
                other.latitude == latitude) &&
            (identical(other.altitude, altitude) ||
                other.altitude == altitude) &&
            (identical(other.accuracy, accuracy) ||
                other.accuracy == accuracy));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, longitude, latitude, altitude, accuracy);

  /// Create a copy of PositionConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PositionConfigImplCopyWith<_$PositionConfigImpl> get copyWith =>
      __$$PositionConfigImplCopyWithImpl<_$PositionConfigImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PositionConfigImplToJson(
      this,
    );
  }
}

abstract class _PositionConfig implements PositionConfig {
  const factory _PositionConfig(
          {@JsonKey(name: 'longitude') required final double longitude,
          @JsonKey(name: 'latitude') required final double latitude,
          @JsonKey(name: 'altitude') required final double altitude,
          @JsonKey(name: 'accuracy') required final double accuracy}) =
      _$PositionConfigImpl;

  factory _PositionConfig.fromJson(Map<String, dynamic> json) =
      _$PositionConfigImpl.fromJson;

  @override
  @JsonKey(name: 'longitude')
  double get longitude; // 经度
  @override
  @JsonKey(name: 'latitude')
  double get latitude; // 纬度
  @override
  @JsonKey(name: 'altitude')
  double get altitude; // 海拔高度
  @override
  @JsonKey(name: 'accuracy')
  double get accuracy;

  /// Create a copy of PositionConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PositionConfigImplCopyWith<_$PositionConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CanvasConfig _$CanvasConfigFromJson(Map<String, dynamic> json) {
  return _CanvasConfig.fromJson(json);
}

/// @nodoc
mixin _$CanvasConfig {
  @JsonKey(name: 'noise')
  double get noise => throw _privateConstructorUsedError;

  /// Serializes this CanvasConfig to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CanvasConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CanvasConfigCopyWith<CanvasConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CanvasConfigCopyWith<$Res> {
  factory $CanvasConfigCopyWith(
          CanvasConfig value, $Res Function(CanvasConfig) then) =
      _$CanvasConfigCopyWithImpl<$Res, CanvasConfig>;
  @useResult
  $Res call({@JsonKey(name: 'noise') double noise});
}

/// @nodoc
class _$CanvasConfigCopyWithImpl<$Res, $Val extends CanvasConfig>
    implements $CanvasConfigCopyWith<$Res> {
  _$CanvasConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CanvasConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? noise = null,
  }) {
    return _then(_value.copyWith(
      noise: null == noise
          ? _value.noise
          : noise // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CanvasConfigImplCopyWith<$Res>
    implements $CanvasConfigCopyWith<$Res> {
  factory _$$CanvasConfigImplCopyWith(
          _$CanvasConfigImpl value, $Res Function(_$CanvasConfigImpl) then) =
      __$$CanvasConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({@JsonKey(name: 'noise') double noise});
}

/// @nodoc
class __$$CanvasConfigImplCopyWithImpl<$Res>
    extends _$CanvasConfigCopyWithImpl<$Res, _$CanvasConfigImpl>
    implements _$$CanvasConfigImplCopyWith<$Res> {
  __$$CanvasConfigImplCopyWithImpl(
      _$CanvasConfigImpl _value, $Res Function(_$CanvasConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of CanvasConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? noise = null,
  }) {
    return _then(_$CanvasConfigImpl(
      noise: null == noise
          ? _value.noise
          : noise // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CanvasConfigImpl implements _CanvasConfig {
  const _$CanvasConfigImpl({@JsonKey(name: 'noise') required this.noise});

  factory _$CanvasConfigImpl.fromJson(Map<String, dynamic> json) =>
      _$$CanvasConfigImplFromJson(json);

  @override
  @JsonKey(name: 'noise')
  final double noise;

  @override
  String toString() {
    return 'CanvasConfig(noise: $noise)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CanvasConfigImpl &&
            (identical(other.noise, noise) || other.noise == noise));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, noise);

  /// Create a copy of CanvasConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CanvasConfigImplCopyWith<_$CanvasConfigImpl> get copyWith =>
      __$$CanvasConfigImplCopyWithImpl<_$CanvasConfigImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CanvasConfigImplToJson(
      this,
    );
  }
}

abstract class _CanvasConfig implements CanvasConfig {
  const factory _CanvasConfig(
          {@JsonKey(name: 'noise') required final double noise}) =
      _$CanvasConfigImpl;

  factory _CanvasConfig.fromJson(Map<String, dynamic> json) =
      _$CanvasConfigImpl.fromJson;

  @override
  @JsonKey(name: 'noise')
  double get noise;

  /// Create a copy of CanvasConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CanvasConfigImplCopyWith<_$CanvasConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

WebGLConfig _$WebGLConfigFromJson(Map<String, dynamic> json) {
  return _WebGLConfig.fromJson(json);
}

/// @nodoc
mixin _$WebGLConfig {
  @JsonKey(name: 'vendor')
  String get vendor => throw _privateConstructorUsedError; // 供应商信息
  @JsonKey(name: 'renderer')
  String get renderer => throw _privateConstructorUsedError;

  /// Serializes this WebGLConfig to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of WebGLConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WebGLConfigCopyWith<WebGLConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WebGLConfigCopyWith<$Res> {
  factory $WebGLConfigCopyWith(
          WebGLConfig value, $Res Function(WebGLConfig) then) =
      _$WebGLConfigCopyWithImpl<$Res, WebGLConfig>;
  @useResult
  $Res call(
      {@JsonKey(name: 'vendor') String vendor,
      @JsonKey(name: 'renderer') String renderer});
}

/// @nodoc
class _$WebGLConfigCopyWithImpl<$Res, $Val extends WebGLConfig>
    implements $WebGLConfigCopyWith<$Res> {
  _$WebGLConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WebGLConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? vendor = null,
    Object? renderer = null,
  }) {
    return _then(_value.copyWith(
      vendor: null == vendor
          ? _value.vendor
          : vendor // ignore: cast_nullable_to_non_nullable
              as String,
      renderer: null == renderer
          ? _value.renderer
          : renderer // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WebGLConfigImplCopyWith<$Res>
    implements $WebGLConfigCopyWith<$Res> {
  factory _$$WebGLConfigImplCopyWith(
          _$WebGLConfigImpl value, $Res Function(_$WebGLConfigImpl) then) =
      __$$WebGLConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'vendor') String vendor,
      @JsonKey(name: 'renderer') String renderer});
}

/// @nodoc
class __$$WebGLConfigImplCopyWithImpl<$Res>
    extends _$WebGLConfigCopyWithImpl<$Res, _$WebGLConfigImpl>
    implements _$$WebGLConfigImplCopyWith<$Res> {
  __$$WebGLConfigImplCopyWithImpl(
      _$WebGLConfigImpl _value, $Res Function(_$WebGLConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of WebGLConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? vendor = null,
    Object? renderer = null,
  }) {
    return _then(_$WebGLConfigImpl(
      vendor: null == vendor
          ? _value.vendor
          : vendor // ignore: cast_nullable_to_non_nullable
              as String,
      renderer: null == renderer
          ? _value.renderer
          : renderer // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$WebGLConfigImpl implements _WebGLConfig {
  const _$WebGLConfigImpl(
      {@JsonKey(name: 'vendor') required this.vendor,
      @JsonKey(name: 'renderer') required this.renderer});

  factory _$WebGLConfigImpl.fromJson(Map<String, dynamic> json) =>
      _$$WebGLConfigImplFromJson(json);

  @override
  @JsonKey(name: 'vendor')
  final String vendor;
// 供应商信息
  @override
  @JsonKey(name: 'renderer')
  final String renderer;

  @override
  String toString() {
    return 'WebGLConfig(vendor: $vendor, renderer: $renderer)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WebGLConfigImpl &&
            (identical(other.vendor, vendor) || other.vendor == vendor) &&
            (identical(other.renderer, renderer) ||
                other.renderer == renderer));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, vendor, renderer);

  /// Create a copy of WebGLConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WebGLConfigImplCopyWith<_$WebGLConfigImpl> get copyWith =>
      __$$WebGLConfigImplCopyWithImpl<_$WebGLConfigImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WebGLConfigImplToJson(
      this,
    );
  }
}

abstract class _WebGLConfig implements WebGLConfig {
  const factory _WebGLConfig(
          {@JsonKey(name: 'vendor') required final String vendor,
          @JsonKey(name: 'renderer') required final String renderer}) =
      _$WebGLConfigImpl;

  factory _WebGLConfig.fromJson(Map<String, dynamic> json) =
      _$WebGLConfigImpl.fromJson;

  @override
  @JsonKey(name: 'vendor')
  String get vendor; // 供应商信息
  @override
  @JsonKey(name: 'renderer')
  String get renderer;

  /// Create a copy of WebGLConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WebGLConfigImplCopyWith<_$WebGLConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ClientRectConfig _$ClientRectConfigFromJson(Map<String, dynamic> json) {
  return _ClientRectConfig.fromJson(json);
}

/// @nodoc
mixin _$ClientRectConfig {
  @JsonKey(name: 'x')
  double get x => throw _privateConstructorUsedError; // X轴偏移
  @JsonKey(name: 'y')
  double get y => throw _privateConstructorUsedError;

  /// Serializes this ClientRectConfig to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ClientRectConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ClientRectConfigCopyWith<ClientRectConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ClientRectConfigCopyWith<$Res> {
  factory $ClientRectConfigCopyWith(
          ClientRectConfig value, $Res Function(ClientRectConfig) then) =
      _$ClientRectConfigCopyWithImpl<$Res, ClientRectConfig>;
  @useResult
  $Res call({@JsonKey(name: 'x') double x, @JsonKey(name: 'y') double y});
}

/// @nodoc
class _$ClientRectConfigCopyWithImpl<$Res, $Val extends ClientRectConfig>
    implements $ClientRectConfigCopyWith<$Res> {
  _$ClientRectConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ClientRectConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? x = null,
    Object? y = null,
  }) {
    return _then(_value.copyWith(
      x: null == x
          ? _value.x
          : x // ignore: cast_nullable_to_non_nullable
              as double,
      y: null == y
          ? _value.y
          : y // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ClientRectConfigImplCopyWith<$Res>
    implements $ClientRectConfigCopyWith<$Res> {
  factory _$$ClientRectConfigImplCopyWith(_$ClientRectConfigImpl value,
          $Res Function(_$ClientRectConfigImpl) then) =
      __$$ClientRectConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({@JsonKey(name: 'x') double x, @JsonKey(name: 'y') double y});
}

/// @nodoc
class __$$ClientRectConfigImplCopyWithImpl<$Res>
    extends _$ClientRectConfigCopyWithImpl<$Res, _$ClientRectConfigImpl>
    implements _$$ClientRectConfigImplCopyWith<$Res> {
  __$$ClientRectConfigImplCopyWithImpl(_$ClientRectConfigImpl _value,
      $Res Function(_$ClientRectConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of ClientRectConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? x = null,
    Object? y = null,
  }) {
    return _then(_$ClientRectConfigImpl(
      x: null == x
          ? _value.x
          : x // ignore: cast_nullable_to_non_nullable
              as double,
      y: null == y
          ? _value.y
          : y // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ClientRectConfigImpl implements _ClientRectConfig {
  const _$ClientRectConfigImpl(
      {@JsonKey(name: 'x') required this.x,
      @JsonKey(name: 'y') required this.y});

  factory _$ClientRectConfigImpl.fromJson(Map<String, dynamic> json) =>
      _$$ClientRectConfigImplFromJson(json);

  @override
  @JsonKey(name: 'x')
  final double x;
// X轴偏移
  @override
  @JsonKey(name: 'y')
  final double y;

  @override
  String toString() {
    return 'ClientRectConfig(x: $x, y: $y)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ClientRectConfigImpl &&
            (identical(other.x, x) || other.x == x) &&
            (identical(other.y, y) || other.y == y));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, x, y);

  /// Create a copy of ClientRectConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ClientRectConfigImplCopyWith<_$ClientRectConfigImpl> get copyWith =>
      __$$ClientRectConfigImplCopyWithImpl<_$ClientRectConfigImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ClientRectConfigImplToJson(
      this,
    );
  }
}

abstract class _ClientRectConfig implements ClientRectConfig {
  const factory _ClientRectConfig(
      {@JsonKey(name: 'x') required final double x,
      @JsonKey(name: 'y') required final double y}) = _$ClientRectConfigImpl;

  factory _ClientRectConfig.fromJson(Map<String, dynamic> json) =
      _$ClientRectConfigImpl.fromJson;

  @override
  @JsonKey(name: 'x')
  double get x; // X轴偏移
  @override
  @JsonKey(name: 'y')
  double get y;

  /// Create a copy of ClientRectConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ClientRectConfigImplCopyWith<_$ClientRectConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

GPUConfig _$GPUConfigFromJson(Map<String, dynamic> json) {
  return _GPUConfig.fromJson(json);
}

/// @nodoc
mixin _$GPUConfig {
  @JsonKey(name: 'device')
  String get device => throw _privateConstructorUsedError; // 设备名称
  @JsonKey(name: 'description')
  String get description => throw _privateConstructorUsedError;

  /// Serializes this GPUConfig to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of GPUConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $GPUConfigCopyWith<GPUConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GPUConfigCopyWith<$Res> {
  factory $GPUConfigCopyWith(GPUConfig value, $Res Function(GPUConfig) then) =
      _$GPUConfigCopyWithImpl<$Res, GPUConfig>;
  @useResult
  $Res call(
      {@JsonKey(name: 'device') String device,
      @JsonKey(name: 'description') String description});
}

/// @nodoc
class _$GPUConfigCopyWithImpl<$Res, $Val extends GPUConfig>
    implements $GPUConfigCopyWith<$Res> {
  _$GPUConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of GPUConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? device = null,
    Object? description = null,
  }) {
    return _then(_value.copyWith(
      device: null == device
          ? _value.device
          : device // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GPUConfigImplCopyWith<$Res>
    implements $GPUConfigCopyWith<$Res> {
  factory _$$GPUConfigImplCopyWith(
          _$GPUConfigImpl value, $Res Function(_$GPUConfigImpl) then) =
      __$$GPUConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'device') String device,
      @JsonKey(name: 'description') String description});
}

/// @nodoc
class __$$GPUConfigImplCopyWithImpl<$Res>
    extends _$GPUConfigCopyWithImpl<$Res, _$GPUConfigImpl>
    implements _$$GPUConfigImplCopyWith<$Res> {
  __$$GPUConfigImplCopyWithImpl(
      _$GPUConfigImpl _value, $Res Function(_$GPUConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of GPUConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? device = null,
    Object? description = null,
  }) {
    return _then(_$GPUConfigImpl(
      device: null == device
          ? _value.device
          : device // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$GPUConfigImpl implements _GPUConfig {
  const _$GPUConfigImpl(
      {@JsonKey(name: 'device') required this.device,
      @JsonKey(name: 'description') required this.description});

  factory _$GPUConfigImpl.fromJson(Map<String, dynamic> json) =>
      _$$GPUConfigImplFromJson(json);

  @override
  @JsonKey(name: 'device')
  final String device;
// 设备名称
  @override
  @JsonKey(name: 'description')
  final String description;

  @override
  String toString() {
    return 'GPUConfig(device: $device, description: $description)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GPUConfigImpl &&
            (identical(other.device, device) || other.device == device) &&
            (identical(other.description, description) ||
                other.description == description));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, device, description);

  /// Create a copy of GPUConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$GPUConfigImplCopyWith<_$GPUConfigImpl> get copyWith =>
      __$$GPUConfigImplCopyWithImpl<_$GPUConfigImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GPUConfigImplToJson(
      this,
    );
  }
}

abstract class _GPUConfig implements GPUConfig {
  const factory _GPUConfig(
          {@JsonKey(name: 'device') required final String device,
          @JsonKey(name: 'description') required final String description}) =
      _$GPUConfigImpl;

  factory _GPUConfig.fromJson(Map<String, dynamic> json) =
      _$GPUConfigImpl.fromJson;

  @override
  @JsonKey(name: 'device')
  String get device; // 设备名称
  @override
  @JsonKey(name: 'description')
  String get description;

  /// Create a copy of GPUConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$GPUConfigImplCopyWith<_$GPUConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ScreenConfig _$ScreenConfigFromJson(Map<String, dynamic> json) {
  return _ScreenConfig.fromJson(json);
}

/// @nodoc
mixin _$ScreenConfig {
  @JsonKey(name: 'width')
  double get width => throw _privateConstructorUsedError; // 屏幕宽度
  @JsonKey(name: 'height')
  double get height => throw _privateConstructorUsedError; // 屏幕高度
  @JsonKey(name: 'colorDepth')
  double get colorDepth => throw _privateConstructorUsedError; // 色彩深度
  @JsonKey(name: 'availWidth')
  double get availWidth => throw _privateConstructorUsedError; // 可用宽度
  @JsonKey(name: 'availHeight')
  double get availHeight => throw _privateConstructorUsedError;

  /// Serializes this ScreenConfig to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ScreenConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ScreenConfigCopyWith<ScreenConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ScreenConfigCopyWith<$Res> {
  factory $ScreenConfigCopyWith(
          ScreenConfig value, $Res Function(ScreenConfig) then) =
      _$ScreenConfigCopyWithImpl<$Res, ScreenConfig>;
  @useResult
  $Res call(
      {@JsonKey(name: 'width') double width,
      @JsonKey(name: 'height') double height,
      @JsonKey(name: 'colorDepth') double colorDepth,
      @JsonKey(name: 'availWidth') double availWidth,
      @JsonKey(name: 'availHeight') double availHeight});
}

/// @nodoc
class _$ScreenConfigCopyWithImpl<$Res, $Val extends ScreenConfig>
    implements $ScreenConfigCopyWith<$Res> {
  _$ScreenConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ScreenConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? width = null,
    Object? height = null,
    Object? colorDepth = null,
    Object? availWidth = null,
    Object? availHeight = null,
  }) {
    return _then(_value.copyWith(
      width: null == width
          ? _value.width
          : width // ignore: cast_nullable_to_non_nullable
              as double,
      height: null == height
          ? _value.height
          : height // ignore: cast_nullable_to_non_nullable
              as double,
      colorDepth: null == colorDepth
          ? _value.colorDepth
          : colorDepth // ignore: cast_nullable_to_non_nullable
              as double,
      availWidth: null == availWidth
          ? _value.availWidth
          : availWidth // ignore: cast_nullable_to_non_nullable
              as double,
      availHeight: null == availHeight
          ? _value.availHeight
          : availHeight // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ScreenConfigImplCopyWith<$Res>
    implements $ScreenConfigCopyWith<$Res> {
  factory _$$ScreenConfigImplCopyWith(
          _$ScreenConfigImpl value, $Res Function(_$ScreenConfigImpl) then) =
      __$$ScreenConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'width') double width,
      @JsonKey(name: 'height') double height,
      @JsonKey(name: 'colorDepth') double colorDepth,
      @JsonKey(name: 'availWidth') double availWidth,
      @JsonKey(name: 'availHeight') double availHeight});
}

/// @nodoc
class __$$ScreenConfigImplCopyWithImpl<$Res>
    extends _$ScreenConfigCopyWithImpl<$Res, _$ScreenConfigImpl>
    implements _$$ScreenConfigImplCopyWith<$Res> {
  __$$ScreenConfigImplCopyWithImpl(
      _$ScreenConfigImpl _value, $Res Function(_$ScreenConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of ScreenConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? width = null,
    Object? height = null,
    Object? colorDepth = null,
    Object? availWidth = null,
    Object? availHeight = null,
  }) {
    return _then(_$ScreenConfigImpl(
      width: null == width
          ? _value.width
          : width // ignore: cast_nullable_to_non_nullable
              as double,
      height: null == height
          ? _value.height
          : height // ignore: cast_nullable_to_non_nullable
              as double,
      colorDepth: null == colorDepth
          ? _value.colorDepth
          : colorDepth // ignore: cast_nullable_to_non_nullable
              as double,
      availWidth: null == availWidth
          ? _value.availWidth
          : availWidth // ignore: cast_nullable_to_non_nullable
              as double,
      availHeight: null == availHeight
          ? _value.availHeight
          : availHeight // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ScreenConfigImpl implements _ScreenConfig {
  const _$ScreenConfigImpl(
      {@JsonKey(name: 'width') required this.width,
      @JsonKey(name: 'height') required this.height,
      @JsonKey(name: 'colorDepth') required this.colorDepth,
      @JsonKey(name: 'availWidth') required this.availWidth,
      @JsonKey(name: 'availHeight') required this.availHeight});

  factory _$ScreenConfigImpl.fromJson(Map<String, dynamic> json) =>
      _$$ScreenConfigImplFromJson(json);

  @override
  @JsonKey(name: 'width')
  final double width;
// 屏幕宽度
  @override
  @JsonKey(name: 'height')
  final double height;
// 屏幕高度
  @override
  @JsonKey(name: 'colorDepth')
  final double colorDepth;
// 色彩深度
  @override
  @JsonKey(name: 'availWidth')
  final double availWidth;
// 可用宽度
  @override
  @JsonKey(name: 'availHeight')
  final double availHeight;

  @override
  String toString() {
    return 'ScreenConfig(width: $width, height: $height, colorDepth: $colorDepth, availWidth: $availWidth, availHeight: $availHeight)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ScreenConfigImpl &&
            (identical(other.width, width) || other.width == width) &&
            (identical(other.height, height) || other.height == height) &&
            (identical(other.colorDepth, colorDepth) ||
                other.colorDepth == colorDepth) &&
            (identical(other.availWidth, availWidth) ||
                other.availWidth == availWidth) &&
            (identical(other.availHeight, availHeight) ||
                other.availHeight == availHeight));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, width, height, colorDepth, availWidth, availHeight);

  /// Create a copy of ScreenConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ScreenConfigImplCopyWith<_$ScreenConfigImpl> get copyWith =>
      __$$ScreenConfigImplCopyWithImpl<_$ScreenConfigImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ScreenConfigImplToJson(
      this,
    );
  }
}

abstract class _ScreenConfig implements ScreenConfig {
  const factory _ScreenConfig(
          {@JsonKey(name: 'width') required final double width,
          @JsonKey(name: 'height') required final double height,
          @JsonKey(name: 'colorDepth') required final double colorDepth,
          @JsonKey(name: 'availWidth') required final double availWidth,
          @JsonKey(name: 'availHeight') required final double availHeight}) =
      _$ScreenConfigImpl;

  factory _ScreenConfig.fromJson(Map<String, dynamic> json) =
      _$ScreenConfigImpl.fromJson;

  @override
  @JsonKey(name: 'width')
  double get width; // 屏幕宽度
  @override
  @JsonKey(name: 'height')
  double get height; // 屏幕高度
  @override
  @JsonKey(name: 'colorDepth')
  double get colorDepth; // 色彩深度
  @override
  @JsonKey(name: 'availWidth')
  double get availWidth; // 可用宽度
  @override
  @JsonKey(name: 'availHeight')
  double get availHeight;

  /// Create a copy of ScreenConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ScreenConfigImplCopyWith<_$ScreenConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

MobileConfig _$MobileConfigFromJson(Map<String, dynamic> json) {
  return _MobileConfig.fromJson(json);
}

/// @nodoc
mixin _$MobileConfig {
  @JsonKey(name: 'touchsupport')
  double get touchsupport => throw _privateConstructorUsedError;

  /// Serializes this MobileConfig to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of MobileConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MobileConfigCopyWith<MobileConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MobileConfigCopyWith<$Res> {
  factory $MobileConfigCopyWith(
          MobileConfig value, $Res Function(MobileConfig) then) =
      _$MobileConfigCopyWithImpl<$Res, MobileConfig>;
  @useResult
  $Res call({@JsonKey(name: 'touchsupport') double touchsupport});
}

/// @nodoc
class _$MobileConfigCopyWithImpl<$Res, $Val extends MobileConfig>
    implements $MobileConfigCopyWith<$Res> {
  _$MobileConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MobileConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? touchsupport = null,
  }) {
    return _then(_value.copyWith(
      touchsupport: null == touchsupport
          ? _value.touchsupport
          : touchsupport // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MobileConfigImplCopyWith<$Res>
    implements $MobileConfigCopyWith<$Res> {
  factory _$$MobileConfigImplCopyWith(
          _$MobileConfigImpl value, $Res Function(_$MobileConfigImpl) then) =
      __$$MobileConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({@JsonKey(name: 'touchsupport') double touchsupport});
}

/// @nodoc
class __$$MobileConfigImplCopyWithImpl<$Res>
    extends _$MobileConfigCopyWithImpl<$Res, _$MobileConfigImpl>
    implements _$$MobileConfigImplCopyWith<$Res> {
  __$$MobileConfigImplCopyWithImpl(
      _$MobileConfigImpl _value, $Res Function(_$MobileConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of MobileConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? touchsupport = null,
  }) {
    return _then(_$MobileConfigImpl(
      touchsupport: null == touchsupport
          ? _value.touchsupport
          : touchsupport // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MobileConfigImpl implements _MobileConfig {
  const _$MobileConfigImpl(
      {@JsonKey(name: 'touchsupport') required this.touchsupport});

  factory _$MobileConfigImpl.fromJson(Map<String, dynamic> json) =>
      _$$MobileConfigImplFromJson(json);

  @override
  @JsonKey(name: 'touchsupport')
  final double touchsupport;

  @override
  String toString() {
    return 'MobileConfig(touchsupport: $touchsupport)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MobileConfigImpl &&
            (identical(other.touchsupport, touchsupport) ||
                other.touchsupport == touchsupport));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, touchsupport);

  /// Create a copy of MobileConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MobileConfigImplCopyWith<_$MobileConfigImpl> get copyWith =>
      __$$MobileConfigImplCopyWithImpl<_$MobileConfigImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MobileConfigImplToJson(
      this,
    );
  }
}

abstract class _MobileConfig implements MobileConfig {
  const factory _MobileConfig(
          {@JsonKey(name: 'touchsupport') required final double touchsupport}) =
      _$MobileConfigImpl;

  factory _MobileConfig.fromJson(Map<String, dynamic> json) =
      _$MobileConfigImpl.fromJson;

  @override
  @JsonKey(name: 'touchsupport')
  double get touchsupport;

  /// Create a copy of MobileConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MobileConfigImplCopyWith<_$MobileConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

HardwareConfig _$HardwareConfigFromJson(Map<String, dynamic> json) {
  return _HardwareConfig.fromJson(json);
}

/// @nodoc
mixin _$HardwareConfig {
  @JsonKey(name: 'concurrency')
  double get concurrency => throw _privateConstructorUsedError; // 处理器核心数
  @JsonKey(name: 'memory')
  double get memory => throw _privateConstructorUsedError;

  /// Serializes this HardwareConfig to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of HardwareConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $HardwareConfigCopyWith<HardwareConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HardwareConfigCopyWith<$Res> {
  factory $HardwareConfigCopyWith(
          HardwareConfig value, $Res Function(HardwareConfig) then) =
      _$HardwareConfigCopyWithImpl<$Res, HardwareConfig>;
  @useResult
  $Res call(
      {@JsonKey(name: 'concurrency') double concurrency,
      @JsonKey(name: 'memory') double memory});
}

/// @nodoc
class _$HardwareConfigCopyWithImpl<$Res, $Val extends HardwareConfig>
    implements $HardwareConfigCopyWith<$Res> {
  _$HardwareConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of HardwareConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? concurrency = null,
    Object? memory = null,
  }) {
    return _then(_value.copyWith(
      concurrency: null == concurrency
          ? _value.concurrency
          : concurrency // ignore: cast_nullable_to_non_nullable
              as double,
      memory: null == memory
          ? _value.memory
          : memory // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$HardwareConfigImplCopyWith<$Res>
    implements $HardwareConfigCopyWith<$Res> {
  factory _$$HardwareConfigImplCopyWith(_$HardwareConfigImpl value,
          $Res Function(_$HardwareConfigImpl) then) =
      __$$HardwareConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'concurrency') double concurrency,
      @JsonKey(name: 'memory') double memory});
}

/// @nodoc
class __$$HardwareConfigImplCopyWithImpl<$Res>
    extends _$HardwareConfigCopyWithImpl<$Res, _$HardwareConfigImpl>
    implements _$$HardwareConfigImplCopyWith<$Res> {
  __$$HardwareConfigImplCopyWithImpl(
      _$HardwareConfigImpl _value, $Res Function(_$HardwareConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of HardwareConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? concurrency = null,
    Object? memory = null,
  }) {
    return _then(_$HardwareConfigImpl(
      concurrency: null == concurrency
          ? _value.concurrency
          : concurrency // ignore: cast_nullable_to_non_nullable
              as double,
      memory: null == memory
          ? _value.memory
          : memory // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$HardwareConfigImpl implements _HardwareConfig {
  const _$HardwareConfigImpl(
      {@JsonKey(name: 'concurrency') required this.concurrency,
      @JsonKey(name: 'memory') required this.memory});

  factory _$HardwareConfigImpl.fromJson(Map<String, dynamic> json) =>
      _$$HardwareConfigImplFromJson(json);

  @override
  @JsonKey(name: 'concurrency')
  final double concurrency;
// 处理器核心数
  @override
  @JsonKey(name: 'memory')
  final double memory;

  @override
  String toString() {
    return 'HardwareConfig(concurrency: $concurrency, memory: $memory)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HardwareConfigImpl &&
            (identical(other.concurrency, concurrency) ||
                other.concurrency == concurrency) &&
            (identical(other.memory, memory) || other.memory == memory));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, concurrency, memory);

  /// Create a copy of HardwareConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$HardwareConfigImplCopyWith<_$HardwareConfigImpl> get copyWith =>
      __$$HardwareConfigImplCopyWithImpl<_$HardwareConfigImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$HardwareConfigImplToJson(
      this,
    );
  }
}

abstract class _HardwareConfig implements HardwareConfig {
  const factory _HardwareConfig(
          {@JsonKey(name: 'concurrency') required final double concurrency,
          @JsonKey(name: 'memory') required final double memory}) =
      _$HardwareConfigImpl;

  factory _HardwareConfig.fromJson(Map<String, dynamic> json) =
      _$HardwareConfigImpl.fromJson;

  @override
  @JsonKey(name: 'concurrency')
  double get concurrency; // 处理器核心数
  @override
  @JsonKey(name: 'memory')
  double get memory;

  /// Create a copy of HardwareConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$HardwareConfigImplCopyWith<_$HardwareConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ClientHintConfig _$ClientHintConfigFromJson(Map<String, dynamic> json) {
  return _ClientHintConfig.fromJson(json);
}

/// @nodoc
mixin _$ClientHintConfig {
  @JsonKey(name: 'platform')
  String get platform => throw _privateConstructorUsedError; // 平台
  @JsonKey(name: 'navigator.platform')
  String get navigatorPlatform =>
      throw _privateConstructorUsedError; // Navigator平台
  @JsonKey(name: 'platform_version')
  String get platformVersion => throw _privateConstructorUsedError; // 平台版本
  @JsonKey(name: 'ua_full_version')
  String get uaFullVersion => throw _privateConstructorUsedError; // UA完整版本
  @JsonKey(name: 'mobile')
  String get mobile => throw _privateConstructorUsedError; // 是否移动设备
  @JsonKey(name: 'architecture')
  String get architecture => throw _privateConstructorUsedError; // 架构
  @JsonKey(name: 'bitness')
  String get bitness => throw _privateConstructorUsedError;

  /// Serializes this ClientHintConfig to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ClientHintConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ClientHintConfigCopyWith<ClientHintConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ClientHintConfigCopyWith<$Res> {
  factory $ClientHintConfigCopyWith(
          ClientHintConfig value, $Res Function(ClientHintConfig) then) =
      _$ClientHintConfigCopyWithImpl<$Res, ClientHintConfig>;
  @useResult
  $Res call(
      {@JsonKey(name: 'platform') String platform,
      @JsonKey(name: 'navigator.platform') String navigatorPlatform,
      @JsonKey(name: 'platform_version') String platformVersion,
      @JsonKey(name: 'ua_full_version') String uaFullVersion,
      @JsonKey(name: 'mobile') String mobile,
      @JsonKey(name: 'architecture') String architecture,
      @JsonKey(name: 'bitness') String bitness});
}

/// @nodoc
class _$ClientHintConfigCopyWithImpl<$Res, $Val extends ClientHintConfig>
    implements $ClientHintConfigCopyWith<$Res> {
  _$ClientHintConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ClientHintConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? platform = null,
    Object? navigatorPlatform = null,
    Object? platformVersion = null,
    Object? uaFullVersion = null,
    Object? mobile = null,
    Object? architecture = null,
    Object? bitness = null,
  }) {
    return _then(_value.copyWith(
      platform: null == platform
          ? _value.platform
          : platform // ignore: cast_nullable_to_non_nullable
              as String,
      navigatorPlatform: null == navigatorPlatform
          ? _value.navigatorPlatform
          : navigatorPlatform // ignore: cast_nullable_to_non_nullable
              as String,
      platformVersion: null == platformVersion
          ? _value.platformVersion
          : platformVersion // ignore: cast_nullable_to_non_nullable
              as String,
      uaFullVersion: null == uaFullVersion
          ? _value.uaFullVersion
          : uaFullVersion // ignore: cast_nullable_to_non_nullable
              as String,
      mobile: null == mobile
          ? _value.mobile
          : mobile // ignore: cast_nullable_to_non_nullable
              as String,
      architecture: null == architecture
          ? _value.architecture
          : architecture // ignore: cast_nullable_to_non_nullable
              as String,
      bitness: null == bitness
          ? _value.bitness
          : bitness // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ClientHintConfigImplCopyWith<$Res>
    implements $ClientHintConfigCopyWith<$Res> {
  factory _$$ClientHintConfigImplCopyWith(_$ClientHintConfigImpl value,
          $Res Function(_$ClientHintConfigImpl) then) =
      __$$ClientHintConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'platform') String platform,
      @JsonKey(name: 'navigator.platform') String navigatorPlatform,
      @JsonKey(name: 'platform_version') String platformVersion,
      @JsonKey(name: 'ua_full_version') String uaFullVersion,
      @JsonKey(name: 'mobile') String mobile,
      @JsonKey(name: 'architecture') String architecture,
      @JsonKey(name: 'bitness') String bitness});
}

/// @nodoc
class __$$ClientHintConfigImplCopyWithImpl<$Res>
    extends _$ClientHintConfigCopyWithImpl<$Res, _$ClientHintConfigImpl>
    implements _$$ClientHintConfigImplCopyWith<$Res> {
  __$$ClientHintConfigImplCopyWithImpl(_$ClientHintConfigImpl _value,
      $Res Function(_$ClientHintConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of ClientHintConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? platform = null,
    Object? navigatorPlatform = null,
    Object? platformVersion = null,
    Object? uaFullVersion = null,
    Object? mobile = null,
    Object? architecture = null,
    Object? bitness = null,
  }) {
    return _then(_$ClientHintConfigImpl(
      platform: null == platform
          ? _value.platform
          : platform // ignore: cast_nullable_to_non_nullable
              as String,
      navigatorPlatform: null == navigatorPlatform
          ? _value.navigatorPlatform
          : navigatorPlatform // ignore: cast_nullable_to_non_nullable
              as String,
      platformVersion: null == platformVersion
          ? _value.platformVersion
          : platformVersion // ignore: cast_nullable_to_non_nullable
              as String,
      uaFullVersion: null == uaFullVersion
          ? _value.uaFullVersion
          : uaFullVersion // ignore: cast_nullable_to_non_nullable
              as String,
      mobile: null == mobile
          ? _value.mobile
          : mobile // ignore: cast_nullable_to_non_nullable
              as String,
      architecture: null == architecture
          ? _value.architecture
          : architecture // ignore: cast_nullable_to_non_nullable
              as String,
      bitness: null == bitness
          ? _value.bitness
          : bitness // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ClientHintConfigImpl implements _ClientHintConfig {
  const _$ClientHintConfigImpl(
      {@JsonKey(name: 'platform') required this.platform,
      @JsonKey(name: 'navigator.platform') required this.navigatorPlatform,
      @JsonKey(name: 'platform_version') required this.platformVersion,
      @JsonKey(name: 'ua_full_version') required this.uaFullVersion,
      @JsonKey(name: 'mobile') required this.mobile,
      @JsonKey(name: 'architecture') required this.architecture,
      @JsonKey(name: 'bitness') required this.bitness});

  factory _$ClientHintConfigImpl.fromJson(Map<String, dynamic> json) =>
      _$$ClientHintConfigImplFromJson(json);

  @override
  @JsonKey(name: 'platform')
  final String platform;
// 平台
  @override
  @JsonKey(name: 'navigator.platform')
  final String navigatorPlatform;
// Navigator平台
  @override
  @JsonKey(name: 'platform_version')
  final String platformVersion;
// 平台版本
  @override
  @JsonKey(name: 'ua_full_version')
  final String uaFullVersion;
// UA完整版本
  @override
  @JsonKey(name: 'mobile')
  final String mobile;
// 是否移动设备
  @override
  @JsonKey(name: 'architecture')
  final String architecture;
// 架构
  @override
  @JsonKey(name: 'bitness')
  final String bitness;

  @override
  String toString() {
    return 'ClientHintConfig(platform: $platform, navigatorPlatform: $navigatorPlatform, platformVersion: $platformVersion, uaFullVersion: $uaFullVersion, mobile: $mobile, architecture: $architecture, bitness: $bitness)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ClientHintConfigImpl &&
            (identical(other.platform, platform) ||
                other.platform == platform) &&
            (identical(other.navigatorPlatform, navigatorPlatform) ||
                other.navigatorPlatform == navigatorPlatform) &&
            (identical(other.platformVersion, platformVersion) ||
                other.platformVersion == platformVersion) &&
            (identical(other.uaFullVersion, uaFullVersion) ||
                other.uaFullVersion == uaFullVersion) &&
            (identical(other.mobile, mobile) || other.mobile == mobile) &&
            (identical(other.architecture, architecture) ||
                other.architecture == architecture) &&
            (identical(other.bitness, bitness) || other.bitness == bitness));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, platform, navigatorPlatform,
      platformVersion, uaFullVersion, mobile, architecture, bitness);

  /// Create a copy of ClientHintConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ClientHintConfigImplCopyWith<_$ClientHintConfigImpl> get copyWith =>
      __$$ClientHintConfigImplCopyWithImpl<_$ClientHintConfigImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ClientHintConfigImplToJson(
      this,
    );
  }
}

abstract class _ClientHintConfig implements ClientHintConfig {
  const factory _ClientHintConfig(
      {@JsonKey(name: 'platform') required final String platform,
      @JsonKey(name: 'navigator.platform')
      required final String navigatorPlatform,
      @JsonKey(name: 'platform_version') required final String platformVersion,
      @JsonKey(name: 'ua_full_version') required final String uaFullVersion,
      @JsonKey(name: 'mobile') required final String mobile,
      @JsonKey(name: 'architecture') required final String architecture,
      @JsonKey(name: 'bitness')
      required final String bitness}) = _$ClientHintConfigImpl;

  factory _ClientHintConfig.fromJson(Map<String, dynamic> json) =
      _$ClientHintConfigImpl.fromJson;

  @override
  @JsonKey(name: 'platform')
  String get platform; // 平台
  @override
  @JsonKey(name: 'navigator.platform')
  String get navigatorPlatform; // Navigator平台
  @override
  @JsonKey(name: 'platform_version')
  String get platformVersion; // 平台版本
  @override
  @JsonKey(name: 'ua_full_version')
  String get uaFullVersion; // UA完整版本
  @override
  @JsonKey(name: 'mobile')
  String get mobile; // 是否移动设备
  @override
  @JsonKey(name: 'architecture')
  String get architecture; // 架构
  @override
  @JsonKey(name: 'bitness')
  String get bitness;

  /// Create a copy of ClientHintConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ClientHintConfigImplCopyWith<_$ClientHintConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

LanguagesConfig _$LanguagesConfigFromJson(Map<String, dynamic> json) {
  return _LanguagesConfig.fromJson(json);
}

/// @nodoc
mixin _$LanguagesConfig {
  @JsonKey(name: 'js')
  String get js => throw _privateConstructorUsedError; // JavaScript层语言
  @JsonKey(name: 'http')
  String get http => throw _privateConstructorUsedError;

  /// Serializes this LanguagesConfig to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of LanguagesConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $LanguagesConfigCopyWith<LanguagesConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LanguagesConfigCopyWith<$Res> {
  factory $LanguagesConfigCopyWith(
          LanguagesConfig value, $Res Function(LanguagesConfig) then) =
      _$LanguagesConfigCopyWithImpl<$Res, LanguagesConfig>;
  @useResult
  $Res call(
      {@JsonKey(name: 'js') String js, @JsonKey(name: 'http') String http});
}

/// @nodoc
class _$LanguagesConfigCopyWithImpl<$Res, $Val extends LanguagesConfig>
    implements $LanguagesConfigCopyWith<$Res> {
  _$LanguagesConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LanguagesConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? js = null,
    Object? http = null,
  }) {
    return _then(_value.copyWith(
      js: null == js
          ? _value.js
          : js // ignore: cast_nullable_to_non_nullable
              as String,
      http: null == http
          ? _value.http
          : http // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LanguagesConfigImplCopyWith<$Res>
    implements $LanguagesConfigCopyWith<$Res> {
  factory _$$LanguagesConfigImplCopyWith(_$LanguagesConfigImpl value,
          $Res Function(_$LanguagesConfigImpl) then) =
      __$$LanguagesConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'js') String js, @JsonKey(name: 'http') String http});
}

/// @nodoc
class __$$LanguagesConfigImplCopyWithImpl<$Res>
    extends _$LanguagesConfigCopyWithImpl<$Res, _$LanguagesConfigImpl>
    implements _$$LanguagesConfigImplCopyWith<$Res> {
  __$$LanguagesConfigImplCopyWithImpl(
      _$LanguagesConfigImpl _value, $Res Function(_$LanguagesConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of LanguagesConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? js = null,
    Object? http = null,
  }) {
    return _then(_$LanguagesConfigImpl(
      js: null == js
          ? _value.js
          : js // ignore: cast_nullable_to_non_nullable
              as String,
      http: null == http
          ? _value.http
          : http // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$LanguagesConfigImpl implements _LanguagesConfig {
  const _$LanguagesConfigImpl(
      {@JsonKey(name: 'js') required this.js,
      @JsonKey(name: 'http') required this.http});

  factory _$LanguagesConfigImpl.fromJson(Map<String, dynamic> json) =>
      _$$LanguagesConfigImplFromJson(json);

  @override
  @JsonKey(name: 'js')
  final String js;
// JavaScript层语言
  @override
  @JsonKey(name: 'http')
  final String http;

  @override
  String toString() {
    return 'LanguagesConfig(js: $js, http: $http)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LanguagesConfigImpl &&
            (identical(other.js, js) || other.js == js) &&
            (identical(other.http, http) || other.http == http));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, js, http);

  /// Create a copy of LanguagesConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LanguagesConfigImplCopyWith<_$LanguagesConfigImpl> get copyWith =>
      __$$LanguagesConfigImplCopyWithImpl<_$LanguagesConfigImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LanguagesConfigImplToJson(
      this,
    );
  }
}

abstract class _LanguagesConfig implements LanguagesConfig {
  const factory _LanguagesConfig(
          {@JsonKey(name: 'js') required final String js,
          @JsonKey(name: 'http') required final String http}) =
      _$LanguagesConfigImpl;

  factory _LanguagesConfig.fromJson(Map<String, dynamic> json) =
      _$LanguagesConfigImpl.fromJson;

  @override
  @JsonKey(name: 'js')
  String get js; // JavaScript层语言
  @override
  @JsonKey(name: 'http')
  String get http;

  /// Create a copy of LanguagesConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LanguagesConfigImplCopyWith<_$LanguagesConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

SoftwareConfig _$SoftwareConfigFromJson(Map<String, dynamic> json) {
  return _SoftwareConfig.fromJson(json);
}

/// @nodoc
mixin _$SoftwareConfig {
  @JsonKey(name: 'cookie')
  String get cookie => throw _privateConstructorUsedError; // Cookie开关 (yes/no)
  @JsonKey(name: 'java')
  String get java => throw _privateConstructorUsedError; // Java开关 (yes/no)
  @JsonKey(name: 'dnt')
  String get dnt => throw _privateConstructorUsedError;

  /// Serializes this SoftwareConfig to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SoftwareConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SoftwareConfigCopyWith<SoftwareConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SoftwareConfigCopyWith<$Res> {
  factory $SoftwareConfigCopyWith(
          SoftwareConfig value, $Res Function(SoftwareConfig) then) =
      _$SoftwareConfigCopyWithImpl<$Res, SoftwareConfig>;
  @useResult
  $Res call(
      {@JsonKey(name: 'cookie') String cookie,
      @JsonKey(name: 'java') String java,
      @JsonKey(name: 'dnt') String dnt});
}

/// @nodoc
class _$SoftwareConfigCopyWithImpl<$Res, $Val extends SoftwareConfig>
    implements $SoftwareConfigCopyWith<$Res> {
  _$SoftwareConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SoftwareConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? cookie = null,
    Object? java = null,
    Object? dnt = null,
  }) {
    return _then(_value.copyWith(
      cookie: null == cookie
          ? _value.cookie
          : cookie // ignore: cast_nullable_to_non_nullable
              as String,
      java: null == java
          ? _value.java
          : java // ignore: cast_nullable_to_non_nullable
              as String,
      dnt: null == dnt
          ? _value.dnt
          : dnt // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SoftwareConfigImplCopyWith<$Res>
    implements $SoftwareConfigCopyWith<$Res> {
  factory _$$SoftwareConfigImplCopyWith(_$SoftwareConfigImpl value,
          $Res Function(_$SoftwareConfigImpl) then) =
      __$$SoftwareConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'cookie') String cookie,
      @JsonKey(name: 'java') String java,
      @JsonKey(name: 'dnt') String dnt});
}

/// @nodoc
class __$$SoftwareConfigImplCopyWithImpl<$Res>
    extends _$SoftwareConfigCopyWithImpl<$Res, _$SoftwareConfigImpl>
    implements _$$SoftwareConfigImplCopyWith<$Res> {
  __$$SoftwareConfigImplCopyWithImpl(
      _$SoftwareConfigImpl _value, $Res Function(_$SoftwareConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of SoftwareConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? cookie = null,
    Object? java = null,
    Object? dnt = null,
  }) {
    return _then(_$SoftwareConfigImpl(
      cookie: null == cookie
          ? _value.cookie
          : cookie // ignore: cast_nullable_to_non_nullable
              as String,
      java: null == java
          ? _value.java
          : java // ignore: cast_nullable_to_non_nullable
              as String,
      dnt: null == dnt
          ? _value.dnt
          : dnt // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SoftwareConfigImpl implements _SoftwareConfig {
  const _$SoftwareConfigImpl(
      {@JsonKey(name: 'cookie') required this.cookie,
      @JsonKey(name: 'java') required this.java,
      @JsonKey(name: 'dnt') required this.dnt});

  factory _$SoftwareConfigImpl.fromJson(Map<String, dynamic> json) =>
      _$$SoftwareConfigImplFromJson(json);

  @override
  @JsonKey(name: 'cookie')
  final String cookie;
// Cookie开关 (yes/no)
  @override
  @JsonKey(name: 'java')
  final String java;
// Java开关 (yes/no)
  @override
  @JsonKey(name: 'dnt')
  final String dnt;

  @override
  String toString() {
    return 'SoftwareConfig(cookie: $cookie, java: $java, dnt: $dnt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SoftwareConfigImpl &&
            (identical(other.cookie, cookie) || other.cookie == cookie) &&
            (identical(other.java, java) || other.java == java) &&
            (identical(other.dnt, dnt) || other.dnt == dnt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, cookie, java, dnt);

  /// Create a copy of SoftwareConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SoftwareConfigImplCopyWith<_$SoftwareConfigImpl> get copyWith =>
      __$$SoftwareConfigImplCopyWithImpl<_$SoftwareConfigImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SoftwareConfigImplToJson(
      this,
    );
  }
}

abstract class _SoftwareConfig implements SoftwareConfig {
  const factory _SoftwareConfig(
      {@JsonKey(name: 'cookie') required final String cookie,
      @JsonKey(name: 'java') required final String java,
      @JsonKey(name: 'dnt') required final String dnt}) = _$SoftwareConfigImpl;

  factory _SoftwareConfig.fromJson(Map<String, dynamic> json) =
      _$SoftwareConfigImpl.fromJson;

  @override
  @JsonKey(name: 'cookie')
  String get cookie; // Cookie开关 (yes/no)
  @override
  @JsonKey(name: 'java')
  String get java; // Java开关 (yes/no)
  @override
  @JsonKey(name: 'dnt')
  String get dnt;

  /// Create a copy of SoftwareConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SoftwareConfigImplCopyWith<_$SoftwareConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PortScanConfig _$PortScanConfigFromJson(Map<String, dynamic> json) {
  return _PortScanConfig.fromJson(json);
}

/// @nodoc
mixin _$PortScanConfig {
  @JsonKey(name: 'enable')
  String get enable => throw _privateConstructorUsedError;

  /// Serializes this PortScanConfig to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PortScanConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PortScanConfigCopyWith<PortScanConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PortScanConfigCopyWith<$Res> {
  factory $PortScanConfigCopyWith(
          PortScanConfig value, $Res Function(PortScanConfig) then) =
      _$PortScanConfigCopyWithImpl<$Res, PortScanConfig>;
  @useResult
  $Res call({@JsonKey(name: 'enable') String enable});
}

/// @nodoc
class _$PortScanConfigCopyWithImpl<$Res, $Val extends PortScanConfig>
    implements $PortScanConfigCopyWith<$Res> {
  _$PortScanConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PortScanConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? enable = null,
  }) {
    return _then(_value.copyWith(
      enable: null == enable
          ? _value.enable
          : enable // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PortScanConfigImplCopyWith<$Res>
    implements $PortScanConfigCopyWith<$Res> {
  factory _$$PortScanConfigImplCopyWith(_$PortScanConfigImpl value,
          $Res Function(_$PortScanConfigImpl) then) =
      __$$PortScanConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({@JsonKey(name: 'enable') String enable});
}

/// @nodoc
class __$$PortScanConfigImplCopyWithImpl<$Res>
    extends _$PortScanConfigCopyWithImpl<$Res, _$PortScanConfigImpl>
    implements _$$PortScanConfigImplCopyWith<$Res> {
  __$$PortScanConfigImplCopyWithImpl(
      _$PortScanConfigImpl _value, $Res Function(_$PortScanConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of PortScanConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? enable = null,
  }) {
    return _then(_$PortScanConfigImpl(
      enable: null == enable
          ? _value.enable
          : enable // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PortScanConfigImpl implements _PortScanConfig {
  const _$PortScanConfigImpl({@JsonKey(name: 'enable') required this.enable});

  factory _$PortScanConfigImpl.fromJson(Map<String, dynamic> json) =>
      _$$PortScanConfigImplFromJson(json);

  @override
  @JsonKey(name: 'enable')
  final String enable;

  @override
  String toString() {
    return 'PortScanConfig(enable: $enable)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PortScanConfigImpl &&
            (identical(other.enable, enable) || other.enable == enable));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, enable);

  /// Create a copy of PortScanConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PortScanConfigImplCopyWith<_$PortScanConfigImpl> get copyWith =>
      __$$PortScanConfigImplCopyWithImpl<_$PortScanConfigImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PortScanConfigImplToJson(
      this,
    );
  }
}

abstract class _PortScanConfig implements PortScanConfig {
  const factory _PortScanConfig(
          {@JsonKey(name: 'enable') required final String enable}) =
      _$PortScanConfigImpl;

  factory _PortScanConfig.fromJson(Map<String, dynamic> json) =
      _$PortScanConfigImpl.fromJson;

  @override
  @JsonKey(name: 'enable')
  String get enable;

  /// Create a copy of PortScanConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PortScanConfigImplCopyWith<_$PortScanConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

FontConfig _$FontConfigFromJson(Map<String, dynamic> json) {
  return _FontConfig.fromJson(json);
}

/// @nodoc
mixin _$FontConfig {
  @JsonKey(name: 'removefont')
  String get removefont => throw _privateConstructorUsedError;

  /// Serializes this FontConfig to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of FontConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FontConfigCopyWith<FontConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FontConfigCopyWith<$Res> {
  factory $FontConfigCopyWith(
          FontConfig value, $Res Function(FontConfig) then) =
      _$FontConfigCopyWithImpl<$Res, FontConfig>;
  @useResult
  $Res call({@JsonKey(name: 'removefont') String removefont});
}

/// @nodoc
class _$FontConfigCopyWithImpl<$Res, $Val extends FontConfig>
    implements $FontConfigCopyWith<$Res> {
  _$FontConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FontConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? removefont = null,
  }) {
    return _then(_value.copyWith(
      removefont: null == removefont
          ? _value.removefont
          : removefont // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FontConfigImplCopyWith<$Res>
    implements $FontConfigCopyWith<$Res> {
  factory _$$FontConfigImplCopyWith(
          _$FontConfigImpl value, $Res Function(_$FontConfigImpl) then) =
      __$$FontConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({@JsonKey(name: 'removefont') String removefont});
}

/// @nodoc
class __$$FontConfigImplCopyWithImpl<$Res>
    extends _$FontConfigCopyWithImpl<$Res, _$FontConfigImpl>
    implements _$$FontConfigImplCopyWith<$Res> {
  __$$FontConfigImplCopyWithImpl(
      _$FontConfigImpl _value, $Res Function(_$FontConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of FontConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? removefont = null,
  }) {
    return _then(_$FontConfigImpl(
      removefont: null == removefont
          ? _value.removefont
          : removefont // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$FontConfigImpl implements _FontConfig {
  const _$FontConfigImpl(
      {@JsonKey(name: 'removefont') required this.removefont});

  factory _$FontConfigImpl.fromJson(Map<String, dynamic> json) =>
      _$$FontConfigImplFromJson(json);

  @override
  @JsonKey(name: 'removefont')
  final String removefont;

  @override
  String toString() {
    return 'FontConfig(removefont: $removefont)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FontConfigImpl &&
            (identical(other.removefont, removefont) ||
                other.removefont == removefont));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, removefont);

  /// Create a copy of FontConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FontConfigImplCopyWith<_$FontConfigImpl> get copyWith =>
      __$$FontConfigImplCopyWithImpl<_$FontConfigImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FontConfigImplToJson(
      this,
    );
  }
}

abstract class _FontConfig implements FontConfig {
  const factory _FontConfig(
          {@JsonKey(name: 'removefont') required final String removefont}) =
      _$FontConfigImpl;

  factory _FontConfig.fromJson(Map<String, dynamic> json) =
      _$FontConfigImpl.fromJson;

  @override
  @JsonKey(name: 'removefont')
  String get removefont;

  /// Create a copy of FontConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FontConfigImplCopyWith<_$FontConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

BrowserLaunchConfig _$BrowserLaunchConfigFromJson(Map<String, dynamic> json) {
  return _BrowserLaunchConfig.fromJson(json);
}

/// @nodoc
mixin _$BrowserLaunchConfig {
  @JsonKey(name: 'fingerprint')
  BrowserFingerprintConfig get fingerprint =>
      throw _privateConstructorUsedError; // 指纹配置
  @JsonKey(name: 'userAgent')
  String get userAgent => throw _privateConstructorUsedError; // 用户代理
  @JsonKey(name: 'language')
  String get language => throw _privateConstructorUsedError; // 系统界面语言
  @JsonKey(name: 'timeZone')
  String get timeZone => throw _privateConstructorUsedError; // 时区
  @JsonKey(name: 'userDataDir')
  String get userDataDir => throw _privateConstructorUsedError; // 用户数据目录
  @JsonKey(name: 'enableUnsafeWebGPU')
  bool get enableUnsafeWebGPU => throw _privateConstructorUsedError;

  /// Serializes this BrowserLaunchConfig to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of BrowserLaunchConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BrowserLaunchConfigCopyWith<BrowserLaunchConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BrowserLaunchConfigCopyWith<$Res> {
  factory $BrowserLaunchConfigCopyWith(
          BrowserLaunchConfig value, $Res Function(BrowserLaunchConfig) then) =
      _$BrowserLaunchConfigCopyWithImpl<$Res, BrowserLaunchConfig>;
  @useResult
  $Res call(
      {@JsonKey(name: 'fingerprint') BrowserFingerprintConfig fingerprint,
      @JsonKey(name: 'userAgent') String userAgent,
      @JsonKey(name: 'language') String language,
      @JsonKey(name: 'timeZone') String timeZone,
      @JsonKey(name: 'userDataDir') String userDataDir,
      @JsonKey(name: 'enableUnsafeWebGPU') bool enableUnsafeWebGPU});

  $BrowserFingerprintConfigCopyWith<$Res> get fingerprint;
}

/// @nodoc
class _$BrowserLaunchConfigCopyWithImpl<$Res, $Val extends BrowserLaunchConfig>
    implements $BrowserLaunchConfigCopyWith<$Res> {
  _$BrowserLaunchConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BrowserLaunchConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fingerprint = null,
    Object? userAgent = null,
    Object? language = null,
    Object? timeZone = null,
    Object? userDataDir = null,
    Object? enableUnsafeWebGPU = null,
  }) {
    return _then(_value.copyWith(
      fingerprint: null == fingerprint
          ? _value.fingerprint
          : fingerprint // ignore: cast_nullable_to_non_nullable
              as BrowserFingerprintConfig,
      userAgent: null == userAgent
          ? _value.userAgent
          : userAgent // ignore: cast_nullable_to_non_nullable
              as String,
      language: null == language
          ? _value.language
          : language // ignore: cast_nullable_to_non_nullable
              as String,
      timeZone: null == timeZone
          ? _value.timeZone
          : timeZone // ignore: cast_nullable_to_non_nullable
              as String,
      userDataDir: null == userDataDir
          ? _value.userDataDir
          : userDataDir // ignore: cast_nullable_to_non_nullable
              as String,
      enableUnsafeWebGPU: null == enableUnsafeWebGPU
          ? _value.enableUnsafeWebGPU
          : enableUnsafeWebGPU // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }

  /// Create a copy of BrowserLaunchConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $BrowserFingerprintConfigCopyWith<$Res> get fingerprint {
    return $BrowserFingerprintConfigCopyWith<$Res>(_value.fingerprint, (value) {
      return _then(_value.copyWith(fingerprint: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$BrowserLaunchConfigImplCopyWith<$Res>
    implements $BrowserLaunchConfigCopyWith<$Res> {
  factory _$$BrowserLaunchConfigImplCopyWith(_$BrowserLaunchConfigImpl value,
          $Res Function(_$BrowserLaunchConfigImpl) then) =
      __$$BrowserLaunchConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'fingerprint') BrowserFingerprintConfig fingerprint,
      @JsonKey(name: 'userAgent') String userAgent,
      @JsonKey(name: 'language') String language,
      @JsonKey(name: 'timeZone') String timeZone,
      @JsonKey(name: 'userDataDir') String userDataDir,
      @JsonKey(name: 'enableUnsafeWebGPU') bool enableUnsafeWebGPU});

  @override
  $BrowserFingerprintConfigCopyWith<$Res> get fingerprint;
}

/// @nodoc
class __$$BrowserLaunchConfigImplCopyWithImpl<$Res>
    extends _$BrowserLaunchConfigCopyWithImpl<$Res, _$BrowserLaunchConfigImpl>
    implements _$$BrowserLaunchConfigImplCopyWith<$Res> {
  __$$BrowserLaunchConfigImplCopyWithImpl(_$BrowserLaunchConfigImpl _value,
      $Res Function(_$BrowserLaunchConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of BrowserLaunchConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fingerprint = null,
    Object? userAgent = null,
    Object? language = null,
    Object? timeZone = null,
    Object? userDataDir = null,
    Object? enableUnsafeWebGPU = null,
  }) {
    return _then(_$BrowserLaunchConfigImpl(
      fingerprint: null == fingerprint
          ? _value.fingerprint
          : fingerprint // ignore: cast_nullable_to_non_nullable
              as BrowserFingerprintConfig,
      userAgent: null == userAgent
          ? _value.userAgent
          : userAgent // ignore: cast_nullable_to_non_nullable
              as String,
      language: null == language
          ? _value.language
          : language // ignore: cast_nullable_to_non_nullable
              as String,
      timeZone: null == timeZone
          ? _value.timeZone
          : timeZone // ignore: cast_nullable_to_non_nullable
              as String,
      userDataDir: null == userDataDir
          ? _value.userDataDir
          : userDataDir // ignore: cast_nullable_to_non_nullable
              as String,
      enableUnsafeWebGPU: null == enableUnsafeWebGPU
          ? _value.enableUnsafeWebGPU
          : enableUnsafeWebGPU // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BrowserLaunchConfigImpl implements _BrowserLaunchConfig {
  const _$BrowserLaunchConfigImpl(
      {@JsonKey(name: 'fingerprint') required this.fingerprint,
      @JsonKey(name: 'userAgent') required this.userAgent,
      @JsonKey(name: 'language') required this.language,
      @JsonKey(name: 'timeZone') required this.timeZone,
      @JsonKey(name: 'userDataDir') required this.userDataDir,
      @JsonKey(name: 'enableUnsafeWebGPU') this.enableUnsafeWebGPU = false});

  factory _$BrowserLaunchConfigImpl.fromJson(Map<String, dynamic> json) =>
      _$$BrowserLaunchConfigImplFromJson(json);

  @override
  @JsonKey(name: 'fingerprint')
  final BrowserFingerprintConfig fingerprint;
// 指纹配置
  @override
  @JsonKey(name: 'userAgent')
  final String userAgent;
// 用户代理
  @override
  @JsonKey(name: 'language')
  final String language;
// 系统界面语言
  @override
  @JsonKey(name: 'timeZone')
  final String timeZone;
// 时区
  @override
  @JsonKey(name: 'userDataDir')
  final String userDataDir;
// 用户数据目录
  @override
  @JsonKey(name: 'enableUnsafeWebGPU')
  final bool enableUnsafeWebGPU;

  @override
  String toString() {
    return 'BrowserLaunchConfig(fingerprint: $fingerprint, userAgent: $userAgent, language: $language, timeZone: $timeZone, userDataDir: $userDataDir, enableUnsafeWebGPU: $enableUnsafeWebGPU)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BrowserLaunchConfigImpl &&
            (identical(other.fingerprint, fingerprint) ||
                other.fingerprint == fingerprint) &&
            (identical(other.userAgent, userAgent) ||
                other.userAgent == userAgent) &&
            (identical(other.language, language) ||
                other.language == language) &&
            (identical(other.timeZone, timeZone) ||
                other.timeZone == timeZone) &&
            (identical(other.userDataDir, userDataDir) ||
                other.userDataDir == userDataDir) &&
            (identical(other.enableUnsafeWebGPU, enableUnsafeWebGPU) ||
                other.enableUnsafeWebGPU == enableUnsafeWebGPU));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, fingerprint, userAgent, language,
      timeZone, userDataDir, enableUnsafeWebGPU);

  /// Create a copy of BrowserLaunchConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BrowserLaunchConfigImplCopyWith<_$BrowserLaunchConfigImpl> get copyWith =>
      __$$BrowserLaunchConfigImplCopyWithImpl<_$BrowserLaunchConfigImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BrowserLaunchConfigImplToJson(
      this,
    );
  }
}

abstract class _BrowserLaunchConfig implements BrowserLaunchConfig {
  const factory _BrowserLaunchConfig(
          {@JsonKey(name: 'fingerprint')
          required final BrowserFingerprintConfig fingerprint,
          @JsonKey(name: 'userAgent') required final String userAgent,
          @JsonKey(name: 'language') required final String language,
          @JsonKey(name: 'timeZone') required final String timeZone,
          @JsonKey(name: 'userDataDir') required final String userDataDir,
          @JsonKey(name: 'enableUnsafeWebGPU') final bool enableUnsafeWebGPU}) =
      _$BrowserLaunchConfigImpl;

  factory _BrowserLaunchConfig.fromJson(Map<String, dynamic> json) =
      _$BrowserLaunchConfigImpl.fromJson;

  @override
  @JsonKey(name: 'fingerprint')
  BrowserFingerprintConfig get fingerprint; // 指纹配置
  @override
  @JsonKey(name: 'userAgent')
  String get userAgent; // 用户代理
  @override
  @JsonKey(name: 'language')
  String get language; // 系统界面语言
  @override
  @JsonKey(name: 'timeZone')
  String get timeZone; // 时区
  @override
  @JsonKey(name: 'userDataDir')
  String get userDataDir; // 用户数据目录
  @override
  @JsonKey(name: 'enableUnsafeWebGPU')
  bool get enableUnsafeWebGPU;

  /// Create a copy of BrowserLaunchConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BrowserLaunchConfigImplCopyWith<_$BrowserLaunchConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
