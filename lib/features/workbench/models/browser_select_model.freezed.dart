// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'browser_select_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

BrowserSelectModel _$BrowserSelectModelFromJson(Map<String, dynamic> json) {
  return _BrowserSelectModel.fromJson(json);
}

/// @nodoc
mixin _$BrowserSelectModel {
  List<BrowserWindowItem> get browserWindows =>
      throw _privateConstructorUsedError;
  List<int> get selectedWindowIds => throw _privateConstructorUsedError;

  /// Serializes this BrowserSelectModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of BrowserSelectModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BrowserSelectModelCopyWith<BrowserSelectModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BrowserSelectModelCopyWith<$Res> {
  factory $BrowserSelectModelCopyWith(
          BrowserSelectModel value, $Res Function(BrowserSelectModel) then) =
      _$BrowserSelectModelCopyWithImpl<$Res, BrowserSelectModel>;
  @useResult
  $Res call(
      {List<BrowserWindowItem> browserWindows, List<int> selectedWindowIds});
}

/// @nodoc
class _$BrowserSelectModelCopyWithImpl<$Res, $Val extends BrowserSelectModel>
    implements $BrowserSelectModelCopyWith<$Res> {
  _$BrowserSelectModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BrowserSelectModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? browserWindows = null,
    Object? selectedWindowIds = null,
  }) {
    return _then(_value.copyWith(
      browserWindows: null == browserWindows
          ? _value.browserWindows
          : browserWindows // ignore: cast_nullable_to_non_nullable
              as List<BrowserWindowItem>,
      selectedWindowIds: null == selectedWindowIds
          ? _value.selectedWindowIds
          : selectedWindowIds // ignore: cast_nullable_to_non_nullable
              as List<int>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BrowserSelectModelImplCopyWith<$Res>
    implements $BrowserSelectModelCopyWith<$Res> {
  factory _$$BrowserSelectModelImplCopyWith(_$BrowserSelectModelImpl value,
          $Res Function(_$BrowserSelectModelImpl) then) =
      __$$BrowserSelectModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<BrowserWindowItem> browserWindows, List<int> selectedWindowIds});
}

/// @nodoc
class __$$BrowserSelectModelImplCopyWithImpl<$Res>
    extends _$BrowserSelectModelCopyWithImpl<$Res, _$BrowserSelectModelImpl>
    implements _$$BrowserSelectModelImplCopyWith<$Res> {
  __$$BrowserSelectModelImplCopyWithImpl(_$BrowserSelectModelImpl _value,
      $Res Function(_$BrowserSelectModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of BrowserSelectModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? browserWindows = null,
    Object? selectedWindowIds = null,
  }) {
    return _then(_$BrowserSelectModelImpl(
      browserWindows: null == browserWindows
          ? _value._browserWindows
          : browserWindows // ignore: cast_nullable_to_non_nullable
              as List<BrowserWindowItem>,
      selectedWindowIds: null == selectedWindowIds
          ? _value._selectedWindowIds
          : selectedWindowIds // ignore: cast_nullable_to_non_nullable
              as List<int>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BrowserSelectModelImpl implements _BrowserSelectModel {
  const _$BrowserSelectModelImpl(
      {required final List<BrowserWindowItem> browserWindows,
      final List<int> selectedWindowIds = const []})
      : _browserWindows = browserWindows,
        _selectedWindowIds = selectedWindowIds;

  factory _$BrowserSelectModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$BrowserSelectModelImplFromJson(json);

  final List<BrowserWindowItem> _browserWindows;
  @override
  List<BrowserWindowItem> get browserWindows {
    if (_browserWindows is EqualUnmodifiableListView) return _browserWindows;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_browserWindows);
  }

  final List<int> _selectedWindowIds;
  @override
  @JsonKey()
  List<int> get selectedWindowIds {
    if (_selectedWindowIds is EqualUnmodifiableListView)
      return _selectedWindowIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_selectedWindowIds);
  }

  @override
  String toString() {
    return 'BrowserSelectModel(browserWindows: $browserWindows, selectedWindowIds: $selectedWindowIds)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BrowserSelectModelImpl &&
            const DeepCollectionEquality()
                .equals(other._browserWindows, _browserWindows) &&
            const DeepCollectionEquality()
                .equals(other._selectedWindowIds, _selectedWindowIds));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_browserWindows),
      const DeepCollectionEquality().hash(_selectedWindowIds));

  /// Create a copy of BrowserSelectModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BrowserSelectModelImplCopyWith<_$BrowserSelectModelImpl> get copyWith =>
      __$$BrowserSelectModelImplCopyWithImpl<_$BrowserSelectModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BrowserSelectModelImplToJson(
      this,
    );
  }
}

abstract class _BrowserSelectModel implements BrowserSelectModel {
  const factory _BrowserSelectModel(
      {required final List<BrowserWindowItem> browserWindows,
      final List<int> selectedWindowIds}) = _$BrowserSelectModelImpl;

  factory _BrowserSelectModel.fromJson(Map<String, dynamic> json) =
      _$BrowserSelectModelImpl.fromJson;

  @override
  List<BrowserWindowItem> get browserWindows;
  @override
  List<int> get selectedWindowIds;

  /// Create a copy of BrowserSelectModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BrowserSelectModelImplCopyWith<_$BrowserSelectModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
