// 浏览器配置状态模型
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:frontend_re/features/workbench/models/browser_fingerprint_model.dart';

part 'browser_config_model.freezed.dart';
part 'browser_config_model.g.dart';

@freezed
class BrowserConfigModel with _$BrowserConfigModel {
  // 使用 const factory 替代原来的构造函数
  const factory BrowserConfigModel({
    required String name,

    /// 账号平台
    required String account,

    /// 配置项的用户名和密码
    required String accountUsername,
    required String accountPassword,

    /// Cookie
    required String cookie,

    /// 代理id
    required int proxyId,

    /// 代理类型
    required int proxyType,

    /// 浏览器版本
    required String chromeSelectedValue,

    /// 操作系统版本
    required String oswSelectedValue,

    /// UA 环境设置
    required String ua,

    // 语言
    required bool evaluateLanguage, // 是否自定义语言
    required String languageSelectedValue, // 自定义语言内容

    // 界面语言
    required bool localeEvaluateLanguage, // 是否自定义界面语言
    required String locale, // 自定义界面语言内容

    // 时区
    required bool timezone, // 是否自定义时区
    required String timezoneSelectedValue, // 自定义时区内容

    // 地区
    required bool locationByIP, // 是否自定义地区
    required String location, // 经 纬 海拔 精度

    // WebRTC
    required String webrtc,

    // Canvas
    required bool canvas, // true 噪音 false 真实

    // WebGL
    required bool webGL,

    // WebGL 厂商和渲染器
    required String webGLVendor, // 选中的供应商
    required String webGLRender, // 随机选择的渲染器

    // Plugin
    required bool plugin,

    // 字体
    required bool font,

    // ClientRects
    required bool clientRects,

    // AudioContext
    required bool audioContext,

    // 分辨率
    required String resolution,

    // 设备内存
    required String memory,

    // 设备 CPU
    required String kernel,

    // 以下变量在 BrowserConfig 中不存在，保留原类型
    required String chromeVersion,
    required String oswVersion,

    required bool editing,
    
    // 备注
    required String comment,

    // 标签
    required String tag,

    // 分组id
    required int groupId,
  }) = _BrowserConfigModel;

  // 添加 fromJson factory
  factory BrowserConfigModel.fromJson(Map<String, dynamic> json) =>
      _$BrowserConfigModelFromJson(json);

  // Freezed 会自动生成 copyWith, toString, ==, hashCode
  // 删除手写的 copyWith 方法
}
