// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'browser_config_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$BrowserConfigModelImpl _$$BrowserConfigModelImplFromJson(
        Map<String, dynamic> json) =>
    _$BrowserConfigModelImpl(
      name: json['name'] as String,
      account: json['account'] as String,
      accountUsername: json['accountUsername'] as String,
      accountPassword: json['accountPassword'] as String,
      cookie: json['cookie'] as String,
      proxyId: (json['proxyId'] as num).toInt(),
      proxyType: (json['proxyType'] as num).toInt(),
      chromeSelectedValue: json['chromeSelectedValue'] as String,
      oswSelectedValue: json['oswSelectedValue'] as String,
      ua: json['ua'] as String,
      evaluateLanguage: json['evaluateLanguage'] as bool,
      languageSelectedValue: json['languageSelectedValue'] as String,
      localeEvaluateLanguage: json['localeEvaluateLanguage'] as bool,
      locale: json['locale'] as String,
      timezone: json['timezone'] as bool,
      timezoneSelectedValue: json['timezoneSelectedValue'] as String,
      locationByIP: json['locationByIP'] as bool,
      location: json['location'] as String,
      webrtc: json['webrtc'] as String,
      canvas: json['canvas'] as bool,
      webGL: json['webGL'] as bool,
      webGLVendor: json['webGLVendor'] as String,
      webGLRender: json['webGLRender'] as String,
      plugin: json['plugin'] as bool,
      font: json['font'] as bool,
      clientRects: json['clientRects'] as bool,
      audioContext: json['audioContext'] as bool,
      resolution: json['resolution'] as String,
      memory: json['memory'] as String,
      kernel: json['kernel'] as String,
      chromeVersion: json['chromeVersion'] as String,
      oswVersion: json['oswVersion'] as String,
      editing: json['editing'] as bool,
      comment: json['comment'] as String,
      tag: json['tag'] as String,
      groupId: (json['groupId'] as num).toInt(),
    );

Map<String, dynamic> _$$BrowserConfigModelImplToJson(
        _$BrowserConfigModelImpl instance) =>
    <String, dynamic>{
      'name': instance.name,
      'account': instance.account,
      'accountUsername': instance.accountUsername,
      'accountPassword': instance.accountPassword,
      'cookie': instance.cookie,
      'proxyId': instance.proxyId,
      'proxyType': instance.proxyType,
      'chromeSelectedValue': instance.chromeSelectedValue,
      'oswSelectedValue': instance.oswSelectedValue,
      'ua': instance.ua,
      'evaluateLanguage': instance.evaluateLanguage,
      'languageSelectedValue': instance.languageSelectedValue,
      'localeEvaluateLanguage': instance.localeEvaluateLanguage,
      'locale': instance.locale,
      'timezone': instance.timezone,
      'timezoneSelectedValue': instance.timezoneSelectedValue,
      'locationByIP': instance.locationByIP,
      'location': instance.location,
      'webrtc': instance.webrtc,
      'canvas': instance.canvas,
      'webGL': instance.webGL,
      'webGLVendor': instance.webGLVendor,
      'webGLRender': instance.webGLRender,
      'plugin': instance.plugin,
      'font': instance.font,
      'clientRects': instance.clientRects,
      'audioContext': instance.audioContext,
      'resolution': instance.resolution,
      'memory': instance.memory,
      'kernel': instance.kernel,
      'chromeVersion': instance.chromeVersion,
      'oswVersion': instance.oswVersion,
      'editing': instance.editing,
      'comment': instance.comment,
      'tag': instance.tag,
      'groupId': instance.groupId,
    };
