// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'browser_select_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$BrowserSelectModelImpl _$$BrowserSelectModelImplFromJson(
        Map<String, dynamic> json) =>
    _$BrowserSelectModelImpl(
      browserWindows: (json['browserWindows'] as List<dynamic>)
          .map((e) => BrowserWindowItem.fromJson(e as Map<String, dynamic>))
          .toList(),
      selectedWindowIds: (json['selectedWindowIds'] as List<dynamic>?)
              ?.map((e) => (e as num).toInt())
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$BrowserSelectModelImplToJson(
        _$BrowserSelectModelImpl instance) =>
    <String, dynamic>{
      'browserWindows': instance.browserWindows,
      'selectedWindowIds': instance.selectedWindowIds,
    };
