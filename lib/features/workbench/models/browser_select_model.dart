import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:frontend_re/domain/models/browser_window_model.dart';

part 'browser_select_model.freezed.dart';
part 'browser_select_model.g.dart';

@freezed
class BrowserSelectModel with _$BrowserSelectModel {
  const factory BrowserSelectModel({
    required List<BrowserWindowItem> browserWindows,
    @Default([]) List<int> selectedWindowIds,
  }) = _BrowserSelectModel;

  factory BrowserSelectModel.fromJson(Map<String, dynamic> json) => _$BrowserSelectModelFromJson(json);
} 
