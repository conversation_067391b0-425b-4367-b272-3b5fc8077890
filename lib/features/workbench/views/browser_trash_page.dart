import 'package:flutter/material.dart';
import 'package:frontend_re/core/utils/ui/snack_bar_util.dart';
import 'package:frontend_re/features/workbench/controllers/browser_trash_controller.dart';
import 'package:frontend_re/features/workbench/views/components/browser_trash_table.dart';
import 'package:frontend_re/widgets/custom_solid_button.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class BrowserTrashPage extends ConsumerWidget {
  const BrowserTrashPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 监听回收站状态
    ref.watch(browserTrashControllerProvider);
    // 监听回收站表格状态
    ref.watch(browserTrashTableControllerProvider);

    return Scaffold(
      body: Column(
        spacing: 16,
        children: [
          Row(
            spacing: 16,
            children: [
              // 返回按钮
              CustomSolidButton(
                iconPath: 'assets/svg/right_default.svg', // 需要准备恢复图标
                backgroundColor: const Color(0xFFFFFFFF),
                iconColor: const Color(0xFFFFFFFF),
                iconBackgroundColor: const Color(0xFF1E1C1D),
                iconSize: 14,
                textColor: const Color(0xFF8D8E93),
                text: '返回工作台',
                onPressed: () {
                  context.pop();
                },
              ),
              // 恢复按钮
              CustomSolidButton(
                iconPath: 'assets/svg/add.svg', // 需要准备恢复图标
                backgroundColor: const Color(0xFFFFFFFF),
                iconColor: const Color(0xFFFFFFFF),
                iconBackgroundColor: const Color(0xFF1E1C1D),
                iconSize: 14,
                textColor: const Color(0xFF8D8E93),
                text: '恢复',
                onPressed: () {
                  _showRestoreDialog(context, ref);
                },
              ),
              // 彻底删除按钮
              CustomSolidButton(
                iconPath: 'assets/svg/delete.svg', // 需要准备永久删除图标
                backgroundColor: const Color(0xFFFFFFFF),
                iconColor: const Color(0xFFFFFFFF),
                iconBackgroundColor: const Color(0xFF1E1C1D),
                iconSize: 14,
                textColor: const Color(0xFF8D8E93),
                text: '彻底删除',
                onPressed: () async {
                  final selectedIds = ref.read(browserTrashTableControllerProvider);
                  if (selectedIds.isEmpty) {
                    if (context.mounted) SnackBarUtil().showInfo(context, '请选择要操作的窗口');
                    return;
                  }

                  // 显示确认删除对话框
                  final bool? confirmed = await showDialog<bool>(
                    context: context,
                    builder: (BuildContext dialogContext) {
                      return AlertDialog(
                        title: const Text('确认彻底删除'),
                        content: Text('您确定要永久删除选中的 ${selectedIds.length} 个窗口吗？此操作不可恢复！'),
                        actions: <Widget>[
                          TextButton(
                            child: const Text('取消'),
                            onPressed: () => Navigator.of(dialogContext).pop(false),
                          ),
                          TextButton(
                            child: const Text('确认'),
                            onPressed: () => Navigator.of(dialogContext).pop(true),
                          ),
                        ],
                      );
                    },
                  );

                  if (confirmed == true) {
                    try {
                      final res = await ref.read(browserTrashControllerProvider.notifier).deleteSelectedTrash(selectedIds);
                      if (context.mounted) SnackBarUtil().showSuccess(context, res);
                    } catch (e) {
                      if (context.mounted) SnackBarUtil().showError(context, e.toString());
                    }
                  }
                },
              ),
            ],
          ),
          const Expanded(child: BrowserTrashTable())
        ],
      ),
    );
  }

  /// 显示恢复确认对话框
  void _showRestoreDialog(BuildContext context, WidgetRef ref) async {
    final selectedIds = ref.watch(browserTrashTableControllerProvider);
    print('selectedIds: $selectedIds');
    if (selectedIds.isEmpty) {
      if (context.mounted) SnackBarUtil().showInfo(context, '请选择要恢复的窗口');
      return;
    }

    final bool? confirmed = await showDialog<bool>(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: const Text('确认恢复'),
          content: Text('您确定要恢复选中的 ${selectedIds.length} 个窗口吗？'),
          actions: <Widget>[
            TextButton(
              child: const Text('取消'),
              onPressed: () => Navigator.of(dialogContext).pop(false),
            ),
            TextButton(
              child: const Text('确认'),
              onPressed: () => Navigator.of(dialogContext).pop(true),
            ),
          ],
        );
      },
    );

    if (confirmed == true) {
      try {
        final res = await ref.read(browserTrashControllerProvider.notifier).restoreSelectedTrash(selectedIds);
        if (context.mounted) SnackBarUtil().showSuccess(context, res);
      } catch (e) {
        if (context.mounted) SnackBarUtil().showError(context, e.toString());
      }
    }
  }
}
