import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:frontend_re/domain/services/group_service.dart';
import 'package:frontend_re/domain/services/proxy_service.dart';
import 'package:frontend_re/domain/models/proxy_model.dart';
import 'package:frontend_re/features/workbench/controllers/browser_config_controller.dart';
import 'package:frontend_re/features/workbench/views/components/browser_add_form_sections/index.dart';

class BrowserAddPageRefactored extends HookConsumerWidget {
  final Object? config;
  
  const BrowserAddPageRefactored({super.key, this.config});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 监听浏览器配置状态
    final browserConfig = ref.watch(browserConfigControllerProvider(config));

    // 控制器
    final platformController = useTextEditingController(
      text: browserConfig.oswSelectedValue,
    );
    final platformVersionController = useTextEditingController(text: '15.0.0');
    final fullVersionController = useTextEditingController(text: '127.0.6533.72');

    // 页面加载时获取分组列表和代理列表
    useEffect(() {
      Future(() {
        ref.read(groupServiceProvider.notifier).getGroups(1);
        ref.read(selfProxyProvider.notifier).getProxies(
          const ProxyListRequest(limit: 100, offset: 0),
        );
      });
      return null;
    }, []);

    return LayoutBuilder(
      builder: (context, constraints) {
        final width = constraints.maxWidth;
        final height = constraints.maxHeight;
        
        return Column(
          children: [
            Expanded(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 左侧表单区域
                  Flexible(
                    child: Container(
                      width: width,
                      height: height,
                      padding: const EdgeInsets.only(top: 20, bottom: 20),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(30),
                      ),
                      child: Column(
                        children: [
                          Expanded(
                            child: SingleChildScrollView(
                              child: Container(
                                padding: const EdgeInsets.only(
                                  left: 20,
                                  right: 20,
                                  bottom: 20,
                                ),
                                child: Column(
                                  spacing: 10,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    // 基础信息区域
                                    BasicInfoSection(
                                      config: config,
                                      browserConfig: browserConfig,
                                    ),
                                    
                                    // 代理信息区域
                                    ProxyInfoSection(
                                      config: config,
                                      browserConfig: browserConfig,
                                    ),
                                    
                                    // 环境设置区域
                                    EnvironmentSection(
                                      config: config,
                                      browserConfig: browserConfig,
                                      fullVersionController: fullVersionController,
                                    ),
                                    
                                    // 高级设置区域
                                    AdvancedSettingsSection(
                                      config: config,
                                      browserConfig: browserConfig,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  
                  const SizedBox(width: 10),
                  
                  // 右侧概要区域
                  Consumer(
                    builder: (context, ref, child) {
                      final browserConfig = ref.watch(
                        browserConfigControllerProvider(config),
                      );
                      return SummarySection(browserConfig: browserConfig);
                    },
                  ),
                ],
              ),
            ),
            
            // 底部按钮组
            ActionButtonsSection(
              config: config,
              browserConfig: browserConfig,
              platformController: platformController,
              platformVersionController: platformVersionController,
              fullVersionController: fullVersionController,
            ),
          ],
        );
      },
    );
  }
} 
