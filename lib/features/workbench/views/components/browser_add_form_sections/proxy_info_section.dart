import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:frontend_re/domain/services/proxy_service.dart';
import 'package:frontend_re/domain/models/proxy_model.dart';
import 'package:frontend_re/features/workbench/controllers/browser_config_controller.dart';
import 'package:frontend_re/features/workbench/models/browser_config_model.dart';
import 'package:frontend_re/widgets/custom_dropdown_menu.dart';

class ProxyInfoSection extends ConsumerWidget {
  final Object? config;
  final BrowserConfigModel browserConfig;

  const ProxyInfoSection({
    super.key,
    required this.config,
    required this.browserConfig,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final proxyServiceState = ref.watch(selfProxyProvider);

    return Column(
      spacing: 10,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 代理信息标题
        Padding(
          padding: const EdgeInsets.only(top: 40),
          child: Row(
            spacing: 8,
            children: [
              Container(
                width: 16,
                height: 16,
                decoration: const BoxDecoration(
                  color: Color(0xFF0C75F8),
                  shape: BoxShape.circle,
                ),
              ),
              const Text(
                '代理信息',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF999999),
                ),
              ),
            ],
          ),
        ),
        
        // 代理选择
        _buildRowTemp(
          '代理',
          SizedBox(
            width: 300,
            child: proxyServiceState.when(
              data: (proxyResponse) {
                final proxyOptions = [
                  {'name': '无代理'},
                  ...proxyResponse.proxies.map((proxy) => {'name': proxy.name}),
                ];
                
                // 查找当前选中的代理名称
                String currentProxyName = '无代理';
                if (browserConfig.proxyId != 0) {
                  final selectedProxy = proxyResponse.proxies
                      .where((p) => p.id == browserConfig.proxyId)
                      .firstOrNull;
                  if (selectedProxy != null) {
                    currentProxyName = selectedProxy.name;
                  }
                }
                
                return CustomDropdownMenu(
                  value: currentProxyName,
                  items: proxyOptions,
                  onChanged: (selectedValue) {
                    if (selectedValue == '无代理') {
                      ref.read(browserConfigControllerProvider(config).notifier)
                          .updateConfig(proxyId: 0, proxyType: 2);
                    } else {
                      final selectedProxy = proxyResponse.proxies
                          .where((p) => p.name == selectedValue)
                          .firstOrNull;
                      if (selectedProxy != null) {
                        // 自有代理的proxyType固定为2
                        ref.read(browserConfigControllerProvider(config).notifier)
                            .updateConfig(
                          proxyId: selectedProxy.id,
                          proxyType: 2,
                        );
                      }
                    }
                  },
                );
              },
              loading: () => CustomDropdownMenu(
                value: '无代理',
                items: const [{'name': '无代理'}],
                onChanged: (value) {},
              ),
              error: (error, stackTrace) => CustomDropdownMenu(
                value: '无代理',
                items: const [{'name': '无代理'}],
                onChanged: (value) {},
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildRowTemp(String leftText, Widget rightWidget) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(width: 90, child: Text(leftText)),
        Flexible(child: rightWidget),
      ],
    );
  }
} 
