import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:frontend_re/core/constants/constant.dart';
import 'package:frontend_re/core/utils/random.dart';
import 'package:frontend_re/core/utils/ua_tool.dart';
import 'package:frontend_re/features/workbench/controllers/browser_config_controller.dart';
import 'package:frontend_re/features/workbench/models/browser_config_model.dart';
import 'package:frontend_re/features/workbench/views/components/browser_add_form_sections/browser_config_helper.dart';
import 'package:frontend_re/widgets/primary_botton.dart';
import 'package:frontend_re/widgets/secondary_botton.dart';

class ActionButtonsSection extends HookConsumerWidget {
  final Object? config;
  final BrowserConfigModel browserConfig;
  final TextEditingController platformController;
  final TextEditingController platformVersionController;
  final TextEditingController fullVersionController;

  const ActionButtonsSection({
    super.key,
    required this.config,
    required this.browserConfig,
    required this.platformController,
    required this.platformVersionController,
    required this.fullVersionController,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 10),
      child: Container(
        height: 60,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(30),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // 一键随机生成指纹按钮
              PrimaryButton(
                child: const Text('一键随机生成指纹'),
                onPressed: () => _onRandomGenerate(ref),
              ),
              
              // 操作按钮组
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SecondaryButton(
                    onPressed: () => Navigator.pop(context),
                    child: config != null ? const Text('返回') : const Text('取消'),
                  ),
                  const SizedBox(width: 10),
                  PrimaryButton(
                    child: config != null ? const Text('更新') : const Text('创建'),
                    onPressed: () => _onSave(context, ref),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 一键随机生成指纹
  void _onRandomGenerate(WidgetRef ref) {
    final randomChrome = BrowserConfigHelper.getRandomItem(chromeVersionOptions);
    final randomOS = BrowserConfigHelper.getRandomItem(osOptions);
    final randomWebGL = BrowserConfigHelper.getRandomItem(webGL);
    final randomResolution = BrowserConfigHelper.getRandomItem(resolution);
    final randomMemory = BrowserConfigHelper.getRandomItem(memory);
    final randomKernel = BrowserConfigHelper.getRandomItem(kernel);

    // 更新各种配置
    ref.read(browserConfigControllerProvider(config).notifier)
        .updateConfig(chromeSelectedValue: randomChrome['name']);
    ref.read(browserConfigControllerProvider(config).notifier)
        .updateConfig(oswSelectedValue: randomOS['name']);
    ref.read(browserConfigControllerProvider(config).notifier)
        .updateConfig(webGLVendor: randomWebGL['name']);
    ref.read(browserConfigControllerProvider(config).notifier)
        .updateConfig(resolution: randomResolution['name']);
    ref.read(browserConfigControllerProvider(config).notifier)
        .updateConfig(memory: randomMemory['name']);
    ref.read(browserConfigControllerProvider(config).notifier)
        .updateConfig(kernel: randomKernel['name']);

    // 生成详细版本和UA
    fullVersionController.text = generateDetailedChromeVersion(randomChrome['name']);
    ref.read(browserConfigControllerProvider(config).notifier)
        .updateConfig(
      ua: generateUserAgent(fullVersionController.text, randomOS['name']),
    );

    // 随机选择WebGL渲染器
    final List<String>? options = vendors[browserConfig.webGLVendor];
    if (options != null && options.isNotEmpty) {
      int random = BrowserConfigHelper.getRandomInt(options.length);
      ref.read(browserConfigControllerProvider(config).notifier)
          .updateConfig(webGLRender: options[random]);
    }

    // 设置平台相关信息
    platformController.text = identifyOperatingSystem(randomOS['name']);
    platformVersionController.text = generateVersion(randomOS['name']);
  }

  /// 保存配置
  Future<void> _onSave(BuildContext context, WidgetRef ref) async {
    await BrowserConfigHelper.generateConfig(
      browserSettings: browserConfig,
      ref: ref,
      context: context,
      platformController: platformController,
      platformVersionController: platformVersionController,
      fullVersionController: fullVersionController,
      configMap: config != null 
          ? jsonDecode(config as String) as Map<String, dynamic> 
          : null,
    );
  }
} 
