import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:frontend_re/domain/services/group_service.dart';
import 'package:frontend_re/features/workbench/controllers/browser_config_controller.dart';
import 'package:frontend_re/features/workbench/models/browser_config_model.dart';
import 'package:frontend_re/widgets/custom_dropdown_menu.dart';
import 'package:frontend_re/widgets/custom_text_field.dart';

class BasicInfoSection extends HookConsumerWidget {
  final Object? config;
  final BrowserConfigModel browserConfig;

  const BasicInfoSection({
    super.key,
    required this.config,
    required this.browserConfig,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final textControllerName = useTextEditingController(text: browserConfig.name);
    final textControllerTag = useTextEditingController(text: browserConfig.tag);
    final textControllerComment = useTextEditingController(text: browserConfig.comment);
    
    // 分组相关状态
    final selectedGroupId = useState<int?>(null);
    final selectedGroupName = useState<String>('无分组');
    
    // 获取分组列表
    final groupServiceState = ref.watch(groupServiceProvider);
    
    // 初始化分组状态
    useEffect(() {
      if (config == null) {
        selectedGroupId.value = browserConfig.groupId == 0 ? null : browserConfig.groupId;
        selectedGroupName.value = '无分组';
      }
      return null;
    }, [config]);

    return Column(
      spacing: 10,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 基础信息标题
        Row(
          spacing: 8,
          children: [
            Container(
              width: 16,
              height: 16,
              decoration: const BoxDecoration(
                color: Color(0xFF0C75F8),
                shape: BoxShape.circle,
              ),
            ),
            const Text(
              '基础信息',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Color(0xFF999999),
              ),
            ),
          ],
        ),
        
        // 名称
        _buildRowTemp(
          '名称',
          ConstrainedBox(
            constraints: const BoxConstraints(minWidth: 200),
            child: IntrinsicWidth(
              child: CustomTextField(
                controller: textControllerName,
                hint: "选填",
                lines: 1,
                onChanged: (value) {
                  ref.read(browserConfigControllerProvider(config).notifier)
                      .updateConfig(name: value);
                },
              ),
            ),
          ),
        ),
        
        // 分组
        _buildRowTemp(
          '分组',
          SizedBox(
            width: 200,
            child: groupServiceState.when(
              data: (groupResponse) {
                final groupOptions = [
                  {'name': '无分组'},
                  ...groupResponse.groups.map((group) => {'name': group.name}).toList(),
                ];
                
                String currentDisplayValue = selectedGroupName.value;
                
                // 编辑模式时从config中获取分组信息
                if (config != null && selectedGroupName.value == '无分组') {
                  try {
                    Map<String, dynamic> configMap;
                    if (config is String) {
                      configMap = jsonDecode(config as String) as Map<String, dynamic>;
                    } else if (config is Map<String, dynamic>) {
                      configMap = config as Map<String, dynamic>;
                    } else {
                      configMap = jsonDecode(config.toString()) as Map<String, dynamic>;
                    }
                    
                    final groupId = configMap['group_id'] as int?;
                    final groupName = configMap['group_name'] as String?;
                    
                    if (groupId != null && groupId != 0) {
                      if (groupName != null && groupName.isNotEmpty) {
                        currentDisplayValue = groupName;
                        Future.microtask(() {
                          selectedGroupId.value = groupId;
                          selectedGroupName.value = groupName;
                        });
                      } else {
                        try {
                          final group = groupResponse.groups.firstWhere(
                            (g) => g.id == groupId,
                          );
                          currentDisplayValue = group.name;
                          Future.microtask(() {
                            selectedGroupId.value = groupId;
                            selectedGroupName.value = group.name;
                          });
                        } catch (e) {
                          currentDisplayValue = '无分组';
                        }
                      }
                    }
                  } catch (e) {
                    // 解析失败时使用默认值
                  }
                }
                
                return CustomDropdownMenu(
                  height: 40,
                  hintText: '请选择分组',
                  items: groupOptions,
                  value: currentDisplayValue,
                  onChanged: (value) {
                    selectedGroupName.value = value ?? '无分组';
                    
                    if (value == '无分组') {
                      selectedGroupId.value = null;
                    } else {
                      final group = groupResponse.groups.firstWhere(
                        (g) => g.name == value,
                        orElse: () => throw StateError('Group not found'),
                      );
                      selectedGroupId.value = group.id;
                    }
                    
                    ref.read(browserConfigControllerProvider(config).notifier)
                        .updateConfig(groupId: selectedGroupId.value ?? 0);
                  },
                );
              },
              loading: () => CustomDropdownMenu(
                height: 40,
                hintText: '加载中...',
                items: const [],
                value: '加载中...',
                onChanged: (value) {},
              ),
              error: (error, stack) => CustomDropdownMenu(
                height: 40,
                hintText: '加载失败',
                items: const [{'name': '无分组'}],
                value: '无分组',
                onChanged: (value) {
                  selectedGroupName.value = '无分组';
                  selectedGroupId.value = null;
                  ref.read(browserConfigControllerProvider(config).notifier)
                      .updateConfig(groupId: 0);
                },
              ),
            ),
          ),
        ),
        
        // 标签
        _buildRowTemp(
          '标签',
          CustomTextField(
            controller: textControllerTag,
            hint: "选填，多个标签用逗号分隔，例如：标签1,标签2,标签3",
            onChanged: (value) {
              ref.read(browserConfigControllerProvider(config).notifier)
                  .updateConfig(tag: value);
            },
          ),
        ),
        
        // 备注
        _buildRowTemp(
          '备注',
          SizedBox(
            width: 600,
            child: TextField(
              maxLines: 4,
              minLines: 4,
              controller: textControllerComment,
              enabled: true,
              style: const TextStyle(color: Color(0xFF666666), fontSize: 14),
              onChanged: (value) {
                ref.read(browserConfigControllerProvider(config).notifier)
                    .updateConfig(comment: value);
              },
              decoration: InputDecoration(
                hintText: "选填",
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Colors.transparent),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Colors.transparent),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Color(0xFF0C75F8)),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildRowTemp(String leftText, Widget rightWidget) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(width: 90, child: Text(leftText)),
        Flexible(child: rightWidget),
      ],
    );
  }
} 
