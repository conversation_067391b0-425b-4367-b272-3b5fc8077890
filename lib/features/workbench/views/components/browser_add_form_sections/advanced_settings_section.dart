import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:frontend_re/core/constants/constant.dart';
import 'package:frontend_re/features/workbench/views/components/browser_add_form_sections/browser_config_helper.dart';
import 'package:frontend_re/features/workbench/controllers/browser_config_controller.dart';
import 'package:frontend_re/features/workbench/models/browser_config_model.dart';
import 'package:frontend_re/widgets/custom_dropdown_menu.dart';
import 'package:frontend_re/widgets/custom_switch.dart';

class AdvancedSettingsSection extends HookConsumerWidget {
  final Object? config;
  final BrowserConfigModel browserConfig;

  const AdvancedSettingsSection({
    super.key,
    required this.config,
    required this.browserConfig,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final textControllerLocation = useTextEditingController(
      text: browserConfig.location,
    );
    final textControllerWebGL = useTextEditingController(
      text: browserConfig.webGLRender,
    );

    return Column(
      spacing: 10,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 语言设置
        _buildRowTemp(
          '语言',
          Row(
            spacing: 6,
            children: [
              CustomSwitch(
                options: const ['基于IP', '自定义'],
                selectedIndex: browserConfig.evaluateLanguage ? 0 : 1,
                onChanged: (selectedIndex) {
                  ref.read(browserConfigControllerProvider(config).notifier)
                      .updateConfig(evaluateLanguage: selectedIndex == 0);
                },
              ),
              if (!browserConfig.evaluateLanguage)
                SizedBox(
                  width: 200,
                  child: CustomDropdownMenu(
                    value: browserConfig.languageSelectedValue,
                    items: languageLocaleOptions,
                    onChanged: (selectedValue) {
                      ref.read(browserConfigControllerProvider(config).notifier)
                          .updateConfig(languageSelectedValue: selectedValue);
                    },
                  ),
                ),
            ],
          ),
        ),

        // 界面语言设置
        _buildRowTemp(
          '界面语言',
          Row(
            spacing: 6,
            children: [
              CustomSwitch(
                options: const ['基于IP', '自定义'],
                selectedIndex: browserConfig.localeEvaluateLanguage ? 0 : 1,
                onChanged: (selectedIndex) {
                  ref.read(browserConfigControllerProvider(config).notifier)
                      .updateConfig(localeEvaluateLanguage: selectedIndex == 0);
                },
              ),
              if (!browserConfig.localeEvaluateLanguage)
                SizedBox(
                  width: 200,
                  child: CustomDropdownMenu(
                    value: browserConfig.locale,
                    items: languageLocaleOptions,
                    onChanged: (selectedValue) {
                      ref.read(browserConfigControllerProvider(config).notifier)
                          .updateConfig(locale: selectedValue);
                    },
                  ),
                ),
            ],
          ),
        ),

        // 时区设置
        _buildRowTemp(
          '时区',
          Row(
            spacing: 6,
            children: [
              CustomSwitch(
                options: const ['基于IP', '自定义'],
                selectedIndex: browserConfig.timezone ? 0 : 1,
                onChanged: (selectedIndex) {
                  ref.read(browserConfigControllerProvider(config).notifier)
                      .updateConfig(timezone: selectedIndex == 0);
                },
              ),
              if (!browserConfig.timezone)
                SizedBox(
                  width: 200,
                  child: CustomDropdownMenu(
                    value: browserConfig.timezoneSelectedValue,
                    items: timezone,
                    onChanged: (selectedValue) {
                      ref.read(browserConfigControllerProvider(config).notifier)
                          .updateConfig(timezoneSelectedValue: selectedValue);
                    },
                  ),
                ),
            ],
          ),
        ),

        // 经纬度设置
        _buildRowTemp(
          '经纬度',
          Row(
            spacing: 6,
            children: [
              CustomSwitch(
                options: const ['基于IP', '自定义'],
                selectedIndex: browserConfig.locationByIP ? 0 : 1,
                onChanged: (selectedIndex) {
                  ref.read(browserConfigControllerProvider(config).notifier)
                      .updateConfig(locationByIP: selectedIndex == 0);
                },
              ),
              if (!browserConfig.locationByIP)
                Flexible(
                  child: ConstrainedBox(
                    constraints: const BoxConstraints(maxWidth: 400),
                    child: _buildInputField(
                      hint: '经,纬,海拔,精度(用逗号隔开)',
                      controller: textControllerLocation,
                      onChanged: (value) {
                        ref.read(browserConfigControllerProvider(config).notifier)
                            .updateConfig(location: value);
                      },
                    ),
                  ),
                ),
            ],
          ),
        ),

        // WebGL设置
        _buildRowTemp(
          'WebGL',
          CustomSwitch(
            options: const ['开启', '关闭'],
            selectedIndex: browserConfig.webGL ? 0 : 1,
            onChanged: (selectedIndex) {
              ref.read(browserConfigControllerProvider(config).notifier)
                  .updateConfig(webGL: selectedIndex == 0);
            },
          ),
        ),

        // WebGL厂商
        _buildRowTemp(
          'WebGL厂商',
          SizedBox(
            width: 300,
            child: CustomDropdownMenu(
              value: browserConfig.webGLVendor,
              items: vendors.keys.map((vendor) => {'name': vendor}).toList(),
              onChanged: (selectedValue) {
                if (selectedValue != null) {
                  final List<String>? renderers = vendors[selectedValue];
                  if (renderers != null && renderers.isNotEmpty) {
                    final int random = BrowserConfigHelper.getRandomInt(renderers.length);
                    final String renderer = renderers[random];
                    ref.read(browserConfigControllerProvider(config).notifier)
                        .updateConfig(
                      webGLVendor: selectedValue,
                      webGLRender: renderer,
                    );
                  }
                }
              },
            ),
          ),
        ),

        // WebGL渲染
        _buildRowTemp(
          'WebGL渲染',
          _buildWebGLRender(ref, textControllerWebGL),
        ),

        // Plugin设置
        _buildRowTemp(
          'Plugin',
          CustomSwitch(
            options: const ['开启', '关闭'],
            selectedIndex: browserConfig.plugin ? 0 : 1,
            onChanged: (selectedIndex) {
              ref.read(browserConfigControllerProvider(config).notifier)
                  .updateConfig(plugin: selectedIndex == 0);
            },
          ),
        ),

        // Canvas设置
        _buildRowTemp(
          'Canvas',
          CustomSwitch(
            options: const ['开启', '关闭'],
            selectedIndex: browserConfig.canvas ? 0 : 1,
            onChanged: (selectedIndex) {
              ref.read(browserConfigControllerProvider(config).notifier)
                  .updateConfig(canvas: selectedIndex == 0);
            },
          ),
        ),

        // 字体设置
        _buildRowTemp(
          '字体',
          CustomSwitch(
            options: const ['开启', '关闭'],
            selectedIndex: browserConfig.font ? 0 : 1,
            onChanged: (selectedIndex) {
              ref.read(browserConfigControllerProvider(config).notifier)
                  .updateConfig(font: selectedIndex == 0);
            },
          ),
        ),

        // ClientRects设置
        _buildRowTemp(
          'ClientRects',
          CustomSwitch(
            options: const ['开启', '关闭'],
            selectedIndex: browserConfig.clientRects ? 0 : 1,
            onChanged: (selectedIndex) {
              ref.read(browserConfigControllerProvider(config).notifier)
                  .updateConfig(clientRects: selectedIndex == 0);
            },
          ),
        ),

        // AudioVoice设置
        _buildRowTemp(
          'AudioVoice',
          CustomSwitch(
            options: const ['开启', '关闭'],
            selectedIndex: browserConfig.audioContext ? 0 : 1,
            onChanged: (selectedIndex) {
              ref.read(browserConfigControllerProvider(config).notifier)
                  .updateConfig(audioContext: selectedIndex == 0);
            },
          ),
        ),

        // 分辨率设置
        _buildRowTemp(
          '分辨率',
          CustomDropdownMenu(
            width: 200,
            value: browserConfig.resolution,
            items: resolution,
            onChanged: (selectedValue) {
              ref.read(browserConfigControllerProvider(config).notifier)
                  .updateConfig(resolution: selectedValue);
            },
          ),
        ),

        // 设备内存
        _buildRowTemp(
          '设备内存',
          CustomDropdownMenu(
            width: 200,
            value: browserConfig.memory,
            items: memory,
            onChanged: (selectedValue) {
              ref.read(browserConfigControllerProvider(config).notifier)
                  .updateConfig(memory: selectedValue);
            },
          ),
        ),

        // 设备CPU
        _buildRowTemp(
          '设备CPU',
          CustomDropdownMenu(
            width: 200,
            value: browserConfig.kernel,
            items: kernel,
            onChanged: (selectedValue) {
              ref.read(browserConfigControllerProvider(config).notifier)
                  .updateConfig(kernel: selectedValue);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildRowTemp(String leftText, Widget rightWidget) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(width: 90, child: Text(leftText)),
        Flexible(child: rightWidget),
      ],
    );
  }

  Widget _buildInputField({
    required String hint,
    required TextEditingController controller,
    Function(String)? onChanged,
  }) {
    return SizedBox(
      height: 40,
      child: TextField(
        controller: controller,
        onChanged: onChanged,
        decoration: InputDecoration(
          hintText: hint,
          hintStyle: const TextStyle(color: Color(0xFF666666), fontSize: 14),
          filled: true,
          fillColor: const Color(0xFFF3F4F8),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(50),
            borderSide: const BorderSide(color: Colors.black45),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(50),
            borderSide: const BorderSide(color: Color(0xFF0C75F8)),
          ),
        ),
      ),
    );
  }

  Widget _buildWebGLRender(WidgetRef ref, TextEditingController controller) {
    return SizedBox(
      width: 600,
      child: TextField(
        maxLines: 4,
        minLines: 4,
        controller: controller,
        enabled: true,
        style: const TextStyle(color: Color(0xFF666666), fontSize: 14),
        decoration: InputDecoration(
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(color: Colors.transparent),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(color: Colors.transparent),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(color: Color(0xFF0C75F8)),
          ),
          suffixIcon: IconButton(
            icon: const Icon(
              Icons.update,
              color: Colors.amber,
              size: 16,
            ),
            onPressed: () {
              final List<String>? options = vendors[browserConfig.webGLVendor];
              if (options != null && options.isNotEmpty) {
                                 int random = BrowserConfigHelper.getRandomInt(options.length);
                final String renderer = options[random];
                controller.text = renderer;
                ref.read(browserConfigControllerProvider(config).notifier)
                    .updateConfig(webGLRender: renderer);
              }
            },
          ),
        ),
      ),
    );
  }
} 
