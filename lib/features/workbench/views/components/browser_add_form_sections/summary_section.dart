import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:frontend_re/core/constants/constant.dart';
import 'package:frontend_re/features/workbench/models/browser_config_model.dart';

class SummarySection extends ConsumerWidget {
  final BrowserConfigModel browserConfig;

  const SummarySection({
    super.key,
    required this.browserConfig,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      padding: const EdgeInsets.only(top: 20, bottom: 20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(30),
      ),
      child: SingleChildScrollView(
        child: Container(
          width: 360,
          padding: const EdgeInsets.only(left: 20, right: 20),
          child: Column(
            spacing: 10,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 概要标题
              Row(
                spacing: 10,
                children: [
                  Container(
                    height: 16,
                    width: 16,
                    decoration: BoxDecoration(
                      color: const Color(0xFF0C75F8),
                      borderRadius: BorderRadius.circular(50),
                    ),
                  ),
                  const Text(
                    '概要',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              
              // 分隔线
              Container(
                height: 2,
                width: 300,
                decoration: BoxDecoration(
                  color: const Color(0xFFF8F8F8),
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              
              // 配置项列表
              ..._buildConfigItems(),
            ],
          ),
        ),
      ),
    );
  }

  List<Widget> _buildConfigItems() {
    List<Widget> items = [];

    // 窗口名称
    if (browserConfig.name.isNotEmpty) {
      items.add(_buildRowTextTemp('窗口名称', browserConfig.name));
    }

    // 用户名
    if (browserConfig.accountUsername.isNotEmpty) {
      items.add(_buildRowTextTemp('用户名', browserConfig.accountUsername));
    }

    // 密码
    if (browserConfig.accountPassword.isNotEmpty) {
      items.add(_buildRowTextTemp('密码', browserConfig.accountPassword));
    }

    // 账号平台
    if (browserConfig.account.isNotEmpty) {
      items.add(_buildRowTextTemp('账号平台', browserConfig.account));
    }

    // 浏览器版本
    items.add(_buildRowTextTemp('浏览器版本', browserConfig.chromeSelectedValue));

    // 操作系统
    items.add(_buildRowTextTemp('操作系统', browserConfig.oswSelectedValue));

    // UA环境
    items.add(_buildRowTextTemp('UA环境', browserConfig.ua));

    // 语言
    items.add(_buildRowTextTemp(
      '语言',
      browserConfig.evaluateLanguage ? '基于IP' : browserConfig.languageSelectedValue,
    ));

    // 界面语言
    items.add(_buildRowTextTemp(
      '界面语言',
      browserConfig.localeEvaluateLanguage ? '基于IP' : browserConfig.locale,
    ));

    // 时区
    items.add(_buildRowTextTemp(
      '时区',
      browserConfig.timezone ? '基于IP' : browserConfig.timezoneSelectedValue,
    ));

    // 经纬度
    items.add(_buildRowTextTemp(
      '经纬度',
      browserConfig.locationByIP ? '基于IP' : browserConfig.location,
    ));

    // WebRTC
    items.add(_buildRowTextTemp(
      'WebRTC',
      webRTCOptions.firstWhere((e) => e.value == browserConfig.webrtc).label,
    ));

    // WebGL
    items.add(_buildRowTextTemp('WebGL', browserConfig.webGL ? '开启' : '关闭'));

    // WebGL厂商
    items.add(_buildRowTextTemp('WebGL厂商', browserConfig.webGLVendor));

    // WebGL渲染
    items.add(_buildRowTextTemp('WebGL渲染', browserConfig.webGLRender));

    // Plugin
    items.add(_buildRowTextTemp('Plugin', browserConfig.plugin ? '开启' : '关闭'));

    // 字体
    items.add(_buildRowTextTemp('字体', browserConfig.font ? '开启' : '关闭'));

    // ClientRects
    items.add(_buildRowTextTemp('ClientRects', browserConfig.clientRects ? '开启' : '关闭'));

    // AudioVoice
    items.add(_buildRowTextTemp('AudioVoice', browserConfig.audioContext ? '开启' : '关闭'));

    // 分辨率
    if (browserConfig.resolution.isNotEmpty) {
      items.add(_buildRowTextTemp('分辨率', browserConfig.resolution));
    }

    // 内存
    if (browserConfig.memory.isNotEmpty) {
      items.add(_buildRowTextTemp('内存', browserConfig.memory));
    }

    // CPU
    if (browserConfig.kernel.isNotEmpty) {
      items.add(_buildRowTextTemp('CPU', browserConfig.kernel));
    }

    return items;
  }

  Widget _buildRowTextTemp(String leftText, String rightText) {
    return Row(
      spacing: 30,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          leftText,
          style: const TextStyle(color: Color(0xFF999999)),
        ),
        Expanded(
          child: Text(
            rightText,
            style: const TextStyle(color: Color(0xFF666666)),
            textAlign: TextAlign.right,
          ),
        ),
      ],
    );
  }
} 
