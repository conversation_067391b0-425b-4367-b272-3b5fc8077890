import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:frontend_re/core/constants/constant.dart';
import 'package:frontend_re/core/utils/ua_tool.dart';
import 'package:frontend_re/features/workbench/controllers/browser_config_controller.dart';
import 'package:frontend_re/features/workbench/models/browser_config_model.dart';
import 'package:frontend_re/features/workbench/views/components/platform_select_dialog.dart';
import 'package:frontend_re/widgets/custom_dropdown_menu.dart';
import 'package:frontend_re/widgets/custom_text_field.dart';

class EnvironmentSection extends HookConsumerWidget {
  final Object? config;
  final BrowserConfigModel browserConfig;
  final TextEditingController fullVersionController;

  const EnvironmentSection({
    super.key,
    required this.config,
    required this.browserConfig,
    required this.fullVersionController,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final textControllerUsername = useTextEditingController(
      text: browserConfig.accountUsername,
    );
    final textControllerPassword = useTextEditingController(
      text: browserConfig.accountPassword,
    );
    final textControllerCookie = useTextEditingController(
      text: browserConfig.cookie,
    );
    final textControllerUA = useTextEditingController(text: browserConfig.ua);

    return Column(
      spacing: 10,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 环境设置标题
        Padding(
          padding: const EdgeInsets.only(top: 40),
          child: Row(
            spacing: 8,
            children: [
              Container(
                width: 16,
                height: 16,
                decoration: const BoxDecoration(
                  color: Color(0xFF0C75F8),
                  shape: BoxShape.circle,
                ),
              ),
              const Text(
                '环境设置',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF999999),
                ),
              ),
            ],
          ),
        ),
        
        // 平台类型
        _buildRowTemp(
          '平台类型',
          MouseRegion(
            cursor: SystemMouseCursors.click,
            child: GestureDetector(
              onTap: () {
                showDialog(
                  context: context,
                  builder: (context) => PlatformSelectDialog(config: config),
                );
              },
              child: Container(
                width: 500,
                height: 50,
                decoration: BoxDecoration(
                  color: const Color(0xFFF3F4F8),
                  borderRadius: BorderRadius.circular(50),
                ),
                child: Padding(
                  padding: const EdgeInsets.only(left: 16, right: 12),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                        browserConfig.account,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Color(0xFF666666),
                        ),
                      ),
                      const Spacer(),
                      const Icon(
                        Icons.keyboard_arrow_down,
                        size: 16,
                        color: Color(0xFF666666),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
        
        // 用户名
        _buildRowTemp(
          '用户名',
          ConstrainedBox(
            constraints: const BoxConstraints(minWidth: 200),
            child: IntrinsicWidth(
              child: CustomTextField(
                controller: textControllerUsername,
                hint: "选填",
                lines: 1,
                onChanged: (value) {
                  ref.read(browserConfigControllerProvider(config).notifier)
                      .updateConfig(accountUsername: value);
                },
              ),
            ),
          ),
        ),
        
        // 密码
        _buildRowTemp(
          '密码',
          ConstrainedBox(
            constraints: const BoxConstraints(minWidth: 200),
            child: IntrinsicWidth(
              child: CustomTextField(
                controller: textControllerPassword,
                hint: "选填",
                lines: 1,
                onChanged: (value) {
                  ref.read(browserConfigControllerProvider(config).notifier)
                      .updateConfig(accountPassword: value);
                },
              ),
            ),
          ),
        ),
        
        // Cookie
        _buildRowTemp(
          'Cookie',
          SizedBox(
            width: 600,
            child: TextField(
              maxLines: 4,
              minLines: 4,
              controller: textControllerCookie,
              enabled: true,
              style: const TextStyle(color: Color(0xFF666666), fontSize: 14),
              onChanged: (value) {
                ref.read(browserConfigControllerProvider(config).notifier)
                    .updateConfig(cookie: value);
              },
              decoration: InputDecoration(
                hintText: "选填",
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Colors.transparent),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Colors.transparent),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Color(0xFF0C75F8)),
                ),
              ),
            ),
          ),
        ),
        
        // 浏览器版本
        _buildRowTemp(
          '浏览器版本',
          SizedBox(
            width: 200,
            child: CustomDropdownMenu(
              value: browserConfig.chromeSelectedValue,
              items: chromeVersionOptions,
              onChanged: (selectedValue) {
                fullVersionController.text = 
                    generateDetailedChromeVersion(selectedValue!);
                ref.read(browserConfigControllerProvider(config).notifier)
                    .updateConfig(chromeSelectedValue: selectedValue);
                ref.read(browserConfigControllerProvider(config).notifier)
                    .updateConfig(
                  ua: generateUserAgent(
                    fullVersionController.text,
                    browserConfig.oswSelectedValue,
                  ),
                );
              },
            ),
          ),
        ),
        
        // 操作系统
        _buildRowTemp(
          '操作系统',
          SizedBox(
            width: 200,
            child: CustomDropdownMenu(
              value: browserConfig.oswSelectedValue,
              items: osOptions,
              onChanged: (selectedValue) {
                fullVersionController.text = 
                    generateDetailedChromeVersion(browserConfig.chromeSelectedValue);
                ref.read(browserConfigControllerProvider(config).notifier)
                    .updateConfig(oswSelectedValue: selectedValue);
                ref.read(browserConfigControllerProvider(config).notifier)
                    .updateConfig(
                  ua: generateUserAgent(
                    fullVersionController.text,
                    selectedValue ?? 'Windows 11',
                  ),
                );
              },
            ),
          ),
        ),
        
        // UA环境设置
        _buildRowTemp(
          'UA环境设置',
          Consumer(
            builder: (context, ref, child) {
              final ua = ref.watch(
                browserConfigControllerProvider(config)
                    .select((value) => value.ua),
              );
              textControllerUA.text = ua;
              return _buildUaField(ref, textControllerUA);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildRowTemp(String leftText, Widget rightWidget) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(width: 90, child: Text(leftText)),
        Flexible(child: rightWidget),
      ],
    );
  }

  Widget _buildUaField(WidgetRef ref, TextEditingController controller) {
    return SizedBox(
      child: TextField(
        maxLines: 4,
        minLines: 4,
        controller: controller,
        enabled: true,
        style: const TextStyle(color: Color(0xFF666666), fontSize: 14),
        decoration: InputDecoration(
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(color: Colors.transparent),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(color: Colors.transparent),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(color: Color(0xFF0C75F8)),
          ),
          suffixIcon: IconButton(
            icon: const Icon(
              Icons.update,
              color: Colors.amber,
              size: 16,
            ),
            onPressed: () {
              fullVersionController.text = 
                  generateDetailedChromeVersion(browserConfig.chromeSelectedValue);
              ref.read(browserConfigControllerProvider(config).notifier)
                  .updateConfig(
                ua: generateUserAgent(
                  fullVersionController.text,
                  browserConfig.oswSelectedValue,
                ),
              );
            },
          ),
        ),
      ),
    );
  }
} 
