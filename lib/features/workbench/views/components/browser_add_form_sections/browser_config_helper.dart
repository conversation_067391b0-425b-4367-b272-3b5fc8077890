import 'dart:convert';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:frontend_re/core/constants/constant.dart';
import 'package:frontend_re/core/utils/random.dart';
import 'package:frontend_re/core/utils/ua_tool.dart';
import 'package:frontend_re/domain/models/browser_window_model.dart';
import 'package:frontend_re/features/workbench/models/browser_config_model.dart';
import 'package:frontend_re/features/workbench/controllers/browser_window_controller.dart';
import 'package:frontend_re/core/utils/ui/snack_bar_util.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class BrowserConfigHelper {
  // 生成随机RGB值
  static String setRGB() {
    final Random random = Random();
    final int rand1 = random.nextInt(5);
    final int rand2 = random.nextInt(6);
    final int rand3 = random.nextInt(7);
    final int rand4 = random.nextInt(255);
    return '$rand1,$rand2,$rand3,$rand4';
  }

  // 生成Canvas配置，噪声值在0.5-3之间
  static Map<String, String> generateCanvasConfig() {
    final Random random = Random();
    final double noise = 0.5 + (random.nextDouble() * 2.5);
    final String noiseStr = noise.toStringAsFixed(2);
    return {"noise": noiseStr};
  }

  // 生成0-100之间的随机数
  static int generateRandomNumber() {
    final Random random = Random();
    return random.nextInt(101);
  }

  // 生成ClientRect配置
  static Map<String, String> generateClientRectConfig() {
    return {"x": "0.0", "y": "0.0"};
  }

  // 生成屏幕配置
  static Map<String, String> generateScreenConfig(Map<String, int> dimensions) {
    final Random random = Random();
    final int width = dimensions['width'] ?? 1920;
    final int height = dimensions['height'] ?? 1080;
    
    final int taskbarHeight = 20 + random.nextInt(61);
    final int sidebarWidth = random.nextInt(21);
    
    final double availWidth = (width - sidebarWidth).toDouble();
    final double availHeight = (height - taskbarHeight).toDouble();
    final double colorDepth = 24.0;
    
    return {
      "width": width.toString(),
      "height": height.toString(),
      "colorDepth": colorDepth.toString(),
      "availWidth": availWidth.toString(),
      "availHeight": availHeight.toString()
    };
  }

  // 生成客户端提示配置
  static Map<String, String> generateClientHintConfig(
    String? platform,
    String? platformVersion,
    String? uaFullVersion,
  ) {
    String finalPlatform = platform ?? "Windows";
    String finalPlatformVersion = platformVersion ?? "15.0.0";
    String finalUaFullVersion = uaFullVersion ?? "118.0.0.0";

    String navigatorPlatform;
    if (finalPlatform.startsWith("Windows")) {
      navigatorPlatform = "Win32";
      if (platformVersion == null) {
        if (finalPlatform.contains("11")) {
          finalPlatformVersion = "15.0.0";
        } else if (finalPlatform.contains("10")) {
          finalPlatformVersion = "10.0.0";
        } else {
          finalPlatformVersion = "15.0.0";
        }
      }
    } else if (finalPlatform.startsWith("macOS")) {
      navigatorPlatform = "MacIntel";
      if (platformVersion == null) {
        if (finalPlatform.contains("14")) {
          finalPlatformVersion = "14.0.0";
        } else if (finalPlatform.contains("13")) {
          finalPlatformVersion = "13.0.0";
        } else if (finalPlatform.contains("12")) {
          finalPlatformVersion = "12.0.0";
        } else if (finalPlatform.contains("11")) {
          finalPlatformVersion = "11.0.0";
        } else {
          finalPlatformVersion = "14.0.0";
        }
      }
    } else if (finalPlatform.startsWith("Linux")) {
      navigatorPlatform = "Linux x86_64";
      finalPlatformVersion = "0.0.0";
    } else {
      navigatorPlatform = "Win32";
      finalPlatformVersion = "15.0.0";
    }

    if (finalPlatform.startsWith("Windows")) {
      finalPlatform = "Windows";
    } else if (finalPlatform.startsWith("macOS")) {
      finalPlatform = "macOS";
    }

    return {
      "platform": finalPlatform,
      "navigator.platform": navigatorPlatform,
      "platform_version": finalPlatformVersion,
      "ua_full_version": finalUaFullVersion,
      "mobile": "?0",
      "architecture": "x64",
      "bitness": "64"
    };
  }

  // 解析分辨率字符串
  static Map<String, int> splitResolution(String resolution) {
    final parts = resolution.split('*');
    if (parts.length == 2) {
      final int? width = int.tryParse(parts[0]);
      final int? height = int.tryParse(parts[1]);
      if (width != null && height != null) {
        return {'width': width, 'height': height};
      }
    }
    return {'width': 0, 'height': 0};
  }

  // 生成随机偏移量
  static int getRandomOffset() => Random().nextInt(20);

  // 生成随机整数（针对特定条件）
  static int getRandomIntForFoo2Modern() {
    final int tmp = Random().nextInt(10);
    return tmp > 0 ? 0 : 1;
  }

  // 生成填充文本的随机整数
  static int getRandomFillText() => Random().nextInt(3);

  // 生成ClientRect的随机整数
  static int getRandomClientRect() => Random().nextInt(4);

  // 生成WebGL的随机小数
  static double getRandomFloatWebGL() {
    final Random random = Random();
    const double min = 0.0000001;
    const double max = 0.0009999;
    return min + (max - min) * random.nextDouble();
  }

  // 获取随机整数
  static int getRandomInt(int max) => Random().nextInt(max);

  // 从列表中获取随机项
  static T getRandomItem<T>(List<T> list) {
    final random = Random();
    int index = random.nextInt(list.length);
    return list[index];
  }

  // 将标签字符串转换为JSON数组
  static String convertTagsToJson(String tags) {
    if (tags.isEmpty) return '[]';
    
    final List<String> tagList = tags
        .replaceAll('，', ',')
        .split(',')
        .map((tag) => tag.trim())
        .where((tag) => tag.isNotEmpty)
        .toList();
    
    return jsonEncode(tagList);
  }

  // 生成完整的浏览器配置
  static Future<String> generateConfig({
    required BrowserConfigModel browserSettings,
    required WidgetRef ref,
    required BuildContext context,
    required TextEditingController platformController,
    required TextEditingController platformVersionController,
    required TextEditingController fullVersionController,
    required Map<String, dynamic>? configMap,
  }) async {
    Map<String, int> dimensions = splitResolution(browserSettings.resolution);
    final String name = browserSettings.name.isNotEmpty
        ? browserSettings.name
        : generateRandomString(6);

    final oldConfig = browserSettings.editing ? configMap : null;
    final id = browserSettings.editing ? oldConfig!['id'] : 0;
    
    // 生成或保留各种随机值
    final sampleRate = browserSettings.editing
        ? oldConfig!['sample_rate']
        : getRandomInt(100).toString();
    final plugin = browserSettings.editing
        ? oldConfig!['plugin']
        : getRandomInt(10).toString();
    final font = browserSettings.editing
        ? oldConfig!['font']
        : getRandomOffset().toString();
    final offset1 = browserSettings.editing
        ? oldConfig!['offset1']
        : getRandomIntForFoo2Modern().toString();
    final fillText = browserSettings.editing
        ? oldConfig!['fill_text']
        : getRandomFillText().toString();
    final clientRect = browserSettings.editing
        ? oldConfig!['client_rect']
        : getRandomClientRect().toString();
    final webglPicture = browserSettings.editing
        ? oldConfig!['webgl_picture']
        : getRandomOffset().toString();
    final webgl = browserSettings.editing
        ? oldConfig!['webgl']
        : getRandomFloatWebGL().toString();
    final rgb = browserSettings.editing ? oldConfig!['rgb'] : setRGB();

    // 准备设置数据
    final Map<String, dynamic> settings = {
      // UI状态恢复参数
      'ui_name': name,
      'ui_account': browserSettings.account,
      'ui_account_username': browserSettings.accountUsername,
      'ui_account_password': browserSettings.accountPassword,
      'ui_cookie': browserSettings.cookie,
      'ui_chrome_version': browserSettings.chromeSelectedValue,
      'ui_os_version': browserSettings.oswSelectedValue,
      'ui_ua': browserSettings.ua,
      'ui_language_ip': browserSettings.evaluateLanguage,
      'ui_language_value': browserSettings.languageSelectedValue,
      'ui_locale_ip': browserSettings.localeEvaluateLanguage,
      'ui_locale_value': browserSettings.locale,
      'ui_timezone_ip': browserSettings.timezone,
      'ui_timezone_value': browserSettings.timezoneSelectedValue,
      'ui_location_ip': browserSettings.locationByIP,
      'ui_location': browserSettings.location,
      'ui_webrtc': browserSettings.webrtc,
      'ui_canvas': browserSettings.canvas,
      'ui_webgl': browserSettings.webGL,
      'ui_webgl_vendor': browserSettings.webGLVendor,
      'ui_webgl_render': browserSettings.webGLRender,
      'ui_plugin': browserSettings.plugin,
      'ui_font': browserSettings.font,
      'ui_resolution': browserSettings.resolution,
      'ui_audio_context': browserSettings.audioContext,
      'ui_client_rect': browserSettings.clientRects,
      'ui_memory': browserSettings.memory,
      'ui_kernel': browserSettings.kernel,

      // 实际浏览器配置参数
      'account': browserSettings.account != "" ? browserSettings.account : null,
      'account_username': browserSettings.accountUsername != ""
          ? browserSettings.accountUsername
          : null,
      'account_password': browserSettings.accountPassword != ""
          ? browserSettings.accountPassword
          : null,
      'cookie': browserSettings.cookie != "" ? browserSettings.cookie : null,
      'timezone': browserSettings.timezone
          ? null
          : browserSettings.timezoneSelectedValue,
      'locale': browserSettings.localeEvaluateLanguage
          ? null
          : browserSettings.locale,
      'language': browserSettings.evaluateLanguage
          ? null
          : browserSettings.languageSelectedValue,
      'location': browserSettings.location,
      'resolution_height': dimensions['height'],
      'resolution_width': dimensions['width'],
      'kernel': browserSettings.kernel,
      'memory': browserSettings.memory,
      'useragent': browserSettings.ua,
      'sample_rate': browserSettings.audioContext ? sampleRate : null,
      'plugin': browserSettings.plugin ? plugin : null,
      'webgl_render': browserSettings.webGLRender,
      'webgl_vendor': browserSettings.webGLVendor,
      'offset1': offset1,
      'fill_text': fillText,
      'client_rect': browserSettings.clientRects ? clientRect : null,
      'webgl_picture': browserSettings.webGL ? webglPicture : null,
      'rgb': browserSettings.canvas ? rgb : null,
      'dnk': '1',
      'url': browserSettings.account != "" ? browserSettings.account : null,
      'platform': platformController.text,
      'platform_version': platformVersionController.text,
      'full_version': fullVersionController.text,
      'major_version': browserSettings.chromeSelectedValue,

      // 新增指纹配置
      'portScan': {"enable": "no"},
      'canvas': generateCanvasConfig(),
      'webrtc': {"public": null, "private": null},
      'position': browserSettings.location,
      'webgl': {
        "vendor": browserSettings.webGLVendor,
        "renderer": browserSettings.webGLRender
      },
      'gpu': {
        "device": "Intel UHD Graphics 620",
        "description": "Intel integrated graphics adapter"
      },
      'webaudio': generateRandomNumber(),
      'clientrect': generateClientRectConfig(),
      'screen': generateScreenConfig(dimensions),
      'mobile': {"touchsupport": 0.0},
      'hardware': {
        "concurrency": browserSettings.kernel,
        "memory": browserSettings.memory
      },
      'clientHint': generateClientHintConfig(
        platformController.text,
        platformVersionController.text,
        fullVersionController.text,
      ),
      'languages': null,
      'software': {"cookie": "yes", "java": "no", "dnt": "no"},
      'font': {"removefont": "Arial Unicode MS,Helvetica Neue"},
    };

    final String jsonSettings = jsonEncode(settings);

    // 执行创建或更新操作
    if (browserSettings.editing) {
      await _updateBrowserWindow(
        ref, context, id, name, browserSettings, jsonSettings);
    } else {
      await _createBrowserWindow(
        ref, context, name, browserSettings, jsonSettings);
    }

    return jsonSettings;
  }

  // 更新浏览器窗口
  static Future<void> _updateBrowserWindow(
    WidgetRef ref,
    BuildContext context,
    int id,
    String name,
    BrowserConfigModel browserSettings,
    String jsonSettings,
  ) async {
    final updateRequests = [
      BrowserWindowUpdateRequest(
        id: id,
        name: name,
        platform: browserSettings.account,
        groupId: browserSettings.groupId,
        proxyId: browserSettings.proxyId,
        proxyType: browserSettings.proxyType,
        parameters: jsonSettings,
        storage: '',
        tag: convertTagsToJson(browserSettings.tag),
        comment: browserSettings.comment,
        sort: 0,
      ),
    ];

    try {
      final res = await ref
          .read(browserWindowControllerProvider.notifier)
          .updateBrowserWindow(updateRequests);
      if (context.mounted) {
        SnackBarUtil().showSuccess(context, res);
      }
    } catch (error) {
      if (context.mounted) {
        SnackBarUtil().showError(context, '环境更新失败: $error');
      }
    }
  }

  // 创建浏览器窗口
  static Future<void> _createBrowserWindow(
    WidgetRef ref,
    BuildContext context,
    String name,
    BrowserConfigModel browserSettings,
    String jsonSettings,
  ) async {
    final createRequest = BrowserCreateRequest(
      name: name,
      platform: browserSettings.account,
      groupId: browserSettings.groupId,
      proxyId: browserSettings.proxyId,
      proxyType: browserSettings.proxyType,
      parameters: jsonSettings,
      tag: convertTagsToJson(browserSettings.tag),
      comment: browserSettings.comment,
    );

    try {
      final res = await ref
          .read(browserWindowControllerProvider.notifier)
          .createBrowserWindow(createRequest);
      if (context.mounted) {
        SnackBarUtil().showSuccess(context, res);
        Navigator.pop(context);
      }
    } catch (error) {
      if (context.mounted) {
        SnackBarUtil().showError(context, '环境创建失败: $error');
      }
    }
  }
} 
