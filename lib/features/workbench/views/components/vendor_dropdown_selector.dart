import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'dart:math';

class VendorDropdownSelector extends HookConsumerWidget {
  final Map<String, List<String>> vendors;
  final String? selectedVendor;
  final Function(String vendor, String renderer) onChanged;
  final double? width;
  final double? height;
  final Color? textColor;
  final double? fontSize;

  const VendorDropdownSelector({
    super.key,
    required this.vendors,
    required this.selectedVendor,
    required this.onChanged,
    this.width,
    this.height,
    this.textColor,
    this.fontSize = 14,
  });

  // 获取随机渲染器
  String _getRandomRenderer(String vendor) {
    if (!vendors.containsKey(vendor)) return '';
    final renderers = vendors[vendor]!;
    if (renderers.isEmpty) return '';
    return renderers[Random().nextInt(renderers.length)];
  }

  // 构建下拉菜单项
  DropdownMenuItem<String> _buildDropdownMenuItem(String text) {
    return DropdownMenuItem<String>(
      value: text,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12),
        child: Text(
          text,
          style: TextStyle(
            fontSize: fontSize,
            color: textColor ?? Colors.black.withOpacity(0.8),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isOpen = useState(false);

    // 检查当前值是否在列表中
    final bool valueExists = vendors.keys.contains(selectedVendor);
    final String currentValue = valueExists ? selectedVendor! : "请选择";

    return Container(
      width: width,
      height: height ?? 50,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.black), // 黑色边框
        borderRadius: BorderRadius.circular(8),
      ),
      child: DropdownButton<String>(
        dropdownColor: Colors.white,
        value: currentValue,
        isExpanded: true,
        icon: Icon(
          isOpen.value ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
          size: 16,
          color: Colors.black.withOpacity(0.8), // 黑色图标
        ),
        underline: Container(), // 移除下划线
        style: TextStyle(
          fontSize: fontSize,
          color: Colors.black.withOpacity(0.8), // 黑色文字
        ),
        onChanged: (newValue) {
          if (newValue != null) {
            isOpen.value = !isOpen.value;
            if (newValue != "请选择") {
              final renderer = _getRandomRenderer(newValue);
              onChanged(newValue, renderer);
            }
          }
        },
        items: [
          // 添加默认选项
          _buildDropdownMenuItem("请选择"),
          // 添加供应商选项
          ...vendors.keys
              .map((vendor) => _buildDropdownMenuItem(vendor))
              .toList(),
        ],
      ),
    );
  }
}
