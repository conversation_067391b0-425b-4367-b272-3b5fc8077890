import 'package:flutter/material.dart';
import 'package:frontend_re/core/utils/ui/snack_bar_util.dart';
import 'package:frontend_re/widgets/primary_botton.dart';
import 'package:frontend_re/widgets/secondary_botton.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../controllers/browser_window_controller.dart';

class ConfirmDeleteDialog extends StatelessWidget {
  final List<int> ids; // 要删除的窗口 ID 列表
  final String name; // 提示中显示的名称

  const ConfirmDeleteDialog({
    super.key,
    required this.ids,
    required this.name,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: Theme.of(context).colorScheme.surfaceContainer,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      title: const Text(
        '确认删除',
        style: TextStyle(fontWeight: FontWeight.bold,fontSize: 18),
      ),
      content: Text(
        '确定要删除以下窗口吗？\n\n名称: $name\n数量: ${ids.length}',
        style: const TextStyle(fontSize: 14),
      ),
      actions: [
        SecondaryButton(
          onPressed: () {
            Navigator.of(context).pop(); // 关闭弹窗
          },
          child: const Text('取消'),
        ),
        Consumer(
          builder: (context, ref, child) {
            return PrimaryButton(
              onPressed: () async {
                try {
                  final res = await ref.read(browserWindowControllerProvider.notifier).deleteBrowserWindows(ids); // 调用删除方法
                  if (context.mounted) {
                    Navigator.of(context).pop(); // 关闭弹窗
                    SnackBarUtil().showSuccess(context, res);
                  }
                } catch (e) {
                  if (context.mounted) {
                    SnackBarUtil().showError(context, '删除失败: $e');
                  }
                }
              },
              child: const Text('确认删除'),
            );
          },
        ),
      ],
    );
  }
}
