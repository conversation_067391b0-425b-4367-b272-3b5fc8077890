import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:frontend_re/core/utils/ui/snack_bar_util.dart';
import 'package:frontend_re/domain/models/browser_window_model.dart';
import 'package:frontend_re/features/workbench/controllers/browser_config_controller.dart';
import 'package:frontend_re/features/workbench/controllers/browser_window_controller.dart';
import 'package:frontend_re/widgets/custom_switch_button.dart';
import 'package:frontend_re/widgets/custom_text_field.dart';
import 'package:frontend_re/widgets/primary_botton.dart';
import 'package:frontend_re/widgets/secondary_botton.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:frontend_re/core/constants/constant.dart';

class PlatformSelectDialog extends HookConsumerWidget {
  const PlatformSelectDialog({super.key, this.config, this.isEdit = false, this.id});

  final Object? config;
  final bool isEdit;
  final int? id;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 平台数据
    final data = useState<List<Map<String, dynamic>>>(websiteOptions);
    
    // 按平台类型分组
    final eCommercePlatforms = data.value.where((item) => item['type'] == PlatformType.eCommerce).toList();
    final socialMediaPlatforms = data.value.where((item) => item['type'] == PlatformType.socialMedia).toList();
    final paymentPlatforms = data.value.where((item) => item['type'] == PlatformType.payment).toList();

    // 所选的地区索引
    final selectedRegionIndex = useState<int>(0);
    // 所选的平台
    final selectedPlatform = useState<Map<String, dynamic>?>(null);
    
    // 自定义URL输入控制器
    final customUrlController = useTextEditingController();

    // 当选中平台改变时，重置地区选择
    useEffect(() {
      if (selectedPlatform.value != null) {
        selectedRegionIndex.value = 0;
      }
      return null;
    }, [selectedPlatform.value]);

    return Scaffold(
      backgroundColor: Colors.black.withValues(alpha: 0.5),
      body: Center(
        child: LayoutBuilder(
          builder: (context, constraints) {
            final screenHeight = MediaQuery.of(context).size.height;
            final maxHeight = screenHeight * 0.8;
            final minHeight = maxHeight < 640 ? maxHeight : 640.0;
            
            return ConstrainedBox(
              constraints: BoxConstraints(
                minHeight: minHeight,
                maxHeight: maxHeight,
              ),
              child: Container(
                width: 700,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
                  child: Column(
                    spacing: 10,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 标题
                      Row(
                        children: [
                          Container(
                            width: 16,
                            height: 16,
                            decoration: BoxDecoration(
                              color: const Color(0xFF0C75F8),
                              borderRadius: BorderRadius.circular(50),
                            ),
                          ),
                          const SizedBox(width: 10),
                          const Text('平台类型', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
                          const Spacer(),
                          // 关闭按钮
                          InkWell(
                            onTap: () {
                              Navigator.of(context).pop();
                            },
                            child: Container(
                              padding: const EdgeInsets.all(4),
                              decoration: BoxDecoration(
                                color: Colors.black.withValues(alpha: 0.3),
                                borderRadius: BorderRadius.circular(50),
                              ),
                              child: const Icon(
                                Icons.close,
                                color: Colors.white,
                                size: 16,
                              ),
                            ),
                          ),
                        ],
                      ),
                      // 分割线
                      const Divider(
                        color: Color(0xFFF8F8F8),
                        height: 1,
                      ),
                      // 内容区域
                      Expanded(
                        child: SingleChildScrollView(
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 44.0, vertical: 16.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                // 电商平台
                                if (eCommercePlatforms.isNotEmpty) ...[
                                  _buildSectionRow('电商平台', eCommercePlatforms, selectedPlatform),
                                  const SizedBox(height: 24),
                                ],
                                
                                // 社交媒体
                                if (socialMediaPlatforms.isNotEmpty) ...[
                                  _buildSectionRow('社交媒体', socialMediaPlatforms, selectedPlatform),
                                  const SizedBox(height: 24),
                                ],
                                
                                // 支付平台
                                if (paymentPlatforms.isNotEmpty) ...[
                                  _buildSectionRow('支付平台', paymentPlatforms, selectedPlatform),
                                  const SizedBox(height: 24),
                                ],
                                
                                // 其他平台
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    // 左侧标题
                                    const SizedBox(
                                      width: 80,
                                      child: Padding(
                                        padding: EdgeInsets.only(top: 10),
                                        child: Text(
                                          '其他',
                                          style: TextStyle(
                                            fontSize: 14,
                                            fontWeight: FontWeight.bold,
                                            color: Color(0xFF374151),
                                          ),
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 20),
                                    // 右侧平台网格
                                    Expanded(
                                      child: CustomTextField(
                                        controller: customUrlController,
                                        hint: '输入平台URL，例如：https://www.example.com',
                                        onChanged: (value) {
                                          // 更新时，重置选中平台
                                          selectedPlatform.value = null;
                                        },
                                      )
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 24),
                                // 地区
                                if (selectedPlatform.value != null && selectedPlatform.value!['details'] != null) ...[
                                  Row(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      // 左侧标题
                                      const SizedBox(
                                        width: 80,
                                        child: Padding(
                                          padding: EdgeInsets.only(top: 10),
                                          child: Text(
                                            '地区',
                                            style: TextStyle(
                                              fontSize: 14,
                                              fontWeight: FontWeight.bold,
                                              color: Color(0xFF374151),
                                            ),
                                          ),
                                        ),
                                      ),
                                      const SizedBox(width: 20),
                                      // 右侧平台网格
                                      Expanded(
                                        child: CustomSwitchButton(
                                          options: selectedPlatform.value!['details']
                                            .map<String>((item) => item['name']?.toString() ?? '')
                                            .toList(), 
                                          selectedIndex: selectedRegionIndex.value, 
                                          onChanged: (index) {
                                            selectedRegionIndex.value = index;
                                          }
                                        )
                                      ),
                                    ],
                                  ),
                                ],
                              ],
                            ),
                          ),
                        ),
                      ),
                      // 操作按钮
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          SecondaryButton(
                            onPressed: () {
                              Navigator.of(context).pop();
                            },
                            child: const Text('取消')
                          ),
                          const SizedBox(width: 10),
                          PrimaryButton(
                            onPressed: () {
                              // 返回选中的数据
                              Map<String, dynamic>? result;
                              
                              if (selectedPlatform.value != null) {
                                final platform = selectedPlatform.value!;
                                final regionDetails = platform['details'];
                                if (regionDetails != null && regionDetails.isNotEmpty) {
                                  result = {
                                    'platform': platform['name'],
                                    'url': regionDetails[selectedRegionIndex.value]['url'],
                                    'region': regionDetails[selectedRegionIndex.value]['name'],
                                  };
                                }
                              } else if (customUrlController.text.isNotEmpty) {
                                result = {
                                  'platform': '自定义',
                                  'url': customUrlController.text,
                                  'region': '自定义',
                                };
                              }

                              try {
                                if (isEdit) {
                                  ref.read(browserWindowControllerProvider.notifier).updateBrowserWindow([BrowserWindowUpdateRequest(id: id!, platform: result?['url'])]);

                                  Navigator.of(context).pop(result);
                                } else {
                                  // 调用外部回调，更新account字段为选中的URL
                                  ref.read(browserConfigControllerProvider(config).notifier)
                                  .updateConfig(account: result?['url']);
                                  
                                  Navigator.of(context).pop(result);
                                }
                              } catch (e) {
                                SnackBarUtil().showError(context, '更新失败: $e');
                              }
                            },
                            child: const Text('确定')
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            );
          }
        ),
      ),
    );
  }

  // 构建水平排列的分组行（标题在左，网格在右）
  Widget _buildSectionRow(String title, List<Map<String, dynamic>> platforms, ValueNotifier<Map<String, dynamic>?> selectedPlatform) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 左侧标题
        SizedBox(
          width: 80,
          child: Padding(
            padding: const EdgeInsets.only(top: 10),
            child: Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Color(0xFF374151),
              ),
            ),
          ),
        ),
        const SizedBox(width: 20),
        // 右侧平台网格
        Expanded(
          child: _buildPlatformGrid(platforms, selectedPlatform),
        ),
      ],
    );
  }

  // 构建平台网格
  Widget _buildPlatformGrid(List<Map<String, dynamic>> platforms, ValueNotifier<Map<String, dynamic>?> selectedPlatform) {
    return Wrap(
      spacing: 0,
      runSpacing: 0,
      children: platforms.map((platform) => _buildPlatformItem(platform, selectedPlatform)).toList(),
    );
  }

  // 构建单个平台项目
  Widget _buildPlatformItem(Map<String, dynamic> platform, ValueNotifier<Map<String, dynamic>?> selectedPlatform) {
    return _PlatformItem(
      platform: platform,
      isSelected: selectedPlatform.value != null && selectedPlatform.value!['name'] == platform['name'],
      onTap: () {
        // 更新内部选中状态
        selectedPlatform.value = platform;
      },
    );
  }
}

// 单个平台项目组件，支持hover和选中状态
class _PlatformItem extends HookConsumerWidget {
  const _PlatformItem({
    required this.platform,
    required this.isSelected,
    required this.onTap,
  });

  final Map<String, dynamic> platform;
  final bool isSelected;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isHovered = useState(false);

    return MouseRegion(
      onEnter: (_) => isHovered.value = true,
      onExit: (_) => isHovered.value = false,
      child: GestureDetector(
        onTap: onTap,
        child: Tooltip(
          message: platform['name'] ?? '',
          child: Container(
            width: 54,
            height: 54,
            decoration: BoxDecoration(
              color: isHovered.value ? const Color(0x660C75F8) : Colors.transparent,
              border: isSelected 
                ? Border.all(color: const Color(0xFF0C75F8), width: 2)
                : null,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Center(
              child: _buildPlatformIcon(platform['icon']),
            ),
          ),
        ),
      ),
    );
  }

  // 构建平台图标（支持SVG、PNG等图片和IconData）
  Widget _buildPlatformIcon(dynamic icon) {    
    if (icon is String) {
      // 根据文件扩展名判断图片类型
      if (icon.toLowerCase().endsWith('.svg')) {
        // SVG图标
        return SvgPicture.asset(
          icon,
          width: 40,
          height: 40,
          fit: BoxFit.contain,
        );
      } else {
        // PNG、JPG等其他图片格式
        return Image.asset(
          icon,
          width: 40,
          height: 40,
          fit: BoxFit.contain,
          errorBuilder: (context, error, stackTrace) {
            // 图片加载失败时显示默认图标
            return const Icon(
              Icons.web,
              size: 40,
            );
          },
        );
      }
    } else if (icon is IconData) {
      // Material图标
      return Icon(
        icon,
        size: 40,
      );
    } else {
      // 默认图标
      return const Icon(
        Icons.web,
        size: 40,
      );
    }
  }
}
