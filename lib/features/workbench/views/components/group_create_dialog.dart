import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:frontend_re/core/utils/date_formatter.dart';
import 'package:frontend_re/core/utils/ui/snack_bar_util.dart';
import 'package:frontend_re/domain/models/group_model.dart';
import 'package:frontend_re/domain/services/group_service.dart';
import 'package:frontend_re/widgets/custom_edit_button.dart';
import 'package:frontend_re/widgets/custom_text_field.dart';
import 'package:frontend_re/widgets/primary_botton.dart';
import 'package:frontend_re/widgets/secondary_botton.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

class GroupCreateDialog extends HookConsumerWidget {
  const GroupCreateDialog({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final groupNameController = useTextEditingController();
    final searchController = useTextEditingController();
    final isLoading = useState(false);
    final errorMessage = useState<String?>(null);
    final filteredGroups = useState<List<Group>>([]);
    
    // 监听分组服务状态
    final groupServiceState = ref.watch(groupServiceProvider);
    
    // 搜索过滤逻辑
    void filterGroups(String query) {
      groupServiceState.whenData((groupResponse) {
        if (query.isEmpty) {
          filteredGroups.value = groupResponse.groups;
        } else {
          filteredGroups.value = groupResponse.groups
              .where((group) => 
                  group.name.toLowerCase().contains(query.toLowerCase()))
              .toList();
        }
      });
    }
    
    // 页面加载时获取分组列表
    useEffect(() {
      // TODO: 这里需要传入实际的用户ID，可以从用户状态管理中获取
      ref.read(groupServiceProvider.notifier).getGroups(1);
      return null;
    }, []);
    
    // 监听分组数据变化，更新过滤列表
    useEffect(() {
      groupServiceState.whenData((groupResponse) {
        filteredGroups.value = groupResponse.groups;
      });
      return null;
    }, [groupServiceState]);

    return AlertDialog(
      backgroundColor: Theme.of(context).colorScheme.surfaceContainer,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      title: Row(
        children: [
          Container(
            width: 16,
            height: 16,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
              borderRadius: BorderRadius.circular(50),
            ),
          ),
          const SizedBox(width: 12),
          const Text(
            '创建分组',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
      contentPadding: const EdgeInsets.fromLTRB(24, 8, 24, 0),
      content: SizedBox(
        width: 500,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 16),
            // 已有分组标题和搜索
            Row(
              children: [
                const Text(
                  '已有分组',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const Spacer(),
                SizedBox(
                  width: 200,
                  child: CustomTextField(
                    controller: searchController,
                    hint: '搜索分组...',
                    height: 12,
                    onChanged: (value) {
                      filterGroups(value);
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Container(
              constraints: const BoxConstraints(
                maxHeight: 200,
                minHeight: 100,
              ),
              decoration: BoxDecoration(
                color: const Color(0xFFF3F4F8),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: const Color(0xFFE5E5E5),
                  width: 1,
                ),
              ),
              child: groupServiceState.when(
                data: (groupResponse) {
                  final displayGroups = filteredGroups.value.isNotEmpty 
                      ? filteredGroups.value 
                      : groupResponse.groups;
                      
                  if (displayGroups.isEmpty) {
                    return Center(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Text(
                          searchController.text.isEmpty ? '暂无分组' : '没有找到匹配的分组',
                          style: const TextStyle(
                            color: Color(0xFF999999),
                            fontSize: 14,
                          ),
                        ),
                      ),
                    );
                  }
                  
                  return Column(
                    children: [
                      // 分组统计信息
                      if (searchController.text.isNotEmpty) ...[
                        Padding(
                          padding: const EdgeInsets.all(8),
                          child: Text(
                            '找到 ${displayGroups.length} 个分组',
                            style: const TextStyle(
                              fontSize: 12,
                              color: Color(0xFF999999),
                            ),
                          ),
                        ),
                      ],
                      // 分组列表
                      Expanded(
                        child: ListView.builder(
                          shrinkWrap: true,
                          padding: const EdgeInsets.all(8),
                          itemCount: displayGroups.length,
                          itemBuilder: (context, index) {
                            final group = displayGroups[index];
                            return Container(
                              margin: const EdgeInsets.only(bottom: 4),
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 8,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(6),
                              ),
                              child: Row(
                                children: [
                                  Container(
                                    width: 6,
                                    height: 6,
                                    decoration: BoxDecoration(
                                      color: Theme.of(context).colorScheme.primary,
                                      borderRadius: BorderRadius.circular(3),
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          group.name,
                                          style: const TextStyle(
                                            fontSize: 13,
                                            color: Color(0xFF333333),
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                        Text(
                                          '创建时间: ${DateFormatter.formatRelativeTime(group.createdAt)}',
                                          style: const TextStyle(
                                            fontSize: 11,
                                            color: Color(0xFF999999),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  // 编辑按钮
                                  CustomEditButton(
                                    iconPath: 'assets/svg/edit.svg',
                                    iconWidth: 14,
                                    iconHeight: 14,
                                    tooltip: '编辑分组',
                                    onPressed: () => _showEditGroupDialog(context, ref, group),
                                  ),
                                  const SizedBox(width: 4),
                                  // 删除按钮
                                  CustomEditButton(
                                    iconPath: 'assets/svg/delete.svg',
                                    iconWidth: 14,
                                    iconHeight: 14,
                                    tooltip: '删除分组',
                                    hoverBackgroundColor: Colors.red,
                                    onPressed: () => _showDeleteGroupDialog(context, ref, group),
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  );
                },
                loading: () => const Center(
                  child: Padding(
                    padding: EdgeInsets.all(16),
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                ),
                error: (error, stack) => Center(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Text(
                      '加载失败: $error',
                      style: const TextStyle(
                        color: Colors.red,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 20),
            // 分组名称标签
            const Text(
              '新建分组名称',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            // 输入框
            CustomTextField(
              controller: groupNameController,
              hint: '请输入分组名称',
              height: 16,
            ),
            // 错误提示
            if (errorMessage.value != null) ...[
              const SizedBox(height: 8),
              Text(
                errorMessage.value!,
                style: const TextStyle(
                  color: Colors.red,
                  fontSize: 12,
                ),
              ),
            ],
            const SizedBox(height: 24),
          ],
        ),
      ),
      actionsPadding: const EdgeInsets.fromLTRB(24, 0, 24, 24),
      actions: [
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            SecondaryButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('取消'),
            ),
            const SizedBox(width: 12),
            PrimaryButton(
              onPressed: () async {
                final groupName = groupNameController.text.trim();
                
                // 表单验证
                if (groupName.isEmpty) {
                  errorMessage.value = '请输入分组名称';
                  return;
                }
                
                if (groupName.length > 50) {
                  errorMessage.value = '分组名称不能超过50个字符';
                  return;
                }
                
                // 清除错误信息
                errorMessage.value = null;
                isLoading.value = true;
                
                try {
                  final message = await ref.read(groupServiceProvider.notifier).createGroup(groupName);
                  if (context.mounted) {
                    SnackBarUtil().showSuccess(context, message);
                    // 刷新分组列表
                    ref.read(groupServiceProvider.notifier).getGroups(1);
                  }
                } catch (e) {
                  if (e is DioException) {
                    errorMessage.value = e.response?.data['message'] ?? e.response?.data['error'];
                  } else {
                    errorMessage.value = e.toString();
                  }
                } finally {
                  isLoading.value = false;
                }
              },
              child: isLoading.value
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Text('创建'),
            ),
          ],
        ),
      ],
    );
  }

  // 显示编辑分组对话框
  void _showEditGroupDialog(BuildContext context, WidgetRef ref, Group group) {
    final editController = TextEditingController(text: group.name);
    
    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        backgroundColor: Theme.of(context).colorScheme.surfaceContainer,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: const Text('编辑分组', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),),
        content: SizedBox(
          width: 300,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Row(
                children: [
                  Text(
                    '* ',
                    style: TextStyle(color: Colors.red, fontSize: 14),
                  ),
                  Text(
                    '分组名称',
                    style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              CustomTextField(
                controller: editController,
                hint: '请输入分组名称',
                height: 16,
              ),
            ],
          ),
        ),
        actions: [
          SecondaryButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: const Text('取消'),
          ),
          const SizedBox(width: 12),
          PrimaryButton(
            onPressed: () async {
              final newName = editController.text.trim();
              if (newName.isEmpty) {
                SnackBarUtil().showError(dialogContext, '请输入分组名称');
                return;
              }
              if (newName == group.name) {
                Navigator.of(dialogContext).pop();
                return;
              }
              
              try {
                final message = await ref
                    .read(groupServiceProvider.notifier)
                    .updateGroup(group.id, newName);
                
                if (dialogContext.mounted) {
                  Navigator.of(dialogContext).pop();
                  SnackBarUtil().showSuccess(context, message);
                  // 刷新分组列表
                  ref.read(groupServiceProvider.notifier).getGroups(1);
                }
              } catch (e) {
                if (dialogContext.mounted) {
                  SnackBarUtil().showError(dialogContext, e.toString());
                }
              }
            },
            child: const Text('保存'),
          ),
        ],
      ),
    );
  }

  // 显示删除分组确认对话框
  void _showDeleteGroupDialog(BuildContext context, WidgetRef ref, Group group) {
    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        backgroundColor: Theme.of(context).colorScheme.surfaceContainer,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: const Text(
          '确认删除',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
        ),
        content: Text(
          '确定要删除分组 "${group.name}" 吗？\n\n删除后该分组下的所有环境将移到默认分组。',
          style: const TextStyle(fontSize: 14),
        ),
        actions: [
          SecondaryButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: const Text('取消'),
          ),
          const SizedBox(width: 12),
          PrimaryButton(
            onPressed: () async {
              try {
                final message = await ref
                    .read(groupServiceProvider.notifier)
                    .deleteGroup(group.id);
                
                if (dialogContext.mounted) {
                  Navigator.of(dialogContext).pop();
                  SnackBarUtil().showSuccess(context, message);
                  // 刷新分组列表
                  ref.read(groupServiceProvider.notifier).getGroups(1);
                }
              } catch (e) {
                if (dialogContext.mounted) {
                  SnackBarUtil().showError(dialogContext, e.toString());
                }
              }
            },
            child: const Text(
              '确认删除',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }
} 
