import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:frontend_re/core/utils/ui/snack_bar_util.dart';
import 'package:frontend_re/widgets/primary_botton.dart';
import 'package:frontend_re/widgets/secondary_botton.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class GeneralEditDialog extends HookConsumerWidget {
  final String title;
  final String hintText;
  final String initialValue;
  final Future<void> Function(String newValue) onSave;
  final VoidCallback? onSuccess;
  final TextInputType keyboardType;
  final List<TextInputFormatter>? inputFormatters;
  final int maxLines;

  const GeneralEditDialog({
    super.key,
    required this.title,
    required this.hintText,
    required this.initialValue,
    required this.onSave,
    this.onSuccess,
    this.keyboardType = TextInputType.text,
    this.inputFormatters,
    this.maxLines = 1,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final textController = useTextEditingController(text: initialValue);
    final isLoading = useState(false);

    return Dialog(
      backgroundColor: Theme.of(context).colorScheme.surfaceContainer,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Container(
        width: 400,
        padding: const EdgeInsets.all(30),
        child: Column(
          spacing: 20,
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            TextField(
              controller: textController,
              style: const TextStyle(fontSize: 14),
              keyboardType: keyboardType,
              inputFormatters: inputFormatters,
              maxLines: maxLines,
              decoration: InputDecoration(
                border: const OutlineInputBorder(
                  borderRadius: BorderRadius.all(Radius.circular(8)),
                ),
                enabledBorder: const OutlineInputBorder(
                  borderRadius: BorderRadius.all(Radius.circular(8)),
                  borderSide: BorderSide(color: Colors.grey),
                ),
                focusedBorder: const OutlineInputBorder(
                  borderRadius: BorderRadius.all(Radius.circular(8)),
                  borderSide: BorderSide(color: Color(0xFF0C75F8)),
                ),
                hintText: hintText,
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                SecondaryButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('取消'),
                ),
                const SizedBox(width: 16),
                PrimaryButton(
                  onPressed: isLoading.value
                      ? () {}
                      : () async {
                          final newText = textController.text.trim();
                          
                          if (newText == initialValue) {
                            Navigator.of(context).pop();
                            return;
                          }

                          isLoading.value = true;
                          try {
                            // 调用外部传入的保存逻辑
                            await onSave(newText);

                            if (context.mounted) {
                              SnackBarUtil().showSuccess(context, '更新成功');
                              onSuccess?.call();
                              Navigator.of(context).pop();
                            }
                          } catch (e) {
                            if (context.mounted) {
                              SnackBarUtil().showError(context, '更新失败: $e');
                            }
                          } finally {
                            isLoading.value = false;
                          }
                        },
                  child: isLoading.value
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Text('确定'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
} 
