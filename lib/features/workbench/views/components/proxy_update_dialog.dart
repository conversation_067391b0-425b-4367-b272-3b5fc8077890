import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:frontend_re/core/utils/ui/snack_bar_util.dart';
import 'package:frontend_re/domain/services/proxy_service.dart';
import 'package:frontend_re/features/proxy/proxy_platform/controllers/proxy_platform_controller.dart';
import 'package:frontend_re/features/workbench/controllers/browser_window_controller.dart';
import 'package:frontend_re/widgets/primary_botton.dart';
import 'package:frontend_re/widgets/secondary_botton.dart';
import 'package:frontend_re/widgets/underline_dropdown_widget.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class ProxyUpdateDialog extends HookConsumerWidget {
  final int windowId;
  final VoidCallback? onProxyUpdated;

  const ProxyUpdateDialog({
    super.key,
    required this.windowId,
    this.onProxyUpdated,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 自建代理状态
    final proxyState = ref.watch(selfProxyProvider);
    // 平台代理状态
    final platformProxyState = ref.watch(platformProxyControllerProvider);
    final selectedProxyId = useState<int?>(0);
    // 是否选择了平台代理
    final isPlatformProxy = useState<bool?>(true);

    return Dialog(
      backgroundColor: Theme.of(context).colorScheme.surfaceContainer,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Container(
        width: 400,
        height: 500,
        padding: const EdgeInsets.all(30),
        child: Column(
          spacing: 20,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // 标题
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              spacing: 20,
              children: [
                TextButton(
                  onPressed: () {
                    isPlatformProxy.value = true;
                  },
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 16,vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(50),
                    ),
                    textStyle: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                    foregroundColor: isPlatformProxy.value == true
                        ? const Color(0xFF0C75F8)
                        : const Color.fromARGB(255, 60, 60, 60),
                    overlayColor: Colors.transparent,
                    backgroundColor: isPlatformProxy.value == true
                        ? const Color(0xFF0C75F8).withValues(alpha: 0.1)
                        : Colors.transparent,
                  ),
                  child: const Text('平台代理'),
                ),
                TextButton(
                  onPressed: () {
                    isPlatformProxy.value = false;
                  },
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 16,vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(50),
                    ),
                    textStyle: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                    foregroundColor: isPlatformProxy.value == false
                        ? const Color(0xFF0C75F8)
                        : const Color.fromARGB(255, 60, 60, 60),
                    overlayColor: Colors.transparent,
                    backgroundColor: isPlatformProxy.value == false
                        ? const Color(0xFF0C75F8).withValues(alpha: 0.1)
                        : Colors.transparent,
                  ),
                  child: const Text('自建代理'),
                ),
              ],
            ),
            isPlatformProxy.value == false
                ? proxyState.when(
                    data: (proxyList) => UnderlineDropdownWidget(
                        width: 300,
                        hintText: '自建代理',
                        items: proxyList.proxies.map((proxy) => proxy.name).toList(),
                        onSelected: (value) {
                          selectedProxyId.value = proxyList.proxies
                              .firstWhere((proxy) => proxy.name == value)
                              .id;
                        },
                      ),
                    loading: () => const Center(
                        child: CircularProgressIndicator()), // 加载状态显示
                    error: (error, stack) =>
                        const Center(child: Text('加载失败')), // 错误状态显示
                  )
                : platformProxyState.when(
                    data: (proxyList) => UnderlineDropdownWidget(
                        width: 300,
                        hintText: '平台代理',
                        items: proxyList.proxies.map((proxy) => proxy.name).toList(),
                        onSelected: (value) {
                          selectedProxyId.value = proxyList.proxies
                              .firstWhere((proxy) => proxy.name == value)
                              .id;
                        },
                      ),
                    loading: () => const Center(
                        child: CircularProgressIndicator()), // 加载状态显示
                    error: (error, stack) =>
                        const Center(child: Text('加载失败')), // 错误状态显示
                  ),
            const Spacer(),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                SecondaryButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('取消'),
                ),
                const SizedBox(width: 16),
                PrimaryButton(
                  onPressed: () async {
                    try {
                      if (isPlatformProxy.value == true) {
                      await ref.read(browserWindowControllerProvider.notifier)
                               .updateBrowserWindowProxy([windowId], selectedProxyId.value ?? 0);
                    } else {
                      await ref.read(browserWindowControllerProvider.notifier)
                               .updateBrowserWindowProxy([windowId], selectedProxyId.value ?? 0);
                    }
                    } catch (e) {
                      if(context.mounted) {
                        SnackBarUtil().showError(context, e.toString());
                      }
                    }

                    onProxyUpdated?.call();
                    if(context.mounted) {
                      Navigator.of(context).pop();
                    }
                  },
                  child: const Text('确定'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
