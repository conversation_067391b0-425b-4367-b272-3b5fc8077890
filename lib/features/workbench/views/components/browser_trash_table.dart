import 'dart:convert';

import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:frontend_re/features/workbench/controllers/browser_trash_controller.dart';
import 'package:frontend_re/core/utils/ui/snack_bar_util.dart';
import 'package:frontend_re/domain/models/browser_window_model.dart';
import 'package:frontend_re/widgets/custom_edit_button.dart';
import 'package:frontend_re/widgets/data_table_widget.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';

class BrowserTrashTable extends HookConsumerWidget {
  const BrowserTrashTable({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 监听回收站数据
    final trashState = ref.watch(browserTrashControllerProvider);
    final controller = ref.read(browserTrashControllerProvider.notifier);

    return Scaffold(
      body: LayoutBuilder(
        builder: (context, constraints) {
          return Column(
            children: [
              Expanded(
                child: trashState.when(
                  data: (trashModel) => 
                  DataTableWidget(
                    headingTextStyle: const TextStyle(fontSize: 14, color: Color(0xFF333333)),
                    dataTextStyle: const TextStyle(fontSize: 14, color: Color(0xFF8D8E93)),
                    groupHeaderTextStyle: const TextStyle(fontSize: 14, color: Color(0xFF333333)),
                    minWidth: 1700,
                    showCheckbox: true,
                    columnSpacing: 0,
                    groupBy: (trash) => trash.groupName,
                    groupHeaderBuilder: (groupKey, groupSize, isExpanded) {
                      return DataRow(cells: [
                        DataCell(Text(groupKey)),
                      ]);
                    },
                    onSelectionChanged: (selectedRows) {
                      // selectedRows 是选中行的索引集合 (Set<int>)
                      // 由于数据被分组显示，需要特殊处理索引映射
                      final windows = trashModel.windows ?? [];
                      
                      // 按照与DataTableWidget相同的逻辑创建分组数据
                      final groupedData = <String, List<BrowserWindowItem>>{};
                      for (var window in windows) {
                        groupedData.putIfAbsent(window.groupName, () => []).add(window);
                      }
                      
                      // 按分组重新排列数据，与UI显示顺序保持一致
                      final reorderedWindows = <BrowserWindowItem>[];
                      groupedData.forEach((groupName, groupWindows) {
                        reorderedWindows.addAll(groupWindows);
                      });
                      
                      // 根据重新排列后的数据获取选中的ID
                      final selectedIds = selectedRows
                          .where((index) => index < reorderedWindows.length)
                          .map((index) => reorderedWindows[index].id)
                          .toList();
                      ref.read(browserTrashTableControllerProvider.notifier).updateSelectedWindowIds(selectedIds);
                    },
                    noDataImagePath: 'assets/images/proxy/NoData.png',
                    noDataText: '暂无回收站数据',
                    // noDataButtonText: '添加回收站',
                    // noDataButtonEvent: () {
                    //   context.pushNamed('addBrowserTrash');
                    // },
                    enablePagination: true,
                    totalCount: trashModel.windows?.length ?? 0,
                    pageSize: 10,
                    currentPage: 1,
                    onPageChanged: (page, pageSize) {
                      controller.loadTrashItems(offset: (page - 1) * pageSize, pageSize: pageSize);
                    },
                    columns: const [
                      DataColumn2(label: Text('窗口名称'), size: ColumnSize.M),
                      DataColumn2(label: Text('账号平台'), size: ColumnSize.M),
                      DataColumn2(label: Text('代理IP'), size: ColumnSize.M),
                      DataColumn2(label: Text('标签'), size: ColumnSize.M),
                      DataColumn2(label: Text('备注'), size: ColumnSize.M),
                      DataColumn2(label: Text('云空间'), size: ColumnSize.M),
                      DataColumn2(label: Text('创建时间'), size: ColumnSize.M),
                      DataColumn2(label: Text('操作'), fixedWidth: 150),
                    ],
                    data: trashModel.windows ?? [],
                    rowBuilder: (trash, index, isHovered) {
                      return DataRow2(cells: [
                        DataCell(Text(trash.name)),
                        DataCell(Text(trash.platform)),
                        DataCell(Text(trash.proxy.name == '' ? "未绑定" : trash.proxy.name)),
                        DataCell(_buildTagsWidget(trash.tag)),
                        DataCell(_buildCommentWidget(trash.comment)),
                        DataCell(_cloudStorageBadge(size: trash.size ?? 0)),
                        DataCell(Text(DateFormat('yyyy-MM-dd HH:mm:ss').format(trash.createdAt))),
                        DataCell(_buildActionButtons(context, trash, ref)),
                      ]);
                    },
                  ),
                  loading: () => const Center(
                    child: CircularProgressIndicator(),
                  ),
                  error: (error, stack) => Center(
                    child: Text('加载失败: $error'),
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  // 备注显示组件
  Widget _buildCommentWidget(String? comment) {
    final displayComment = comment ?? '';
    
    return Container(
      constraints: const BoxConstraints(maxWidth: 150),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        border: Border.all(color: const Color(0xFFEDEDF0), width: 1),
        borderRadius: BorderRadius.circular(4),
        color: const Color(0xFFF3F4F8),
      ),
      child: Text(
        displayComment.isEmpty ? '无备注' : displayComment,
        overflow: TextOverflow.ellipsis,
        maxLines: 2,
        style: TextStyle(
          color: displayComment.isEmpty ? const Color(0xFF8D8E93) : const Color(0xFF8D8E93),
          fontSize: 12,
          height: 1.3,
        ),
      ),
    );
  }

  // 云空间显示组件
  Widget _cloudStorageBadge({required int size}) {
    // 格式化显示
    String displayText;
    if (size < 1024) {
      displayText = '$size MB';
    } else {
      double gbSize = size / 1024;
      displayText = '${gbSize.toStringAsFixed(1)} GB';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
      decoration: BoxDecoration(
        color: const Color(0xFFF3F4F8),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: const Color(0xFFEDEDF0), width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(
            Icons.cloud,
            size: 14,
            color: Color(0xFF0C75F8),
          ),
          const SizedBox(width: 4),
          Text(
            displayText,
            style: const TextStyle(
              color: Color(0xFF8D8E93),
              fontWeight: FontWeight.normal,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  // 操作按钮组
  Widget _buildActionButtons(BuildContext context, BrowserWindowItem trash, WidgetRef ref) {
    return Row(
      spacing: 6,
      children: [
        // 恢复按钮
        CustomEditButton(
          iconPath: 'assets/svg/add.svg',
          iconWidth: 16,
          iconHeight: 16,
          tooltip: '恢复',
          onPressed: () => _showRestoreDialog(context, trash, ref),
        ),
        // 彻底删除按钮
        CustomEditButton(
          iconPath: 'assets/svg/delete.svg',
          iconWidth: 16,
          iconHeight: 16,
          tooltip: '彻底删除',
          onPressed: () => _showDeleteDialog(context, trash, ref),
        ),
      ],
    );
  }

  // 显示恢复确认对话框
  void _showRestoreDialog(BuildContext context, BrowserWindowItem trash, WidgetRef ref) async {
    final bool? confirmed = await showDialog<bool>(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: const Text('确认恢复'),
          content: Text('您确定要恢复窗口 "${trash.name}" 吗？'),
          actions: <Widget>[
            TextButton(
              child: const Text('取消'),
              onPressed: () => Navigator.of(dialogContext).pop(false),
            ),
            TextButton(
              child: const Text('确认'),
              onPressed: () => Navigator.of(dialogContext).pop(true),
            ),
          ],
        );
      },
    );

    if (confirmed == true) {
      try {
        // 恢复单个项目
        final res = await ref.read(browserTrashControllerProvider.notifier).restoreSelectedTrash([trash.id]);
        if (context.mounted) SnackBarUtil().showSuccess(context, res);
      } catch (e) {
        if (context.mounted) SnackBarUtil().showError(context, e.toString());
      }
    }
  }

  // 显示删除确认对话框
  void _showDeleteDialog(BuildContext context, BrowserWindowItem trash, WidgetRef ref) async {
    final bool? confirmed = await showDialog<bool>(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: const Text('确认彻底删除'),
          content: Text('您确定要永久删除窗口 "${trash.name}" 吗？此操作不可恢复！'),
          actions: <Widget>[
            TextButton(
              child: const Text('取消'),
              onPressed: () => Navigator.of(dialogContext).pop(false),
            ),
            TextButton(
              child: const Text('确认'),
              onPressed: () => Navigator.of(dialogContext).pop(true),
            ),
          ],
        );
      },
    );

    if (confirmed == true) {
      try {
        // 删除单个项目
        final res = await ref.read(browserTrashControllerProvider.notifier).deleteSelectedTrash([trash.id]);
        if (context.mounted) SnackBarUtil().showSuccess(context, res);
      } catch (e) {
        if (context.mounted) SnackBarUtil().showError(context, e.toString());
      }
    }
  }

  // 标签展示组件
  Widget _buildTagsWidget(String tagJson) {
    List<dynamic> tags = [];
    try {
      tags = jsonDecode(tagJson);
    } catch (e) {
      if (tagJson.isNotEmpty) {
        tags = tagJson.split(',');
      }
    }

    if (tags.isEmpty) {
      return const Text('无标签');
    }

    return Wrap(
      spacing: 4,
      runSpacing: 4,
      children: tags.map((tag) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
          decoration: BoxDecoration(
            color: const Color(0xFFF3F4F8),
            borderRadius: BorderRadius.circular(4),
            border: Border.all(color: const Color(0xFFEDEDF0), width: 1),
          ),
          child: Text(
            tag.toString(),
            style: const TextStyle(
              color: Color(0xFF8D8E93),
              fontSize: 12,
              fontWeight: FontWeight.normal,
            ),
          ),
        );
      }).toList(),
    );
  }
}
