import 'dart:convert';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:frontend_re/core/constants/constant.dart';
import 'package:frontend_re/core/utils/random.dart';
import 'package:frontend_re/core/utils/ua_tool.dart';
import 'package:frontend_re/domain/models/browser_window_model.dart';
import 'package:frontend_re/domain/services/group_service.dart';
import 'package:frontend_re/domain/services/proxy_service.dart';
import 'package:frontend_re/domain/models/proxy_model.dart';
import 'package:frontend_re/features/workbench/controllers/browser_config_controller.dart';
import 'package:frontend_re/features/workbench/controllers/browser_fingerprint_controller.dart';
import 'package:frontend_re/features/workbench/controllers/browser_window_controller.dart';
import 'package:frontend_re/features/workbench/models/browser_config_model.dart';
import 'package:frontend_re/core/utils/ui/snack_bar_util.dart';
import 'package:frontend_re/features/workbench/views/components/platform_select_dialog.dart';
import 'package:frontend_re/widgets/custom_dropdown_menu.dart';
import 'package:frontend_re/widgets/custom_text_field.dart';
import 'package:frontend_re/widgets/primary_botton.dart';
import 'package:frontend_re/widgets/secondary_botton.dart';
import 'package:frontend_re/widgets/two_level_dropdown_menu.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '/widgets/custom_switch.dart';




class BrowserAddPage extends HookConsumerWidget {
  final Object? config;
  
  const BrowserAddPage({super.key, this.config});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 监听浏览器配置状态
    final browserConfig = ref.watch(browserConfigControllerProvider(config));

    final textControllerName = useTextEditingController(text: browserConfig.name); //环境名称
    final textControllerUsername = useTextEditingController(text: browserConfig.accountUsername); //用户名
    final textControllerPassword = useTextEditingController(text: browserConfig.accountPassword); //密码
    final textControllerCookie = useTextEditingController(text: browserConfig.cookie); //UA环境设置
    final textControllerUA = useTextEditingController(text: browserConfig.ua); //UA环境设置

    final textControllerLocation = useTextEditingController(text: browserConfig.location); //经度
    final textControllerWebGL = useTextEditingController(text: browserConfig.webGLRender); //webGL渲染
    final platformController = useTextEditingController(text: browserConfig.oswSelectedValue);
    final platformVersionController = useTextEditingController(text: '15.0.0');
    final fullVersionController = useTextEditingController(text: '127.0.6533.72');

    //备注
    final textControllerComment = useTextEditingController(text: browserConfig.comment); //备注
    // 标签
    final textControllerTag = useTextEditingController(text: browserConfig.tag); //标签
    // 分组相关状态
    final selectedGroupId = useState<int?>(null);
    final selectedGroupName = useState<String>('无分组');
    
    // 选中的平台状态
    final selectedPlatform = useState<Map<String, dynamic>?>(null);
    
    // 获取分组列表
    final groupServiceState = ref.watch(groupServiceProvider);
    
    // 获取代理列表
    final proxyServiceState = ref.watch(selfProxyProvider);
    
    // 页面加载时获取分组列表和代理列表
    useEffect(() {
      Future(() {
        ref.read(groupServiceProvider.notifier).getGroups(1);
        ref.read(selfProxyProvider.notifier).getProxies(const ProxyListRequest(limit: 100, offset: 0));
      });
      return null;
    }, []);
    
    // 初始化分组状态（仅设置默认值）
    useEffect(() {
      if (config == null) {
        // 新建模式：使用默认值
        selectedGroupId.value = browserConfig.groupId == 0 ? null : browserConfig.groupId;
        selectedGroupName.value = '无分组';
      }
      return null;
    }, [config]);

    return LayoutBuilder(
      builder: (context, constraints) {
        final width = constraints.maxWidth * 1; // 取父容器宽度的40%
        final height = constraints.maxHeight * 1; // 取父容器高度的60%
        return Column(
          children: [
            Expanded(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Flexible(
                    child: Container(
                      width: width,
                      height: height,
                      padding: const EdgeInsets.only(top: 20, bottom: 20),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(30),
                      ),
                      child: Column(
                        children: [
                          Expanded(
                            child: SingleChildScrollView(
                            child: Container(
                              padding: const EdgeInsets.only(left: 20, right: 20, bottom: 20),
                              child: Column(
                                spacing: 10,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // 基础信息 ---
                                  Row(
                                    spacing: 8,
                                    children: [
                                      Container(
                                        width: 16,
                                        height: 16,
                                        decoration: const BoxDecoration(
                                          color: Color(0xFF0C75F8),
                                          shape: BoxShape.circle,
                                        ),
                                      ),
                                      const Text('基础信息', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Color(0xFF999999))),
                                    ]
                                  ),
                                  // 名称
                                  buildRowTemp(
                                    '名称',
                                    ConstrainedBox(
                                      constraints: const BoxConstraints(minWidth: 200),
                                      child: IntrinsicWidth(
                                        child: CustomTextField(
                                          controller: textControllerName,
                                          hint: "选填",
                                          lines: 1,
                                          onChanged: (value) {
                                            ref.read(browserConfigControllerProvider(config).notifier)
                                            .updateConfig(name: value);
                                            ref.read(browserFingerprintControllerProvider(config).notifier);
                                          },
                                        ),
                                      ),
                                    ),
                                  ),
                                  // 分组
                                  buildRowTemp(
                                    '分组',
                                    SizedBox(
                                      width: 200,
                                      child: groupServiceState.when(
                                        data: (groupResponse) {
                                          // 准备下拉菜单的选项
                                          final groupOptions = [
                                            {'name': '无分组'}, // 默认选项
                                            ...groupResponse.groups.map((group) => {'name': group.name}).toList(),
                                          ];
                                          
                                                                                // 确定当前应该显示的值，不在build中修改状态
                                          String currentDisplayValue = selectedGroupName.value;
                                          
                                          // 如果是编辑模式且还没有正确设置分组名称，尝试从config中获取
                                          if (config != null && selectedGroupName.value == '无分组') {
                                            try {
                                              print('原始config类型: ${config.runtimeType}');
                                              print('原始config内容: $config');
                                              
                                              Map<String, dynamic> configMap;
                                              if (config is String) {
                                                configMap = jsonDecode(config as String) as Map<String, dynamic>;
                                              } else if (config is Map<String, dynamic>) {
                                                configMap = config as Map<String, dynamic>;
                                              } else {
                                                configMap = jsonDecode(config.toString()) as Map<String, dynamic>;
                                              }
                                              
                                              final groupId = configMap['group_id'] as int?;
                                              final groupName = configMap['group_name'] as String?;
                                              
                                              print('下拉框渲染时解析config: groupId=$groupId, groupName=$groupName');
                                              
                                              if (groupId != null && groupId != 0) {
                                                // 优先使用config中的group_name
                                                if (groupName != null && groupName.isNotEmpty) {
                                                  currentDisplayValue = groupName;
                                                  // 使用 Future.microtask 延迟状态更新，避免在build中修改状态
                                                  Future.microtask(() {
                                                    selectedGroupId.value = groupId;
                                                    selectedGroupName.value = groupName;
                                                  });
                                                } else {
                                                  // 如果没有group_name，根据group_id查找
                                                  try {
                                                    final group = groupResponse.groups.firstWhere(
                                                      (g) => g.id == groupId,
                                                    );
                                                    currentDisplayValue = group.name;
                                                    // 使用 Future.microtask 延迟状态更新
                                                    Future.microtask(() {
                                                      selectedGroupId.value = groupId;
                                                      selectedGroupName.value = group.name;
                                                    });
                                                  } catch (e) {
                                                    print('根据groupId查找分组失败: $e');
                                                    currentDisplayValue = '无分组';
                                                  }
                                                }
                                              }
                                            } catch (e) {
                                              print('解析config失败: $e');
                                            }
                                          }
                                          
                                          print('最终显示值: $currentDisplayValue');
                                          
                                          return CustomDropdownMenu(
                                            height: 40,
                                            hintText: '请选择分组',
                                            items: groupOptions,
                                            value: currentDisplayValue,
                                            onChanged: (value) {
                                              selectedGroupName.value = value ?? '无分组';
                                              
                                              if (value == '无分组') {
                                                selectedGroupId.value = null;
                                              } else {
                                                // 找到对应的分组ID
                                                final group = groupResponse.groups.firstWhere(
                                                  (g) => g.name == value,
                                                  orElse: () => throw StateError('Group not found'),
                                                );
                                                selectedGroupId.value = group.id;
                                              }
                                              
                                              // 更新配置
                                              ref.read(browserConfigControllerProvider(config).notifier)
                                                  .updateConfig(groupId: selectedGroupId.value ?? 0);
                                            },
                                          );
                                        },
                                        loading: () => CustomDropdownMenu(
                                          height: 40,
                                          hintText: '加载中...',
                                          items: const [],
                                          value: '加载中...',
                                          onChanged: (value) {
                                            // 加载中时不处理
                                          },
                                        ),
                                                                             error: (error, stack) => CustomDropdownMenu(
                                           height: 40,
                                           hintText: '加载失败',
                                           items: const [{'name': '无分组'}],
                                           value: '无分组',
                                           onChanged: (value) {
                                             selectedGroupName.value = '无分组';
                                             selectedGroupId.value = null;
                                             ref.read(browserConfigControllerProvider(config).notifier)
                                                 .updateConfig(groupId: 0);
                                           },
                                         ),
                                      ),
                                    ),
                                  ),
                                  // 标签
                                  buildRowTemp(
                                    '标签',
                                    CustomTextField(
                                      controller: textControllerTag,
                                      hint: "选填，多个标签用逗号分隔，例如：标签1,标签2,标签3",
                                      onChanged: (value) {
                                        ref.read(browserConfigControllerProvider(config).notifier)
                                        .updateConfig(tag: value);
                                      },
                                    ),
                                  ),
                                  // 备注
                                  buildRowTemp(
                                    '备注',
                                    SizedBox(
                                      width: 600,
                                      child: TextField(
                                        maxLines: 4,
                                        minLines: 4,
                                        controller: textControllerComment,
                                        enabled: true,
                                        style: const TextStyle(color: Color(0xFF666666), fontSize: 14),
                                        onChanged: (value) {
                                          ref.read(browserConfigControllerProvider(config).notifier)
                                          .updateConfig(comment: value);
                                        },
                                        decoration: InputDecoration(
                                          hintText: "选填",
                                          border: OutlineInputBorder(
                                            borderRadius: BorderRadius.circular(8),
                                            borderSide: const BorderSide(color: Colors.transparent),
                                          ),
                                          enabledBorder: OutlineInputBorder(
                                            borderRadius: BorderRadius.circular(8),
                                            borderSide: const BorderSide(color: Colors.transparent),
                                          ),
                                          focusedBorder: OutlineInputBorder(
                                            borderRadius: BorderRadius.circular(8),
                                            borderSide: const BorderSide(color: Color(0xFF0C75F8)),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                  // 代理信息
                                  Padding(
                                    padding: const EdgeInsets.only(top: 40),
                                    child: Row(
                                      spacing: 8,
                                      children: [
                                        Container(
                                          width: 16,
                                          height: 16,
                                          decoration: const BoxDecoration(
                                            color: Color(0xFF0C75F8),
                                            shape: BoxShape.circle,
                                          ),
                                        ),
                                        const Text('代理信息', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Color(0xFF999999))),
                                      ]
                                    ),
                                  ),
                                  // 代理
                                  buildRowTemp(
                                    '代理',
                                    SizedBox(
                                      width: 300,
                                      child: proxyServiceState.when(
                                        data: (proxyResponse) {
                                          // 准备下拉菜单的选项
                                          final proxyOptions = [
                                            {'name': '无代理'}, // 默认选项
                                            ...proxyResponse.proxies.map((proxy) => {'name': proxy.name}),
                                          ];
                                          
                                          // 查找当前选中的代理名称
                                          String currentProxyName = '无代理';
                                          if (browserConfig.proxyId != 0) {
                                            final selectedProxy = proxyResponse.proxies.where((p) => p.id == browserConfig.proxyId).firstOrNull;
                                            if (selectedProxy != null) {
                                              currentProxyName = selectedProxy.name;
                                            }
                                          }
                                          
                                          return CustomDropdownMenu(
                                            value: currentProxyName,
                                            items: proxyOptions,
                                            onChanged: (selectedValue) {
                                              if (selectedValue == '无代理') {
                                                ref.read(browserConfigControllerProvider(config).notifier)
                                                .updateConfig(proxyId: 0, proxyType: 2);
                                              } else {
                                                final selectedProxy = proxyResponse.proxies.where((p) => p.name == selectedValue).firstOrNull;
                                                if (selectedProxy != null) {
                                                  // 自有代理的proxyType固定为2
                                                  ref.read(browserConfigControllerProvider(config).notifier)
                                                  .updateConfig(proxyId: selectedProxy.id, proxyType: 2);
                                                  print('selectedProxy: ${selectedProxy.id}');
                                                }
                                              }
                                            },
                                          );
                                        },
                                        loading: () => CustomDropdownMenu(
                                          value: '无代理',
                                          items: const [{'name': '无代理'}],
                                          onChanged: (value) {},
                                        ),
                                        error: (error, stackTrace) => CustomDropdownMenu(
                                          value: '无代理',
                                          items: const [{'name': '无代理'}],
                                          onChanged: (value) {},
                                        ),
                                      ),
                                    ),
                                  ),
                                  // 环境设置
                                  Padding(
                                    padding: const EdgeInsets.only(top: 40),
                                    child: Row(
                                      spacing: 8,
                                      children: [
                                        Container(
                                          width: 16,
                                          height: 16,
                                          decoration: const BoxDecoration(
                                            color: Color(0xFF0C75F8),
                                            shape: BoxShape.circle,
                                          ),
                                        ),
                                        const Text('环境设置', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Color(0xFF999999))),
                                      ]
                                    ),
                                  ),
                                  // 账号平台
                                  buildRowTemp(
                                    '平台类型',
                                    MouseRegion(
                                      cursor: SystemMouseCursors.click,
                                      child: GestureDetector(
                                        onTap: () {
                                          showDialog(
                                            context: context,
                                            builder: (context) => PlatformSelectDialog(config: config),
                                          );
                                        },
                                        child: Container(
                                          width: 500,
                                          height: 50,
                                          decoration: BoxDecoration(
                                            color: const Color(0xFFF3F4F8),
                                            borderRadius: BorderRadius.circular(50),
                                          ),
                                          child: Padding(
                                            padding: const EdgeInsets.only(left: 16, right: 12),
                                            child: Row(
                                              crossAxisAlignment: CrossAxisAlignment.center,
                                              children: [
                                                Text(
                                                  browserConfig.account,
                                                  style: TextStyle(
                                                    fontSize: 14, 
                                                    color: selectedPlatform.value != null 
                                                      ? const Color(0xFF333333)
                                                      : const Color(0xFF666666)
                                                  )
                                                ),
                                                const Spacer(),
                                                const Icon(Icons.keyboard_arrow_down, size: 16, color: Color(0xFF666666)),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                    )
                                  ),
                                  // 用户名
                                  buildRowTemp(
                                    '用户名',
                                    ConstrainedBox(
                                      constraints: const BoxConstraints(minWidth: 200),
                                      child: IntrinsicWidth(
                                        child: CustomTextField(
                                          controller: textControllerUsername,
                                          hint: "选填",
                                          lines: 1,
                                          onChanged: (value) {
                                            ref.read(browserConfigControllerProvider(config).notifier)
                                            .updateConfig(accountUsername:value);
                                          },
                                        ),
                                      ),
                                    )
                                  ),
                                  // 密码
                                  buildRowTemp(
                                    '密码',
                                    ConstrainedBox(
                                      constraints: const BoxConstraints(minWidth: 200),
                                      child: IntrinsicWidth(
                                        child: CustomTextField(
                                          controller: textControllerPassword,
                                          hint: "选填",
                                          lines: 1,
                                          onChanged: (value) {
                                            ref.read(browserConfigControllerProvider(config).notifier)
                                            .updateConfig(accountPassword:value);
                                          },
                                        ),
                                      ),
                                    )
                                  ),
                                  // Cookie
                                  buildRowTemp(
                                    'Cookie',
                                    SizedBox(
                                      width: 600,
                                      child: TextField(
                                        maxLines: 4,
                                        minLines: 4,
                                        controller: textControllerCookie,
                                        enabled: true,
                                        style: const TextStyle(color: Color(0xFF666666), fontSize: 14),
                                        onChanged: (value) {
                                          ref.read(browserConfigControllerProvider(config).notifier)
                                          .updateConfig(cookie: value);
                                        },
                                        decoration: InputDecoration(
                                          hintText: "选填",
                                          border: OutlineInputBorder(
                                            borderRadius: BorderRadius.circular(8),
                                            borderSide: const BorderSide(color: Colors.transparent),
                                          ),
                                          enabledBorder: OutlineInputBorder(
                                            borderRadius: BorderRadius.circular(8),
                                            borderSide: const BorderSide(color: Colors.transparent),
                                          ),
                                          focusedBorder: OutlineInputBorder(
                                            borderRadius: BorderRadius.circular(8),
                                            borderSide: const BorderSide(color: Color(0xFF0C75F8)),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                  // 浏览器版本
                                  buildRowTemp(
                                    '浏览器版本',
                                    SizedBox(
                                      width: 200,
                                      child: CustomDropdownMenu(
                                        value: browserConfig.chromeSelectedValue,
                                        items: chromeVersionOptions,
                                        onChanged: (selectedValue) {
                                          fullVersionController.text = generateDetailedChromeVersion(selectedValue!);
                                          ref.read(browserConfigControllerProvider(config).notifier)
                                          .updateConfig(chromeSelectedValue: selectedValue);
                                          ref.read(browserConfigControllerProvider(config).notifier)
                                          .updateConfig(ua: generateUserAgent(fullVersionController.text,browserConfig.oswSelectedValue));
                                        },
                                      ),
                                    ),
                                  ),
                                  // 操作系统
                                  buildRowTemp(
                                    '操作系统',
                                    SizedBox(
                                      width: 200,
                                      child: CustomDropdownMenu(
                                        value: browserConfig.oswSelectedValue,
                                        items: osOptions,
                                        onChanged: (selectedValue) {
                                          fullVersionController.text = generateDetailedChromeVersion(browserConfig.chromeSelectedValue);
                                          ref.read(browserConfigControllerProvider(config).notifier)
                                          .updateConfig(oswSelectedValue: selectedValue);
                                          ref.read(browserConfigControllerProvider(config).notifier)
                                          .updateConfig(ua: generateUserAgent(fullVersionController.text,selectedValue ?? 'Windows 11'));
                                        },
                                      ),
                                    ),
                                  ),
                                  // UA环境设置
                                  buildRowTemp(
                                    'UA环境设置',
                                    Consumer(
                                      builder: (context, ref, child) {
                                        final ua = ref.watch(browserConfigControllerProvider(config).select((value) => value.ua));
                                        textControllerUA.text = ua;
                                        return buildUa(ref, browserConfig, textControllerUA, fullVersionController);
                                      },
                                    ),
                                  ),
                                  // --- 高级设置 ---
                                  // 语言
                                  buildRowTemp(
                                    '语言',
                                    Row(
                                      spacing: 6,
                                      children: [
                                        CustomSwitch(
                                          options: const ['基于IP', '自定义'],
                                          selectedIndex: browserConfig.evaluateLanguage ? 0 : 1,
                                          onChanged: (selectedIndex) {
                                            ref.read(browserConfigControllerProvider(config).notifier)
                                            .updateConfig(evaluateLanguage: selectedIndex == 0);
                                          },
                                        ),
                                        browserConfig.evaluateLanguage == false
                                          ? SizedBox(
                                            width: 200,
                                            child: CustomDropdownMenu(
                                                value: browserConfig.languageSelectedValue,
                                                items: languageLocaleOptions,
                                                onChanged: (selectedValue) {
                                                  ref.read(browserConfigControllerProvider(config).notifier)
                                                  .updateConfig(languageSelectedValue:selectedValue);
                                                },
                                              ),
                                          )
                                          : const SizedBox(),
                                      ],
                                    )
                                  ),
                                  // 界面语言
                                  buildRowTemp(
                                    '界面语言',
                                    Row(
                                      spacing: 6,
                                      children: [
                                        CustomSwitch(
                                          options: const ['基于IP', '自定义'],
                                          selectedIndex: browserConfig.localeEvaluateLanguage ? 0 : 1,
                                          onChanged: (selectedIndex) {
                                            ref.read(browserConfigControllerProvider(config).notifier)
                                            .updateConfig(localeEvaluateLanguage: selectedIndex == 0);
                                          },
                                        ),
                                        browserConfig.localeEvaluateLanguage == false
                                          ? SizedBox(
                                            width: 200,
                                            child: CustomDropdownMenu(
                                                value: browserConfig.locale,
                                                items: languageLocaleOptions,
                                                onChanged: (selectedValue) {
                                                  ref.read(browserConfigControllerProvider(config).notifier)
                                                  .updateConfig(locale: selectedValue);
                                                },
                                              ),
                                          )
                                          : const SizedBox(),
                                      ],
                                    )
                                  ),
                                  // 时区
                                  buildRowTemp(
                                    '时区',
                                    Row(
                                      spacing: 6,
                                      children: [
                                        CustomSwitch(
                                          options: const ['基于IP', '自定义'],
                                          selectedIndex: browserConfig.timezone ? 0 : 1,
                                          onChanged: (selectedIndex) {
                                            ref.read(browserConfigControllerProvider(config).notifier)
                                            .updateConfig(timezone: selectedIndex == 0);
                                          },
                                        ),
                                        browserConfig.timezone == false
                                          ? SizedBox(
                                            width: 200,
                                            child: CustomDropdownMenu(
                                                value: browserConfig.timezoneSelectedValue,
                                                items: timezone,
                                                onChanged: (selectedValue) {
                                                  ref.read(browserConfigControllerProvider(config).notifier)
                                                  .updateConfig(timezoneSelectedValue: selectedValue);
                                                },
                                              ),
                                          )
                                          : const SizedBox(),
                                      ],
                                    )
                                  ),
                                  // 经纬度
                                  buildRowTemp(
                                    '经纬度',
                                    Row(
                                      spacing: 6,
                                      children: [
                                        CustomSwitch(
                                          options: const ['基于IP', '自定义'],
                                          selectedIndex: browserConfig.locationByIP ? 0 : 1,
                                          onChanged: (selectedIndex) {
                                            ref.read(browserConfigControllerProvider(config).notifier)
                                            .updateConfig(locationByIP: selectedIndex == 0);
                                          },
                                        ),
                                        browserConfig.locationByIP == false
                                          ? Flexible(
                                            child: ConstrainedBox(
                                              constraints: const BoxConstraints(maxWidth: 400),
                                              child: _buildInputField(
                                                hint: '经,纬,海拔,精度(用逗号隔开)',
                                                controller: textControllerLocation,
                                                onChanged: (value) {
                                                  ref.read(browserConfigControllerProvider(config).notifier)
                                                  .updateConfig(location: value);
                                                },
                                              ),
                                            ),
                                          )
                                          : const SizedBox(),
                                      ],
                                    )
                                  ),
                                  // WebGL
                                  buildRowTemp(
                                    'WebGL',
                                    Row(
                                      spacing: 6,
                                      children: [
                                        CustomSwitch(
                                          options: const ['开启', '关闭'],
                                          selectedIndex: browserConfig.webGL ? 0 : 1,
                                          onChanged: (selectedIndex) {
                                            ref.read(browserConfigControllerProvider(config).notifier)
                                            .updateConfig(webGL: selectedIndex == 0);
                                          },
                                        ),
                                      ],
                                    )
                                  ),
                                  // WebGL厂商
                                  buildRowTemp(
                                    'WebGL厂商',
                                    SizedBox(
                                      width: 300,
                                      child: CustomDropdownMenu(
                                        value: browserConfig.webGLVendor,
                                        items: vendors.keys.map((vendor) => {'name': vendor}).toList(),
                                        onChanged: (selectedValue) {
                                          if (selectedValue != null) {
                                            // 获取选中厂商的渲染器列表
                                            final List<String>? renderers = vendors[selectedValue];
                                            // 随机选择一个渲染器
                                            if (renderers != null && renderers.isNotEmpty) {
                                              final int random = getRandomInt(renderers.length);
                                              final String renderer = renderers[random];
                                              // 更新状态
                                              ref.read(browserConfigControllerProvider(config).notifier)
                                                .updateConfig(
                                                  webGLVendor: selectedValue,
                                                  webGLRender: renderer,
                                                );
                                            }
                                          }
                                        },
                                      ),
                                    ),
                                  ),
                                  // WebGL渲染
                                  buildRowTemp(
                                    'WebGL渲染',
                                    buildWebGLRender(ref, browserConfig, textControllerWebGL),
                                  ),
                                  // Plugin ClientRects 字体 AudioVoice
                                  buildRowTemp(
                                    'Plugin',
                                    Row(
                                      spacing: 6,
                                      children: [
                                        CustomSwitch(
                                          options: const ['开启','关闭'],
                                          selectedIndex: browserConfig.plugin ? 0 : 1,
                                          onChanged: (selectedIndex) {
                                            ref.read(browserConfigControllerProvider(config).notifier)
                                            .updateConfig(plugin: selectedIndex == 0);
                                          },
                                        ),
                                      ],
                                    ),
                                  ),
                                  buildRowTemp(
                                    'Canvas',
                                    Row(
                                      children: [
                                        CustomSwitch(
                                          options: const ['开启','关闭'],
                                          selectedIndex: browserConfig.canvas ? 0 : 1,
                                          onChanged: (selectedIndex) {
                                            ref.read(browserConfigControllerProvider(config).notifier)
                                            .updateConfig(canvas: selectedIndex == 0);
                                          },
                                        ),
                                      ],
                                    ),
                                  ),
                                  buildRowTemp(
                                    '字体',
                                    Row(
                                      children: [
                                        CustomSwitch(
                                          options: const ['开启','关闭'],
                                          selectedIndex: browserConfig.font ? 0 : 1,
                                          onChanged: (selectedIndex) {
                                            ref.read(browserConfigControllerProvider(config).notifier)
                                            .updateConfig(font: selectedIndex == 0);
                                          },
                                        ),
                                      ],
                                    ),
                                  ),
                                  buildRowTemp(
                                    'ClientRects',
                                    Row(
                                      children: [
                                        CustomSwitch(
                                          options: const ['开启','关闭'],
                                          selectedIndex: browserConfig.clientRects ? 0 : 1,
                                          onChanged: (selectedIndex) {
                                            ref.read(browserConfigControllerProvider(config).notifier)
                                            .updateConfig(clientRects: selectedIndex == 0);
                                          },
                                        ),
                                      ],
                                    ),
                                  ),
                                  buildRowTemp(
                                    'AudioVoice',
                                    Row(
                                      children: [
                                        CustomSwitch(
                                          options: const ['开启','关闭'],
                                          selectedIndex: browserConfig.audioContext ? 0 : 1,
                                          onChanged: (selectedIndex) {
                                            ref.read(browserConfigControllerProvider(config).notifier)
                                            .updateConfig(audioContext: selectedIndex == 0);
                                          },
                                        ),
                                      ],
                                    ),
                                  ),
                                  
                                  // 分辨率
                                  buildRowTemp(
                                    '分辨率',
                                    CustomDropdownMenu(
                                      width: 200,
                                      value: browserConfig.resolution,
                                      items: resolution,
                                      onChanged: (selectedValue) {
                                        ref.read(browserConfigControllerProvider(config).notifier)
                                        .updateConfig(resolution: selectedValue);
                                      },
                                    ),
                                  ),
                                  // 设备内存
                                  buildRowTemp(
                                    '设备内存',
                                    CustomDropdownMenu(
                                      width: 200,
                                      value: browserConfig.memory,
                                      items: memory,
                                      onChanged: (selectedValue) {
                                        ref.read(browserConfigControllerProvider(config).notifier)
                                        .updateConfig(memory: selectedValue);
                                      },
                                    ),
                                  ),
                                  // 设备CPU
                                  buildRowTemp(
                                    '设备CPU',
                                    CustomDropdownMenu(
                                      width: 200,
                                      value: browserConfig.kernel,
                                      items: kernel,
                                      onChanged: (selectedValue) {
                                        ref.read(browserConfigControllerProvider(config).notifier)
                                        .updateConfig(kernel: selectedValue);
                                      },
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 10),
                // 概要
                Consumer(
                  builder: (context, ref, child) {
                    final browserConfig = ref.watch(browserConfigControllerProvider(config));
                    return Container(
                      padding: const EdgeInsets.only(top: 20, bottom: 20),

                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(30),
                      ),
                      child: SingleChildScrollView(
                        child: Container(
                          width: 360,
                          padding: const EdgeInsets.only(left: 20, right: 20),
                          child: Column(
                            spacing: 10,
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                spacing: 10,
                                children: [
                                  Container(
                                    height: 16,
                                    width: 16,
                                    decoration: BoxDecoration(
                                      color: const Color(0xFF0C75F8),
                                      borderRadius: BorderRadius.circular(50),
                                    ),
                                  ),
                                  const Text('概要', style: TextStyle(fontWeight: FontWeight.bold),),
                                ],
                              ),
                              Container(
                                height: 2,
                                width: 300,
                                decoration: BoxDecoration(
                                  color: const Color(0xFFF8F8F8),
                                  borderRadius: BorderRadius.circular(10),
                                ),
                              ),
                              browserConfig.name.isNotEmpty ? buildRowTextTemp('窗口名称', browserConfig.name) : null,
                              browserConfig.accountUsername.isNotEmpty ? buildRowTextTemp('用户名', browserConfig.accountUsername) : null,
                              browserConfig.accountPassword.isNotEmpty ? buildRowTextTemp('密码', browserConfig.accountPassword) : null,
                              browserConfig.account.isNotEmpty ? buildRowTextTemp('账号平台', browserConfig.account) : null,
                              //browserConfig.cookie.isNotEmpty ? buildRowTextTemp('Cookie', browserConfig.cookie) : null,
                              buildRowTextTemp('浏览器版本', browserConfig.chromeSelectedValue),
                              buildRowTextTemp('操作系统', browserConfig.oswSelectedValue),
                              buildRowTextTemp('UA环境', browserConfig.ua),
                              buildRowTextTemp('语言', browserConfig.evaluateLanguage ? '基于IP' : browserConfig.languageSelectedValue),
                              buildRowTextTemp('界面语言', browserConfig.localeEvaluateLanguage ? '基于IP' : browserConfig.locale),
                              buildRowTextTemp('时区', browserConfig.timezone ? '基于IP' : browserConfig.timezoneSelectedValue),
                              buildRowTextTemp('经纬度', browserConfig.locationByIP ? '基于IP' : browserConfig.location),
                              buildRowTextTemp('WebRTC', webRTCOptions.firstWhere((e) => e.value == browserConfig.webrtc).label),
                              buildRowTextTemp('WebGL', browserConfig.webGL ? '开启' : '关闭'),
                              buildRowTextTemp('WebGL厂商', browserConfig.webGLVendor),
                              buildRowTextTemp('WebGL渲染', browserConfig.webGLRender),
                              buildRowTextTemp('Plugin', browserConfig.plugin ? '开启' : '关闭'),
                              buildRowTextTemp('字体', browserConfig.font ? '开启' : '关闭'),
                              buildRowTextTemp('ClientRects', browserConfig.clientRects ? '开启' : '关闭'),
                              buildRowTextTemp('AudioVoice', browserConfig.audioContext ? '开启' : '关闭'),
            
                              browserConfig.resolution.isNotEmpty ? buildRowTextTemp('分辨率', browserConfig.resolution) : const SizedBox(),
                              browserConfig.memory.isNotEmpty ? buildRowTextTemp('内存', browserConfig.memory) : const SizedBox(),
                              browserConfig.kernel.isNotEmpty ? buildRowTextTemp('CPU', browserConfig.kernel) : const SizedBox(),
                            ].whereType<Widget>().toList(),
                          ),
                        ),
                      ),
                    );
                  }
                )
              ],
            ),
          ),
            // 按钮组
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 10),
              child: Container(
                height: 60,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(30),
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                    PrimaryButton(
                        child: const Text('一键随机生成指纹'),
                        onPressed: () {
                          final randomChrome = getRandomItem(chromeVersionOptions);
                          final randomOS = getRandomItem(osOptions);
                          final randomWebGL = getRandomItem(webGL);
                          final randomResolution = getRandomItem(resolution);
                          final randomMemory = getRandomItem(memory);
                          final randomKernel = getRandomItem(kernel);

                          ref.read(browserConfigControllerProvider(config).notifier)
                          .updateConfig(chromeSelectedValue: randomChrome['name']);
                          ref.read(browserConfigControllerProvider(config).notifier)
                          .updateConfig(oswSelectedValue: randomOS['name']);
                          ref.read(browserConfigControllerProvider(config).notifier)
                          .updateConfig(webGLVendor: randomWebGL['name']);
                          ref.read(browserConfigControllerProvider(config).notifier)
                          .updateConfig(resolution: randomResolution['name']);
                          ref.read(browserConfigControllerProvider(config).notifier)
                          .updateConfig(memory: randomMemory['name']);
                          ref.read(browserConfigControllerProvider(config).notifier)
                          .updateConfig(kernel: randomKernel['name']);
                          fullVersionController.text = generateDetailedChromeVersion(randomChrome['name']);
                          ref.read(browserConfigControllerProvider(config).notifier)
                          .updateConfig(ua: generateUserAgent(fullVersionController.text, randomOS['name']));

                          final List<String>? options = vendors[browserConfig.webGLVendor];
                          // 随机选择一个值
                          int random = getRandomInt(options!.length);
                          // 更新状态
                          ref.read(browserConfigControllerProvider(config).notifier)
                          .updateConfig(webGLRender: options[random]);
                          platformController.text = identifyOperatingSystem(randomOS['name']);
                          platformVersionController.text = generateVersion(randomOS['name']);
                          // fullVersionController.text = generateDetailedChromeVersion(randomChrome['name']);
                        },
                      ),
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          SecondaryButton(
                            onPressed: () => Navigator.pop(context),
                            child: config != null ? const Text('返回') : const Text('取消'),
                          ),
                          const SizedBox(width: 10),
                          PrimaryButton(
                            child: config != null ? const Text('更新') : const Text('创建'),
                            onPressed: () async {
                              await generateConfig(
                                browserSettings: browserConfig,
                                ref: ref,
                                context: context,
                                platformController: platformController,
                                platformVersionController: platformVersionController,
                                fullVersionController: fullVersionController,
                                configMap: config != null ? jsonDecode(config as String) as Map<String, dynamic> : null
                                );
                            },
                          ),
                        ],
                      ),
                  ],
                                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget buildRowTextTemp(String leftText, String rightText) {
    return Row(
      spacing: 30,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(leftText, style: const TextStyle(color: Color(0xFF999999)),),
        Expanded(
          child: Text(
            rightText,
            style: const TextStyle(color: Color(0xFF666666)),
            textAlign: TextAlign.right,
          ),
        ),
      ],
    );
  }

  Widget buildRowTemp(String leftText, Widget rightWidget) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(width: 90, child: Text(leftText)),
        Flexible(child: rightWidget),
      ],
    );
  }

  Widget buildUa(
    WidgetRef ref,
    BrowserConfigModel browserSettings,
    TextEditingController textControllerUA,
    TextEditingController fullVersionController,
  ) {
    return SizedBox(
      //width: 400,
      child: TextField(
        maxLines: 4,
        minLines: 4,
        controller: textControllerUA,
        enabled: true,
        style: const TextStyle(color: Color(0xFF666666), fontSize: 14),
        decoration: InputDecoration(
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(color: Colors.transparent),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(color: Colors.transparent),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(color: Color(0xFF0C75F8)),
          ),
          suffixIcon: IconButton(
            icon: const Icon(
              Icons.update,
              color: Colors.amber,
              size: 16,
            ),
            onPressed: () {
              fullVersionController.text = generateDetailedChromeVersion(browserSettings.chromeSelectedValue);
              ref.read(browserConfigControllerProvider(null).notifier)
                 .updateConfig(ua: generateUserAgent(fullVersionController.text,
                          browserSettings.oswSelectedValue));
            },
          ),
        ),
      ),
    );
  }

  Widget buildWebGLRender(
    WidgetRef ref,
    BrowserConfigModel browserSettings,
    TextEditingController controller,
  ) {
    return SizedBox(
      width: 600,
      child: TextField(
        maxLines: 4,
        minLines: 4,
        controller: controller,
        enabled: true,
        style: const TextStyle(color: Color(0xFF666666), fontSize: 14),
        decoration: InputDecoration(
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(color: Colors.transparent),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(color: Colors.transparent),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide:
                const BorderSide(color: Color(0xFF0C75F8)),
          ),
          suffixIcon: IconButton(
            icon: const Icon(
              Icons.update,
              color: Colors.amber,
              size: 16,
            ),
            onPressed: () {
              final List<String>? options =
                  vendors[browserSettings.webGLVendor];
              // 随机选择一个值
              if (options != null && options.isNotEmpty) {
                int random = getRandomInt(options.length);
                // 更新选择的渲染器值
                final String renderer = options[random];
                // 更新控制器文本
                controller.text = renderer;
                // 更新状态
                ref.read(browserConfigControllerProvider(config).notifier)
                   .updateConfig(webGLRender: renderer);
              }
            },
          ),
        ),
      ),
    );
  }

  // 创建单个输入框的方法
  Widget _buildInputField({
    required String hint,
    required TextEditingController controller,
    Function(String)? onChanged,
  }) {
    return SizedBox(
      height: 40,
      child: TextField(
        controller: controller,
        onChanged: onChanged,
        decoration: InputDecoration(
          hintText: hint,
          hintStyle: const TextStyle(color: Color(0xFF666666), fontSize: 14),
          filled: true,
          fillColor: const Color(0xFFF3F4F8),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(50),
            borderSide: const BorderSide(color: Colors.black45),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(50),
            borderSide: const BorderSide(
              color: Color(0xFF0C75F8),
            ),
          ),
        ),
      ),
    );
  }

  Future<String> generateConfig(
    {required BrowserConfigModel browserSettings,
    required WidgetRef ref, 
    required BuildContext context, 
    required TextEditingController platformController,  //保留
    required TextEditingController platformVersionController,  // 保留
    required TextEditingController fullVersionController,  // 保留
    required Map<String, dynamic>? configMap}
    ) async {
    Map<String, int> dimensions = splitResolution(browserSettings.resolution);
    final String name = browserSettings.name.isNotEmpty
        ? browserSettings.name
        : generateRandomString(6);

    final oldConfig = browserSettings.editing ? configMap: null;
    final id = browserSettings.editing ? oldConfig!['id'] : 0;
    final sampleRate = browserSettings.editing
        ? oldConfig!['sample_rate']
        : getRandomInt(100).toString();
    final plugin = browserSettings.editing
        ? oldConfig!['plugin']
        : getRandomInt(10).toString();
    final font = browserSettings.editing
        ? oldConfig!['font']
        : getRandomOffset().toString();
    final offset1 = browserSettings.editing
        ? oldConfig!['offset1']
        : getRandomIntForFoo2Modern().toString();
    final fillText = browserSettings.editing
        ? oldConfig!['fill_text']
        : getRandomFillText().toString();
    final clientRect = browserSettings.editing
        ? oldConfig!['client_rect']
        : getRandomClientRect().toString();
    final webglPicture = browserSettings.editing
        ? oldConfig!['webgl_picture']
        : getRandomOffset().toString();
    final webgl = browserSettings.editing
        ? oldConfig!['webgl']
        : getRandomFloatWebGL().toString();
    final rgb = browserSettings.editing ? oldConfig!['rgb'] : setRGB();
    // Prepare settings data
    final Map<String, dynamic> settings = {
      // UI状态恢复
      // 基础信息
      'ui_name': name,
      'ui_account': browserSettings.account,
      'ui_account_username': browserSettings.accountUsername,
      'ui_account_password': browserSettings.accountPassword,
      'ui_cookie': browserSettings.cookie,
      'ui_chrome_version': browserSettings.chromeSelectedValue,
      'ui_os_version': browserSettings.oswSelectedValue,
      'ui_ua': browserSettings.ua,
      'ui_language_ip': browserSettings.evaluateLanguage,
      'ui_language_value': browserSettings.languageSelectedValue,
      'ui_locale_ip': browserSettings.localeEvaluateLanguage,
      'ui_locale_value': browserSettings.locale,
      'ui_timezone_ip': browserSettings.timezone,
      'ui_timezone_value': browserSettings.timezoneSelectedValue,
      'ui_location_ip': browserSettings.locationByIP,
      'ui_location': browserSettings.location,
      'ui_webrtc': browserSettings.webrtc,
      'ui_canvas': browserSettings.canvas,
      'ui_webgl': browserSettings.webGL,
      'ui_webgl_vendor': browserSettings.webGLVendor,
      'ui_webgl_render': browserSettings.webGLRender,
      'ui_plugin': browserSettings.plugin,
      'ui_font': browserSettings.font,
      'ui_resolution': browserSettings.resolution,
      'ui_audio_context': browserSettings.audioContext,
      'ui_client_rect': browserSettings.clientRects,
      'ui_memory': browserSettings.memory,
      'ui_kernel': browserSettings.kernel,
      // Cookie 账号密码
      'account': browserSettings.account != "" ? browserSettings.account : null,
      'account_username': browserSettings.accountUsername != ""
          ? browserSettings.accountUsername
          : null,
      'account_password': browserSettings.accountPassword != ""
          ? browserSettings.accountPassword
          : null,
      'cookie':
          browserSettings.cookie != "" ? browserSettings.cookie : null,
      // Geolocation Settings
      'timezone': browserSettings.timezone
          ? null
          : browserSettings.timezoneSelectedValue,
      'locale': browserSettings.localeEvaluateLanguage
          ? null
          : browserSettings.locale,
      'language': browserSettings.evaluateLanguage
          ? null
          : browserSettings.languageSelectedValue,
      'location': browserSettings.location,

      // Device Settings
      'resolution_height': dimensions['height'],
      'resolution_width': dimensions['width'],
      'kernel': browserSettings.kernel,
      'memory': browserSettings.memory,

      // Browser Settings
      'useragent': browserSettings.ua,
      'sample_rate': browserSettings.audioContext ? sampleRate : null,
      'plugin': browserSettings.plugin ? plugin : null,
      'webgl_render': browserSettings.webGLRender,
      'webgl_vendor': browserSettings.webGLVendor,
      // 'font': browserSettings.font ? font : '0',
      'offset1': offset1,
      'fill_text': fillText,
      'client_rect': browserSettings.clientRects ? clientRect : null,
      'webgl_picture': browserSettings.webGL ? webglPicture : null,
      // 'webgl': browserSettings.webGL ? webgl : null,
      'rgb': browserSettings.canvas ? rgb : null,
      'dnk': '1',
      'url': browserSettings.account != "" ? browserSettings.account : null,
      'platform': platformController.text,
      'platform_version': platformVersionController.text,
      'full_version': fullVersionController.text,
      'major_version': browserSettings.chromeSelectedValue,

      // 新增指纹
      'portScan': {"enable": "no"},
      'canvas': _generateCanvasConfig(),
      'webrtc': {"public":null, "private": null},
      'position': browserSettings.location,
      'webgl': {"vendor":browserSettings.webGLVendor,"renderer":browserSettings.webGLRender},
      'gpu': {"device":"Intel UHD Graphics 620","description":"Intel integrated graphics adapter"},
      'webaudio': _generateRandomNumber(),
      'clientrect': _generateClientRectConfig(),
      'screen': _generateScreenConfig(dimensions),
      'mobile': {"touchsupport": 0.0},
      'hardware': {"concurrency":browserSettings.kernel,"memory":browserSettings.memory},
      'clientHint': _generateClientHintConfig(platformController.text,platformVersionController.text,fullVersionController.text),
      'languages': null,
      'software': {"cookie":"yes","java":"no","dnt":"no"},
      'font': {"removefont":"Arial Unicode MS,Helvetica Neue"},
      // ...additional settings 可以在这里继续添加其他设置
    };

    // Encode settings to JSON
    final String jsonSettings = jsonEncode(settings);
    print('jsonSettings: $jsonSettings');
    if (browserSettings.editing) {
      final updateRequests = [
        BrowserWindowUpdateRequest(
          id: id,
          name: name,
          platform: browserSettings.account,
          groupId: browserSettings.groupId,
          proxyId: browserSettings.proxyId,
          proxyType: browserSettings.proxyType,
          parameters: jsonSettings,
          storage: '',
          tag: _convertTagsToJson(browserSettings.tag),
          comment: browserSettings.comment,
          sort: 0,
        ),
      ];

      // final controller = ref.read(browserWindowControllerProvider.notifier);

      try {
        // await controller.updateBrowserWindow(updateRequests);
        final res = await ref.read(browserWindowControllerProvider.notifier).updateBrowserWindow(updateRequests);
        if (context.mounted) {
          SnackBarUtil().showSuccess(context, res);
        }
      } catch (error) {
        if (context.mounted) {
          SnackBarUtil().showError(context, '环境更新失败: $error');
        }
      }
    } else {
      final createRequest = BrowserCreateRequest(
        name: name,
        platform: browserSettings.account,
        groupId: browserSettings.groupId,
        proxyId: browserSettings.proxyId,
        proxyType: browserSettings.proxyType,
        parameters: jsonSettings,
        tag: _convertTagsToJson(browserSettings.tag),
        comment: browserSettings.comment,
      );

      print('Create request data: ${createRequest.parameters}');

      try {
        final res = await ref.read(browserWindowControllerProvider.notifier).createBrowserWindow(createRequest);
        // 创建成功，返回工作台
        if (context.mounted) {
          SnackBarUtil().showSuccess(context, res);
          Navigator.pop(context);
          //context.pop();
        }
      } catch (error) {
        if (context.mounted) {
          SnackBarUtil().showError(context, '环境创建失败: $error');
        }
      }
    }
    return jsonSettings;
  }

  // Generate random RGB values
  String setRGB() {
    final Random random = Random();
    final int rand1 = random.nextInt(5);
    final int rand2 = random.nextInt(6);
    final int rand3 = random.nextInt(7);
    final int rand4 = random.nextInt(255);
    return '$rand1,$rand2,$rand3,$rand4';
  }

  // Generate canvas config with random noise between 0.5-3
  Map<String, String> _generateCanvasConfig() {
    final Random random = Random();
    // 生成0.5到3之间的随机噪声值
    final double noise = 0.5 + (random.nextDouble() * 2.5);
    // 保留2位小数
    final String noiseStr = noise.toStringAsFixed(2);
    return {"noise": noiseStr};
  }

  // Generate random number between 0 and 100
  int _generateRandomNumber() {
    final Random random = Random();
    return random.nextInt(101); // 0-100 (包含100)
  }

  // Generate client rect config with random x and y values
  Map<String, String> _generateClientRectConfig() {
    final Random random = Random();
    // 生成随机的x和y值，范围可以根据需要调整
    final double x = random.nextDouble() * 10; // 0-10之间的随机值
    final double y = random.nextDouble() * 10; // 0-10之间的随机值
    // 保留1位小数
    final String xStr = x.toStringAsFixed(1);
    final String yStr = y.toStringAsFixed(1);
    return {"x":"0.0","y":"0.0"};
  }

  // Generate screen config with availWidth/Height not exceeding actual dimensions
  Map<String, String> _generateScreenConfig(Map<String, int> dimensions) {
    final Random random = Random();
    final int width = dimensions['width'] ?? 1920;
    final int height = dimensions['height'] ?? 1080;
    
    // availWidth和availHeight应该小于或等于实际尺寸
    // 通常会减去任务栏等系统界面占用的空间（20-80像素）
    final int taskbarHeight = 20 + random.nextInt(61); // 20-80像素的任务栏高度
    final int sidebarWidth = random.nextInt(21); // 0-20像素的侧边栏宽度
    
    final double availWidth = (width - sidebarWidth).toDouble();
    final double availHeight = (height - taskbarHeight).toDouble();
    final double colorDepth = 24.0; // 通常是24位色深
    
    return {"width": width.toString(), "height": height.toString(), "colorDepth": colorDepth.toString(), "availWidth": availWidth.toString(), "availHeight": availHeight.toString()};
  }

  // Generate client hint config by parsing user agent string
  Map<String, String> _generateClientHintConfig(String? platform, String? platformVersion, String? uaFullVersion) {
    // 设置默认值
    String finalPlatform = platform ?? "Windows";
    String finalPlatformVersion = platformVersion ?? "15.0.0";
    String finalUaFullVersion = uaFullVersion ?? "118.0.0.0";

    // 根据platform生成navigator.platform
    String navigatorPlatform;
    if (finalPlatform.startsWith("Windows")) {
      navigatorPlatform = "Win32";
      // 如果没有提供platformVersion，根据Windows版本设置默认值
      if (platformVersion == null) {
        if (finalPlatform.contains("11")) {
          finalPlatformVersion = "15.0.0"; // Windows 11
        } else if (finalPlatform.contains("10")) {
          finalPlatformVersion = "10.0.0"; // Windows 10
        } else {
          finalPlatformVersion = "15.0.0"; // 默认Windows 11
        }
      }
    } else if (finalPlatform.startsWith("macOS")) {
      navigatorPlatform = "MacIntel";
      // 如果没有提供platformVersion，根据macOS版本设置默认值
      if (platformVersion == null) {
        if (finalPlatform.contains("14")) {
          finalPlatformVersion = "14.0.0"; // macOS 14
        } else if (finalPlatform.contains("13")) {
          finalPlatformVersion = "13.0.0"; // macOS 13
        } else if (finalPlatform.contains("12")) {
          finalPlatformVersion = "12.0.0"; // macOS 12
        } else if (finalPlatform.contains("11")) {
          finalPlatformVersion = "11.0.0"; // macOS 11
        } else {
          finalPlatformVersion = "14.0.0"; // 默认macOS 14
        }
      }
    } else if (finalPlatform.startsWith("Linux")) {
      navigatorPlatform = "Linux x86_64";
      finalPlatformVersion = "0.0.0";
    } else {
      // 未知平台，默认为Windows
      navigatorPlatform = "Win32";
      finalPlatformVersion = "15.0.0";
    }

    // 规范化平台名称
    if (finalPlatform.startsWith("Windows")) {
      finalPlatform = "Windows";
    } else if (finalPlatform.startsWith("macOS")) {
      finalPlatform = "macOS";
    }

    // 默认配置（主要针对桌面端）
    String architecture = "x64";
    String bitness = "64";
    String mobile = "?0";

    return {
      "platform": finalPlatform,
      "navigator.platform": navigatorPlatform,
      "platform_version": finalPlatformVersion,
      "ua_full_version": finalUaFullVersion,
      "mobile": mobile,
      "architecture": architecture,
      "bitness": bitness
    };
  }

  // Generate languages config with random q values between 0.5-0.9
  Map<String, String> _generateLanguagesConfig() {
    final Random random = Random();
    // 生成0.5到0.9之间的随机q值
    final double q1 = 0.5 + (random.nextDouble() * 0.4); // 0.5-0.9
    final double q2 = 0.5 + (random.nextDouble() * 0.4); // 0.5-0.9
    
    // 保留1位小数
    final String q1Str = q1.toStringAsFixed(1);
    final String q2Str = q2.toStringAsFixed(1);
    
    return {
      "js": "en-US,en,zh-CN",
      "http": "en-US,en;q=$q1Str,zh-CN;q=$q2Str"
    };
  }

  // 辅助函数：将分辨率字符串解析为宽度和高度的映射
  Map<String, int> splitResolution(String resolution) {
    final parts = resolution.split('*');
    if (parts.length == 2) {
      final int? width = int.tryParse(parts[0]);
      final int? height = int.tryParse(parts[1]);
      if (width != null && height != null) {
        return {'width': width, 'height': height};
      }
    }
    // 如果解析失败，返回默认值
    return {'width': 0, 'height': 0};
  }

  // Generate a random offset for font settings
  int getRandomOffset() {
    return Random().nextInt(20);
  }

  // Generate a random integer for specific conditions
  int getRandomIntForFoo2Modern() {
    final int tmp = Random().nextInt(10);
    return tmp > 0 ? 0 : 1;
  }

  // Generate a random integer for fillText
  int getRandomFillText() {
    return Random().nextInt(3);
  }

  // Generate a random integer for clientRect
  int getRandomClientRect() {
    return Random().nextInt(4);
  }

  // Generate a random small float for WebGL settings
  double getRandomFloatWebGL() {
    final Random random = Random();
    const double min = 0.0000001;
    const double max = 0.0009999;
    return min + (max - min) * random.nextDouble();
  }

  // Convert a map to a string representation
  String mapToString(Map<String, dynamic> map) {
    return map.entries.map((entry) => "${entry.key}:${entry.value}").join(",");
  }

  int getRandomInt(int max) => Random().nextInt(max);

  T getRandomItem<T>(List<T> list) {
    final random = Random();
    int index = random.nextInt(list.length); // 随机获取一个列表中的索引
    return list[index]; // 返回该索引对应的值
  }

  // 将逗号分隔的标签字符串转换为JSON数组字符串
  String _convertTagsToJson(String tags) {
    if (tags.isEmpty) return '[]';
    
    // 先将中文逗号替换为英文逗号，然后按逗号分割并去除空白
    final List<String> tagList = tags
        .replaceAll('，', ',')  // 将中文逗号替换为英文逗号
        .split(',')
        .map((tag) => tag.trim())
        .where((tag) => tag.isNotEmpty)
        .toList();
    
    // 转换为JSON数组字符串
    return jsonEncode(tagList);
  }
}
