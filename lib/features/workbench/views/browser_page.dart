import 'package:flutter/material.dart';
import 'package:frontend_re/features/workbench/controllers/browser_table_controller.dart';
import 'package:frontend_re/features/workbench/controllers/browser_window_controller.dart';
import 'package:frontend_re/widgets/ad_banner.dart';
import 'package:frontend_re/features/workbench/views/components/browser_table.dart';
import 'package:frontend_re/features/workbench/views/components/group_create_dialog.dart';
import 'package:frontend_re/core/utils/ui/snack_bar_util.dart';
import 'package:frontend_re/widgets/custom_border_button.dart';
import 'package:frontend_re/widgets/custom_solid_button.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class BrowserPage extends ConsumerWidget {
  const BrowserPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Temporarily watch the provider to keep it alive
    ref.watch(browserWindowControllerProvider);
    ref.watch(browserTableControllerProvider);



    return Column(
      spacing: 16,
      children: [
        Row(
          spacing: 16,
          children: [
            // 快捷创建按钮
            CustomSolidButton(
              iconPath: 'assets/svg/right_default.svg',
              backgroundColor: const Color(0xFF1E1C1D),
              iconColor: const Color(0xFF1E1C1D),
              iconSize: 12,
              textColor: const Color(0xFFFFFFFF),
              text: '新建环境',
              onPressed: () {
                // 使用路径导航，更直观
                context.pushNamed('addBrowser');
              },
              textDirection: TextDirection.rtl,
            ),
            // 最近删除按钮
            CustomSolidButton(
              iconPath: 'assets/svg/delete.svg',
              iconColor: const Color(0xFFFFFFFF),
              iconBackgroundColor: const Color(0xFF1E1C1D),
              backgroundColor: const Color(0xFFFFFFFF),
              textColor: const Color(0xFF8D8E93),
              textDirection: TextDirection.ltr,
              text: '最近删除',
              onPressed: () {
                context.pushNamed('browserTrash');
              },
            ),
            // 广告
            Flexible(
              fit: FlexFit.tight,
              child: Container(
                height: 40,
                width: 400,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(50),
                ),
                child: const Center(child: AdBanner()),
              ),
            )
          ],
        ),
        Row(
          spacing: 16,
          children: [
            // 分组按钮
            CustomSolidButton(
              iconPath: 'assets/svg/right_default.svg',
              backgroundColor: const Color(0xFFFFFFFF),
              iconColor: const Color(0xFFFFFFFF),
              iconBackgroundColor: const Color(0xFF1E1C1D),
              iconSize: 12,
              textColor: const Color(0xFF8D8E93),
              text: '管理分组',
              onPressed: () {
                showDialog(
                  context: context,
                  builder: (context) => const GroupCreateDialog(),
                );
              },
              textDirection: TextDirection.rtl,
            ),
            // 打开按钮
            CustomSolidButton(
              iconPath: 'assets/svg/open.svg',
              iconColor: const Color(0xFFFFFFFF),
              iconBackgroundColor: const Color(0xFF1E1C1D),
              backgroundColor: const Color(0xFFFFFFFF),
              textColor: const Color(0xFF8D8E93),
              iconSize: 14,
              textDirection: TextDirection.ltr,
              text: '批量打开',
              onPressed: () async {
                final selectedIds = ref.read(browserTableControllerProvider).selectedWindowIds;
                print(selectedIds);
                if (selectedIds.isEmpty) {
                  if (context.mounted) SnackBarUtil().showInfo(context, '请选择要打开的窗口');
                  return;
                }

                // 显示确认打开对话框
                final bool? confirmed = await showDialog<bool>(
                  context: context,
                  builder: (BuildContext dialogContext) {
                    return AlertDialog(
                      title: const Text('确认打开'),
                      content: Text('您确定要打开选中的 ${selectedIds.length} 个窗口吗？'),
                      actions: <Widget>[
                        TextButton(
                          child: const Text('取消'),
                          onPressed: () {
                            Navigator.of(dialogContext).pop(false); // 用户取消
                          },
                        ),
                        TextButton(
                          child: const Text('确认'),
                          onPressed: () {
                            Navigator.of(dialogContext).pop(true); // 用户确认
                            if (context.mounted) SnackBarUtil().showInfo(context, '正在打开···');
                          },
                        ),
                      ],
                    );
                  },
                );

                // 如果用户确认打开
                if (confirmed == true) {
                  try {
                    final res = await ref.read(browserTableControllerProvider.notifier).openSelectedWindows();
                    if (context.mounted) SnackBarUtil().showInfo(context, res);
                  } catch (e) {
                    if (context.mounted) SnackBarUtil().showError(context, e.toString());
                  }
                }
              },
            ),
            Expanded(
              child: Row(
                children: [
                  CustomBorderButton(
                    borderColor: const Color(0xFFFFFFFF),
                    iconPath: 'assets/svg/close.svg',
                    backgroundColor: const Color(0xFFFFFFFF),
                    iconColor: const Color(0xFF8D8E93),
                    horizontalPadding: 12,
                    verticalPadding: 12,
                    iconSize: 20,
                    tooltip: '全部关闭',
                    onPressed: () async {
                      // 显示确认打开对话框
                      final bool? confirmed = await showDialog<bool>(
                        context: context,
                        builder: (BuildContext dialogContext) {
                          return AlertDialog(
                            title: const Text('确认关闭'),
                            content: const Text('您确定要关闭所有的已启动浏览器窗口吗？'),
                            actions: <Widget>[
                              TextButton(
                                child: const Text('取消'),
                                onPressed: () {
                                  Navigator.of(dialogContext).pop(false); // 用户取消
                                },
                              ),
                              TextButton(
                                child: const Text('确认'),
                                onPressed: () {
                                  Navigator.of(dialogContext).pop(true); // 用户确认
                                  if (context.mounted) SnackBarUtil().showInfo(context, '正在关闭···');
                                },
                              ),
                            ],
                          );
                        },
                      );

                      // 如果用户确认关闭
                      if (confirmed == true) {
                        try {
                          final res = await ref.read(browserTableControllerProvider.notifier).closeWindows();
                          if (context.mounted) SnackBarUtil().showInfo(context, res);
                        } catch (e) {
                          if (context.mounted) SnackBarUtil().showError(context, e.toString());
                        }
                      }
                    },
                  ),
                  CustomBorderButton(
                    iconPath: 'assets/svg/output.svg',
                    backgroundColor: const Color(0xFFFFFFFF),
                    iconColor: const Color(0xFF8D8E93),
                    horizontalPadding: 12,
                    verticalPadding: 12,
                    iconSize: 20,
                    tooltip: '批量上传环境文件',
                    onPressed: () {
                      print('一键上传环境文件');
                    },
                  ),
                  // CustomBorderButton(
                  //   borderColor: const Color(0xFFFFFFFF),
                  //   iconPath: 'assets/svg/move.svg',
                  //   backgroundColor: const Color(0xFFFFFFFF),
                  //   iconColor: const Color(0xFF8D8E93),
                  //   horizontalPadding: 12,
                  //   verticalPadding: 12,
                  //   iconSize: 20,
                  //   onPressed: () {
                  //     print('快捷创建');
                  //   },
                  // ),
                  // CustomBorderButton(
                  //   borderColor: const Color(0xFFFFFFFF),
                  //   iconPath: 'assets/svg/open.svg',
                  //   backgroundColor: const Color(0xFFFFFFFF),
                  //   iconColor: const Color(0xFF8D8E93),
                  //   horizontalPadding: 12,
                  //   verticalPadding: 12,
                  //   iconSize: 20,
                  //   onPressed: () {
                  //     print('快捷创建');
                  //   },
                  // ),
                  CustomBorderButton(
                    borderColor: const Color(0xFFFFFFFF),
                    iconPath: 'assets/svg/delete.svg',
                    backgroundColor: const Color(0xFFFFFFFF),
                    iconColor: const Color(0xFF8D8E93),
                    horizontalPadding: 12,
                    verticalPadding: 12,
                    iconSize: 20,
                    tooltip: '批量删除',
                    onPressed: () async {
                      final selectedIds = ref.read(browserTableControllerProvider).selectedWindowIds;
                      if (selectedIds.isEmpty) {
                        if (context.mounted) SnackBarUtil().showInfo(context, '请选择要删除的窗口');
                        return;
                      }

                      // 显示确认删除对话框
                      final bool? confirmed = await showDialog<bool>(
                        context: context,
                        builder: (BuildContext dialogContext) {
                          return AlertDialog(
                            title: const Text('确认删除'),
                            content: Text('您确定要删除选中的 ${selectedIds.length} 个窗口吗？'),
                            actions: <Widget>[
                              TextButton(
                                child: const Text('取消'),
                                onPressed: () {
                                  Navigator.of(dialogContext).pop(false); // 用户取消
                                },
                              ),
                              TextButton(
                                child: const Text('确认'),
                                onPressed: () {
                                  Navigator.of(dialogContext).pop(true); // 用户确认
                                },
                              ),
                            ],
                          );
                        },
                      );

                      // 如果用户确认删除
                      if (confirmed == true) {
                        try {
                          final res = await ref.read(browserTableControllerProvider.notifier).deleteSelectedWindows();
                          if (context.mounted) SnackBarUtil().showSuccess(context, res);
                        } catch (e) {
                          if (context.mounted) SnackBarUtil().showError(context, e.toString());
                        }
                      }
                    },
                  ),
                  const Spacer(),
                  // 右侧按钮组
                  // CustomBorderButton(
                  //   borderColor: const Color(0xFFFFFFFF),
                  //   iconPath: 'assets/svg/tags.svg',
                  //   backgroundColor: const Color(0xFFFFFFFF),
                  //   iconColor: const Color(0xFF8D8E93),
                  //   horizontalPadding: 12,
                  //   verticalPadding: 12,
                  //   iconSize: 20,
                  //   onPressed: () {
                  //     print('快捷创建');
                  //   },
                  // ),
                  CustomBorderButton(
                    borderColor: const Color(0xFFFFFFFF),
                    iconPath: 'assets/svg/refresh.svg',
                    backgroundColor: const Color(0xFFFFFFFF),
                    iconColor: const Color(0xFF8D8E93),
                    horizontalPadding: 12,
                    verticalPadding: 12,
                    iconSize: 20,
                    onPressed: () {
                      // 刷新浏览器窗口数据，保持当前分页状态
                      final currentPage = ref.read(currentPageProvider);
                      ref.read(browserWindowControllerProvider.notifier).loadBrowserWindows(
                        offset: currentPage.offset,
                        pageSize: currentPage.pageSize,
                      );
                    },
                  ),
                ],
              ),
            )
          ],
        ),
        // 显示子路由内容
        const Expanded(
          // 优先使用navigationShell，其次使用child，最后默认显示BrowserTable
          child: BrowserTable(),
        ),
      ],
    );
  }
}
