import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../domain/services/browser_management_service.dart';
import '../../../domain/models/browser_instance_model.dart';

part 'browser_management_controller.g.dart';

/// 浏览器管理服务Provider
@Riverpod(keepAlive: true)
BrowserManagementService browserManagementService(Ref ref) {
  final service = BrowserManagementService.getInstance();
  service.initialize();
  
  // 确保在Provider销毁时清理资源
  ref.onDispose(() {
    service.dispose();
  });
  
  return service;
}

/// 浏览器状态变化流Provider
@Riverpod(keepAlive: true)
Stream<BrowserInstanceModel> browserStatusStream(Ref ref) {
  final service = ref.watch(browserManagementServiceProvider);
  return service.onStatusChanged;
}

/// 特定浏览器的状态Provider
@Riverpod(keepAlive: true)
BrowserInstanceModel? browserInstance(Ref ref, String browserId) {
  // 监听状态流，确保状态变化能够传播到UI
  final statusStreamAsyncValue = ref.watch(browserStatusStreamProvider);
  
  final controller = ref.watch(browserManagementControllerProvider);
  final browser = controller.getBrowser(browserId);
  
  // 添加调试信息
  print('🎯 browserInstance($browserId): ${browser?.status?.name ?? "null"} (stream state: ${statusStreamAsyncValue.isLoading ? "loading" : statusStreamAsyncValue.hasError ? "error" : "data"})');
  
  return browser;
}

/// 浏览器管理控制器
class BrowserManagementController {
  final BrowserManagementService _service;

  BrowserManagementController(this._service);

  /// 启动浏览器
  Future<bool> launchBrowser({
    required String id,
    required String name,
    required String configPath,
    required Map<String, dynamic> config,
  }) async {
    return await _service.launchBrowser(
      id: id,
      name: name,
      configPath: configPath,
      config: config,
    );
  }

  /// 停止浏览器
  Future<bool> stopBrowser(String id) async {
    return await _service.stopBrowser(id);
  }

  /// 重启浏览器
  Future<bool> restartBrowser(String id) async {
    return await _service.restartBrowser(id);
  }

  /// 批量启动浏览器
  Future<Map<String, bool>> batchLaunchBrowsers(
    List<Map<String, dynamic>> browserConfigs,
  ) async {
    return await _service.batchLaunchBrowsers(browserConfigs);
  }

  /// 批量停止浏览器
  Future<Map<String, bool>> batchStopBrowsers(List<String> browserIds) async {
    return await _service.batchStopBrowsers(browserIds);
  }

  /// 停止所有浏览器
  Future<void> stopAllBrowsers() async {
    await _service.stopAllBrowsers();
  }

  /// 移除浏览器实例
  void removeBrowser(String id) {
    _service.removeBrowser(id);
  }

  /// 获取浏览器实例
  BrowserInstanceModel? getBrowser(String id) {
    return _service.getBrowser(id);
  }

  /// 获取所有浏览器
  List<BrowserInstanceModel> getAllBrowsers() {
    return _service.allBrowsers;
  }

  /// 获取运行中的浏览器
  List<BrowserInstanceModel> getRunningBrowsers() {
    return _service.runningBrowsers;
  }

  /// 获取已停止的浏览器
  List<BrowserInstanceModel> getStoppedBrowsers() {
    return _service.stoppedBrowsers;
  }

  /// 获取服务状态
  Map<String, dynamic> getStatus() {
    return _service.getStatus();
  }
}

/// 浏览器管理控制器Provider
final browserManagementControllerProvider = Provider<BrowserManagementController>((ref) {
  final service = ref.watch(browserManagementServiceProvider);
  return BrowserManagementController(service);
}); 
