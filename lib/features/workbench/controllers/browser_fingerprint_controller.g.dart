// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'browser_fingerprint_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$browserFingerprintControllerHash() =>
    r'37ed356cc70987b1565c0c4585da0aa3dd70bac6';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$BrowserFingerprintController
    extends BuildlessAutoDisposeNotifier<BrowserFingerprintConfig> {
  late final Object? initData;

  BrowserFingerprintConfig build(
    Object? initData,
  );
}

/// See also [BrowserFingerprintController].
@ProviderFor(BrowserFingerprintController)
const browserFingerprintControllerProvider =
    BrowserFingerprintControllerFamily();

/// See also [BrowserFingerprintController].
class BrowserFingerprintControllerFamily
    extends Family<BrowserFingerprintConfig> {
  /// See also [BrowserFingerprintController].
  const BrowserFingerprintControllerFamily();

  /// See also [BrowserFingerprintController].
  BrowserFingerprintControllerProvider call(
    Object? initData,
  ) {
    return BrowserFingerprintControllerProvider(
      initData,
    );
  }

  @override
  BrowserFingerprintControllerProvider getProviderOverride(
    covariant BrowserFingerprintControllerProvider provider,
  ) {
    return call(
      provider.initData,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'browserFingerprintControllerProvider';
}

/// See also [BrowserFingerprintController].
class BrowserFingerprintControllerProvider
    extends AutoDisposeNotifierProviderImpl<BrowserFingerprintController,
        BrowserFingerprintConfig> {
  /// See also [BrowserFingerprintController].
  BrowserFingerprintControllerProvider(
    Object? initData,
  ) : this._internal(
          () => BrowserFingerprintController()..initData = initData,
          from: browserFingerprintControllerProvider,
          name: r'browserFingerprintControllerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$browserFingerprintControllerHash,
          dependencies: BrowserFingerprintControllerFamily._dependencies,
          allTransitiveDependencies:
              BrowserFingerprintControllerFamily._allTransitiveDependencies,
          initData: initData,
        );

  BrowserFingerprintControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.initData,
  }) : super.internal();

  final Object? initData;

  @override
  BrowserFingerprintConfig runNotifierBuild(
    covariant BrowserFingerprintController notifier,
  ) {
    return notifier.build(
      initData,
    );
  }

  @override
  Override overrideWith(BrowserFingerprintController Function() create) {
    return ProviderOverride(
      origin: this,
      override: BrowserFingerprintControllerProvider._internal(
        () => create()..initData = initData,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        initData: initData,
      ),
    );
  }

  @override
  AutoDisposeNotifierProviderElement<BrowserFingerprintController,
      BrowserFingerprintConfig> createElement() {
    return _BrowserFingerprintControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is BrowserFingerprintControllerProvider &&
        other.initData == initData;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, initData.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin BrowserFingerprintControllerRef
    on AutoDisposeNotifierProviderRef<BrowserFingerprintConfig> {
  /// The parameter `initData` of this provider.
  Object? get initData;
}

class _BrowserFingerprintControllerProviderElement
    extends AutoDisposeNotifierProviderElement<BrowserFingerprintController,
        BrowserFingerprintConfig> with BrowserFingerprintControllerRef {
  _BrowserFingerprintControllerProviderElement(super.provider);

  @override
  Object? get initData =>
      (origin as BrowserFingerprintControllerProvider).initData;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
