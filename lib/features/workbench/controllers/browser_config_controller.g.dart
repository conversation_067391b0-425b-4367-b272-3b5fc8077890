// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'browser_config_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$browserConfigControllerHash() =>
    r'6e96c1a5adb25e815b4c695dd1f3de3128ca399d';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$BrowserConfigController
    extends BuildlessAutoDisposeNotifier<BrowserConfigModel> {
  late final Object? initData;

  BrowserConfigModel build(
    Object? initData,
  );
}

/// See also [BrowserConfigController].
@ProviderFor(BrowserConfigController)
const browserConfigControllerProvider = BrowserConfigControllerFamily();

/// See also [BrowserConfigController].
class BrowserConfigControllerFamily extends Family<BrowserConfigModel> {
  /// See also [BrowserConfigController].
  const BrowserConfigControllerFamily();

  /// See also [BrowserConfigController].
  BrowserConfigControllerProvider call(
    Object? initData,
  ) {
    return BrowserConfigControllerProvider(
      initData,
    );
  }

  @override
  BrowserConfigControllerProvider getProviderOverride(
    covariant BrowserConfigControllerProvider provider,
  ) {
    return call(
      provider.initData,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'browserConfigControllerProvider';
}

/// See also [BrowserConfigController].
class BrowserConfigControllerProvider extends AutoDisposeNotifierProviderImpl<
    BrowserConfigController, BrowserConfigModel> {
  /// See also [BrowserConfigController].
  BrowserConfigControllerProvider(
    Object? initData,
  ) : this._internal(
          () => BrowserConfigController()..initData = initData,
          from: browserConfigControllerProvider,
          name: r'browserConfigControllerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$browserConfigControllerHash,
          dependencies: BrowserConfigControllerFamily._dependencies,
          allTransitiveDependencies:
              BrowserConfigControllerFamily._allTransitiveDependencies,
          initData: initData,
        );

  BrowserConfigControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.initData,
  }) : super.internal();

  final Object? initData;

  @override
  BrowserConfigModel runNotifierBuild(
    covariant BrowserConfigController notifier,
  ) {
    return notifier.build(
      initData,
    );
  }

  @override
  Override overrideWith(BrowserConfigController Function() create) {
    return ProviderOverride(
      origin: this,
      override: BrowserConfigControllerProvider._internal(
        () => create()..initData = initData,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        initData: initData,
      ),
    );
  }

  @override
  AutoDisposeNotifierProviderElement<BrowserConfigController,
      BrowserConfigModel> createElement() {
    return _BrowserConfigControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is BrowserConfigControllerProvider &&
        other.initData == initData;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, initData.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin BrowserConfigControllerRef
    on AutoDisposeNotifierProviderRef<BrowserConfigModel> {
  /// The parameter `initData` of this provider.
  Object? get initData;
}

class _BrowserConfigControllerProviderElement
    extends AutoDisposeNotifierProviderElement<BrowserConfigController,
        BrowserConfigModel> with BrowserConfigControllerRef {
  _BrowserConfigControllerProviderElement(super.provider);

  @override
  Object? get initData => (origin as BrowserConfigControllerProvider).initData;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
