import 'dart:convert';

import 'package:frontend_re/features/workbench/models/browser_fingerprint_model.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'browser_fingerprint_controller.g.dart';

@riverpod
class BrowserFingerprintController extends _$BrowserFingerprintController {
  @override
  BrowserFingerprintConfig build(Object? initData) {
    if (initData != '' && initData != null) {
      final config = jsonDecode(initData as String);
      return BrowserFingerprintConfig.fromJson(config);
    }
    
    // 默认配置
    return const BrowserFingerprintConfig(
      webrtc: WebRTCConfig(
        public: '192.168.1.1',
        private: '10.0.0.1',
      ),
      position: PositionConfig(
        longitude: 116.397128,
        latitude: 39.916527,
        altitude: 50.0,
        accuracy: 10.0,
      ),
      canvas: CanvasConfig(noise: 0.1),
      webgl: WebGLConfig(
        vendor: 'Google Inc. (AMD)',
        renderer: 'ANGLE (Intel, Intel(R) HD Graphics 520 Direct3D11 vs_5_0 ps_5_0)',
      ),
      webaudio: 0.1,
      clientrect: ClientRectConfig(x: 0.5, y: 0.5),
      gpu: GPUConfig(
        device: 'Intel HD Graphics 520',
        description: 'Intel HD Graphics 520',
      ),
      screen: ScreenConfig(
        width: 1920,
        height: 1080,
        colorDepth: 24,
        availWidth: 1920,
        availHeight: 1040,
      ),
      mobile: MobileConfig(touchsupport: 0),
      hardware: HardwareConfig(
        concurrency: 4,
        memory: 8,
      ),
      clientHint: ClientHintConfig(
        platform: 'Windows',
        navigatorPlatform: 'Win32',
        platformVersion: '10.0.0',
        uaFullVersion: '127.0.6533.72',
        mobile: 'false',
        architecture: 'x86',
        bitness: '64',
      ),
      languages: LanguagesConfig(
        js: 'zh-CN',
        http: 'zh-CN,zh;q=0.9,en;q=0.8',
      ),
      software: SoftwareConfig(
        cookie: 'yes',
        java: 'no',
        dnt: 'no',
      ),
      portScan: PortScanConfig(enable: 'no'),
      font: FontConfig(removefont: ''),
    );
  }

  // 更新WebRTC配置
  void updateWebRTC({
    String? publicIP,
    String? privateIP,
  }) {
    final currentWebRTC = state.webrtc ?? const WebRTCConfig(public: '', private: '');
    state = state.copyWith(
      webrtc: currentWebRTC.copyWith(
        public: publicIP ?? currentWebRTC.public,
        private: privateIP ?? currentWebRTC.private,
      ),
    );
  }

  // 更新地理位置配置
  void updatePosition({
    double? longitude,
    double? latitude,
    double? altitude,
    double? accuracy,
  }) {
    final currentPosition = state.position ?? const PositionConfig(
      longitude: 0,
      latitude: 0,
      altitude: 0,
      accuracy: 0,
    );
    state = state.copyWith(
      position: currentPosition.copyWith(
        longitude: longitude ?? currentPosition.longitude,
        latitude: latitude ?? currentPosition.latitude,
        altitude: altitude ?? currentPosition.altitude,
        accuracy: accuracy ?? currentPosition.accuracy,
      ),
    );
  }

  // 更新Canvas配置
  void updateCanvas({double? noise}) {
    final currentCanvas = state.canvas ?? const CanvasConfig(noise: 0);
    state = state.copyWith(
      canvas: currentCanvas.copyWith(
        noise: noise ?? currentCanvas.noise,
      ),
    );
  }

  // 更新WebGL配置
  void updateWebGL({
    String? vendor,
    String? renderer,
  }) {
    final currentWebGL = state.webgl ?? const WebGLConfig(vendor: '', renderer: '');
    state = state.copyWith(
      webgl: currentWebGL.copyWith(
        vendor: vendor ?? currentWebGL.vendor,
        renderer: renderer ?? currentWebGL.renderer,
      ),
    );
  }

  // 更新WebAudio配置
  void updateWebAudio(double noise) {
    state = state.copyWith(webaudio: noise);
  }

  // 更新ClientRect配置
  void updateClientRect({
    double? x,
    double? y,
  }) {
    final currentRect = state.clientrect ?? const ClientRectConfig(x: 0, y: 0);
    state = state.copyWith(
      clientrect: currentRect.copyWith(
        x: x ?? currentRect.x,
        y: y ?? currentRect.y,
      ),
    );
  }

  // 更新GPU配置
  void updateGPU({
    String? device,
    String? description,
  }) {
    final currentGPU = state.gpu ?? const GPUConfig(device: '', description: '');
    state = state.copyWith(
      gpu: currentGPU.copyWith(
        device: device ?? currentGPU.device,
        description: description ?? currentGPU.description,
      ),
    );
  }

  // 更新屏幕配置
  void updateScreen({
    double? width,
    double? height,
    double? colorDepth,
    double? availWidth,
    double? availHeight,
  }) {
    final currentScreen = state.screen ?? const ScreenConfig(
      width: 0,
      height: 0,
      colorDepth: 0,
      availWidth: 0,
      availHeight: 0,
    );
    state = state.copyWith(
      screen: currentScreen.copyWith(
        width: width ?? currentScreen.width,
        height: height ?? currentScreen.height,
        colorDepth: colorDepth ?? currentScreen.colorDepth,
        availWidth: availWidth ?? currentScreen.availWidth,
        availHeight: availHeight ?? currentScreen.availHeight,
      ),
    );
  }

  // 更新移动设备配置
  void updateMobile({double? touchsupport}) {
    final currentMobile = state.mobile ?? const MobileConfig(touchsupport: 0);
    state = state.copyWith(
      mobile: currentMobile.copyWith(
        touchsupport: touchsupport ?? currentMobile.touchsupport,
      ),
    );
  }

  // 更新硬件配置
  void updateHardware({
    double? concurrency,
    double? memory,
  }) {
    final currentHardware = state.hardware ?? const HardwareConfig(concurrency: 0, memory: 0);
    state = state.copyWith(
      hardware: currentHardware.copyWith(
        concurrency: concurrency ?? currentHardware.concurrency,
        memory: memory ?? currentHardware.memory,
      ),
    );
  }

  // 更新客户端提示配置
  void updateClientHint({
    String? platform,
    String? navigatorPlatform,
    String? platformVersion,
    String? uaFullVersion,
    String? mobile,
    String? architecture,
    String? bitness,
  }) {
    final currentHint = state.clientHint ?? const ClientHintConfig(
      platform: '',
      navigatorPlatform: '',
      platformVersion: '',
      uaFullVersion: '',
      mobile: '',
      architecture: '',
      bitness: '',
    );
    state = state.copyWith(
      clientHint: currentHint.copyWith(
        platform: platform ?? currentHint.platform,
        navigatorPlatform: navigatorPlatform ?? currentHint.navigatorPlatform,
        platformVersion: platformVersion ?? currentHint.platformVersion,
        uaFullVersion: uaFullVersion ?? currentHint.uaFullVersion,
        mobile: mobile ?? currentHint.mobile,
        architecture: architecture ?? currentHint.architecture,
        bitness: bitness ?? currentHint.bitness,
      ),
    );
  }

  // 更新语言配置
  void updateLanguages({
    String? js,
    String? http,
  }) {
    final currentLang = state.languages ?? const LanguagesConfig(js: '', http: '');
    state = state.copyWith(
      languages: currentLang.copyWith(
        js: js ?? currentLang.js,
        http: http ?? currentLang.http,
      ),
    );
  }

  // 更新软件开关配置
  void updateSoftware({
    String? cookie,
    String? java,
    String? dnt,
  }) {
    final currentSoftware = state.software ?? const SoftwareConfig(
      cookie: 'yes',
      java: 'no',
      dnt: 'no',
    );
    state = state.copyWith(
      software: currentSoftware.copyWith(
        cookie: cookie ?? currentSoftware.cookie,
        java: java ?? currentSoftware.java,
        dnt: dnt ?? currentSoftware.dnt,
      ),
    );
  }

  // 更新端口扫描配置
  void updatePortScan({String? enable}) {
    final currentPortScan = state.portScan ?? const PortScanConfig(enable: 'no');
    state = state.copyWith(
      portScan: currentPortScan.copyWith(
        enable: enable ?? currentPortScan.enable,
      ),
    );
  }

  // 更新字体配置
  void updateFont({String? removefont}) {
    final currentFont = state.font ?? const FontConfig(removefont: '');
    state = state.copyWith(
      font: currentFont.copyWith(
        removefont: removefont ?? currentFont.removefont,
      ),
    );
  }

  // 一次性更新整个配置
  void updateConfig(BrowserFingerprintConfig config) {
    state = config;
  }

  // 重置配置到默认值
  void resetConfig() {
    state = build(null);
  }

  // 从JSON加载配置
  Future<void> loadConfig(String jsonData) async {
    try {
      final config = BrowserFingerprintConfig.fromJson(jsonDecode(jsonData));
      state = config;
    } catch (e) {
      // 处理加载错误
      throw Exception('无法加载浏览器指纹配置: $e');
    }
  }

  // 保存配置到JSON
  Future<String> saveConfig() async {
    try {
      return jsonEncode(state.toJson());
    } catch (e) {
      throw Exception('无法保存浏览器指纹配置: $e');
    }
  }

  // 验证配置是否有效
  bool validateConfig() {
    // 基本验证逻辑
    if (state.webrtc == null || 
        state.webrtc!.public.isEmpty || 
        state.webrtc!.private.isEmpty) {
      return false;
    }
    
    if (state.position == null) {
      return false;
    }
    
    return true;
  }
} 
