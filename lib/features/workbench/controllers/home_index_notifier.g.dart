// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'home_index_notifier.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$homeIndexNotifierHash() => r'30be01b17a7bc0b0aed177f303589682b6444685';

/// See also [HomeIndexNotifier].
@ProviderFor(HomeIndexNotifier)
final homeIndexNotifierProvider =
    AutoDisposeNotifierProvider<HomeIndexNotifier, int>.internal(
  HomeIndexNotifier.new,
  name: r'homeIndexNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$homeIndexNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$HomeIndexNotifier = AutoDisposeNotifier<int>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
