import 'dart:convert';
import 'dart:io';
import 'package:browser_tool/browser_tool.dart';
import 'package:flutter/foundation.dart';
import 'package:frontend_re/core/network/http_service.dart';
import '../../../core/browser/chrome.dart';

/// 简单的浏览器状态枚举
enum SimpleBrowserStatus {
  stopped,   // 已停止
  launching, // 启动中  
  running,   // 运行中
  stopping,  // 停止中
}

/// 简单的浏览器实例类
class SimpleBrowserInstance {
  final String id;
  final String name;
  final String configPath;
  final Map<String, dynamic> config;
  
  SimpleBrowserStatus status;
  int? pid;
  Process? process;
  DateTime? startTime;

  SimpleBrowserInstance({
    required this.id,
    required this.name,
    required this.configPath,
    required this.config,
    this.status = SimpleBrowserStatus.stopped,
    this.pid,
    this.process,
    this.startTime,
  });

  bool get isRunning => status == SimpleBrowserStatus.running;
  bool get isStopped => status == SimpleBrowserStatus.stopped;
  
  @override
  String toString() => 'Browser($id, $name, ${status.name}, pid: $pid)';
}

/// 简单的浏览器管理器 - 单例模式
class SimpleBrowserManager {
  static SimpleBrowserManager? _instance;
  static SimpleBrowserManager get instance => _instance ??= SimpleBrowserManager._();
  
  SimpleBrowserManager._();

  // 使用Map存储所有浏览器实例
  final Map<String, SimpleBrowserInstance> _browsers = {};
  
  // 状态变化回调列表
  final List<void Function(SimpleBrowserInstance)> _listeners = [];

  /// 获取所有浏览器
  List<SimpleBrowserInstance> get allBrowsers => _browsers.values.toList();
  
  /// 获取运行中的浏览器
  List<SimpleBrowserInstance> get runningBrowsers => 
      _browsers.values.where((b) => b.isRunning).toList();

  /// 获取特定浏览器
  SimpleBrowserInstance? getBrowser(String id) => _browsers[id];

  /// 创建一个停止状态的浏览器实例（仅用于UI初始化）
  void createStoppedInstance(String id, String name) {
    if (!_browsers.containsKey(id)) {
      _browsers[id] = SimpleBrowserInstance(
        id: id,
        name: name,
        configPath: '', // 临时的，启动时会设置正确的路径
        config: {},
        status: SimpleBrowserStatus.stopped,
      );
      debugPrint('🆕 Created stopped browser instance for $id');
    }
  }

  /// 添加状态变化监听器
  void addListener(void Function(SimpleBrowserInstance) listener) {
    _listeners.add(listener);
  }

  /// 移除状态变化监听器
  void removeListener(void Function(SimpleBrowserInstance) listener) {
    _listeners.remove(listener);
  }

  /// 通知所有监听器
  void _notifyListeners(SimpleBrowserInstance browser) {
    debugPrint('📢 Browser ${browser.id} status changed to: ${browser.status.name}');
    _printAllBrowsers(); // 打印当前所有浏览器状态
    for (final listener in _listeners) {
      try {
        listener(browser);
      } catch (e) {
        debugPrint('Error in listener: $e');
      }
    }
  }

  /// 调试方法：打印所有浏览器状态
  void _printAllBrowsers() {
    debugPrint('🗂️ All browsers status:');
    if (_browsers.isEmpty) {
      debugPrint('   No browsers in manager');
    } else {
      for (final browser in _browsers.values) {
        debugPrint('   ${browser.id}: ${browser.status.name} (pid: ${browser.pid})');
      }
    }
  }

  /// 启动浏览器
  Future<bool> launchBrowser({
    required String id,
    required String name,
    required String configPath,
    required Map<String, dynamic> config,
  }) async {
    try {
      // 检查是否已存在并运行中
      final existing = _browsers[id];
      if (existing != null && existing.isRunning) {
        debugPrint('Browser $id is already running');
        return false;
      }

      // 创建或更新浏览器实例
      final browser = SimpleBrowserInstance(
        id: id,
        name: name,
        configPath: configPath,
        config: config,
      );

      browser.status = SimpleBrowserStatus.launching;
      _browsers[id] = browser;
      _notifyListeners(browser);
      
      debugPrint('🚀 Starting browser launch process for $id...');

      // 在后台启动Chrome进程，避免阻塞UI
      final pid = await _launchInBackground(config, configPath);
      
      if (pid != null) {
        // 获取进程对象
        final process = getProcessByPid(pid);
        
        browser.pid = pid;
        browser.process = process;
        browser.status = SimpleBrowserStatus.running;
        browser.startTime = DateTime.now();

        // 监听进程退出
        if (process != null) {
          process.exitCode.then((exitCode) {
            _onProcessExit(browser, exitCode);
          });
        }

        _notifyListeners(browser);
        debugPrint('✅ Browser $id launched successfully with PID: $pid');
        return true;
      } else {
        browser.status = SimpleBrowserStatus.stopped;
        _notifyListeners(browser);
        debugPrint('❌ Failed to launch browser $id');
        return false;
      }
    } catch (e) {
      debugPrint('❌ Error launching browser $id: $e');
      final browser = _browsers[id];
      if (browser != null) {
        browser.status = SimpleBrowserStatus.stopped;
        _notifyListeners(browser);
      }
      return false;
    }
  }

  /// 在后台启动浏览器，避免阻塞UI
  Future<int?> _launchInBackground(
    Map<String, dynamic> config, 
    String configPath
  ) async {
    // 分步骤执行启动过程，在关键点给UI机会更新
    try {
      debugPrint('Starting browser launch in background...');
      
      // 给UI一个机会更新
      await Future.delayed(const Duration(milliseconds: 50));
      
      // 使用完整版本，已优化为isolate执行
      final pid = await launchChromeWithConfig(config, configPath);
      
      return pid;
    } catch (e) {
      debugPrint('Error in background launch: $e');
      return null;
    }
  }

  /// 停止浏览器
  Future<bool> stopBrowser(String id) async {
    final browser = _browsers[id];
    if (browser == null || !browser.isRunning) {
      return false;
    }

    try {
      browser.status = SimpleBrowserStatus.stopping;
      _notifyListeners(browser);

      // 杀死进程
      if (browser.process != null) {
        browser.process!.kill(ProcessSignal.sigterm);
        
        // 等待进程结束，最多5秒
        final exitCode = await browser.process!.exitCode.timeout(
          const Duration(seconds: 5),
          onTimeout: () {
            browser.process!.kill(ProcessSignal.sigkill);
            return -1;
          },
        );
        
        debugPrint('Browser $id stopped with exit code: $exitCode');
      } else if (browser.pid != null) {
        await _killProcessByPid(browser.pid!);
      }

      browser.status = SimpleBrowserStatus.stopped;
      browser.pid = null;
      browser.process = null;
      browser.startTime = null;
      
      _notifyListeners(browser);
      return true;
    } catch (e) {
      debugPrint('❌ Error stopping browser $id: $e');
      return false;
    }
  }

  /// 进程退出处理
  Future<void> _onProcessExit(SimpleBrowserInstance browser, int exitCode) async {
    debugPrint('Browser ${browser.id} process exited with code: $exitCode');

    // 获取最新的cookie
    final cookie = BrowserTool.getCookie(browser.configPath);
    // debugPrint('browser: ${browser.config}');
    debugPrint('cookie: $cookie');
    if (cookie != null) {
      // 同时更新cookie和ui_cookie字段
      browser.config['cookie'] = cookie;
      browser.config['ui_cookie'] = cookie;
      
      // 更新最新的浏览器信息到后端
      _updateBrowserEnvironment(browser);
    }
    

    browser.status = SimpleBrowserStatus.stopped;
    browser.pid = null;
    browser.process = null;
    browser.startTime = null;
    _notifyListeners(browser);
  }

  /// 更新浏览器环境信息到后端
  Future<void> _updateBrowserEnvironment(SimpleBrowserInstance browser) async {
    try {
      // 将config转换为JSON字符串
      final parametersJson = jsonEncode(browser.config);
      
      // 构建请求数据，获取浏览器ID并转换为int
      final browserIdValue = browser.config['id'] ?? browser.id;
      final browserId = int.tryParse(browserIdValue.toString()) ?? 0;
      
      final requestData = {
        "environments": [
          {
            "id": browserId,
            "parameters": parametersJson,
          }
        ]
      };
      
      debugPrint('🔄 更新浏览器环境信息: $browserId');
      debugPrint('📤 请求数据: ${jsonEncode(requestData)}');
      
      final response = await HttpService.instance.put('/environments', data: requestData);
      debugPrint('✅ 环境信息更新成功: $response');
    } catch (e) {
      debugPrint('❌ 更新环境信息失败: $e');
    }
  }

  /// 通过PID杀死进程
  Future<void> _killProcessByPid(int pid) async {
    try {
      if (Platform.isWindows) {
        // 使用 /F 强制终止，/T 终止子进程
        final result = await Process.run('taskkill', ['/F', '/T', '/PID', '$pid'])
            .timeout(const Duration(seconds: 1));
        debugPrint('💀 进程 $pid 终止结果: ${result.exitCode}');
      } else {
        final result = await Process.run('kill', ['-9', '$pid'])
            .timeout(const Duration(seconds: 1));
        debugPrint('💀 进程 $pid 终止结果: ${result.exitCode}');
      }
    } catch (e) {
      debugPrint('❌ 杀死进程 $pid 时出错: $e');
    }
  }

  /// 停止所有浏览器
  Future<void> stopAllBrowsers() async {
    final runningBrowserList = runningBrowsers;
    debugPrint('🔄 SimpleBrowserManager.stopAllBrowsers(): 开始停止${runningBrowserList.length}个浏览器');
    
    if (runningBrowserList.isEmpty) {
      debugPrint('🔄 SimpleBrowserManager.stopAllBrowsers(): 没有运行中的浏览器');
      return;
    }
    
    // 并发关闭所有浏览器，不等待单个浏览器完成
    final stopFutures = <Future>[];
    for (final browser in runningBrowserList) {
      debugPrint('🔄 SimpleBrowserManager.stopAllBrowsers(): 开始关闭浏览器 ${browser.id} (PID: ${browser.pid})');
      
      // 直接强制杀死进程，不使用优雅关闭
      if (browser.pid != null) {
        stopFutures.add(_forceKillBrowser(browser));
      }
    }
    
    // 等待所有关闭操作完成，但设置短暂的超时
    try {
      await Future.wait(stopFutures).timeout(const Duration(seconds: 2));
      debugPrint('✅ SimpleBrowserManager.stopAllBrowsers(): 所有浏览器关闭完成');
    } catch (e) {
      debugPrint('⚠️ SimpleBrowserManager.stopAllBrowsers(): 关闭超时或出错: $e');
    }
  }
  
  /// 强制关闭浏览器（用于应用关闭时的快速清理）
  Future<void> _forceKillBrowser(SimpleBrowserInstance browser) async {
    try {
      browser.status = SimpleBrowserStatus.stopping;
      _notifyListeners(browser);
      
      if (browser.pid != null) {
        // 直接强制杀死进程
        await _killProcessByPid(browser.pid!);
        debugPrint('🔥 强制关闭浏览器 ${browser.id} (PID: ${browser.pid})');
      }
      
      // 更新状态
      browser.status = SimpleBrowserStatus.stopped;
      browser.pid = null;
      browser.process = null;
      browser.startTime = null;
      _notifyListeners(browser);
    } catch (e) {
      debugPrint('❌ 强制关闭浏览器 ${browser.id} 失败: $e');
    }
  }

  /// 获取状态信息
  Map<String, dynamic> getStatus() {
    return {
      'total': _browsers.length,
      'running': runningBrowsers.length,
      'stopped': _browsers.length - runningBrowsers.length,
      'browsers': _browsers.values.map((b) => {
        'id': b.id,
        'name': b.name,
        'status': b.status.name,
        'pid': b.pid,
      }).toList(),
    };
  }

  /// 清理资源
  void dispose() {
    stopAllBrowsers();
    _browsers.clear();
    _listeners.clear();
  }
} 
