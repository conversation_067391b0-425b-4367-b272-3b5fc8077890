import 'dart:convert';

import 'package:frontend_re/features/workbench/models/browser_config_model.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'browser_config_controller.g.dart';

@riverpod
class BrowserConfigController extends _$BrowserConfigController {
  @override
  BrowserConfigModel build(Object? initData) {
    if (initData != '' && initData != null) {
      final config = jsonDecode(initData as String);
      return BrowserConfigModel(
        name: config['ui_name'] ?? "",
        account: config['ui_account'] ?? "",
        accountUsername: config['ui_account_username'] ?? "",
        accountPassword: config['ui_account_password'] ?? "",
        cookie: config['ui_cookie'] ?? "",
        proxyId: config['ui_proxy_id'] ?? 0,
        proxyType: config['ui_proxy_type'] ?? 2,
        chromeSelectedValue: config['ui_chrome_version'] ?? "127",
        oswSelectedValue: config['ui_os_version'] ?? "Windows 11",
        ua: config['ui_ua'] ?? "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.6533.72 Safari/537.36",
        evaluateLanguage: config['ui_language_ip'] ?? true,
        languageSelectedValue: config['ui_language_value'] ?? "zh-CN",
        localeEvaluateLanguage: config['ui_locale_ip'] ?? true,
        locale: config['ui_locale_value'] ?? "zh-CN",
        timezone: config['ui_timezone_ip'] ?? true,
        timezoneSelectedValue: config['ui_timezone_value'] ?? "Asia/Shanghai",
        locationByIP: config['ui_location_ip'] ?? true,
        location: config['ui_location'] ?? "",
        webrtc: config['ui_webrtc'] ?? "forward",
        canvas: config['ui_canvas'] ?? true,
        webGL: config['ui_webgl'] ?? true,
        webGLVendor: config['ui_webgl_vendor'] ?? "Google Inc. (AMD)",
        webGLRender: config['ui_webgl_render'] ?? "ANGLE (Intel, Intel(R) HD Graphics 520 Direct3D11 vs_5_0 ps_5_0)",
        plugin: config['ui_plugin'] ?? true,
        font: config['ui_font'] ?? true,
        clientRects: config['ui_client_rect'] ?? true,
        audioContext: config['ui_audio_context'] ?? true,
        resolution: config['ui_resolution'] ?? "1280*720",
        memory: config['ui_memory'] ?? "2",
        kernel: config['ui_kernel'] ?? "2",
        // 可选参数
        chromeVersion: "",
        oswVersion: "",
        editing: true,
        comment: config['ui_comment'] ?? "",
        tag: config['ui_tag'] ?? "",
        groupId: config['ui_group'] ?? 0,
      );
    }
    return const BrowserConfigModel(
      name: "",
      account: "",
      accountUsername: "",
      accountPassword: "",
      cookie: "",
      proxyId: 0,
      proxyType: 2,
      chromeSelectedValue: "127",
      oswSelectedValue: "Windows 11",
      ua: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.6533.72 Safari/537.36",
      evaluateLanguage: true,
      languageSelectedValue: "zh-CN",
      localeEvaluateLanguage: true,
      locale: "zh-CN",
      timezone: true,
      timezoneSelectedValue: "Asia/Shanghai",
      locationByIP: true,
      location: "",
      webrtc: "forward",
      canvas: true,
      webGL: true,
      webGLVendor: "Google Inc. (AMD)",
      webGLRender:
          "ANGLE (Intel, Intel(R) HD Graphics 520 Direct3D11 vs_5_0 ps_5_0)",
      plugin: true,
      font: true,
      clientRects: true,
      audioContext: true,
      resolution: "1280*720",
      memory: "2",
      kernel: "2",
      // 可选参数
      chromeVersion: "",
      oswVersion: "",
      editing: false,
      comment: "",
      tag: "",
      groupId: 0,
    );
  }

  void updateConfig({
    // 基础设置
    String? name, // 窗口名称
    String? account, // 账号平台
    String? accountUsername, // 账号用户名
    String? accountPassword, // 账号密码
    String? cookie, // Cookie
    int? proxyId, // 代理ID
    int? proxyType, // 代理类型
    String? chromeSelectedValue, // 浏览器版本
    String? oswSelectedValue, // 操作系统
    String? ua, // UA 环境
    // 高级设置
    // 语言
    bool? evaluateLanguage, // 是否自定义语言
    String? languageSelectedValue, // 自定义语言内容
    // 界面语言
    bool? localeEvaluateLanguage, // 是否自定义界面语言
    String? locale, // 自定义界面语言内容
    // 时区
    bool? timezone, // 是否自定义时区
    String? timezoneSelectedValue, // 自定义时区内容
    // 地区和经纬度
    bool? locationByIP, // 是否自定义地区
    String? location, // 经 纬 海拔 精度
    // WebRTC
    String? webrtc,
    // Canvas
    bool? canvas, // true 噪音 false 真实
    // WebGL
    bool? webGL, // true 噪音 false 真实
    String? webGLVendor, // WebGL 厂商
    String? webGLRender, // WebGL 渲染
    // Plugin
    bool? plugin, // Plugin
    // 字体
    bool? font, // 字体
    // ClientRects
    bool? clientRects, // ClientRects
    // AudioContext
    bool? audioContext, // AudioContext
    // 分辨率和设备内存
    String? resolution, // 分辨率
    String? memory, // 设备内存
    // 设备 CPU
    String? kernel, // 设备 CPU
    String? chromeVersion, // 浏览器版本
    String? oswVersion, // 操作系统版本
    String? comment, // 备注
    String? tag, // 标签
    int? groupId, // 分组id
  }) {
    // 创建一个与当前状态相同的BrowserConfigModel对象
    BrowserConfigModel updatedModel = state;

    // 只有在参数非空时才更新对应字段
    if (name != null) updatedModel = updatedModel.copyWith(name: name);
    if (account != null) updatedModel = updatedModel.copyWith(account: account);
    if (accountUsername != null) updatedModel = updatedModel.copyWith(accountUsername: accountUsername);
    if (accountPassword != null) updatedModel = updatedModel.copyWith(accountPassword: accountPassword);
    if (cookie != null) updatedModel = updatedModel.copyWith(cookie: cookie);
    if (proxyId != null) updatedModel = updatedModel.copyWith(proxyId: proxyId);
    if (proxyType != null) updatedModel = updatedModel.copyWith(proxyType: proxyType);
    if (chromeSelectedValue != null) updatedModel = updatedModel.copyWith(chromeSelectedValue: chromeSelectedValue);
    if (oswSelectedValue != null) updatedModel = updatedModel.copyWith(oswSelectedValue: oswSelectedValue);
    if (ua != null) updatedModel = updatedModel.copyWith(ua: ua);
    if (evaluateLanguage != null) updatedModel = updatedModel.copyWith(evaluateLanguage: evaluateLanguage);
    if (languageSelectedValue != null) updatedModel = updatedModel.copyWith(languageSelectedValue: languageSelectedValue);
    if (localeEvaluateLanguage != null) updatedModel = updatedModel.copyWith(localeEvaluateLanguage: localeEvaluateLanguage);
    if (locale != null) updatedModel = updatedModel.copyWith(locale: locale);
    if (timezone != null) updatedModel = updatedModel.copyWith(timezone: timezone);
    if (timezoneSelectedValue != null) updatedModel = updatedModel.copyWith(timezoneSelectedValue: timezoneSelectedValue);
    if (locationByIP != null) updatedModel = updatedModel.copyWith(locationByIP: locationByIP);
    if (location != null) updatedModel = updatedModel.copyWith(location: location);
    if (webrtc != null) updatedModel = updatedModel.copyWith(webrtc: webrtc);
    if (canvas != null) updatedModel = updatedModel.copyWith(canvas: canvas);
    if (webGL != null) updatedModel = updatedModel.copyWith(webGL: webGL);
    if (webGLVendor != null) updatedModel = updatedModel.copyWith(webGLVendor: webGLVendor);
    if (webGLRender != null) updatedModel = updatedModel.copyWith(webGLRender: webGLRender);
    if (plugin != null) updatedModel = updatedModel.copyWith(plugin: plugin);
    if (font != null) updatedModel = updatedModel.copyWith(font: font);
    if (clientRects != null) updatedModel = updatedModel.copyWith(clientRects: clientRects);
    if (audioContext != null) updatedModel = updatedModel.copyWith(audioContext: audioContext);
    if (resolution != null) updatedModel = updatedModel.copyWith(resolution: resolution);
    if (memory != null) updatedModel = updatedModel.copyWith(memory: memory);
    if (kernel != null) updatedModel = updatedModel.copyWith(kernel: kernel);
    if (chromeVersion != null) updatedModel = updatedModel.copyWith(chromeVersion: chromeVersion);
    if (oswVersion != null) updatedModel = updatedModel.copyWith(oswVersion: oswVersion);
    if (comment != null) updatedModel = updatedModel.copyWith(comment: comment);
    if (tag != null) updatedModel = updatedModel.copyWith(tag: tag);
    if (groupId != null) updatedModel = updatedModel.copyWith(groupId: groupId);
    // 更新状态
    state = updatedModel;
  }

  // void resetConfig() {
  //   state = BrowserConfigModel();
  // }

  Future<void> loadConfig() async {
    // TODO: 实现从本地存储或API加载配置的逻辑
  }

  Future<void> saveConfig() async {
    // TODO: 实现保存配置到本地存储或API的逻辑
  }
}
