// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'browser_trash_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$browserTrashControllerHash() =>
    r'524b06cc3b9d78c82060137a1f4b033234a20d39';

/// See also [BrowserTrashController].
@ProviderFor(BrowserTrashController)
final browserTrashControllerProvider = AutoDisposeAsyncNotifierProvider<
    BrowserTrashController, BrowserWindowModel>.internal(
  BrowserTrashController.new,
  name: r'browserTrashControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$browserTrashControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$BrowserTrashController = AutoDisposeAsyncNotifier<BrowserWindowModel>;
String _$browserTrashTableControllerHash() =>
    r'6b77cbf3ecbd7171199c533198a5de5ca907136f';

/// See also [BrowserTrashTableController].
@ProviderFor(BrowserTrashTableController)
final browserTrashTableControllerProvider = AutoDisposeNotifierProvider<
    BrowserTrashTableController, List<int>>.internal(
  BrowserTrashTableController.new,
  name: r'browserTrashTableControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$browserTrashTableControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$BrowserTrashTableController = AutoDisposeNotifier<List<int>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
