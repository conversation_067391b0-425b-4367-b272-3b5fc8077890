import 'package:frontend_re/domain/models/browser_window_model.dart';
import 'package:frontend_re/domain/services/browser_service.dart';
import 'package:frontend_re/features/workbench/controllers/browser_window_controller.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'browser_trash_controller.g.dart';

@riverpod
class BrowserTrashController extends _$BrowserTrashController {
  @override
  FutureOr<BrowserWindowModel> build() async {
    return await loadTrashItems();
  }

  /// 加载回收站数据
  Future<BrowserWindowModel> loadTrashItems({
    int? offset,
    int? pageSize,
    String? name,
    int? groupId,
    int? userId,
  }) async {
    try {
      state = const AsyncLoading();
      
      final service = ref.read(browserServiceProvider.notifier);
      
      final request = DeletedBrowserWindowRequest(
        offset: offset ?? 0,
        limit: pageSize ?? 10,
        name: name,
        groupId: groupId,
        userId: userId,
      );
      
      final result = await service.getDeletedEnvironments(request);
      
      state = AsyncData(result);
      return result;
    } catch (e) {
      state = AsyncError(Exception('加载回收站数据失败: $e'), StackTrace.current);
      throw Exception('加载回收站数据失败: $e');
    }
  }

  /// 恢复选中的环境
  Future<String> restoreSelectedTrash(List<int> selectedIds) async {
    final currentData = state.valueOrNull;
    if (currentData == null) {
      throw Exception('数据未加载，请先刷新页面');
    }
    
    if (selectedIds.isEmpty) {
      throw Exception('请先选择要恢复的环境');
    }

    try {
      final service = ref.read(browserServiceProvider.notifier);
      final result = await service.restoreEnvironments(selectedIds);
      
      // 重新加载回收站数据
      await loadTrashItems();
      
      // 刷新主页面的浏览器窗口数据
      ref.invalidate(browserWindowControllerProvider);
      
      return result.isNotEmpty ? result : '环境恢复成功';
    } catch (e) {
      // 保持当前数据状态，但抛出错误供UI处理
      throw Exception('恢复环境失败: $e');
    }
  }

  /// 永久删除选中的环境
  Future<String> deleteSelectedTrash(List<int> selectedIds) async {
    final currentData = state.valueOrNull;
    if (currentData == null) {
      throw Exception('数据未加载，请先刷新页面');
    }
    
    if (selectedIds.isEmpty) {
      throw Exception('请先选择要删除的环境');
    }

    try {
      final service = ref.read(browserServiceProvider.notifier);
      final result = await service.hardDeleteEnvironments(selectedIds);
      
      // 重新加载最新数据以确保数据同步
      await loadTrashItems();
      
      return result.isNotEmpty ? result : '环境删除成功';
    } catch (e) {
      // 保持当前数据状态，但抛出错误供UI处理
      throw Exception('删除环境失败: $e');
    }
  }

  /// 获取当前环境列表
  List<BrowserWindowItem> get browserWindows => state.valueOrNull?.windows ?? [];

  /// 获取总数
  int get total => state.valueOrNull?.total ?? 0;
}

@riverpod
class BrowserTrashTableController extends _$BrowserTrashTableController {
  @override
  List<int> build() {
    return [];
  }

  bool get hasSelectedIds => state.isNotEmpty;
  List<int> get selectedIds => state;
  
  // 更新选中的窗口ID
  void updateSelectedWindowIds(List<int> ids) {
    state = ids;
  }

  // 清空状态
  void clearSelectedWindowIds() {
    state = [];
  }

  // 删除选中的窗口
  Future<String> deleteSelectedWindows() async {
    final ids = state;
    if (ids.isEmpty) {
      throw Exception('请先选择要删除的环境');
    }
    
    // 删除窗口
    final res = await ref.read(browserTrashControllerProvider.notifier).deleteSelectedTrash(ids);
    // 删除后清空选中的ids
    clearSelectedWindowIds();
    return res;
  }

  // 恢复选中的窗口
  Future<String> restoreSelectedWindows() async {
    final ids = state;
    if (ids.isEmpty) {
      throw Exception('请先选择要恢复的环境');
    }
    
    // 恢复窗口
    final res = await ref.read(browserTrashControllerProvider.notifier).restoreSelectedTrash(ids);
    // 恢复后清空选中的ids
    clearSelectedWindowIds();
    return res;
  }
}
