// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'browser_management_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$browserManagementServiceHash() =>
    r'983749984fdefea461ba5b001513dbe1db220666';

/// 浏览器管理服务Provider
///
/// Copied from [browserManagementService].
@ProviderFor(browserManagementService)
final browserManagementServiceProvider =
    Provider<BrowserManagementService>.internal(
  browserManagementService,
  name: r'browserManagementServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$browserManagementServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef BrowserManagementServiceRef = ProviderRef<BrowserManagementService>;
String _$browserStatusStreamHash() =>
    r'1a9ae53e4c70239d752a9594aaed59ec75102726';

/// 浏览器状态变化流Provider
///
/// Copied from [browserStatusStream].
@ProviderFor(browserStatusStream)
final browserStatusStreamProvider =
    StreamProvider<BrowserInstanceModel>.internal(
  browserStatusStream,
  name: r'browserStatusStreamProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$browserStatusStreamHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef BrowserStatusStreamRef = StreamProviderRef<BrowserInstanceModel>;
String _$browserInstanceHash() => r'0f1fe093349f003626f8dcce0347c61b435ba1a8';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// 特定浏览器的状态Provider
///
/// Copied from [browserInstance].
@ProviderFor(browserInstance)
const browserInstanceProvider = BrowserInstanceFamily();

/// 特定浏览器的状态Provider
///
/// Copied from [browserInstance].
class BrowserInstanceFamily extends Family<BrowserInstanceModel?> {
  /// 特定浏览器的状态Provider
  ///
  /// Copied from [browserInstance].
  const BrowserInstanceFamily();

  /// 特定浏览器的状态Provider
  ///
  /// Copied from [browserInstance].
  BrowserInstanceProvider call(
    String browserId,
  ) {
    return BrowserInstanceProvider(
      browserId,
    );
  }

  @override
  BrowserInstanceProvider getProviderOverride(
    covariant BrowserInstanceProvider provider,
  ) {
    return call(
      provider.browserId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'browserInstanceProvider';
}

/// 特定浏览器的状态Provider
///
/// Copied from [browserInstance].
class BrowserInstanceProvider extends Provider<BrowserInstanceModel?> {
  /// 特定浏览器的状态Provider
  ///
  /// Copied from [browserInstance].
  BrowserInstanceProvider(
    String browserId,
  ) : this._internal(
          (ref) => browserInstance(
            ref as BrowserInstanceRef,
            browserId,
          ),
          from: browserInstanceProvider,
          name: r'browserInstanceProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$browserInstanceHash,
          dependencies: BrowserInstanceFamily._dependencies,
          allTransitiveDependencies:
              BrowserInstanceFamily._allTransitiveDependencies,
          browserId: browserId,
        );

  BrowserInstanceProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.browserId,
  }) : super.internal();

  final String browserId;

  @override
  Override overrideWith(
    BrowserInstanceModel? Function(BrowserInstanceRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: BrowserInstanceProvider._internal(
        (ref) => create(ref as BrowserInstanceRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        browserId: browserId,
      ),
    );
  }

  @override
  ProviderElement<BrowserInstanceModel?> createElement() {
    return _BrowserInstanceProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is BrowserInstanceProvider && other.browserId == browserId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, browserId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin BrowserInstanceRef on ProviderRef<BrowserInstanceModel?> {
  /// The parameter `browserId` of this provider.
  String get browserId;
}

class _BrowserInstanceProviderElement
    extends ProviderElement<BrowserInstanceModel?> with BrowserInstanceRef {
  _BrowserInstanceProviderElement(super.provider);

  @override
  String get browserId => (origin as BrowserInstanceProvider).browserId;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
