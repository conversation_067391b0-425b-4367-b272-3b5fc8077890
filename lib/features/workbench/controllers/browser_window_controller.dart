import 'package:frontend_re/domain/models/browser_window_model.dart';
import 'package:frontend_re/domain/services/browser_service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';


// 生成的代码将在此文件中
part 'browser_window_controller.g.dart';

// 定义分页状态的类型别名，包含 offset 和 pageSize
typedef PaginationState = ({int offset, int pageSize});

@Riverpod(keepAlive: true)
class CurrentPage extends _$CurrentPage {
  // 初始状态，假设默认每页 10 条，从第一页开始 (offset 0)
  @override
  PaginationState build() => (offset: 0, pageSize: 10); // 您可以根据需要修改默认 pageSize

  // 更新 offset 和 pageSize
  void setPage({required int offset, required int pageSize}) {
    // 只有当 offset 或 pageSize 变化时才更新状态
    if (state.offset != offset || state.pageSize != pageSize) {
       state = (offset: offset, pageSize: pageSize);
    }
  }
}

@riverpod
class BrowserWindowController extends _$BrowserWindowController {
  @override
  FutureOr<BrowserWindowModel> build() {
    // 初始化状态
    return loadBrowserWindows();
  }

  /// 加载浏览器窗口列表
  Future<BrowserWindowModel> loadBrowserWindows({int offset = 0, int pageSize = 10}) async {
    state = const AsyncLoading();
    try {
      final service = ref.read(browserServiceProvider.notifier);
      final browserWindows = await service.getBrowserWindows(BrowserWindowRequest(offset: offset, limit: pageSize));
      state = AsyncData(browserWindows);
      return browserWindows;
    } catch (error, stackTrace) {
      throw AsyncError(error, stackTrace);
    }
  }

  /// 创建浏览器窗口
  Future<String> createBrowserWindow(BrowserCreateRequest createRequest) async {
    try {
      state = const AsyncLoading();
      final service = ref.read(browserServiceProvider.notifier);
      final res = await service.createBrowserWindow(createRequest);
      // 成功后刷新列表
      state = await AsyncValue.guard(() => loadBrowserWindows());
      return res;
    } catch (error, stackTrace) {
      state = AsyncError(error, stackTrace);
      rethrow;
    }
  }

  /// 更新浏览器窗口
  Future<String> updateBrowserWindow(List<BrowserWindowUpdateRequest> updateRequests) async {
    try {
      state = const AsyncLoading();
      final service = ref.read(browserServiceProvider.notifier);
      final res = await service.updateBrowserWindow(updateRequests);
      // 成功后刷新列表
      state = await AsyncValue.guard(() => loadBrowserWindows(offset: ref.read(currentPageProvider).offset, pageSize: ref.read(currentPageProvider).pageSize));
      return res;
    } catch (error, stackTrace) {
      state = AsyncError(error, stackTrace);
      rethrow;
    }
  }

  /// 删除浏览器窗口
  Future<String> deleteBrowserWindows(List<int> ids) async {
    try {
      state = const AsyncLoading();
      final service = ref.read(browserServiceProvider.notifier);
      final res = await service.deleteBrowserWindows(ids);
      // 成功后刷新列表
      state = await AsyncValue.guard(() => loadBrowserWindows());
      return res;
    } catch (error, stackTrace) {
      state = AsyncError(error, stackTrace);
      rethrow;
    }
  }

  /// 获取单个浏览器窗口
  Future<BrowserWindow?> getBrowserWindowById(int id) async {
    try {
      final service = ref.read(browserServiceProvider.notifier);
      final browserWindow = await service.getBrowserWindowById(id);
      return browserWindow;
    } catch (error) {
      rethrow;
    }
  }

  /// 更新浏览器窗口代理
  Future<String> updateBrowserWindowProxy(List<int> ids, int proxyId) async {
    try {
      final service = ref.read(browserServiceProvider.notifier);
      final res = await service.updateEnvironmentProxy(ids, proxyId);
      // 更新成功后刷新窗口列表
      state = const AsyncLoading();
      state = await AsyncValue.guard(() => loadBrowserWindows());
      return res;
    } catch (error) {
      rethrow;
    }
  }
}
