// lib/theme/colors.dart
import 'package:flutter/material.dart';

class AppColors {
  // 浅色主题颜色
  static const lightColors = ColorScheme(
    brightness: Brightness.light,
    primary: Color(0xFF0C75F8),    // 主要按钮的背景色
    onPrimary: Colors.white,      // 主要按钮的文字颜色
    primaryContainer: Color(0xFFFFFFFF),    // 主要容器的背景色
    onPrimaryContainer: Color(0xFF8D8E93),      // 主要容器的文字颜色
    secondary: Color(0xFF1E1C1D),  // 次要按钮的背景色
    onSecondary: Colors.white,    // 次要按钮的文字颜色
    tertiary: Color(0xFFFFFFFF),    // 第三级按钮的背景色
    onTertiary: Color(0xFF8D8E93),  // 第三级按钮的文字颜色
    surface: Color(0xFFF3F4F8),    //页面背景色
    surfaceContainer: Color(0xFFFFFFFF),    //卡片,弹窗背景色
    onSurface: Colors.black,      //页面文字颜色
    error: Colors.red,           //错误颜色
    onError: Colors.white,       //错误文字颜色
  );

  // 暗色主题颜色
  static const darkColors = ColorScheme(
    brightness: Brightness.dark,
    primary: Color(0xFF0C75F8),    // 主要按钮的背景色
    onPrimary: Colors.white,      // 主要按钮的文字颜色
    primaryContainer: Color(0xFFFFFFFF),    // 主要容器的背景色
    onPrimaryContainer: Color(0xFF8D8E93),  // 主要容器的文字颜色
    secondary: Color(0xFF1E1C1D),  // 次要按钮的背景色
    onSecondary: Colors.white,    // 次要按钮的文字颜色
    tertiary: Color(0xFFFFFFFF),    // 第三级按钮的背景色
    onTertiary: Color(0xFF8D8E93),  // 第三级按钮的文字颜色
    surface: Color(0xFFF3F4F8),    //页面背景色
    onSurface: Colors.black,      //页面文字颜色
    error: Colors.red,           //错误颜色
    onError: Colors.white,       //错误文字颜色
  );

  // 自定义颜色
  static const cardColor = Color(0xFFFFFFFF);  //卡片背景色
  static const secondaryColor = Color(0xFFEDEDF0);  //次要按钮的背景色
  static const onSecondaryColor = Color(0xFF8D8E93);  //次要按钮的文字颜色
  static const successColor = Colors.green;
  static const warningColor = Colors.orange;
  static const infoColor = Colors.blue;
}
