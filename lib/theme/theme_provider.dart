import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:frontend_re/theme/color.dart';

// --- 主题模式状态 ---
final themeModeProvider = StateProvider<ThemeMode>((ref) {
  // TODO: 从持久化存储中加载用户偏好设置
  return ThemeMode.light; // 默认为跟随系统
});

// --- 浅色 ThemeData Provider ---
final lightThemeProvider = Provider<ThemeData>((ref) {
  return ThemeData(
    brightness: Brightness.light,
    colorScheme: AppColors.lightColors, // 使用浅色主题颜色
    cardColor: AppColors.cardColor,
    hoverColor: const Color(0xFFEDF0FD),
    // 设置全局字体
    fontFamily: 'PingFang SC',
    inputDecorationTheme: InputDecorationTheme(
      filled: true, // 启用填充
      fillColor: const Color(0xFFF3F4F8), // 设置背景颜色为浅灰色
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(50),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(50),
        borderSide: const BorderSide(color: Colors.transparent),
      ),
      hoverColor: Colors.transparent,
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(50),
        borderSide: const BorderSide(color: Color(0xFF0C75F8)),
      ),
      hintStyle: const TextStyle(color: Color(0xFF999999)),
    ),
    // textButtonTheme: TextButtonThemeData(
    //   style: TextButton.styleFrom(
    //     backgroundColor: AppColors.secondaryColor,
    //     foregroundColor: AppColors.onSecondaryColor,
    //   ),
    // ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.lightColors.primary,
        foregroundColor: AppColors.lightColors.onPrimary,
        shadowColor: Colors.transparent,
      ),
    ),
    splashFactory: NoSplash.splashFactory,
    highlightColor: Colors.transparent, // 去除长按高亮颜色
    splashColor: Colors.transparent,    // 去除点击水波纹颜色
    // 其他样式配置
  );
});

// --- 暗色 ThemeData Provider ---
final darkThemeProvider = Provider<ThemeData>((ref) {
  return ThemeData(
    brightness: Brightness.dark,
    colorScheme: AppColors.darkColors, // 使用暗色主题颜色
    // 设置全局字体
    fontFamily: 'PingFang SC',
    // 其他样式配置
  );
}); 
