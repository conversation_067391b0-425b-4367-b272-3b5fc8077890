import 'package:frontend_re/domain/models/group_model.dart';
import 'package:frontend_re/repositories/group_repository.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'group_service.g.dart';

@riverpod
class GroupService extends _$GroupService {
  @override
  FutureOr<GroupResponse> build() {
    return const GroupResponse(groups: [], total: 0);
  }

  /// 创建分组
  Future<String> createGroup(String groupName) async {
    final repository = ref.read(groupRepositoryProvider.notifier);
    return await repository.createGroup(groupName);
  }

  /// 获取分组列表
  Future<GroupResponse> getGroups(int userId) async {
    final repository = ref.read(groupRepositoryProvider.notifier);
    state = AsyncValue.data(await repository.getGroups(userId));
    return state.value!;
  }

  /// 更新分组
  Future<String> updateGroup(int id, String groupName) async {
    final repository = ref.read(groupRepositoryProvider.notifier);
    return await repository.updateGroup(id, groupName);
  }

  /// 删除分组
  Future<String> deleteGroup(int id) async {
    final repository = ref.read(groupRepositoryProvider.notifier);
    return await repository.deleteGroup(id);
  }
} 
