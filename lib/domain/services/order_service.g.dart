// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$orderServiceHash() => r'f340bc6b8c7017b4b95231495670e423ebead916';

/// See also [OrderService].
@ProviderFor(OrderService)
final orderServiceProvider =
    AutoDisposeAsyncNotifierProvider<OrderService, OrderListResponse?>.internal(
  OrderService.new,
  name: r'orderServiceProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$orderServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$OrderService = AutoDisposeAsyncNotifier<OrderListResponse?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
