// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'proxy_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$selfProxyHash() => r'5e5c9c775567376f6d67b7bf068851eacfee733d';

/// See also [SelfProxy].
@ProviderFor(SelfProxy)
final selfProxyProvider =
    AutoDisposeAsyncNotifierProvider<SelfProxy, SelfProxyResponse>.internal(
  SelfProxy.new,
  name: r'selfProxyProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$selfProxyHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SelfProxy = AutoDisposeAsyncNotifier<SelfProxyResponse>;
String _$proxyPageIndexHash() => r'd466af0ceec6f195ac01d5b3a094912f2b803838';

/// See also [ProxyPageIndex].
@ProviderFor(ProxyPageIndex)
final proxyPageIndexProvider = NotifierProvider<ProxyPageIndex, int>.internal(
  ProxyPageIndex.new,
  name: r'proxyPageIndexProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$proxyPageIndexHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ProxyPageIndex = Notifier<int>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
