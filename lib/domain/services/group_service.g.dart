// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'group_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$groupServiceHash() => r'051cc7543c21bf39aec40ceeb328db59b48b8188';

/// See also [GroupService].
@ProviderFor(GroupService)
final groupServiceProvider =
    AutoDisposeAsyncNotifierProvider<GroupService, GroupResponse>.internal(
  GroupService.new,
  name: r'groupServiceProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$groupServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$GroupService = AutoDisposeAsyncNotifier<GroupResponse>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
