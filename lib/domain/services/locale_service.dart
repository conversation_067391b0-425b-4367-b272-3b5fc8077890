import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'locale_service.g.dart';

@Riverpod(keepAlive: true)
class LocaleService extends _$LocaleService {
  @override
  FutureOr<Locale?> build() async {
    // 初始化时返回默认语言
    return const Locale('zh'); // 默认语言为中文
  }

  // 切换语言的方法
  void changeLanguage(String languageCode) {
    state = AsyncValue.data(Locale(languageCode));
  }

  // 获取支持的语言及其名称
  Map<Locale, String> getSupportedLanguages() {
    return {
      const Locale('zh'): '简体中文',
      const Locale('en'): 'English',
    };
  }
}
