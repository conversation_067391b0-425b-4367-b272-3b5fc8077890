import 'package:frontend_re/domain/models/order_model.dart';
import 'package:frontend_re/repositories/order_repository.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'order_service.g.dart';

@riverpod
class OrderService extends _$OrderService {
  @override
  FutureOr<OrderListResponse?> build() async {
    return null; 
  }

  /// 获取订单列表
  Future<OrderListResponse> getOrders(OrderListRequest request) async {
    state = const AsyncValue.loading();
    try {
      final orderListResponse = await OrderRepository().getOrders(request);
      state = AsyncValue.data(orderListResponse);
      return orderListResponse;
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
      rethrow;
    }
  }

  /// 创建订单
  Future<CreateOrderResponse> createOrder(CreateOrderRequest request) async {
    try {
      final createOrderResponse = await OrderRepository().createOrder(request);
      return createOrderResponse;
    } catch (e) {
      rethrow;
    }
  }

  /// 取消订单
  Future<String> cancelOrder({int? orderId, int? orderNumber}) async {
    try {
      final cancelOrderResponse = await OrderRepository().cancelOrder(orderId: orderId, orderNumber: orderNumber);
      return cancelOrderResponse;
    } catch (e) {
      rethrow;
    }
  }

  /// 获取订单详情
  Future<Order> getOrderDetail({int? orderId, int? orderNumber}) async {
    try {
      final orderDetail = await OrderRepository().getOrderDetail(orderId: orderId, orderNumber: orderNumber);
      return orderDetail;
    } catch (e) {
      rethrow;
    }
  }

  /// 计算订单价格
  Future<double> calculateOrderPrice(CalculateOrderPriceRequest request) async {
    try {
      final calculateOrderPriceResponse = await OrderRepository().calculateOrderPrice(request);
      return calculateOrderPriceResponse;
    } catch (e) {
      rethrow;
    }
  }
}
