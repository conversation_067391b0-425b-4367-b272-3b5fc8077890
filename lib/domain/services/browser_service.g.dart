// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'browser_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$browserServiceHash() => r'5fb7ea72bae85700546530fce1422776e35b3bfd';

/// See also [BrowserService].
@ProviderFor(BrowserService)
final browserServiceProvider =
    AutoDisposeAsyncNotifierProvider<BrowserService, void>.internal(
  BrowserService.new,
  name: r'browserServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$browserServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$BrowserService = AutoDisposeAsyncNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
