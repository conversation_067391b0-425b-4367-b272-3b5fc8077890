import 'package:frontend_re/repositories/auth_repository.dart';
import 'package:frontend_re/domain/models/auth_model.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'auth_service.g.dart';

@riverpod
class AuthService extends _$AuthService {
  @override
  FutureOr<void> build() {
    return null;
  }

  /// 登录
  Future<AuthResponse> login(LoginRequest request) async {
    // 验证数据
    if (request.identifier.isEmpty || request.password.isEmpty) {
      throw Exception('请输入账号和密码');
    }

    final repository = ref.read(authRepositoryProvider.notifier);
    return await repository.login(request);
  }

  /// 注册
  Future<AuthResponse> register(RegisterRequest request) async {
    // 验证数据
    if (request.username.isEmpty || request.password.isEmpty) {
      throw Exception('请输入用户名和密码');
    }

    if (request.email == null && request.telephone == null) {
      throw Exception('邮箱或手机号必须填一个');
    }

    final repository = ref.read(authRepositoryProvider.notifier);
    return await repository.register(request);
  }

  /// 生成验证码
  Future<CaptchaResponse> generateCaptcha() async {
    final repository = ref.read(authRepositoryProvider.notifier);
    return await repository.generateCaptcha();
  }

  /// 发送注册验证邮件
  Future<SendRegisterEmailResponse> sendRegisterEmail(SendRegisterEmailRequest request) async {
    final repository = ref.read(authRepositoryProvider.notifier);
    return await repository.sendRegisterEmail(request);
  }
} 
