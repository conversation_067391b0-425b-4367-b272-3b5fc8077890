import 'package:frontend_re/repositories/team_repository.dart';
import 'package:frontend_re/domain/models/role_model.dart';
import 'package:frontend_re/domain/models/team_model.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'team_service.g.dart';

@riverpod
class TeamService extends _$TeamService {
  @override
  FutureOr<void> build() {
    return null;
  }

  /// 获取团队成员列表
  Future<TeamModelResponse> getUsers(TeamRequest request) async {
    final repository = ref.read(teamRepositoryProvider.notifier);
    return await repository.getUsers(request);
  }

  /// 添加团队成员
  Future<String> addUsers(List<AddTeamUserRequest> users) async {
    final repository = ref.read(teamRepositoryProvider.notifier);
    return await repository.addUsers(users);
  }

  /// 更新团队成员
  Future<String> updateUsers(List<UpdateTeamUserRequest> users) async {
    final repository = ref.read(teamRepositoryProvider.notifier);
    return await repository.updateUsers(users);
  }

  /// 删除团队成员
  Future<String> deleteUsers(List<int> userIds) async {
    final repository = ref.read(teamRepositoryProvider.notifier);
    return await repository.deleteUsers(userIds);
  }
}

@riverpod
class RoleService extends _$RoleService {
  @override
  FutureOr<void> build() {
    return null;
  }

  /// 获取角色列表
  Future<List<RoleModel>> getRoles(int id) async {
    final repository = ref.read(roleRepositoryProvider.notifier);
    return await repository.getRoles(id);
  }

  /// 删除角色
  Future<String> deleteRole(int id) async {
    final repository = ref.read(roleRepositoryProvider.notifier);
    return await repository.deleteRole(id);
  }

  /// 创建角色
  Future<String> createRole(CreateRoleRequest request) async {
    final repository = ref.read(roleRepositoryProvider.notifier);
    return await repository.createRole(request);
  }

  /// 更新角色
  Future<String> updateRole(UpdateRoleRequest request) async {
    final repository = ref.read(roleRepositoryProvider.notifier);
    return await repository.updateRole(request);
  }
} 
