import 'dart:async';
import 'package:frontend_re/core/storage/auth_storage.dart';
import 'package:frontend_re/domain/models/browser_window_model.dart';
import 'package:frontend_re/domain/models/team_model.dart';
import 'package:frontend_re/repositories/browser_window_repository.dart';
import 'package:frontend_re/repositories/team_repository.dart';
import 'package:frontend_re/domain/models/user_model.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'package:frontend_re/repositories/user_repository.dart';

part 'user_info_service.g.dart';

@Riverpod(keepAlive: true)
class UserInfoNotifier extends _$UserInfoNotifier {
  late final UserRepository _repository;
  //初始化获取团队用户列表
  late final TeamRepository _teamRepository;
  //初始化获取窗口环境列表
  late final BrowserWindowRepository _windowEnvironmentRepository;

  ///初始化的时候调用一次，也就是会执行一次过程
  @override
  FutureOr<UserModel?> build() async {
    _repository = ref.read(userRepositoryProvider.notifier);
    // 初始化团队用户列表
    _teamRepository = ref.read(teamRepositoryProvider.notifier);
    // 初始化窗口环境列表
    _windowEnvironmentRepository = ref.read(browserWindowRepositoryProvider.notifier);
    try {
      // 获取用户信息
      final user = await _repository.getUserInfo();
      // 获取团队用户列表
      await _teamRepository.getUsers(const TeamRequest(limit: 1, offset: 0));
      // 获取窗口环境列表
      await _windowEnvironmentRepository.getEnvironments(const BrowserWindowRequest(offset: 0, limit: 1));
      return user;
    } catch (e) {
      // 如果获取失败，返回 null，表示用户未登录
      return null;
    }
  }

  /// 获取用户信息
  Future<UserModel> getUserInfo() async {
    state = const AsyncLoading();
    try {
      final user = await _repository.getUserInfo();
      state = AsyncData(user);
      return user;
    } catch (e) {
      state = AsyncError(e, StackTrace.current);
      rethrow;
    }
  }

  /// 更新用户信息
  Future<String> updateUserInfo(UpdateUserInfoRequest request) async {
    state = const AsyncLoading();
    try {
      final result = await _repository.updateUserInfo(request);
      return result;
    } catch (e) {
      state = AsyncError(e, StackTrace.current);
      rethrow;
    }
  }

  // 刷新用户信息的方法
  Future<void> refreshUserInfo() async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(() async {
      final user = await _repository.getUserInfo();
      return user;
    });
  }

  // 清除用户信息的方法（例如在注销时调用）
  Future<bool> clearUserInfo() async {
    try {
      // 先清除本地存储的认证信息
      // 返回 true 表示清除成功，false 表示清除失败
      final result = await AuthStorage.clear();
      // 把当前状态设空
      state = const AsyncValue.data(null);
      if (result) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      return false;
    }
  }
}
