import 'package:frontend_re/repositories/proxy_repository.dart';
import 'package:frontend_re/domain/models/proxy_model.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'proxy_service.g.dart';


// 自建代理状态
@Riverpod(keepAlive: false)
class SelfProxy extends _$SelfProxy {
  @override
  FutureOr<SelfProxyResponse> build() {
    // 初始化时加载代理列表
    return getProxies(const ProxyListRequest(limit: 10, offset: 0));
  }

  /// 获取单一代理
  Future<SelfProxyDetail?> getProxy(int id) async { // 返回类型修改为 SelfProxyDetail? 以匹配 Mock
    // 使用真实 Repository
    final repository = ref.read(proxyRepositoryProvider.notifier);
    return await repository.getSelfHostProxy(id);
  }

  /// 获取代理列表
  Future<SelfProxyResponse> getProxies(ProxyListRequest request) async {
    state = const AsyncValue.loading();
    try {
      final repository = ref.read(proxyRepositoryProvider.notifier);
      SelfProxyResponse proxies = await repository.getSelfHostProxies(request);

      state = AsyncValue.data(proxies);
      return proxies;
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
      rethrow;
    }
  }

  /// 创建代理
  Future<String> addProxy(SelfProxyCreateRequest request) async {
    final repository = ref.read(proxyRepositoryProvider.notifier);
    String res = await repository.createSelfHostProxy(request);

    // 刷新代理列表
    // 考虑当前页码和每页数量进行刷新，或简单刷新第一页
    final currentState = state.value;
    if (currentState != null) {
      // 为了简单起见，我们重新加载当前可能存在的请求参数，或者默认参数
      // 在实际应用中，您可能需要更精细地处理分页和过滤参数
      await getProxies(const ProxyListRequest(limit: 10, offset: 0)); 
    }

    return res;
  }

  /// 批量创建代理
  Future<String> addBatchProxy(List<SelfProxyCreateRequest> requests) async {
    final repository = ref.read(proxyRepositoryProvider.notifier);
    String res = await repository.createSelfHostProxies(requests);

    // 刷新代理列表
    // 考虑当前页码和每页数量进行刷新，或简单刷新第一页
    final currentState = state.value;
    if (currentState != null) {
      // 为了简单起见，我们重新加载当前可能存在的请求参数，或者默认参数
      // 在实际应用中，您可能需要更精细地处理分页和过滤参数
      await getProxies(const ProxyListRequest(limit: 10, offset: 0)); 
    }
    
    return res;
  }

  /// 更新代理
  Future<String> updateProxy(SelfProxyUpdateRequest request) async { // 返回类型修改为 String 以匹配 Mock
    final repository = ref.read(proxyRepositoryProvider.notifier);
    String res = await repository.updateSelfHostProxy(request); // 假设真实仓库的 update 方法返回 String
    
    // 刷新代理列表
    final currentState = state.value;
    if (currentState != null) {
      await getProxies(const ProxyListRequest(limit: 10, offset: 0));
    }
    return res;
  }

  /// 删除代理
  Future<String> deleteProxy(List<int> ids) async { // 返回类型修改为 String 以匹配 Mock
    final repository = ref.read(proxyRepositoryProvider.notifier);
    String res = await repository.deleteSelfHostProxy(ids);

    // 刷新代理列表
    final currentState = state.value;
    if (currentState != null) {
      await getProxies(const ProxyListRequest(limit: 10, offset: 0));
    }
    return res;
  }
}

// 代理页面索引
@Riverpod(keepAlive: true)
class ProxyPageIndex extends _$ProxyPageIndex {
  @override
  int build() {
    return 0;
  }

  void setIndex(int index) {
    state = index;
  }
}
