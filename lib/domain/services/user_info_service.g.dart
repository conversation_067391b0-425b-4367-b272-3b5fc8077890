// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_info_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$userInfoNotifierHash() => r'ba2e0ad639957778d53d7476785247b79a2bb4d7';

/// See also [UserInfoNotifier].
@ProviderFor(UserInfoNotifier)
final userInfoNotifierProvider =
    AsyncNotifierProvider<UserInfoNotifier, UserModel?>.internal(
  UserInfoNotifier.new,
  name: r'userInfoNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$userInfoNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$UserInfoNotifier = AsyncNotifier<UserModel?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
