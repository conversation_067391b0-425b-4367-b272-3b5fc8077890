// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'locale_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$localeServiceHash() => r'0d02483b7e444ff33cd4afb500b8c97ae8b5ce82';

/// See also [LocaleService].
@ProviderFor(LocaleService)
final localeServiceProvider =
    AsyncNotifierProvider<LocaleService, Locale?>.internal(
  LocaleService.new,
  name: r'localeServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$localeServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$LocaleService = AsyncNotifier<Locale?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
