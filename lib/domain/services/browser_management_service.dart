import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';

import '../models/browser_instance_model.dart';
import '../../core/browser/chrome.dart';

/// 浏览器管理服务
class BrowserManagementService {
  // 私有构造函数
  BrowserManagementService._internal();
  
  // 静态实例
  static BrowserManagementService? _instance;
  
  /// 获取单例实例
  static BrowserManagementService getInstance() {
    _instance ??= BrowserManagementService._internal();
    return _instance!;
  }
  
  /// 重置实例（主要用于测试）
  static void resetInstance() {
    _instance?.dispose();
    _instance = null;
  }

  // 存储所有浏览器实例运行时
  final Map<String, BrowserInstanceRuntime> _browsers = {};
  
  // 状态变化通知流
  final StreamController<BrowserInstanceModel> _statusController = 
      StreamController<BrowserInstanceModel>.broadcast();
  
  // 定时器用于监控进程状态
  Timer? _monitorTimer;
  
  // 初始化状态
  bool _initialized = false;
  
  /// 状态变化流
  Stream<BrowserInstanceModel> get onStatusChanged => _statusController.stream;
  
  /// 获取所有浏览器实例
  List<BrowserInstanceModel> get allBrowsers => 
      _browsers.values.map((runtime) => runtime.model).toList();
  
  /// 获取运行中的浏览器
  List<BrowserInstanceModel> get runningBrowsers => 
      _browsers.values.where((runtime) => runtime.isRunning)
          .map((runtime) => runtime.model).toList();
  
  /// 获取已停止的浏览器
  List<BrowserInstanceModel> get stoppedBrowsers => 
      _browsers.values.where((runtime) => runtime.isStopped)
          .map((runtime) => runtime.model).toList();

  /// 初始化服务
  void initialize() {
    if (_initialized) return;
    
    _startMonitoring();
    _initialized = true;
    debugPrint('BrowserManagementService initialized');
  }

  /// 启动浏览器
  Future<bool> launchBrowser({
    required String id,
    required String name,
    required String configPath,
    required Map<String, dynamic> config,
  }) async {
    try {
      // 检查是否已存在
      if (_browsers.containsKey(id)) {
        final existing = _browsers[id]!;
        if (existing.isRunning) {
          debugPrint('Browser $id is already running');
          return false;
        }
      }

      // 创建浏览器模型
      final model = BrowserInstanceModel(
        id: id,
        name: name,
        configPath: configPath,
        config: config,
      );

      // 创建运行时实例
      final runtime = BrowserInstanceRuntime(model);
      
      // 更新状态为启动中
      runtime.updateStatus(BrowserStatus.launching);
      _browsers[id] = runtime;
      _notifyStatusChange(runtime.model);

      // 启动Chrome进程
      final pid = await launchChromeWithConfig(config, configPath);
      
      if (pid != null) {
        // 获取进程对象
        final process = getProcessByPid(pid);
        if (process != null) {
          runtime.setProcess(process, pid);
          
          // 监听进程退出
          process.exitCode.then((exitCode) {
            _onProcessExit(runtime, exitCode);
          });
        } else {
          // 即使没有进程对象，PID存在也算启动成功
          runtime.setPid(pid);
        }

        _notifyStatusChange(runtime.model);
        debugPrint('Browser $id launched successfully with PID: $pid');
        return true;
      } else {
        runtime.updateStatus(BrowserStatus.crashed);
        _notifyStatusChange(runtime.model);
        return false;
      }
    } catch (e) {
      debugPrint('Failed to launch browser $id: $e');
      final runtime = _browsers[id];
      if (runtime != null) {
        runtime.updateStatus(BrowserStatus.crashed);
        _notifyStatusChange(runtime.model);
      }
      return false;
    }
  }

  /// 停止浏览器
  Future<bool> stopBrowser(String id) async {
    final runtime = _browsers[id];
    if (runtime == null || !runtime.isRunning) {
      return false;
    }

    try {
      runtime.updateStatus(BrowserStatus.stopping);
      _notifyStatusChange(runtime.model);

      // 尝试优雅关闭
      if (runtime.process != null) {
        runtime.process!.kill(ProcessSignal.sigterm);
        
        // 等待进程结束，最多等待5秒
        final exitCode = await runtime.process!.exitCode.timeout(
          const Duration(seconds: 5),
          onTimeout: () {
            // 强制杀死进程
            runtime.process!.kill(ProcessSignal.sigkill);
            return -1;
          },
        );
        
        debugPrint('Browser $id stopped with exit code: $exitCode');
      } else if (runtime.pid != null) {
        // 通过PID杀死进程
        await _killProcessByPid(runtime.pid!);
      }

      runtime.updateStatus(BrowserStatus.stopped);
      _notifyStatusChange(runtime.model);
      return true;
    } catch (e) {
      debugPrint('Failed to stop browser $id: $e');
      return false;
    }
  }

  /// 重启浏览器
  Future<bool> restartBrowser(String id) async {
    final runtime = _browsers[id];
    if (runtime == null) return false;

    final success = await stopBrowser(id);
    if (success) {
      // 等待一下再重启
      await Future.delayed(const Duration(seconds: 1));
      return await launchBrowser(
        id: runtime.id,
        name: runtime.name,
        configPath: runtime.configPath,
        config: runtime.config,
      );
    }
    return false;
  }

  /// 获取浏览器实例
  BrowserInstanceModel? getBrowser(String id) => _browsers[id]?.model;

  /// 移除浏览器实例
  void removeBrowser(String id) {
    final runtime = _browsers.remove(id);
    if (runtime != null && runtime.isRunning) {
      stopBrowser(id);
    }
  }

  /// 停止所有浏览器
  Future<void> stopAllBrowsers() async {
    final runningBrowsers = _browsers.values.where((runtime) => runtime.isRunning).toList();
    
    for (final runtime in runningBrowsers) {
      await stopBrowser(runtime.id);
    }
  }

  /// 获取服务状态信息
  Map<String, dynamic> getStatus() {
    return {
      'totalBrowsers': _browsers.length,
      'runningBrowsers': runningBrowsers.length,
      'stoppedBrowsers': stoppedBrowsers.length,
      'browsers': allBrowsers.map((model) => model.toJson()).toList(),
    };
  }

  /// 批量启动浏览器
  Future<Map<String, bool>> batchLaunchBrowsers(
    List<Map<String, dynamic>> browserConfigs,
  ) async {
    final results = <String, bool>{};
    
    for (final config in browserConfigs) {
      final id = config['id'] as String;
      final name = config['name'] as String;
      final configPath = config['configPath'] as String;
      final browserConfig = config['config'] as Map<String, dynamic>;
      
      final success = await launchBrowser(
        id: id,
        name: name,
        configPath: configPath,
        config: browserConfig,
      );
      
      results[id] = success;
      
      // 添加延迟避免同时启动太多浏览器
      await Future.delayed(const Duration(milliseconds: 500));
    }
    
    return results;
  }

  /// 批量停止浏览器
  Future<Map<String, bool>> batchStopBrowsers(List<String> browserIds) async {
    final results = <String, bool>{};
    
    for (final id in browserIds) {
      final success = await stopBrowser(id);
      results[id] = success;
    }
    
    return results;
  }

  /// 开始监控进程状态
  void _startMonitoring() {
    _monitorTimer?.cancel();
    _monitorTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      _checkProcessStatus();
    });
  }

  /// 检查进程状态
  void _checkProcessStatus() {
    for (final runtime in _browsers.values) {
      if (runtime.isRunning && runtime.pid != null) {
        _checkIfProcessExists(runtime);
      }
    }
  }

  /// 检查进程是否存在
  void _checkIfProcessExists(BrowserInstanceRuntime runtime) async {
    try {
      if (Platform.isWindows) {
        final result = await Process.run('tasklist', ['/FI', 'PID eq ${runtime.pid}']);
        if (!result.stdout.toString().contains('${runtime.pid}')) {
          _onProcessExit(runtime, -1);
        }
      } else {
        final result = await Process.run('ps', ['-p', '${runtime.pid}']);
        if (result.exitCode != 0) {
          _onProcessExit(runtime, -1);
        }
      }
    } catch (e) {
      debugPrint('Error checking process ${runtime.pid}: $e');
    }
  }

  /// 通过PID杀死进程
  Future<void> _killProcessByPid(int pid) async {
    try {
      if (Platform.isWindows) {
        await Process.run('taskkill', ['/F', '/PID', '$pid']);
      } else {
        await Process.run('kill', ['-9', '$pid']);
      }
    } catch (e) {
      debugPrint('Error killing process $pid: $e');
    }
  }

  /// 进程退出处理
  void _onProcessExit(BrowserInstanceRuntime runtime, int exitCode) {
    debugPrint('Browser ${runtime.id} process exited with code: $exitCode');
    runtime.updateStatus(exitCode == 0 ? BrowserStatus.stopped : BrowserStatus.crashed);
    _notifyStatusChange(runtime.model);
  }

  /// 通知状态变化
  void _notifyStatusChange(BrowserInstanceModel model) {
    print('🔔 Notifying status change for browser ${model.id}: ${model.status.name} (hasListeners: ${_statusController.hasListener})');
    _statusController.add(model);
  }

  /// 清理资源
  void dispose() {
    if (!_initialized) return;
    
    _monitorTimer?.cancel();
    stopAllBrowsers();
    _statusController.close();
    _initialized = false;
    debugPrint('BrowserManagementService disposed');
  }
} 
