import 'package:frontend_re/repositories/browser_window_repository.dart';
import 'package:frontend_re/domain/models/browser_window_model.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'browser_service.g.dart';

@riverpod
class BrowserService extends _$BrowserService {
  @override
  FutureOr<void> build() {
    return null;
  }

  /// 创建浏览器窗口
  Future<String> createBrowserWindow(BrowserCreateRequest request) async {
    final repository = ref.read(browserWindowRepositoryProvider.notifier);
    return await repository.createEnvironment(request);
  }

  /// 获取浏览器窗口列表
  Future<BrowserWindowModel> getBrowserWindows(BrowserWindowRequest request) async {
    final repository = ref.read(browserWindowRepositoryProvider.notifier);
    return await repository.getEnvironments(request);
  }

  /// 更新浏览器窗口
  Future<String> updateBrowserWindow(List<BrowserWindowUpdateRequest> updateRequests) async {
    final repository = ref.read(browserWindowRepositoryProvider.notifier);
    return await repository.updateEnvironment(updateRequests);
  }

  /// 删除浏览器窗口
  Future<String> deleteBrowserWindows(List<int> ids) async {
    final repository = ref.read(browserWindowRepositoryProvider.notifier);
    return await repository.deleteEnvironments(ids);
  }

  /// 获取单个浏览器窗口
  Future<BrowserWindow> getBrowserWindowById(int id) async {
    final repository = ref.read(browserWindowRepositoryProvider.notifier);
    return await repository.getEnvironmentByID(id);
  }

  /// 批量更新环境代理
  Future<String> updateEnvironmentProxy(List<int> ids, int proxyId) async {
    final repository = ref.read(browserWindowRepositoryProvider.notifier);
    return await repository.updateEnvironmentProxy(ids, proxyId);
  }

  /// 获取已删除的环境
  Future<BrowserWindowModel> getDeletedEnvironments(DeletedBrowserWindowRequest request) async {
    final repository = ref.read(browserWindowRepositoryProvider.notifier);
    return await repository.getDeletedEnvironments(request);
  }

  /// 恢复已删除的环境
  Future<String> restoreEnvironments(List<int> ids) async {
    final repository = ref.read(browserWindowRepositoryProvider.notifier);
    return await repository.restoreEnvironments(ids);
  }

  /// 永久删除环境
  Future<String> hardDeleteEnvironments(List<int> ids) async {
    final repository = ref.read(browserWindowRepositoryProvider.notifier);
    return await repository.hardDeleteEnvironments(ids);
  }
} 
