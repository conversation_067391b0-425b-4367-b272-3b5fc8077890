// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'team_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$teamServiceHash() => r'277ae6dabf0446364b0e1726e20ae5f33a577415';

/// See also [TeamService].
@ProviderFor(TeamService)
final teamServiceProvider =
    AutoDisposeAsyncNotifierProvider<TeamService, void>.internal(
  TeamService.new,
  name: r'teamServiceProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$teamServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$TeamService = AutoDisposeAsyncNotifier<void>;
String _$roleServiceHash() => r'123dd9b52d06769d1872782faefeeb45454a821d';

/// See also [RoleService].
@ProviderFor(RoleService)
final roleServiceProvider =
    AutoDisposeAsyncNotifierProvider<RoleService, void>.internal(
  RoleService.new,
  name: r'roleServiceProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$roleServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$RoleService = AutoDisposeAsyncNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
