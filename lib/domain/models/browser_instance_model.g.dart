// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'browser_instance_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$BrowserInstanceModelImpl _$$BrowserInstanceModelImplFromJson(
        Map<String, dynamic> json) =>
    _$BrowserInstanceModelImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      configPath: json['configPath'] as String,
      config: json['config'] as Map<String, dynamic>,
      status: $enumDecodeNullable(_$BrowserStatusEnumMap, json['status']) ??
          BrowserStatus.stopped,
      pid: (json['pid'] as num?)?.toInt(),
      startTime: json['startTime'] == null
          ? null
          : DateTime.parse(json['startTime'] as String),
      lastActivity: json['lastActivity'] == null
          ? null
          : DateTime.parse(json['lastActivity'] as String),
    );

Map<String, dynamic> _$$BrowserInstanceModelImplToJson(
        _$BrowserInstanceModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'configPath': instance.configPath,
      'config': instance.config,
      'status': _$BrowserStatusEnumMap[instance.status]!,
      'pid': instance.pid,
      'startTime': instance.startTime?.toIso8601String(),
      'lastActivity': instance.lastActivity?.toIso8601String(),
    };

const _$BrowserStatusEnumMap = {
  BrowserStatus.launching: 'launching',
  BrowserStatus.running: 'running',
  BrowserStatus.stopping: 'stopping',
  BrowserStatus.stopped: 'stopped',
  BrowserStatus.crashed: 'crashed',
};
