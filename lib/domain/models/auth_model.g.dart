// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auth_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$LoginRequestImpl _$$LoginRequestImplFromJson(Map<String, dynamic> json) =>
    _$LoginRequestImpl(
      identifier: json['identifier'] as String,
      password: json['password'] as String,
    );

Map<String, dynamic> _$$LoginRequestImplToJson(_$LoginRequestImpl instance) =>
    <String, dynamic>{
      'identifier': instance.identifier,
      'password': instance.password,
    };

_$RegisterRequestImpl _$$RegisterRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$RegisterRequestImpl(
      username: json['user_name'] as String,
      password: json['password'] as String,
      email: json['email'] as String?,
      telephone: json['telephone'] as String?,
      inviteCode: json['invite_code'] as String?,
      emailCode: json['email_code'] as String?,
    );

Map<String, dynamic> _$$RegisterRequestImplToJson(
        _$RegisterRequestImpl instance) =>
    <String, dynamic>{
      'user_name': instance.username,
      'password': instance.password,
      'email': instance.email,
      'telephone': instance.telephone,
      'invite_code': instance.inviteCode,
      'email_code': instance.emailCode,
    };

_$AuthResponseImpl _$$AuthResponseImplFromJson(Map<String, dynamic> json) =>
    _$AuthResponseImpl(
      message: json['message'] as String,
      token: json['token'] as String?,
      user: json['user'] == null
          ? null
          : User.fromJson(json['user'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$AuthResponseImplToJson(_$AuthResponseImpl instance) =>
    <String, dynamic>{
      'message': instance.message,
      'token': instance.token,
      'user': instance.user,
    };

_$UserImpl _$$UserImplFromJson(Map<String, dynamic> json) => _$UserImpl(
      id: (json['id'] as num).toInt(),
      username: json['username'] as String,
      email: json['email'] as String?,
      telephone: json['telephone'] as String?,
      avatar: json['avatar'] as String?,
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
    );

Map<String, dynamic> _$$UserImplToJson(_$UserImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'username': instance.username,
      'email': instance.email,
      'telephone': instance.telephone,
      'avatar': instance.avatar,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
    };

_$CaptchaResponseImpl _$$CaptchaResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$CaptchaResponseImpl(
      captchaImage: json['captcha_image'] as String,
      seed: json['seed'] as String,
      target: json['target'] as String,
    );

Map<String, dynamic> _$$CaptchaResponseImplToJson(
        _$CaptchaResponseImpl instance) =>
    <String, dynamic>{
      'captcha_image': instance.captchaImage,
      'seed': instance.seed,
      'target': instance.target,
    };

_$VerifyCaptchaRequestImpl _$$VerifyCaptchaRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$VerifyCaptchaRequestImpl(
      answer: json['answer'] as String,
      nonce: json['nonce'] as String,
      seed: json['seed'] as String,
      target: json['target'] as String,
    );

Map<String, dynamic> _$$VerifyCaptchaRequestImplToJson(
        _$VerifyCaptchaRequestImpl instance) =>
    <String, dynamic>{
      'answer': instance.answer,
      'nonce': instance.nonce,
      'seed': instance.seed,
      'target': instance.target,
    };

_$VerifyCaptchaResponseImpl _$$VerifyCaptchaResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$VerifyCaptchaResponseImpl(
      valid: json['valid'] as bool,
    );

Map<String, dynamic> _$$VerifyCaptchaResponseImplToJson(
        _$VerifyCaptchaResponseImpl instance) =>
    <String, dynamic>{
      'valid': instance.valid,
    };

_$SendRegisterEmailRequestImpl _$$SendRegisterEmailRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$SendRegisterEmailRequestImpl(
      answer: json['answer'] as String,
      email: json['email'] as String,
      nonce: json['nonce'] as String,
      seed: json['seed'] as String,
      target: json['target'] as String,
    );

Map<String, dynamic> _$$SendRegisterEmailRequestImplToJson(
        _$SendRegisterEmailRequestImpl instance) =>
    <String, dynamic>{
      'answer': instance.answer,
      'email': instance.email,
      'nonce': instance.nonce,
      'seed': instance.seed,
      'target': instance.target,
    };

_$SendRegisterEmailResponseImpl _$$SendRegisterEmailResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$SendRegisterEmailResponseImpl(
      message: json['message'] as String,
      sent: json['sent'] as bool,
    );

Map<String, dynamic> _$$SendRegisterEmailResponseImplToJson(
        _$SendRegisterEmailResponseImpl instance) =>
    <String, dynamic>{
      'message': instance.message,
      'sent': instance.sent,
    };
