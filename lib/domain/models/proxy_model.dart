import 'package:freezed_annotation/freezed_annotation.dart';

part 'proxy_model.freezed.dart';
part 'proxy_model.g.dart';

// ==================== 请求模型 ====================

/// 代理列表请求模型
@freezed
class ProxyListRequest with _$ProxyListRequest {
  const factory ProxyListRequest({
    int? id,
    int? environmentId,
    int? type,
    String? name,
    int? limit,
    int? offset,
  }) = _ProxyListRequest;

  factory ProxyListRequest.fromJson(Map<String, dynamic> json) =>
      _$ProxyListRequestFromJson(json);   
}

/// 自建代理创建请求模型
@freezed
class SelfProxyCreateRequest with _$SelfProxyCreateRequest {
  const factory SelfProxyCreateRequest({
    required String name,
    required int type,
    required String host,
    required int port,
    @JsonKey(includeIfNull: false) String? username,
    @JsonKey(includeIfNull: false) String? password,
    @JsonKey(includeIfNull: false) int? environmentId,
  }) = _SelfProxyCreateRequest;

  factory SelfProxyCreateRequest.fromJson(Map<String, dynamic> json) =>
      _$SelfProxyCreateRequestFromJson(json);
}

/// 自建代理更新请求模型
@freezed
class SelfProxyUpdateRequest with _$SelfProxyUpdateRequest {
  const factory SelfProxyUpdateRequest({
    int? id,
    required String name,
    required int type,
    required String host,
    required int port,
    @JsonKey(includeIfNull: false) String? username,
    @JsonKey(includeIfNull: false) String? password,
    @JsonKey(includeIfNull: false) int? environmentId,
  }) = _SelfProxyUpdateRequest;

  factory SelfProxyUpdateRequest.fromJson(Map<String, dynamic> json) =>
      _$SelfProxyUpdateRequestFromJson(json);
}

// ==================== 响应模型 ====================

/// 自建代理响应模型
@freezed
class SelfProxyResponse with _$SelfProxyResponse {
  const factory SelfProxyResponse({
    required List<SelfProxyItem> proxies,
    required int total,
  }) = _SelfProxyResponse;

  factory SelfProxyResponse.fromJson(Map<String, dynamic> json) =>
      _$SelfProxyResponseFromJson(json);
}

/// 自建代理项模型
@freezed
class SelfProxyItem with _$SelfProxyItem {
  const factory SelfProxyItem({
    required int id,
    required String name,
    required int type,
    required String host,
    required int port,
    @JsonKey(name: 'environment_name') required String environmentName,
    @JsonKey(name: 'team_name') required String teamName,
  }) = _SelfProxyItem;

  factory SelfProxyItem.fromJson(Map<String, dynamic> json) =>
      _$SelfProxyItemFromJson(json);
}

/// 自建代理详细模型
@freezed
class SelfProxyDetail with _$SelfProxyDetail {
  const factory SelfProxyDetail({
    required int id, // 自增主键 ID
    String? name, // 代理名称，用户自定义标识
    required int type, // 代理协议类型（1 HTTP, 2 HTTPS, 3 SOCKS5）
    String? host, // 主机地址，可为 IP 或域名
    required int port, // 代理服务端口
    String? username, // 可选的代理用户名
    String? password, // 可选的代理密码
    @JsonKey(name: 'team_id') int? teamId, // 代理所属团队 ID
    @JsonKey(name: 'environment_id') int? environmentId, // 代理绑定的环境 ID
  }) = _SelfProxyDetail;

  factory SelfProxyDetail.fromJson(Map<String, dynamic> json) =>
      _$SelfProxyDetailFromJson(json);
}

/// 平台代理响应模型
@freezed
class PlatformProxyModel with _$PlatformProxyModel {
  const factory PlatformProxyModel({
    required List<PlatformProxy> proxies,
    required int total,
  }) = _PlatformProxyModel;

  factory PlatformProxyModel.fromJson(Map<String, dynamic> json) =>
      _$PlatformProxyModelFromJson(json);
}

/// 平台代理项模型
@freezed
class PlatformProxy with _$PlatformProxy {
  const factory PlatformProxy({
    required int id,
    required String name, // 代理名称
    @JsonKey(name: 'ip_address') required String ipAddress, // IP地址
    required String environment, // 环境
    required String region, // 地区
    @JsonKey(name: 'is_unlimited') required bool isUnlimited, // 是否无限制
    @JsonKey(name: 'auto_renew') required bool autoRenew, // 是否自动续期
    @JsonKey(name: 'expires_at') required String expiresAt, // 过期时间
  }) = _PlatformProxy;

  factory PlatformProxy.fromJson(Map<String, dynamic> json) =>
      _$PlatformProxyFromJson(json);
} 
