// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'order_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

OrderListRequest _$OrderListRequestFromJson(Map<String, dynamic> json) {
  return _OrderListRequest.fromJson(json);
}

/// @nodoc
mixin _$OrderListRequest {
  @JsonKey(name: 'order_type', includeIfNull: false)
  int? get orderType => throw _privateConstructorUsedError; // 订单类型
  @JsonKey(name: 'status', includeIfNull: false)
  int? get status => throw _privateConstructorUsedError; // 订单状态
  @JsonKey(name: 'payment_method', includeIfNull: false)
  int? get paymentMethod => throw _privateConstructorUsedError; // 支付方式
  @JsonKey(name: 'limit', includeIfNull: false)
  int? get limit => throw _privateConstructorUsedError; // 每页条数
  @JsonKey(name: 'offset', includeIfNull: false)
  int? get offset => throw _privateConstructorUsedError; // 偏移量
  @JsonKey(name: 'start_time', includeIfNull: false)
  String? get startTime => throw _privateConstructorUsedError; // 开始时间
  @JsonKey(name: 'end_time', includeIfNull: false)
  String? get endTime => throw _privateConstructorUsedError;

  /// Serializes this OrderListRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of OrderListRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OrderListRequestCopyWith<OrderListRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrderListRequestCopyWith<$Res> {
  factory $OrderListRequestCopyWith(
          OrderListRequest value, $Res Function(OrderListRequest) then) =
      _$OrderListRequestCopyWithImpl<$Res, OrderListRequest>;
  @useResult
  $Res call(
      {@JsonKey(name: 'order_type', includeIfNull: false) int? orderType,
      @JsonKey(name: 'status', includeIfNull: false) int? status,
      @JsonKey(name: 'payment_method', includeIfNull: false) int? paymentMethod,
      @JsonKey(name: 'limit', includeIfNull: false) int? limit,
      @JsonKey(name: 'offset', includeIfNull: false) int? offset,
      @JsonKey(name: 'start_time', includeIfNull: false) String? startTime,
      @JsonKey(name: 'end_time', includeIfNull: false) String? endTime});
}

/// @nodoc
class _$OrderListRequestCopyWithImpl<$Res, $Val extends OrderListRequest>
    implements $OrderListRequestCopyWith<$Res> {
  _$OrderListRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OrderListRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? orderType = freezed,
    Object? status = freezed,
    Object? paymentMethod = freezed,
    Object? limit = freezed,
    Object? offset = freezed,
    Object? startTime = freezed,
    Object? endTime = freezed,
  }) {
    return _then(_value.copyWith(
      orderType: freezed == orderType
          ? _value.orderType
          : orderType // ignore: cast_nullable_to_non_nullable
              as int?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      paymentMethod: freezed == paymentMethod
          ? _value.paymentMethod
          : paymentMethod // ignore: cast_nullable_to_non_nullable
              as int?,
      limit: freezed == limit
          ? _value.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int?,
      offset: freezed == offset
          ? _value.offset
          : offset // ignore: cast_nullable_to_non_nullable
              as int?,
      startTime: freezed == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as String?,
      endTime: freezed == endTime
          ? _value.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OrderListRequestImplCopyWith<$Res>
    implements $OrderListRequestCopyWith<$Res> {
  factory _$$OrderListRequestImplCopyWith(_$OrderListRequestImpl value,
          $Res Function(_$OrderListRequestImpl) then) =
      __$$OrderListRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'order_type', includeIfNull: false) int? orderType,
      @JsonKey(name: 'status', includeIfNull: false) int? status,
      @JsonKey(name: 'payment_method', includeIfNull: false) int? paymentMethod,
      @JsonKey(name: 'limit', includeIfNull: false) int? limit,
      @JsonKey(name: 'offset', includeIfNull: false) int? offset,
      @JsonKey(name: 'start_time', includeIfNull: false) String? startTime,
      @JsonKey(name: 'end_time', includeIfNull: false) String? endTime});
}

/// @nodoc
class __$$OrderListRequestImplCopyWithImpl<$Res>
    extends _$OrderListRequestCopyWithImpl<$Res, _$OrderListRequestImpl>
    implements _$$OrderListRequestImplCopyWith<$Res> {
  __$$OrderListRequestImplCopyWithImpl(_$OrderListRequestImpl _value,
      $Res Function(_$OrderListRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrderListRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? orderType = freezed,
    Object? status = freezed,
    Object? paymentMethod = freezed,
    Object? limit = freezed,
    Object? offset = freezed,
    Object? startTime = freezed,
    Object? endTime = freezed,
  }) {
    return _then(_$OrderListRequestImpl(
      orderType: freezed == orderType
          ? _value.orderType
          : orderType // ignore: cast_nullable_to_non_nullable
              as int?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      paymentMethod: freezed == paymentMethod
          ? _value.paymentMethod
          : paymentMethod // ignore: cast_nullable_to_non_nullable
              as int?,
      limit: freezed == limit
          ? _value.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int?,
      offset: freezed == offset
          ? _value.offset
          : offset // ignore: cast_nullable_to_non_nullable
              as int?,
      startTime: freezed == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as String?,
      endTime: freezed == endTime
          ? _value.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OrderListRequestImpl implements _OrderListRequest {
  const _$OrderListRequestImpl(
      {@JsonKey(name: 'order_type', includeIfNull: false) this.orderType,
      @JsonKey(name: 'status', includeIfNull: false) this.status,
      @JsonKey(name: 'payment_method', includeIfNull: false) this.paymentMethod,
      @JsonKey(name: 'limit', includeIfNull: false) this.limit,
      @JsonKey(name: 'offset', includeIfNull: false) this.offset,
      @JsonKey(name: 'start_time', includeIfNull: false) this.startTime,
      @JsonKey(name: 'end_time', includeIfNull: false) this.endTime});

  factory _$OrderListRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$OrderListRequestImplFromJson(json);

  @override
  @JsonKey(name: 'order_type', includeIfNull: false)
  final int? orderType;
// 订单类型
  @override
  @JsonKey(name: 'status', includeIfNull: false)
  final int? status;
// 订单状态
  @override
  @JsonKey(name: 'payment_method', includeIfNull: false)
  final int? paymentMethod;
// 支付方式
  @override
  @JsonKey(name: 'limit', includeIfNull: false)
  final int? limit;
// 每页条数
  @override
  @JsonKey(name: 'offset', includeIfNull: false)
  final int? offset;
// 偏移量
  @override
  @JsonKey(name: 'start_time', includeIfNull: false)
  final String? startTime;
// 开始时间
  @override
  @JsonKey(name: 'end_time', includeIfNull: false)
  final String? endTime;

  @override
  String toString() {
    return 'OrderListRequest(orderType: $orderType, status: $status, paymentMethod: $paymentMethod, limit: $limit, offset: $offset, startTime: $startTime, endTime: $endTime)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrderListRequestImpl &&
            (identical(other.orderType, orderType) ||
                other.orderType == orderType) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.paymentMethod, paymentMethod) ||
                other.paymentMethod == paymentMethod) &&
            (identical(other.limit, limit) || other.limit == limit) &&
            (identical(other.offset, offset) || other.offset == offset) &&
            (identical(other.startTime, startTime) ||
                other.startTime == startTime) &&
            (identical(other.endTime, endTime) || other.endTime == endTime));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, orderType, status, paymentMethod,
      limit, offset, startTime, endTime);

  /// Create a copy of OrderListRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OrderListRequestImplCopyWith<_$OrderListRequestImpl> get copyWith =>
      __$$OrderListRequestImplCopyWithImpl<_$OrderListRequestImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OrderListRequestImplToJson(
      this,
    );
  }
}

abstract class _OrderListRequest implements OrderListRequest {
  const factory _OrderListRequest(
      {@JsonKey(name: 'order_type', includeIfNull: false) final int? orderType,
      @JsonKey(name: 'status', includeIfNull: false) final int? status,
      @JsonKey(name: 'payment_method', includeIfNull: false)
      final int? paymentMethod,
      @JsonKey(name: 'limit', includeIfNull: false) final int? limit,
      @JsonKey(name: 'offset', includeIfNull: false) final int? offset,
      @JsonKey(name: 'start_time', includeIfNull: false)
      final String? startTime,
      @JsonKey(name: 'end_time', includeIfNull: false)
      final String? endTime}) = _$OrderListRequestImpl;

  factory _OrderListRequest.fromJson(Map<String, dynamic> json) =
      _$OrderListRequestImpl.fromJson;

  @override
  @JsonKey(name: 'order_type', includeIfNull: false)
  int? get orderType; // 订单类型
  @override
  @JsonKey(name: 'status', includeIfNull: false)
  int? get status; // 订单状态
  @override
  @JsonKey(name: 'payment_method', includeIfNull: false)
  int? get paymentMethod; // 支付方式
  @override
  @JsonKey(name: 'limit', includeIfNull: false)
  int? get limit; // 每页条数
  @override
  @JsonKey(name: 'offset', includeIfNull: false)
  int? get offset; // 偏移量
  @override
  @JsonKey(name: 'start_time', includeIfNull: false)
  String? get startTime; // 开始时间
  @override
  @JsonKey(name: 'end_time', includeIfNull: false)
  String? get endTime;

  /// Create a copy of OrderListRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OrderListRequestImplCopyWith<_$OrderListRequestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CreateOrderRequest _$CreateOrderRequestFromJson(Map<String, dynamic> json) {
  return _CreateOrderRequest.fromJson(json);
}

/// @nodoc
mixin _$CreateOrderRequest {
  @JsonKey(name: 'duration', includeIfNull: false)
  int? get duration => throw _privateConstructorUsedError;
  @JsonKey(name: 'environment_count', includeIfNull: false)
  int? get environmentCount => throw _privateConstructorUsedError;
  @JsonKey(name: 'member_count', includeIfNull: false)
  int? get memberCount => throw _privateConstructorUsedError;
  @JsonKey(name: 'order_type', includeIfNull: false)
  int get orderType => throw _privateConstructorUsedError;
  @JsonKey(name: 'payment_method', includeIfNull: false)
  int get paymentMethod => throw _privateConstructorUsedError;
  @JsonKey(name: 'proxy_count', includeIfNull: false)
  int? get proxyCount => throw _privateConstructorUsedError;
  @JsonKey(name: 'proxy_id', includeIfNull: false)
  int? get proxyId => throw _privateConstructorUsedError;
  @JsonKey(name: 'recharge', includeIfNull: false)
  int? get recharge => throw _privateConstructorUsedError;

  /// Serializes this CreateOrderRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CreateOrderRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CreateOrderRequestCopyWith<CreateOrderRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreateOrderRequestCopyWith<$Res> {
  factory $CreateOrderRequestCopyWith(
          CreateOrderRequest value, $Res Function(CreateOrderRequest) then) =
      _$CreateOrderRequestCopyWithImpl<$Res, CreateOrderRequest>;
  @useResult
  $Res call(
      {@JsonKey(name: 'duration', includeIfNull: false) int? duration,
      @JsonKey(name: 'environment_count', includeIfNull: false)
      int? environmentCount,
      @JsonKey(name: 'member_count', includeIfNull: false) int? memberCount,
      @JsonKey(name: 'order_type', includeIfNull: false) int orderType,
      @JsonKey(name: 'payment_method', includeIfNull: false) int paymentMethod,
      @JsonKey(name: 'proxy_count', includeIfNull: false) int? proxyCount,
      @JsonKey(name: 'proxy_id', includeIfNull: false) int? proxyId,
      @JsonKey(name: 'recharge', includeIfNull: false) int? recharge});
}

/// @nodoc
class _$CreateOrderRequestCopyWithImpl<$Res, $Val extends CreateOrderRequest>
    implements $CreateOrderRequestCopyWith<$Res> {
  _$CreateOrderRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CreateOrderRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? duration = freezed,
    Object? environmentCount = freezed,
    Object? memberCount = freezed,
    Object? orderType = null,
    Object? paymentMethod = null,
    Object? proxyCount = freezed,
    Object? proxyId = freezed,
    Object? recharge = freezed,
  }) {
    return _then(_value.copyWith(
      duration: freezed == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as int?,
      environmentCount: freezed == environmentCount
          ? _value.environmentCount
          : environmentCount // ignore: cast_nullable_to_non_nullable
              as int?,
      memberCount: freezed == memberCount
          ? _value.memberCount
          : memberCount // ignore: cast_nullable_to_non_nullable
              as int?,
      orderType: null == orderType
          ? _value.orderType
          : orderType // ignore: cast_nullable_to_non_nullable
              as int,
      paymentMethod: null == paymentMethod
          ? _value.paymentMethod
          : paymentMethod // ignore: cast_nullable_to_non_nullable
              as int,
      proxyCount: freezed == proxyCount
          ? _value.proxyCount
          : proxyCount // ignore: cast_nullable_to_non_nullable
              as int?,
      proxyId: freezed == proxyId
          ? _value.proxyId
          : proxyId // ignore: cast_nullable_to_non_nullable
              as int?,
      recharge: freezed == recharge
          ? _value.recharge
          : recharge // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CreateOrderRequestImplCopyWith<$Res>
    implements $CreateOrderRequestCopyWith<$Res> {
  factory _$$CreateOrderRequestImplCopyWith(_$CreateOrderRequestImpl value,
          $Res Function(_$CreateOrderRequestImpl) then) =
      __$$CreateOrderRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'duration', includeIfNull: false) int? duration,
      @JsonKey(name: 'environment_count', includeIfNull: false)
      int? environmentCount,
      @JsonKey(name: 'member_count', includeIfNull: false) int? memberCount,
      @JsonKey(name: 'order_type', includeIfNull: false) int orderType,
      @JsonKey(name: 'payment_method', includeIfNull: false) int paymentMethod,
      @JsonKey(name: 'proxy_count', includeIfNull: false) int? proxyCount,
      @JsonKey(name: 'proxy_id', includeIfNull: false) int? proxyId,
      @JsonKey(name: 'recharge', includeIfNull: false) int? recharge});
}

/// @nodoc
class __$$CreateOrderRequestImplCopyWithImpl<$Res>
    extends _$CreateOrderRequestCopyWithImpl<$Res, _$CreateOrderRequestImpl>
    implements _$$CreateOrderRequestImplCopyWith<$Res> {
  __$$CreateOrderRequestImplCopyWithImpl(_$CreateOrderRequestImpl _value,
      $Res Function(_$CreateOrderRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of CreateOrderRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? duration = freezed,
    Object? environmentCount = freezed,
    Object? memberCount = freezed,
    Object? orderType = null,
    Object? paymentMethod = null,
    Object? proxyCount = freezed,
    Object? proxyId = freezed,
    Object? recharge = freezed,
  }) {
    return _then(_$CreateOrderRequestImpl(
      duration: freezed == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as int?,
      environmentCount: freezed == environmentCount
          ? _value.environmentCount
          : environmentCount // ignore: cast_nullable_to_non_nullable
              as int?,
      memberCount: freezed == memberCount
          ? _value.memberCount
          : memberCount // ignore: cast_nullable_to_non_nullable
              as int?,
      orderType: null == orderType
          ? _value.orderType
          : orderType // ignore: cast_nullable_to_non_nullable
              as int,
      paymentMethod: null == paymentMethod
          ? _value.paymentMethod
          : paymentMethod // ignore: cast_nullable_to_non_nullable
              as int,
      proxyCount: freezed == proxyCount
          ? _value.proxyCount
          : proxyCount // ignore: cast_nullable_to_non_nullable
              as int?,
      proxyId: freezed == proxyId
          ? _value.proxyId
          : proxyId // ignore: cast_nullable_to_non_nullable
              as int?,
      recharge: freezed == recharge
          ? _value.recharge
          : recharge // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CreateOrderRequestImpl implements _CreateOrderRequest {
  const _$CreateOrderRequestImpl(
      {@JsonKey(name: 'duration', includeIfNull: false) this.duration,
      @JsonKey(name: 'environment_count', includeIfNull: false)
      this.environmentCount,
      @JsonKey(name: 'member_count', includeIfNull: false) this.memberCount,
      @JsonKey(name: 'order_type', includeIfNull: false)
      required this.orderType,
      @JsonKey(name: 'payment_method', includeIfNull: false)
      required this.paymentMethod,
      @JsonKey(name: 'proxy_count', includeIfNull: false) this.proxyCount,
      @JsonKey(name: 'proxy_id', includeIfNull: false) this.proxyId,
      @JsonKey(name: 'recharge', includeIfNull: false) this.recharge});

  factory _$CreateOrderRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$CreateOrderRequestImplFromJson(json);

  @override
  @JsonKey(name: 'duration', includeIfNull: false)
  final int? duration;
  @override
  @JsonKey(name: 'environment_count', includeIfNull: false)
  final int? environmentCount;
  @override
  @JsonKey(name: 'member_count', includeIfNull: false)
  final int? memberCount;
  @override
  @JsonKey(name: 'order_type', includeIfNull: false)
  final int orderType;
  @override
  @JsonKey(name: 'payment_method', includeIfNull: false)
  final int paymentMethod;
  @override
  @JsonKey(name: 'proxy_count', includeIfNull: false)
  final int? proxyCount;
  @override
  @JsonKey(name: 'proxy_id', includeIfNull: false)
  final int? proxyId;
  @override
  @JsonKey(name: 'recharge', includeIfNull: false)
  final int? recharge;

  @override
  String toString() {
    return 'CreateOrderRequest(duration: $duration, environmentCount: $environmentCount, memberCount: $memberCount, orderType: $orderType, paymentMethod: $paymentMethod, proxyCount: $proxyCount, proxyId: $proxyId, recharge: $recharge)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreateOrderRequestImpl &&
            (identical(other.duration, duration) ||
                other.duration == duration) &&
            (identical(other.environmentCount, environmentCount) ||
                other.environmentCount == environmentCount) &&
            (identical(other.memberCount, memberCount) ||
                other.memberCount == memberCount) &&
            (identical(other.orderType, orderType) ||
                other.orderType == orderType) &&
            (identical(other.paymentMethod, paymentMethod) ||
                other.paymentMethod == paymentMethod) &&
            (identical(other.proxyCount, proxyCount) ||
                other.proxyCount == proxyCount) &&
            (identical(other.proxyId, proxyId) || other.proxyId == proxyId) &&
            (identical(other.recharge, recharge) ||
                other.recharge == recharge));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, duration, environmentCount,
      memberCount, orderType, paymentMethod, proxyCount, proxyId, recharge);

  /// Create a copy of CreateOrderRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreateOrderRequestImplCopyWith<_$CreateOrderRequestImpl> get copyWith =>
      __$$CreateOrderRequestImplCopyWithImpl<_$CreateOrderRequestImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CreateOrderRequestImplToJson(
      this,
    );
  }
}

abstract class _CreateOrderRequest implements CreateOrderRequest {
  const factory _CreateOrderRequest(
      {@JsonKey(name: 'duration', includeIfNull: false) final int? duration,
      @JsonKey(name: 'environment_count', includeIfNull: false)
      final int? environmentCount,
      @JsonKey(name: 'member_count', includeIfNull: false)
      final int? memberCount,
      @JsonKey(name: 'order_type', includeIfNull: false)
      required final int orderType,
      @JsonKey(name: 'payment_method', includeIfNull: false)
      required final int paymentMethod,
      @JsonKey(name: 'proxy_count', includeIfNull: false) final int? proxyCount,
      @JsonKey(name: 'proxy_id', includeIfNull: false) final int? proxyId,
      @JsonKey(name: 'recharge', includeIfNull: false)
      final int? recharge}) = _$CreateOrderRequestImpl;

  factory _CreateOrderRequest.fromJson(Map<String, dynamic> json) =
      _$CreateOrderRequestImpl.fromJson;

  @override
  @JsonKey(name: 'duration', includeIfNull: false)
  int? get duration;
  @override
  @JsonKey(name: 'environment_count', includeIfNull: false)
  int? get environmentCount;
  @override
  @JsonKey(name: 'member_count', includeIfNull: false)
  int? get memberCount;
  @override
  @JsonKey(name: 'order_type', includeIfNull: false)
  int get orderType;
  @override
  @JsonKey(name: 'payment_method', includeIfNull: false)
  int get paymentMethod;
  @override
  @JsonKey(name: 'proxy_count', includeIfNull: false)
  int? get proxyCount;
  @override
  @JsonKey(name: 'proxy_id', includeIfNull: false)
  int? get proxyId;
  @override
  @JsonKey(name: 'recharge', includeIfNull: false)
  int? get recharge;

  /// Create a copy of CreateOrderRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreateOrderRequestImplCopyWith<_$CreateOrderRequestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CalculateOrderPriceRequest _$CalculateOrderPriceRequestFromJson(
    Map<String, dynamic> json) {
  return _CalculateOrderPriceRequest.fromJson(json);
}

/// @nodoc
mixin _$CalculateOrderPriceRequest {
  @JsonKey(name: 'duration', includeIfNull: false)
  int? get duration => throw _privateConstructorUsedError;
  @JsonKey(name: 'environment_count', includeIfNull: false)
  int? get environmentCount => throw _privateConstructorUsedError;
  @JsonKey(name: 'member_count', includeIfNull: false)
  int? get memberCount => throw _privateConstructorUsedError;
  @JsonKey(name: 'order_type', includeIfNull: false)
  int get orderType => throw _privateConstructorUsedError;
  @JsonKey(name: 'proxy_count', includeIfNull: false)
  int? get proxyCount => throw _privateConstructorUsedError;
  @JsonKey(name: 'proxy_id', includeIfNull: false)
  int? get proxyId => throw _privateConstructorUsedError;
  @JsonKey(name: 'recharge', includeIfNull: false)
  int? get recharge => throw _privateConstructorUsedError;

  /// Serializes this CalculateOrderPriceRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CalculateOrderPriceRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CalculateOrderPriceRequestCopyWith<CalculateOrderPriceRequest>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CalculateOrderPriceRequestCopyWith<$Res> {
  factory $CalculateOrderPriceRequestCopyWith(CalculateOrderPriceRequest value,
          $Res Function(CalculateOrderPriceRequest) then) =
      _$CalculateOrderPriceRequestCopyWithImpl<$Res,
          CalculateOrderPriceRequest>;
  @useResult
  $Res call(
      {@JsonKey(name: 'duration', includeIfNull: false) int? duration,
      @JsonKey(name: 'environment_count', includeIfNull: false)
      int? environmentCount,
      @JsonKey(name: 'member_count', includeIfNull: false) int? memberCount,
      @JsonKey(name: 'order_type', includeIfNull: false) int orderType,
      @JsonKey(name: 'proxy_count', includeIfNull: false) int? proxyCount,
      @JsonKey(name: 'proxy_id', includeIfNull: false) int? proxyId,
      @JsonKey(name: 'recharge', includeIfNull: false) int? recharge});
}

/// @nodoc
class _$CalculateOrderPriceRequestCopyWithImpl<$Res,
        $Val extends CalculateOrderPriceRequest>
    implements $CalculateOrderPriceRequestCopyWith<$Res> {
  _$CalculateOrderPriceRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CalculateOrderPriceRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? duration = freezed,
    Object? environmentCount = freezed,
    Object? memberCount = freezed,
    Object? orderType = null,
    Object? proxyCount = freezed,
    Object? proxyId = freezed,
    Object? recharge = freezed,
  }) {
    return _then(_value.copyWith(
      duration: freezed == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as int?,
      environmentCount: freezed == environmentCount
          ? _value.environmentCount
          : environmentCount // ignore: cast_nullable_to_non_nullable
              as int?,
      memberCount: freezed == memberCount
          ? _value.memberCount
          : memberCount // ignore: cast_nullable_to_non_nullable
              as int?,
      orderType: null == orderType
          ? _value.orderType
          : orderType // ignore: cast_nullable_to_non_nullable
              as int,
      proxyCount: freezed == proxyCount
          ? _value.proxyCount
          : proxyCount // ignore: cast_nullable_to_non_nullable
              as int?,
      proxyId: freezed == proxyId
          ? _value.proxyId
          : proxyId // ignore: cast_nullable_to_non_nullable
              as int?,
      recharge: freezed == recharge
          ? _value.recharge
          : recharge // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CalculateOrderPriceRequestImplCopyWith<$Res>
    implements $CalculateOrderPriceRequestCopyWith<$Res> {
  factory _$$CalculateOrderPriceRequestImplCopyWith(
          _$CalculateOrderPriceRequestImpl value,
          $Res Function(_$CalculateOrderPriceRequestImpl) then) =
      __$$CalculateOrderPriceRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'duration', includeIfNull: false) int? duration,
      @JsonKey(name: 'environment_count', includeIfNull: false)
      int? environmentCount,
      @JsonKey(name: 'member_count', includeIfNull: false) int? memberCount,
      @JsonKey(name: 'order_type', includeIfNull: false) int orderType,
      @JsonKey(name: 'proxy_count', includeIfNull: false) int? proxyCount,
      @JsonKey(name: 'proxy_id', includeIfNull: false) int? proxyId,
      @JsonKey(name: 'recharge', includeIfNull: false) int? recharge});
}

/// @nodoc
class __$$CalculateOrderPriceRequestImplCopyWithImpl<$Res>
    extends _$CalculateOrderPriceRequestCopyWithImpl<$Res,
        _$CalculateOrderPriceRequestImpl>
    implements _$$CalculateOrderPriceRequestImplCopyWith<$Res> {
  __$$CalculateOrderPriceRequestImplCopyWithImpl(
      _$CalculateOrderPriceRequestImpl _value,
      $Res Function(_$CalculateOrderPriceRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of CalculateOrderPriceRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? duration = freezed,
    Object? environmentCount = freezed,
    Object? memberCount = freezed,
    Object? orderType = null,
    Object? proxyCount = freezed,
    Object? proxyId = freezed,
    Object? recharge = freezed,
  }) {
    return _then(_$CalculateOrderPriceRequestImpl(
      duration: freezed == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as int?,
      environmentCount: freezed == environmentCount
          ? _value.environmentCount
          : environmentCount // ignore: cast_nullable_to_non_nullable
              as int?,
      memberCount: freezed == memberCount
          ? _value.memberCount
          : memberCount // ignore: cast_nullable_to_non_nullable
              as int?,
      orderType: null == orderType
          ? _value.orderType
          : orderType // ignore: cast_nullable_to_non_nullable
              as int,
      proxyCount: freezed == proxyCount
          ? _value.proxyCount
          : proxyCount // ignore: cast_nullable_to_non_nullable
              as int?,
      proxyId: freezed == proxyId
          ? _value.proxyId
          : proxyId // ignore: cast_nullable_to_non_nullable
              as int?,
      recharge: freezed == recharge
          ? _value.recharge
          : recharge // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CalculateOrderPriceRequestImpl implements _CalculateOrderPriceRequest {
  const _$CalculateOrderPriceRequestImpl(
      {@JsonKey(name: 'duration', includeIfNull: false) this.duration,
      @JsonKey(name: 'environment_count', includeIfNull: false)
      this.environmentCount,
      @JsonKey(name: 'member_count', includeIfNull: false) this.memberCount,
      @JsonKey(name: 'order_type', includeIfNull: false)
      required this.orderType,
      @JsonKey(name: 'proxy_count', includeIfNull: false) this.proxyCount,
      @JsonKey(name: 'proxy_id', includeIfNull: false) this.proxyId,
      @JsonKey(name: 'recharge', includeIfNull: false) this.recharge});

  factory _$CalculateOrderPriceRequestImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$CalculateOrderPriceRequestImplFromJson(json);

  @override
  @JsonKey(name: 'duration', includeIfNull: false)
  final int? duration;
  @override
  @JsonKey(name: 'environment_count', includeIfNull: false)
  final int? environmentCount;
  @override
  @JsonKey(name: 'member_count', includeIfNull: false)
  final int? memberCount;
  @override
  @JsonKey(name: 'order_type', includeIfNull: false)
  final int orderType;
  @override
  @JsonKey(name: 'proxy_count', includeIfNull: false)
  final int? proxyCount;
  @override
  @JsonKey(name: 'proxy_id', includeIfNull: false)
  final int? proxyId;
  @override
  @JsonKey(name: 'recharge', includeIfNull: false)
  final int? recharge;

  @override
  String toString() {
    return 'CalculateOrderPriceRequest(duration: $duration, environmentCount: $environmentCount, memberCount: $memberCount, orderType: $orderType, proxyCount: $proxyCount, proxyId: $proxyId, recharge: $recharge)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CalculateOrderPriceRequestImpl &&
            (identical(other.duration, duration) ||
                other.duration == duration) &&
            (identical(other.environmentCount, environmentCount) ||
                other.environmentCount == environmentCount) &&
            (identical(other.memberCount, memberCount) ||
                other.memberCount == memberCount) &&
            (identical(other.orderType, orderType) ||
                other.orderType == orderType) &&
            (identical(other.proxyCount, proxyCount) ||
                other.proxyCount == proxyCount) &&
            (identical(other.proxyId, proxyId) || other.proxyId == proxyId) &&
            (identical(other.recharge, recharge) ||
                other.recharge == recharge));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, duration, environmentCount,
      memberCount, orderType, proxyCount, proxyId, recharge);

  /// Create a copy of CalculateOrderPriceRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CalculateOrderPriceRequestImplCopyWith<_$CalculateOrderPriceRequestImpl>
      get copyWith => __$$CalculateOrderPriceRequestImplCopyWithImpl<
          _$CalculateOrderPriceRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CalculateOrderPriceRequestImplToJson(
      this,
    );
  }
}

abstract class _CalculateOrderPriceRequest
    implements CalculateOrderPriceRequest {
  const factory _CalculateOrderPriceRequest(
      {@JsonKey(name: 'duration', includeIfNull: false) final int? duration,
      @JsonKey(name: 'environment_count', includeIfNull: false)
      final int? environmentCount,
      @JsonKey(name: 'member_count', includeIfNull: false)
      final int? memberCount,
      @JsonKey(name: 'order_type', includeIfNull: false)
      required final int orderType,
      @JsonKey(name: 'proxy_count', includeIfNull: false) final int? proxyCount,
      @JsonKey(name: 'proxy_id', includeIfNull: false) final int? proxyId,
      @JsonKey(name: 'recharge', includeIfNull: false)
      final int? recharge}) = _$CalculateOrderPriceRequestImpl;

  factory _CalculateOrderPriceRequest.fromJson(Map<String, dynamic> json) =
      _$CalculateOrderPriceRequestImpl.fromJson;

  @override
  @JsonKey(name: 'duration', includeIfNull: false)
  int? get duration;
  @override
  @JsonKey(name: 'environment_count', includeIfNull: false)
  int? get environmentCount;
  @override
  @JsonKey(name: 'member_count', includeIfNull: false)
  int? get memberCount;
  @override
  @JsonKey(name: 'order_type', includeIfNull: false)
  int get orderType;
  @override
  @JsonKey(name: 'proxy_count', includeIfNull: false)
  int? get proxyCount;
  @override
  @JsonKey(name: 'proxy_id', includeIfNull: false)
  int? get proxyId;
  @override
  @JsonKey(name: 'recharge', includeIfNull: false)
  int? get recharge;

  /// Create a copy of CalculateOrderPriceRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CalculateOrderPriceRequestImplCopyWith<_$CalculateOrderPriceRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}

OrderListResponse _$OrderListResponseFromJson(Map<String, dynamic> json) {
  return _OrderListResponse.fromJson(json);
}

/// @nodoc
mixin _$OrderListResponse {
  @JsonKey(name: 'orders')
  List<Order> get orders => throw _privateConstructorUsedError;

  /// Serializes this OrderListResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of OrderListResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OrderListResponseCopyWith<OrderListResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrderListResponseCopyWith<$Res> {
  factory $OrderListResponseCopyWith(
          OrderListResponse value, $Res Function(OrderListResponse) then) =
      _$OrderListResponseCopyWithImpl<$Res, OrderListResponse>;
  @useResult
  $Res call({@JsonKey(name: 'orders') List<Order> orders});
}

/// @nodoc
class _$OrderListResponseCopyWithImpl<$Res, $Val extends OrderListResponse>
    implements $OrderListResponseCopyWith<$Res> {
  _$OrderListResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OrderListResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? orders = null,
  }) {
    return _then(_value.copyWith(
      orders: null == orders
          ? _value.orders
          : orders // ignore: cast_nullable_to_non_nullable
              as List<Order>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OrderListResponseImplCopyWith<$Res>
    implements $OrderListResponseCopyWith<$Res> {
  factory _$$OrderListResponseImplCopyWith(_$OrderListResponseImpl value,
          $Res Function(_$OrderListResponseImpl) then) =
      __$$OrderListResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({@JsonKey(name: 'orders') List<Order> orders});
}

/// @nodoc
class __$$OrderListResponseImplCopyWithImpl<$Res>
    extends _$OrderListResponseCopyWithImpl<$Res, _$OrderListResponseImpl>
    implements _$$OrderListResponseImplCopyWith<$Res> {
  __$$OrderListResponseImplCopyWithImpl(_$OrderListResponseImpl _value,
      $Res Function(_$OrderListResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrderListResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? orders = null,
  }) {
    return _then(_$OrderListResponseImpl(
      orders: null == orders
          ? _value._orders
          : orders // ignore: cast_nullable_to_non_nullable
              as List<Order>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OrderListResponseImpl implements _OrderListResponse {
  const _$OrderListResponseImpl(
      {@JsonKey(name: 'orders') required final List<Order> orders})
      : _orders = orders;

  factory _$OrderListResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$OrderListResponseImplFromJson(json);

  final List<Order> _orders;
  @override
  @JsonKey(name: 'orders')
  List<Order> get orders {
    if (_orders is EqualUnmodifiableListView) return _orders;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_orders);
  }

  @override
  String toString() {
    return 'OrderListResponse(orders: $orders)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrderListResponseImpl &&
            const DeepCollectionEquality().equals(other._orders, _orders));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_orders));

  /// Create a copy of OrderListResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OrderListResponseImplCopyWith<_$OrderListResponseImpl> get copyWith =>
      __$$OrderListResponseImplCopyWithImpl<_$OrderListResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OrderListResponseImplToJson(
      this,
    );
  }
}

abstract class _OrderListResponse implements OrderListResponse {
  const factory _OrderListResponse(
          {@JsonKey(name: 'orders') required final List<Order> orders}) =
      _$OrderListResponseImpl;

  factory _OrderListResponse.fromJson(Map<String, dynamic> json) =
      _$OrderListResponseImpl.fromJson;

  @override
  @JsonKey(name: 'orders')
  List<Order> get orders;

  /// Create a copy of OrderListResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OrderListResponseImplCopyWith<_$OrderListResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Order _$OrderFromJson(Map<String, dynamic> json) {
  return _Order.fromJson(json);
}

/// @nodoc
mixin _$Order {
  @JsonKey(name: 'amount')
  int? get amount => throw _privateConstructorUsedError;
  @JsonKey(name: 'balance_amount')
  int? get balanceAmount => throw _privateConstructorUsedError;
  @JsonKey(name: 'coupon_id')
  int? get couponId => throw _privateConstructorUsedError;
  @JsonKey(name: 'created_at')
  String? get createdAt => throw _privateConstructorUsedError;
  @JsonKey(name: 'currency')
  String? get currency => throw _privateConstructorUsedError;
  @JsonKey(name: 'expires_at')
  String? get expiresAt => throw _privateConstructorUsedError;
  @JsonKey(name: 'id')
  int? get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'order_content')
  String? get orderContent => throw _privateConstructorUsedError;
  @JsonKey(name: 'order_number')
  String? get orderNumber => throw _privateConstructorUsedError;
  @JsonKey(name: 'order_type')
  int? get orderType => throw _privateConstructorUsedError;
  @JsonKey(name: 'payment_method')
  int? get paymentMethod => throw _privateConstructorUsedError;
  @JsonKey(name: 'real_amount')
  int? get realAmount => throw _privateConstructorUsedError;
  @JsonKey(name: 'status')
  int? get status => throw _privateConstructorUsedError;
  @JsonKey(name: 'team_id')
  int? get teamId => throw _privateConstructorUsedError;
  @JsonKey(name: 'updated_at')
  String? get updatedAt => throw _privateConstructorUsedError;
  @JsonKey(name: 'url')
  String? get url => throw _privateConstructorUsedError;
  @JsonKey(name: 'user_id')
  int? get userId => throw _privateConstructorUsedError;

  /// Serializes this Order to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Order
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OrderCopyWith<Order> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrderCopyWith<$Res> {
  factory $OrderCopyWith(Order value, $Res Function(Order) then) =
      _$OrderCopyWithImpl<$Res, Order>;
  @useResult
  $Res call(
      {@JsonKey(name: 'amount') int? amount,
      @JsonKey(name: 'balance_amount') int? balanceAmount,
      @JsonKey(name: 'coupon_id') int? couponId,
      @JsonKey(name: 'created_at') String? createdAt,
      @JsonKey(name: 'currency') String? currency,
      @JsonKey(name: 'expires_at') String? expiresAt,
      @JsonKey(name: 'id') int? id,
      @JsonKey(name: 'order_content') String? orderContent,
      @JsonKey(name: 'order_number') String? orderNumber,
      @JsonKey(name: 'order_type') int? orderType,
      @JsonKey(name: 'payment_method') int? paymentMethod,
      @JsonKey(name: 'real_amount') int? realAmount,
      @JsonKey(name: 'status') int? status,
      @JsonKey(name: 'team_id') int? teamId,
      @JsonKey(name: 'updated_at') String? updatedAt,
      @JsonKey(name: 'url') String? url,
      @JsonKey(name: 'user_id') int? userId});
}

/// @nodoc
class _$OrderCopyWithImpl<$Res, $Val extends Order>
    implements $OrderCopyWith<$Res> {
  _$OrderCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Order
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amount = freezed,
    Object? balanceAmount = freezed,
    Object? couponId = freezed,
    Object? createdAt = freezed,
    Object? currency = freezed,
    Object? expiresAt = freezed,
    Object? id = freezed,
    Object? orderContent = freezed,
    Object? orderNumber = freezed,
    Object? orderType = freezed,
    Object? paymentMethod = freezed,
    Object? realAmount = freezed,
    Object? status = freezed,
    Object? teamId = freezed,
    Object? updatedAt = freezed,
    Object? url = freezed,
    Object? userId = freezed,
  }) {
    return _then(_value.copyWith(
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as int?,
      balanceAmount: freezed == balanceAmount
          ? _value.balanceAmount
          : balanceAmount // ignore: cast_nullable_to_non_nullable
              as int?,
      couponId: freezed == couponId
          ? _value.couponId
          : couponId // ignore: cast_nullable_to_non_nullable
              as int?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as String?,
      currency: freezed == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String?,
      expiresAt: freezed == expiresAt
          ? _value.expiresAt
          : expiresAt // ignore: cast_nullable_to_non_nullable
              as String?,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      orderContent: freezed == orderContent
          ? _value.orderContent
          : orderContent // ignore: cast_nullable_to_non_nullable
              as String?,
      orderNumber: freezed == orderNumber
          ? _value.orderNumber
          : orderNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      orderType: freezed == orderType
          ? _value.orderType
          : orderType // ignore: cast_nullable_to_non_nullable
              as int?,
      paymentMethod: freezed == paymentMethod
          ? _value.paymentMethod
          : paymentMethod // ignore: cast_nullable_to_non_nullable
              as int?,
      realAmount: freezed == realAmount
          ? _value.realAmount
          : realAmount // ignore: cast_nullable_to_non_nullable
              as int?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      teamId: freezed == teamId
          ? _value.teamId
          : teamId // ignore: cast_nullable_to_non_nullable
              as int?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as String?,
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OrderImplCopyWith<$Res> implements $OrderCopyWith<$Res> {
  factory _$$OrderImplCopyWith(
          _$OrderImpl value, $Res Function(_$OrderImpl) then) =
      __$$OrderImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'amount') int? amount,
      @JsonKey(name: 'balance_amount') int? balanceAmount,
      @JsonKey(name: 'coupon_id') int? couponId,
      @JsonKey(name: 'created_at') String? createdAt,
      @JsonKey(name: 'currency') String? currency,
      @JsonKey(name: 'expires_at') String? expiresAt,
      @JsonKey(name: 'id') int? id,
      @JsonKey(name: 'order_content') String? orderContent,
      @JsonKey(name: 'order_number') String? orderNumber,
      @JsonKey(name: 'order_type') int? orderType,
      @JsonKey(name: 'payment_method') int? paymentMethod,
      @JsonKey(name: 'real_amount') int? realAmount,
      @JsonKey(name: 'status') int? status,
      @JsonKey(name: 'team_id') int? teamId,
      @JsonKey(name: 'updated_at') String? updatedAt,
      @JsonKey(name: 'url') String? url,
      @JsonKey(name: 'user_id') int? userId});
}

/// @nodoc
class __$$OrderImplCopyWithImpl<$Res>
    extends _$OrderCopyWithImpl<$Res, _$OrderImpl>
    implements _$$OrderImplCopyWith<$Res> {
  __$$OrderImplCopyWithImpl(
      _$OrderImpl _value, $Res Function(_$OrderImpl) _then)
      : super(_value, _then);

  /// Create a copy of Order
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amount = freezed,
    Object? balanceAmount = freezed,
    Object? couponId = freezed,
    Object? createdAt = freezed,
    Object? currency = freezed,
    Object? expiresAt = freezed,
    Object? id = freezed,
    Object? orderContent = freezed,
    Object? orderNumber = freezed,
    Object? orderType = freezed,
    Object? paymentMethod = freezed,
    Object? realAmount = freezed,
    Object? status = freezed,
    Object? teamId = freezed,
    Object? updatedAt = freezed,
    Object? url = freezed,
    Object? userId = freezed,
  }) {
    return _then(_$OrderImpl(
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as int?,
      balanceAmount: freezed == balanceAmount
          ? _value.balanceAmount
          : balanceAmount // ignore: cast_nullable_to_non_nullable
              as int?,
      couponId: freezed == couponId
          ? _value.couponId
          : couponId // ignore: cast_nullable_to_non_nullable
              as int?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as String?,
      currency: freezed == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String?,
      expiresAt: freezed == expiresAt
          ? _value.expiresAt
          : expiresAt // ignore: cast_nullable_to_non_nullable
              as String?,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      orderContent: freezed == orderContent
          ? _value.orderContent
          : orderContent // ignore: cast_nullable_to_non_nullable
              as String?,
      orderNumber: freezed == orderNumber
          ? _value.orderNumber
          : orderNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      orderType: freezed == orderType
          ? _value.orderType
          : orderType // ignore: cast_nullable_to_non_nullable
              as int?,
      paymentMethod: freezed == paymentMethod
          ? _value.paymentMethod
          : paymentMethod // ignore: cast_nullable_to_non_nullable
              as int?,
      realAmount: freezed == realAmount
          ? _value.realAmount
          : realAmount // ignore: cast_nullable_to_non_nullable
              as int?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      teamId: freezed == teamId
          ? _value.teamId
          : teamId // ignore: cast_nullable_to_non_nullable
              as int?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as String?,
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OrderImpl implements _Order {
  const _$OrderImpl(
      {@JsonKey(name: 'amount') this.amount,
      @JsonKey(name: 'balance_amount') this.balanceAmount,
      @JsonKey(name: 'coupon_id') this.couponId,
      @JsonKey(name: 'created_at') this.createdAt,
      @JsonKey(name: 'currency') this.currency,
      @JsonKey(name: 'expires_at') this.expiresAt,
      @JsonKey(name: 'id') this.id,
      @JsonKey(name: 'order_content') this.orderContent,
      @JsonKey(name: 'order_number') this.orderNumber,
      @JsonKey(name: 'order_type') this.orderType,
      @JsonKey(name: 'payment_method') this.paymentMethod,
      @JsonKey(name: 'real_amount') this.realAmount,
      @JsonKey(name: 'status') this.status,
      @JsonKey(name: 'team_id') this.teamId,
      @JsonKey(name: 'updated_at') this.updatedAt,
      @JsonKey(name: 'url') this.url,
      @JsonKey(name: 'user_id') this.userId});

  factory _$OrderImpl.fromJson(Map<String, dynamic> json) =>
      _$$OrderImplFromJson(json);

  @override
  @JsonKey(name: 'amount')
  final int? amount;
  @override
  @JsonKey(name: 'balance_amount')
  final int? balanceAmount;
  @override
  @JsonKey(name: 'coupon_id')
  final int? couponId;
  @override
  @JsonKey(name: 'created_at')
  final String? createdAt;
  @override
  @JsonKey(name: 'currency')
  final String? currency;
  @override
  @JsonKey(name: 'expires_at')
  final String? expiresAt;
  @override
  @JsonKey(name: 'id')
  final int? id;
  @override
  @JsonKey(name: 'order_content')
  final String? orderContent;
  @override
  @JsonKey(name: 'order_number')
  final String? orderNumber;
  @override
  @JsonKey(name: 'order_type')
  final int? orderType;
  @override
  @JsonKey(name: 'payment_method')
  final int? paymentMethod;
  @override
  @JsonKey(name: 'real_amount')
  final int? realAmount;
  @override
  @JsonKey(name: 'status')
  final int? status;
  @override
  @JsonKey(name: 'team_id')
  final int? teamId;
  @override
  @JsonKey(name: 'updated_at')
  final String? updatedAt;
  @override
  @JsonKey(name: 'url')
  final String? url;
  @override
  @JsonKey(name: 'user_id')
  final int? userId;

  @override
  String toString() {
    return 'Order(amount: $amount, balanceAmount: $balanceAmount, couponId: $couponId, createdAt: $createdAt, currency: $currency, expiresAt: $expiresAt, id: $id, orderContent: $orderContent, orderNumber: $orderNumber, orderType: $orderType, paymentMethod: $paymentMethod, realAmount: $realAmount, status: $status, teamId: $teamId, updatedAt: $updatedAt, url: $url, userId: $userId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrderImpl &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.balanceAmount, balanceAmount) ||
                other.balanceAmount == balanceAmount) &&
            (identical(other.couponId, couponId) ||
                other.couponId == couponId) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            (identical(other.expiresAt, expiresAt) ||
                other.expiresAt == expiresAt) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.orderContent, orderContent) ||
                other.orderContent == orderContent) &&
            (identical(other.orderNumber, orderNumber) ||
                other.orderNumber == orderNumber) &&
            (identical(other.orderType, orderType) ||
                other.orderType == orderType) &&
            (identical(other.paymentMethod, paymentMethod) ||
                other.paymentMethod == paymentMethod) &&
            (identical(other.realAmount, realAmount) ||
                other.realAmount == realAmount) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.teamId, teamId) || other.teamId == teamId) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.url, url) || other.url == url) &&
            (identical(other.userId, userId) || other.userId == userId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      amount,
      balanceAmount,
      couponId,
      createdAt,
      currency,
      expiresAt,
      id,
      orderContent,
      orderNumber,
      orderType,
      paymentMethod,
      realAmount,
      status,
      teamId,
      updatedAt,
      url,
      userId);

  /// Create a copy of Order
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OrderImplCopyWith<_$OrderImpl> get copyWith =>
      __$$OrderImplCopyWithImpl<_$OrderImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OrderImplToJson(
      this,
    );
  }
}

abstract class _Order implements Order {
  const factory _Order(
      {@JsonKey(name: 'amount') final int? amount,
      @JsonKey(name: 'balance_amount') final int? balanceAmount,
      @JsonKey(name: 'coupon_id') final int? couponId,
      @JsonKey(name: 'created_at') final String? createdAt,
      @JsonKey(name: 'currency') final String? currency,
      @JsonKey(name: 'expires_at') final String? expiresAt,
      @JsonKey(name: 'id') final int? id,
      @JsonKey(name: 'order_content') final String? orderContent,
      @JsonKey(name: 'order_number') final String? orderNumber,
      @JsonKey(name: 'order_type') final int? orderType,
      @JsonKey(name: 'payment_method') final int? paymentMethod,
      @JsonKey(name: 'real_amount') final int? realAmount,
      @JsonKey(name: 'status') final int? status,
      @JsonKey(name: 'team_id') final int? teamId,
      @JsonKey(name: 'updated_at') final String? updatedAt,
      @JsonKey(name: 'url') final String? url,
      @JsonKey(name: 'user_id') final int? userId}) = _$OrderImpl;

  factory _Order.fromJson(Map<String, dynamic> json) = _$OrderImpl.fromJson;

  @override
  @JsonKey(name: 'amount')
  int? get amount;
  @override
  @JsonKey(name: 'balance_amount')
  int? get balanceAmount;
  @override
  @JsonKey(name: 'coupon_id')
  int? get couponId;
  @override
  @JsonKey(name: 'created_at')
  String? get createdAt;
  @override
  @JsonKey(name: 'currency')
  String? get currency;
  @override
  @JsonKey(name: 'expires_at')
  String? get expiresAt;
  @override
  @JsonKey(name: 'id')
  int? get id;
  @override
  @JsonKey(name: 'order_content')
  String? get orderContent;
  @override
  @JsonKey(name: 'order_number')
  String? get orderNumber;
  @override
  @JsonKey(name: 'order_type')
  int? get orderType;
  @override
  @JsonKey(name: 'payment_method')
  int? get paymentMethod;
  @override
  @JsonKey(name: 'real_amount')
  int? get realAmount;
  @override
  @JsonKey(name: 'status')
  int? get status;
  @override
  @JsonKey(name: 'team_id')
  int? get teamId;
  @override
  @JsonKey(name: 'updated_at')
  String? get updatedAt;
  @override
  @JsonKey(name: 'url')
  String? get url;
  @override
  @JsonKey(name: 'user_id')
  int? get userId;

  /// Create a copy of Order
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OrderImplCopyWith<_$OrderImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CreateOrderResponse _$CreateOrderResponseFromJson(Map<String, dynamic> json) {
  return _CreateOrderResponse.fromJson(json);
}

/// @nodoc
mixin _$CreateOrderResponse {
  @JsonKey(name: 'amount')
  int? get amount => throw _privateConstructorUsedError;
  @JsonKey(name: 'order_id')
  int? get orderId => throw _privateConstructorUsedError;
  @JsonKey(name: 'order_number')
  String? get orderNumber => throw _privateConstructorUsedError;
  @JsonKey(name: 'payment_url')
  String? get paymentUrl => throw _privateConstructorUsedError;

  /// Serializes this CreateOrderResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CreateOrderResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CreateOrderResponseCopyWith<CreateOrderResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreateOrderResponseCopyWith<$Res> {
  factory $CreateOrderResponseCopyWith(
          CreateOrderResponse value, $Res Function(CreateOrderResponse) then) =
      _$CreateOrderResponseCopyWithImpl<$Res, CreateOrderResponse>;
  @useResult
  $Res call(
      {@JsonKey(name: 'amount') int? amount,
      @JsonKey(name: 'order_id') int? orderId,
      @JsonKey(name: 'order_number') String? orderNumber,
      @JsonKey(name: 'payment_url') String? paymentUrl});
}

/// @nodoc
class _$CreateOrderResponseCopyWithImpl<$Res, $Val extends CreateOrderResponse>
    implements $CreateOrderResponseCopyWith<$Res> {
  _$CreateOrderResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CreateOrderResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amount = freezed,
    Object? orderId = freezed,
    Object? orderNumber = freezed,
    Object? paymentUrl = freezed,
  }) {
    return _then(_value.copyWith(
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as int?,
      orderId: freezed == orderId
          ? _value.orderId
          : orderId // ignore: cast_nullable_to_non_nullable
              as int?,
      orderNumber: freezed == orderNumber
          ? _value.orderNumber
          : orderNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      paymentUrl: freezed == paymentUrl
          ? _value.paymentUrl
          : paymentUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CreateOrderResponseImplCopyWith<$Res>
    implements $CreateOrderResponseCopyWith<$Res> {
  factory _$$CreateOrderResponseImplCopyWith(_$CreateOrderResponseImpl value,
          $Res Function(_$CreateOrderResponseImpl) then) =
      __$$CreateOrderResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'amount') int? amount,
      @JsonKey(name: 'order_id') int? orderId,
      @JsonKey(name: 'order_number') String? orderNumber,
      @JsonKey(name: 'payment_url') String? paymentUrl});
}

/// @nodoc
class __$$CreateOrderResponseImplCopyWithImpl<$Res>
    extends _$CreateOrderResponseCopyWithImpl<$Res, _$CreateOrderResponseImpl>
    implements _$$CreateOrderResponseImplCopyWith<$Res> {
  __$$CreateOrderResponseImplCopyWithImpl(_$CreateOrderResponseImpl _value,
      $Res Function(_$CreateOrderResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of CreateOrderResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amount = freezed,
    Object? orderId = freezed,
    Object? orderNumber = freezed,
    Object? paymentUrl = freezed,
  }) {
    return _then(_$CreateOrderResponseImpl(
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as int?,
      orderId: freezed == orderId
          ? _value.orderId
          : orderId // ignore: cast_nullable_to_non_nullable
              as int?,
      orderNumber: freezed == orderNumber
          ? _value.orderNumber
          : orderNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      paymentUrl: freezed == paymentUrl
          ? _value.paymentUrl
          : paymentUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CreateOrderResponseImpl implements _CreateOrderResponse {
  const _$CreateOrderResponseImpl(
      {@JsonKey(name: 'amount') this.amount,
      @JsonKey(name: 'order_id') this.orderId,
      @JsonKey(name: 'order_number') this.orderNumber,
      @JsonKey(name: 'payment_url') this.paymentUrl});

  factory _$CreateOrderResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$CreateOrderResponseImplFromJson(json);

  @override
  @JsonKey(name: 'amount')
  final int? amount;
  @override
  @JsonKey(name: 'order_id')
  final int? orderId;
  @override
  @JsonKey(name: 'order_number')
  final String? orderNumber;
  @override
  @JsonKey(name: 'payment_url')
  final String? paymentUrl;

  @override
  String toString() {
    return 'CreateOrderResponse(amount: $amount, orderId: $orderId, orderNumber: $orderNumber, paymentUrl: $paymentUrl)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreateOrderResponseImpl &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.orderId, orderId) || other.orderId == orderId) &&
            (identical(other.orderNumber, orderNumber) ||
                other.orderNumber == orderNumber) &&
            (identical(other.paymentUrl, paymentUrl) ||
                other.paymentUrl == paymentUrl));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, amount, orderId, orderNumber, paymentUrl);

  /// Create a copy of CreateOrderResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreateOrderResponseImplCopyWith<_$CreateOrderResponseImpl> get copyWith =>
      __$$CreateOrderResponseImplCopyWithImpl<_$CreateOrderResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CreateOrderResponseImplToJson(
      this,
    );
  }
}

abstract class _CreateOrderResponse implements CreateOrderResponse {
  const factory _CreateOrderResponse(
          {@JsonKey(name: 'amount') final int? amount,
          @JsonKey(name: 'order_id') final int? orderId,
          @JsonKey(name: 'order_number') final String? orderNumber,
          @JsonKey(name: 'payment_url') final String? paymentUrl}) =
      _$CreateOrderResponseImpl;

  factory _CreateOrderResponse.fromJson(Map<String, dynamic> json) =
      _$CreateOrderResponseImpl.fromJson;

  @override
  @JsonKey(name: 'amount')
  int? get amount;
  @override
  @JsonKey(name: 'order_id')
  int? get orderId;
  @override
  @JsonKey(name: 'order_number')
  String? get orderNumber;
  @override
  @JsonKey(name: 'payment_url')
  String? get paymentUrl;

  /// Create a copy of CreateOrderResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreateOrderResponseImplCopyWith<_$CreateOrderResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
