import 'dart:io';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'browser_instance_model.freezed.dart';
part 'browser_instance_model.g.dart';

/// 浏览器状态枚举
enum BrowserStatus {
  launching,    // 启动中
  running,      // 运行中
  stopping,     // 停止中
  stopped,      // 已停止
  crashed,      // 已崩溃
}

/// 浏览器实例模型
@freezed
class BrowserInstanceModel with _$BrowserInstanceModel {
  const factory BrowserInstanceModel({
    required String id,              // 唯一标识符
    required String name,            // 浏览器名称
    required String configPath,      // 配置文件路径
    required Map<String, dynamic> config, // 配置信息
    @Default(BrowserStatus.stopped) BrowserStatus status, // 当前状态
    int? pid,                        // 进程ID
    DateTime? startTime,             // 启动时间
    DateTime? lastActivity,          // 最后活动时间
  }) = _BrowserInstanceModel;

  const BrowserInstanceModel._();

  factory BrowserInstanceModel.fromJson(Map<String, dynamic> json) =>
      _$BrowserInstanceModelFromJson(json);

  /// 获取运行时长
  Duration? get uptime {
    if (startTime == null || status != BrowserStatus.running) return null;
    return DateTime.now().difference(startTime!);
  }

  /// 是否在运行
  bool get isRunning => status == BrowserStatus.running;

  /// 是否已停止
  bool get isStopped => status == BrowserStatus.stopped;
}

/// 浏览器实例运行时管理类
/// 包含不能序列化的运行时状态（如Process对象）
class BrowserInstanceRuntime {
  BrowserInstanceModel _model;
  Process? _process;

  BrowserInstanceRuntime(this._model);

  // Getters
  BrowserInstanceModel get model => _model;
  String get id => _model.id;
  String get name => _model.name;
  String get configPath => _model.configPath;
  Map<String, dynamic> get config => _model.config;
  BrowserStatus get status => _model.status;
  int? get pid => _model.pid;
  DateTime? get startTime => _model.startTime;
  DateTime? get lastActivity => _model.lastActivity;
  Process? get process => _process;
  Duration? get uptime => _model.uptime;
  bool get isRunning => _model.isRunning;
  bool get isStopped => _model.isStopped;

  /// 更新状态
  void updateStatus(BrowserStatus newStatus) {
    final now = DateTime.now();
    
    if (newStatus == BrowserStatus.running && _model.startTime == null) {
      _model = _model.copyWith(
        status: newStatus,
        startTime: now,
        lastActivity: now,
      );
    } else if (newStatus == BrowserStatus.stopped) {
      _process = null;
      _model = _model.copyWith(
        status: newStatus,
        startTime: null,
        pid: null,
        lastActivity: now,
      );
    } else {
      _model = _model.copyWith(
        status: newStatus,
        lastActivity: now,
      );
    }
  }

  /// 设置进程信息
  void setProcess(Process process, int pid) {
    _process = process;
    _model = _model.copyWith(
      pid: pid,
      status: BrowserStatus.running,
      startTime: _model.startTime ?? DateTime.now(),
      lastActivity: DateTime.now(),
    );
  }

  /// 设置PID
  void setPid(int pid) {
    _model = _model.copyWith(
      pid: pid,
      status: BrowserStatus.running,
      startTime: _model.startTime ?? DateTime.now(),
      lastActivity: DateTime.now(),
    );
  }

  @override
  String toString() {
    return 'BrowserInstanceRuntime(id: $id, name: $name, status: ${status.name}, pid: $pid)';
  }
} 
