// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'proxy_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ProxyListRequest _$ProxyListRequestFromJson(Map<String, dynamic> json) {
  return _ProxyListRequest.fromJson(json);
}

/// @nodoc
mixin _$ProxyListRequest {
  int? get id => throw _privateConstructorUsedError;
  int? get environmentId => throw _privateConstructorUsedError;
  int? get type => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  int? get limit => throw _privateConstructorUsedError;
  int? get offset => throw _privateConstructorUsedError;

  /// Serializes this ProxyListRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProxyListRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProxyListRequestCopyWith<ProxyListRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProxyListRequestCopyWith<$Res> {
  factory $ProxyListRequestCopyWith(
          ProxyListRequest value, $Res Function(ProxyListRequest) then) =
      _$ProxyListRequestCopyWithImpl<$Res, ProxyListRequest>;
  @useResult
  $Res call(
      {int? id,
      int? environmentId,
      int? type,
      String? name,
      int? limit,
      int? offset});
}

/// @nodoc
class _$ProxyListRequestCopyWithImpl<$Res, $Val extends ProxyListRequest>
    implements $ProxyListRequestCopyWith<$Res> {
  _$ProxyListRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProxyListRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? environmentId = freezed,
    Object? type = freezed,
    Object? name = freezed,
    Object? limit = freezed,
    Object? offset = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      environmentId: freezed == environmentId
          ? _value.environmentId
          : environmentId // ignore: cast_nullable_to_non_nullable
              as int?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      limit: freezed == limit
          ? _value.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int?,
      offset: freezed == offset
          ? _value.offset
          : offset // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProxyListRequestImplCopyWith<$Res>
    implements $ProxyListRequestCopyWith<$Res> {
  factory _$$ProxyListRequestImplCopyWith(_$ProxyListRequestImpl value,
          $Res Function(_$ProxyListRequestImpl) then) =
      __$$ProxyListRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? id,
      int? environmentId,
      int? type,
      String? name,
      int? limit,
      int? offset});
}

/// @nodoc
class __$$ProxyListRequestImplCopyWithImpl<$Res>
    extends _$ProxyListRequestCopyWithImpl<$Res, _$ProxyListRequestImpl>
    implements _$$ProxyListRequestImplCopyWith<$Res> {
  __$$ProxyListRequestImplCopyWithImpl(_$ProxyListRequestImpl _value,
      $Res Function(_$ProxyListRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProxyListRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? environmentId = freezed,
    Object? type = freezed,
    Object? name = freezed,
    Object? limit = freezed,
    Object? offset = freezed,
  }) {
    return _then(_$ProxyListRequestImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      environmentId: freezed == environmentId
          ? _value.environmentId
          : environmentId // ignore: cast_nullable_to_non_nullable
              as int?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      limit: freezed == limit
          ? _value.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int?,
      offset: freezed == offset
          ? _value.offset
          : offset // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ProxyListRequestImpl implements _ProxyListRequest {
  const _$ProxyListRequestImpl(
      {this.id,
      this.environmentId,
      this.type,
      this.name,
      this.limit,
      this.offset});

  factory _$ProxyListRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProxyListRequestImplFromJson(json);

  @override
  final int? id;
  @override
  final int? environmentId;
  @override
  final int? type;
  @override
  final String? name;
  @override
  final int? limit;
  @override
  final int? offset;

  @override
  String toString() {
    return 'ProxyListRequest(id: $id, environmentId: $environmentId, type: $type, name: $name, limit: $limit, offset: $offset)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProxyListRequestImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.environmentId, environmentId) ||
                other.environmentId == environmentId) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.limit, limit) || other.limit == limit) &&
            (identical(other.offset, offset) || other.offset == offset));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, environmentId, type, name, limit, offset);

  /// Create a copy of ProxyListRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProxyListRequestImplCopyWith<_$ProxyListRequestImpl> get copyWith =>
      __$$ProxyListRequestImplCopyWithImpl<_$ProxyListRequestImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProxyListRequestImplToJson(
      this,
    );
  }
}

abstract class _ProxyListRequest implements ProxyListRequest {
  const factory _ProxyListRequest(
      {final int? id,
      final int? environmentId,
      final int? type,
      final String? name,
      final int? limit,
      final int? offset}) = _$ProxyListRequestImpl;

  factory _ProxyListRequest.fromJson(Map<String, dynamic> json) =
      _$ProxyListRequestImpl.fromJson;

  @override
  int? get id;
  @override
  int? get environmentId;
  @override
  int? get type;
  @override
  String? get name;
  @override
  int? get limit;
  @override
  int? get offset;

  /// Create a copy of ProxyListRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProxyListRequestImplCopyWith<_$ProxyListRequestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

SelfProxyCreateRequest _$SelfProxyCreateRequestFromJson(
    Map<String, dynamic> json) {
  return _SelfProxyCreateRequest.fromJson(json);
}

/// @nodoc
mixin _$SelfProxyCreateRequest {
  String get name => throw _privateConstructorUsedError;
  int get type => throw _privateConstructorUsedError;
  String get host => throw _privateConstructorUsedError;
  int get port => throw _privateConstructorUsedError;
  @JsonKey(includeIfNull: false)
  String? get username => throw _privateConstructorUsedError;
  @JsonKey(includeIfNull: false)
  String? get password => throw _privateConstructorUsedError;
  @JsonKey(includeIfNull: false)
  int? get environmentId => throw _privateConstructorUsedError;

  /// Serializes this SelfProxyCreateRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SelfProxyCreateRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SelfProxyCreateRequestCopyWith<SelfProxyCreateRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SelfProxyCreateRequestCopyWith<$Res> {
  factory $SelfProxyCreateRequestCopyWith(SelfProxyCreateRequest value,
          $Res Function(SelfProxyCreateRequest) then) =
      _$SelfProxyCreateRequestCopyWithImpl<$Res, SelfProxyCreateRequest>;
  @useResult
  $Res call(
      {String name,
      int type,
      String host,
      int port,
      @JsonKey(includeIfNull: false) String? username,
      @JsonKey(includeIfNull: false) String? password,
      @JsonKey(includeIfNull: false) int? environmentId});
}

/// @nodoc
class _$SelfProxyCreateRequestCopyWithImpl<$Res,
        $Val extends SelfProxyCreateRequest>
    implements $SelfProxyCreateRequestCopyWith<$Res> {
  _$SelfProxyCreateRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SelfProxyCreateRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? type = null,
    Object? host = null,
    Object? port = null,
    Object? username = freezed,
    Object? password = freezed,
    Object? environmentId = freezed,
  }) {
    return _then(_value.copyWith(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int,
      host: null == host
          ? _value.host
          : host // ignore: cast_nullable_to_non_nullable
              as String,
      port: null == port
          ? _value.port
          : port // ignore: cast_nullable_to_non_nullable
              as int,
      username: freezed == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String?,
      password: freezed == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String?,
      environmentId: freezed == environmentId
          ? _value.environmentId
          : environmentId // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SelfProxyCreateRequestImplCopyWith<$Res>
    implements $SelfProxyCreateRequestCopyWith<$Res> {
  factory _$$SelfProxyCreateRequestImplCopyWith(
          _$SelfProxyCreateRequestImpl value,
          $Res Function(_$SelfProxyCreateRequestImpl) then) =
      __$$SelfProxyCreateRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String name,
      int type,
      String host,
      int port,
      @JsonKey(includeIfNull: false) String? username,
      @JsonKey(includeIfNull: false) String? password,
      @JsonKey(includeIfNull: false) int? environmentId});
}

/// @nodoc
class __$$SelfProxyCreateRequestImplCopyWithImpl<$Res>
    extends _$SelfProxyCreateRequestCopyWithImpl<$Res,
        _$SelfProxyCreateRequestImpl>
    implements _$$SelfProxyCreateRequestImplCopyWith<$Res> {
  __$$SelfProxyCreateRequestImplCopyWithImpl(
      _$SelfProxyCreateRequestImpl _value,
      $Res Function(_$SelfProxyCreateRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of SelfProxyCreateRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? type = null,
    Object? host = null,
    Object? port = null,
    Object? username = freezed,
    Object? password = freezed,
    Object? environmentId = freezed,
  }) {
    return _then(_$SelfProxyCreateRequestImpl(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int,
      host: null == host
          ? _value.host
          : host // ignore: cast_nullable_to_non_nullable
              as String,
      port: null == port
          ? _value.port
          : port // ignore: cast_nullable_to_non_nullable
              as int,
      username: freezed == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String?,
      password: freezed == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String?,
      environmentId: freezed == environmentId
          ? _value.environmentId
          : environmentId // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SelfProxyCreateRequestImpl implements _SelfProxyCreateRequest {
  const _$SelfProxyCreateRequestImpl(
      {required this.name,
      required this.type,
      required this.host,
      required this.port,
      @JsonKey(includeIfNull: false) this.username,
      @JsonKey(includeIfNull: false) this.password,
      @JsonKey(includeIfNull: false) this.environmentId});

  factory _$SelfProxyCreateRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$SelfProxyCreateRequestImplFromJson(json);

  @override
  final String name;
  @override
  final int type;
  @override
  final String host;
  @override
  final int port;
  @override
  @JsonKey(includeIfNull: false)
  final String? username;
  @override
  @JsonKey(includeIfNull: false)
  final String? password;
  @override
  @JsonKey(includeIfNull: false)
  final int? environmentId;

  @override
  String toString() {
    return 'SelfProxyCreateRequest(name: $name, type: $type, host: $host, port: $port, username: $username, password: $password, environmentId: $environmentId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SelfProxyCreateRequestImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.host, host) || other.host == host) &&
            (identical(other.port, port) || other.port == port) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.password, password) ||
                other.password == password) &&
            (identical(other.environmentId, environmentId) ||
                other.environmentId == environmentId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, name, type, host, port, username, password, environmentId);

  /// Create a copy of SelfProxyCreateRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SelfProxyCreateRequestImplCopyWith<_$SelfProxyCreateRequestImpl>
      get copyWith => __$$SelfProxyCreateRequestImplCopyWithImpl<
          _$SelfProxyCreateRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SelfProxyCreateRequestImplToJson(
      this,
    );
  }
}

abstract class _SelfProxyCreateRequest implements SelfProxyCreateRequest {
  const factory _SelfProxyCreateRequest(
          {required final String name,
          required final int type,
          required final String host,
          required final int port,
          @JsonKey(includeIfNull: false) final String? username,
          @JsonKey(includeIfNull: false) final String? password,
          @JsonKey(includeIfNull: false) final int? environmentId}) =
      _$SelfProxyCreateRequestImpl;

  factory _SelfProxyCreateRequest.fromJson(Map<String, dynamic> json) =
      _$SelfProxyCreateRequestImpl.fromJson;

  @override
  String get name;
  @override
  int get type;
  @override
  String get host;
  @override
  int get port;
  @override
  @JsonKey(includeIfNull: false)
  String? get username;
  @override
  @JsonKey(includeIfNull: false)
  String? get password;
  @override
  @JsonKey(includeIfNull: false)
  int? get environmentId;

  /// Create a copy of SelfProxyCreateRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SelfProxyCreateRequestImplCopyWith<_$SelfProxyCreateRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}

SelfProxyUpdateRequest _$SelfProxyUpdateRequestFromJson(
    Map<String, dynamic> json) {
  return _SelfProxyUpdateRequest.fromJson(json);
}

/// @nodoc
mixin _$SelfProxyUpdateRequest {
  int? get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  int get type => throw _privateConstructorUsedError;
  String get host => throw _privateConstructorUsedError;
  int get port => throw _privateConstructorUsedError;
  @JsonKey(includeIfNull: false)
  String? get username => throw _privateConstructorUsedError;
  @JsonKey(includeIfNull: false)
  String? get password => throw _privateConstructorUsedError;
  @JsonKey(includeIfNull: false)
  int? get environmentId => throw _privateConstructorUsedError;

  /// Serializes this SelfProxyUpdateRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SelfProxyUpdateRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SelfProxyUpdateRequestCopyWith<SelfProxyUpdateRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SelfProxyUpdateRequestCopyWith<$Res> {
  factory $SelfProxyUpdateRequestCopyWith(SelfProxyUpdateRequest value,
          $Res Function(SelfProxyUpdateRequest) then) =
      _$SelfProxyUpdateRequestCopyWithImpl<$Res, SelfProxyUpdateRequest>;
  @useResult
  $Res call(
      {int? id,
      String name,
      int type,
      String host,
      int port,
      @JsonKey(includeIfNull: false) String? username,
      @JsonKey(includeIfNull: false) String? password,
      @JsonKey(includeIfNull: false) int? environmentId});
}

/// @nodoc
class _$SelfProxyUpdateRequestCopyWithImpl<$Res,
        $Val extends SelfProxyUpdateRequest>
    implements $SelfProxyUpdateRequestCopyWith<$Res> {
  _$SelfProxyUpdateRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SelfProxyUpdateRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = null,
    Object? type = null,
    Object? host = null,
    Object? port = null,
    Object? username = freezed,
    Object? password = freezed,
    Object? environmentId = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int,
      host: null == host
          ? _value.host
          : host // ignore: cast_nullable_to_non_nullable
              as String,
      port: null == port
          ? _value.port
          : port // ignore: cast_nullable_to_non_nullable
              as int,
      username: freezed == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String?,
      password: freezed == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String?,
      environmentId: freezed == environmentId
          ? _value.environmentId
          : environmentId // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SelfProxyUpdateRequestImplCopyWith<$Res>
    implements $SelfProxyUpdateRequestCopyWith<$Res> {
  factory _$$SelfProxyUpdateRequestImplCopyWith(
          _$SelfProxyUpdateRequestImpl value,
          $Res Function(_$SelfProxyUpdateRequestImpl) then) =
      __$$SelfProxyUpdateRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? id,
      String name,
      int type,
      String host,
      int port,
      @JsonKey(includeIfNull: false) String? username,
      @JsonKey(includeIfNull: false) String? password,
      @JsonKey(includeIfNull: false) int? environmentId});
}

/// @nodoc
class __$$SelfProxyUpdateRequestImplCopyWithImpl<$Res>
    extends _$SelfProxyUpdateRequestCopyWithImpl<$Res,
        _$SelfProxyUpdateRequestImpl>
    implements _$$SelfProxyUpdateRequestImplCopyWith<$Res> {
  __$$SelfProxyUpdateRequestImplCopyWithImpl(
      _$SelfProxyUpdateRequestImpl _value,
      $Res Function(_$SelfProxyUpdateRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of SelfProxyUpdateRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = null,
    Object? type = null,
    Object? host = null,
    Object? port = null,
    Object? username = freezed,
    Object? password = freezed,
    Object? environmentId = freezed,
  }) {
    return _then(_$SelfProxyUpdateRequestImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int,
      host: null == host
          ? _value.host
          : host // ignore: cast_nullable_to_non_nullable
              as String,
      port: null == port
          ? _value.port
          : port // ignore: cast_nullable_to_non_nullable
              as int,
      username: freezed == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String?,
      password: freezed == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String?,
      environmentId: freezed == environmentId
          ? _value.environmentId
          : environmentId // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SelfProxyUpdateRequestImpl implements _SelfProxyUpdateRequest {
  const _$SelfProxyUpdateRequestImpl(
      {this.id,
      required this.name,
      required this.type,
      required this.host,
      required this.port,
      @JsonKey(includeIfNull: false) this.username,
      @JsonKey(includeIfNull: false) this.password,
      @JsonKey(includeIfNull: false) this.environmentId});

  factory _$SelfProxyUpdateRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$SelfProxyUpdateRequestImplFromJson(json);

  @override
  final int? id;
  @override
  final String name;
  @override
  final int type;
  @override
  final String host;
  @override
  final int port;
  @override
  @JsonKey(includeIfNull: false)
  final String? username;
  @override
  @JsonKey(includeIfNull: false)
  final String? password;
  @override
  @JsonKey(includeIfNull: false)
  final int? environmentId;

  @override
  String toString() {
    return 'SelfProxyUpdateRequest(id: $id, name: $name, type: $type, host: $host, port: $port, username: $username, password: $password, environmentId: $environmentId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SelfProxyUpdateRequestImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.host, host) || other.host == host) &&
            (identical(other.port, port) || other.port == port) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.password, password) ||
                other.password == password) &&
            (identical(other.environmentId, environmentId) ||
                other.environmentId == environmentId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, name, type, host, port,
      username, password, environmentId);

  /// Create a copy of SelfProxyUpdateRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SelfProxyUpdateRequestImplCopyWith<_$SelfProxyUpdateRequestImpl>
      get copyWith => __$$SelfProxyUpdateRequestImplCopyWithImpl<
          _$SelfProxyUpdateRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SelfProxyUpdateRequestImplToJson(
      this,
    );
  }
}

abstract class _SelfProxyUpdateRequest implements SelfProxyUpdateRequest {
  const factory _SelfProxyUpdateRequest(
          {final int? id,
          required final String name,
          required final int type,
          required final String host,
          required final int port,
          @JsonKey(includeIfNull: false) final String? username,
          @JsonKey(includeIfNull: false) final String? password,
          @JsonKey(includeIfNull: false) final int? environmentId}) =
      _$SelfProxyUpdateRequestImpl;

  factory _SelfProxyUpdateRequest.fromJson(Map<String, dynamic> json) =
      _$SelfProxyUpdateRequestImpl.fromJson;

  @override
  int? get id;
  @override
  String get name;
  @override
  int get type;
  @override
  String get host;
  @override
  int get port;
  @override
  @JsonKey(includeIfNull: false)
  String? get username;
  @override
  @JsonKey(includeIfNull: false)
  String? get password;
  @override
  @JsonKey(includeIfNull: false)
  int? get environmentId;

  /// Create a copy of SelfProxyUpdateRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SelfProxyUpdateRequestImplCopyWith<_$SelfProxyUpdateRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}

SelfProxyResponse _$SelfProxyResponseFromJson(Map<String, dynamic> json) {
  return _SelfProxyResponse.fromJson(json);
}

/// @nodoc
mixin _$SelfProxyResponse {
  List<SelfProxyItem> get proxies => throw _privateConstructorUsedError;
  int get total => throw _privateConstructorUsedError;

  /// Serializes this SelfProxyResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SelfProxyResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SelfProxyResponseCopyWith<SelfProxyResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SelfProxyResponseCopyWith<$Res> {
  factory $SelfProxyResponseCopyWith(
          SelfProxyResponse value, $Res Function(SelfProxyResponse) then) =
      _$SelfProxyResponseCopyWithImpl<$Res, SelfProxyResponse>;
  @useResult
  $Res call({List<SelfProxyItem> proxies, int total});
}

/// @nodoc
class _$SelfProxyResponseCopyWithImpl<$Res, $Val extends SelfProxyResponse>
    implements $SelfProxyResponseCopyWith<$Res> {
  _$SelfProxyResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SelfProxyResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? proxies = null,
    Object? total = null,
  }) {
    return _then(_value.copyWith(
      proxies: null == proxies
          ? _value.proxies
          : proxies // ignore: cast_nullable_to_non_nullable
              as List<SelfProxyItem>,
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SelfProxyResponseImplCopyWith<$Res>
    implements $SelfProxyResponseCopyWith<$Res> {
  factory _$$SelfProxyResponseImplCopyWith(_$SelfProxyResponseImpl value,
          $Res Function(_$SelfProxyResponseImpl) then) =
      __$$SelfProxyResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<SelfProxyItem> proxies, int total});
}

/// @nodoc
class __$$SelfProxyResponseImplCopyWithImpl<$Res>
    extends _$SelfProxyResponseCopyWithImpl<$Res, _$SelfProxyResponseImpl>
    implements _$$SelfProxyResponseImplCopyWith<$Res> {
  __$$SelfProxyResponseImplCopyWithImpl(_$SelfProxyResponseImpl _value,
      $Res Function(_$SelfProxyResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of SelfProxyResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? proxies = null,
    Object? total = null,
  }) {
    return _then(_$SelfProxyResponseImpl(
      proxies: null == proxies
          ? _value._proxies
          : proxies // ignore: cast_nullable_to_non_nullable
              as List<SelfProxyItem>,
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SelfProxyResponseImpl implements _SelfProxyResponse {
  const _$SelfProxyResponseImpl(
      {required final List<SelfProxyItem> proxies, required this.total})
      : _proxies = proxies;

  factory _$SelfProxyResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$SelfProxyResponseImplFromJson(json);

  final List<SelfProxyItem> _proxies;
  @override
  List<SelfProxyItem> get proxies {
    if (_proxies is EqualUnmodifiableListView) return _proxies;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_proxies);
  }

  @override
  final int total;

  @override
  String toString() {
    return 'SelfProxyResponse(proxies: $proxies, total: $total)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SelfProxyResponseImpl &&
            const DeepCollectionEquality().equals(other._proxies, _proxies) &&
            (identical(other.total, total) || other.total == total));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_proxies), total);

  /// Create a copy of SelfProxyResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SelfProxyResponseImplCopyWith<_$SelfProxyResponseImpl> get copyWith =>
      __$$SelfProxyResponseImplCopyWithImpl<_$SelfProxyResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SelfProxyResponseImplToJson(
      this,
    );
  }
}

abstract class _SelfProxyResponse implements SelfProxyResponse {
  const factory _SelfProxyResponse(
      {required final List<SelfProxyItem> proxies,
      required final int total}) = _$SelfProxyResponseImpl;

  factory _SelfProxyResponse.fromJson(Map<String, dynamic> json) =
      _$SelfProxyResponseImpl.fromJson;

  @override
  List<SelfProxyItem> get proxies;
  @override
  int get total;

  /// Create a copy of SelfProxyResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SelfProxyResponseImplCopyWith<_$SelfProxyResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

SelfProxyItem _$SelfProxyItemFromJson(Map<String, dynamic> json) {
  return _SelfProxyItem.fromJson(json);
}

/// @nodoc
mixin _$SelfProxyItem {
  int get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  int get type => throw _privateConstructorUsedError;
  String get host => throw _privateConstructorUsedError;
  int get port => throw _privateConstructorUsedError;
  @JsonKey(name: 'environment_name')
  String get environmentName => throw _privateConstructorUsedError;
  @JsonKey(name: 'team_name')
  String get teamName => throw _privateConstructorUsedError;

  /// Serializes this SelfProxyItem to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SelfProxyItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SelfProxyItemCopyWith<SelfProxyItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SelfProxyItemCopyWith<$Res> {
  factory $SelfProxyItemCopyWith(
          SelfProxyItem value, $Res Function(SelfProxyItem) then) =
      _$SelfProxyItemCopyWithImpl<$Res, SelfProxyItem>;
  @useResult
  $Res call(
      {int id,
      String name,
      int type,
      String host,
      int port,
      @JsonKey(name: 'environment_name') String environmentName,
      @JsonKey(name: 'team_name') String teamName});
}

/// @nodoc
class _$SelfProxyItemCopyWithImpl<$Res, $Val extends SelfProxyItem>
    implements $SelfProxyItemCopyWith<$Res> {
  _$SelfProxyItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SelfProxyItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? type = null,
    Object? host = null,
    Object? port = null,
    Object? environmentName = null,
    Object? teamName = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int,
      host: null == host
          ? _value.host
          : host // ignore: cast_nullable_to_non_nullable
              as String,
      port: null == port
          ? _value.port
          : port // ignore: cast_nullable_to_non_nullable
              as int,
      environmentName: null == environmentName
          ? _value.environmentName
          : environmentName // ignore: cast_nullable_to_non_nullable
              as String,
      teamName: null == teamName
          ? _value.teamName
          : teamName // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SelfProxyItemImplCopyWith<$Res>
    implements $SelfProxyItemCopyWith<$Res> {
  factory _$$SelfProxyItemImplCopyWith(
          _$SelfProxyItemImpl value, $Res Function(_$SelfProxyItemImpl) then) =
      __$$SelfProxyItemImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      String name,
      int type,
      String host,
      int port,
      @JsonKey(name: 'environment_name') String environmentName,
      @JsonKey(name: 'team_name') String teamName});
}

/// @nodoc
class __$$SelfProxyItemImplCopyWithImpl<$Res>
    extends _$SelfProxyItemCopyWithImpl<$Res, _$SelfProxyItemImpl>
    implements _$$SelfProxyItemImplCopyWith<$Res> {
  __$$SelfProxyItemImplCopyWithImpl(
      _$SelfProxyItemImpl _value, $Res Function(_$SelfProxyItemImpl) _then)
      : super(_value, _then);

  /// Create a copy of SelfProxyItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? type = null,
    Object? host = null,
    Object? port = null,
    Object? environmentName = null,
    Object? teamName = null,
  }) {
    return _then(_$SelfProxyItemImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int,
      host: null == host
          ? _value.host
          : host // ignore: cast_nullable_to_non_nullable
              as String,
      port: null == port
          ? _value.port
          : port // ignore: cast_nullable_to_non_nullable
              as int,
      environmentName: null == environmentName
          ? _value.environmentName
          : environmentName // ignore: cast_nullable_to_non_nullable
              as String,
      teamName: null == teamName
          ? _value.teamName
          : teamName // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SelfProxyItemImpl implements _SelfProxyItem {
  const _$SelfProxyItemImpl(
      {required this.id,
      required this.name,
      required this.type,
      required this.host,
      required this.port,
      @JsonKey(name: 'environment_name') required this.environmentName,
      @JsonKey(name: 'team_name') required this.teamName});

  factory _$SelfProxyItemImpl.fromJson(Map<String, dynamic> json) =>
      _$$SelfProxyItemImplFromJson(json);

  @override
  final int id;
  @override
  final String name;
  @override
  final int type;
  @override
  final String host;
  @override
  final int port;
  @override
  @JsonKey(name: 'environment_name')
  final String environmentName;
  @override
  @JsonKey(name: 'team_name')
  final String teamName;

  @override
  String toString() {
    return 'SelfProxyItem(id: $id, name: $name, type: $type, host: $host, port: $port, environmentName: $environmentName, teamName: $teamName)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SelfProxyItemImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.host, host) || other.host == host) &&
            (identical(other.port, port) || other.port == port) &&
            (identical(other.environmentName, environmentName) ||
                other.environmentName == environmentName) &&
            (identical(other.teamName, teamName) ||
                other.teamName == teamName));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, name, type, host, port, environmentName, teamName);

  /// Create a copy of SelfProxyItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SelfProxyItemImplCopyWith<_$SelfProxyItemImpl> get copyWith =>
      __$$SelfProxyItemImplCopyWithImpl<_$SelfProxyItemImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SelfProxyItemImplToJson(
      this,
    );
  }
}

abstract class _SelfProxyItem implements SelfProxyItem {
  const factory _SelfProxyItem(
      {required final int id,
      required final String name,
      required final int type,
      required final String host,
      required final int port,
      @JsonKey(name: 'environment_name') required final String environmentName,
      @JsonKey(name: 'team_name')
      required final String teamName}) = _$SelfProxyItemImpl;

  factory _SelfProxyItem.fromJson(Map<String, dynamic> json) =
      _$SelfProxyItemImpl.fromJson;

  @override
  int get id;
  @override
  String get name;
  @override
  int get type;
  @override
  String get host;
  @override
  int get port;
  @override
  @JsonKey(name: 'environment_name')
  String get environmentName;
  @override
  @JsonKey(name: 'team_name')
  String get teamName;

  /// Create a copy of SelfProxyItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SelfProxyItemImplCopyWith<_$SelfProxyItemImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

SelfProxyDetail _$SelfProxyDetailFromJson(Map<String, dynamic> json) {
  return _SelfProxyDetail.fromJson(json);
}

/// @nodoc
mixin _$SelfProxyDetail {
  int get id => throw _privateConstructorUsedError; // 自增主键 ID
  String? get name => throw _privateConstructorUsedError; // 代理名称，用户自定义标识
  int get type =>
      throw _privateConstructorUsedError; // 代理协议类型（1 HTTP, 2 HTTPS, 3 SOCKS5）
  String? get host => throw _privateConstructorUsedError; // 主机地址，可为 IP 或域名
  int get port => throw _privateConstructorUsedError; // 代理服务端口
  String? get username => throw _privateConstructorUsedError; // 可选的代理用户名
  String? get password => throw _privateConstructorUsedError; // 可选的代理密码
  @JsonKey(name: 'team_id')
  int? get teamId => throw _privateConstructorUsedError; // 代理所属团队 ID
  @JsonKey(name: 'environment_id')
  int? get environmentId => throw _privateConstructorUsedError;

  /// Serializes this SelfProxyDetail to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SelfProxyDetail
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SelfProxyDetailCopyWith<SelfProxyDetail> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SelfProxyDetailCopyWith<$Res> {
  factory $SelfProxyDetailCopyWith(
          SelfProxyDetail value, $Res Function(SelfProxyDetail) then) =
      _$SelfProxyDetailCopyWithImpl<$Res, SelfProxyDetail>;
  @useResult
  $Res call(
      {int id,
      String? name,
      int type,
      String? host,
      int port,
      String? username,
      String? password,
      @JsonKey(name: 'team_id') int? teamId,
      @JsonKey(name: 'environment_id') int? environmentId});
}

/// @nodoc
class _$SelfProxyDetailCopyWithImpl<$Res, $Val extends SelfProxyDetail>
    implements $SelfProxyDetailCopyWith<$Res> {
  _$SelfProxyDetailCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SelfProxyDetail
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = freezed,
    Object? type = null,
    Object? host = freezed,
    Object? port = null,
    Object? username = freezed,
    Object? password = freezed,
    Object? teamId = freezed,
    Object? environmentId = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int,
      host: freezed == host
          ? _value.host
          : host // ignore: cast_nullable_to_non_nullable
              as String?,
      port: null == port
          ? _value.port
          : port // ignore: cast_nullable_to_non_nullable
              as int,
      username: freezed == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String?,
      password: freezed == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String?,
      teamId: freezed == teamId
          ? _value.teamId
          : teamId // ignore: cast_nullable_to_non_nullable
              as int?,
      environmentId: freezed == environmentId
          ? _value.environmentId
          : environmentId // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SelfProxyDetailImplCopyWith<$Res>
    implements $SelfProxyDetailCopyWith<$Res> {
  factory _$$SelfProxyDetailImplCopyWith(_$SelfProxyDetailImpl value,
          $Res Function(_$SelfProxyDetailImpl) then) =
      __$$SelfProxyDetailImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      String? name,
      int type,
      String? host,
      int port,
      String? username,
      String? password,
      @JsonKey(name: 'team_id') int? teamId,
      @JsonKey(name: 'environment_id') int? environmentId});
}

/// @nodoc
class __$$SelfProxyDetailImplCopyWithImpl<$Res>
    extends _$SelfProxyDetailCopyWithImpl<$Res, _$SelfProxyDetailImpl>
    implements _$$SelfProxyDetailImplCopyWith<$Res> {
  __$$SelfProxyDetailImplCopyWithImpl(
      _$SelfProxyDetailImpl _value, $Res Function(_$SelfProxyDetailImpl) _then)
      : super(_value, _then);

  /// Create a copy of SelfProxyDetail
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = freezed,
    Object? type = null,
    Object? host = freezed,
    Object? port = null,
    Object? username = freezed,
    Object? password = freezed,
    Object? teamId = freezed,
    Object? environmentId = freezed,
  }) {
    return _then(_$SelfProxyDetailImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int,
      host: freezed == host
          ? _value.host
          : host // ignore: cast_nullable_to_non_nullable
              as String?,
      port: null == port
          ? _value.port
          : port // ignore: cast_nullable_to_non_nullable
              as int,
      username: freezed == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String?,
      password: freezed == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String?,
      teamId: freezed == teamId
          ? _value.teamId
          : teamId // ignore: cast_nullable_to_non_nullable
              as int?,
      environmentId: freezed == environmentId
          ? _value.environmentId
          : environmentId // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SelfProxyDetailImpl implements _SelfProxyDetail {
  const _$SelfProxyDetailImpl(
      {required this.id,
      this.name,
      required this.type,
      this.host,
      required this.port,
      this.username,
      this.password,
      @JsonKey(name: 'team_id') this.teamId,
      @JsonKey(name: 'environment_id') this.environmentId});

  factory _$SelfProxyDetailImpl.fromJson(Map<String, dynamic> json) =>
      _$$SelfProxyDetailImplFromJson(json);

  @override
  final int id;
// 自增主键 ID
  @override
  final String? name;
// 代理名称，用户自定义标识
  @override
  final int type;
// 代理协议类型（1 HTTP, 2 HTTPS, 3 SOCKS5）
  @override
  final String? host;
// 主机地址，可为 IP 或域名
  @override
  final int port;
// 代理服务端口
  @override
  final String? username;
// 可选的代理用户名
  @override
  final String? password;
// 可选的代理密码
  @override
  @JsonKey(name: 'team_id')
  final int? teamId;
// 代理所属团队 ID
  @override
  @JsonKey(name: 'environment_id')
  final int? environmentId;

  @override
  String toString() {
    return 'SelfProxyDetail(id: $id, name: $name, type: $type, host: $host, port: $port, username: $username, password: $password, teamId: $teamId, environmentId: $environmentId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SelfProxyDetailImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.host, host) || other.host == host) &&
            (identical(other.port, port) || other.port == port) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.password, password) ||
                other.password == password) &&
            (identical(other.teamId, teamId) || other.teamId == teamId) &&
            (identical(other.environmentId, environmentId) ||
                other.environmentId == environmentId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, name, type, host, port,
      username, password, teamId, environmentId);

  /// Create a copy of SelfProxyDetail
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SelfProxyDetailImplCopyWith<_$SelfProxyDetailImpl> get copyWith =>
      __$$SelfProxyDetailImplCopyWithImpl<_$SelfProxyDetailImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SelfProxyDetailImplToJson(
      this,
    );
  }
}

abstract class _SelfProxyDetail implements SelfProxyDetail {
  const factory _SelfProxyDetail(
          {required final int id,
          final String? name,
          required final int type,
          final String? host,
          required final int port,
          final String? username,
          final String? password,
          @JsonKey(name: 'team_id') final int? teamId,
          @JsonKey(name: 'environment_id') final int? environmentId}) =
      _$SelfProxyDetailImpl;

  factory _SelfProxyDetail.fromJson(Map<String, dynamic> json) =
      _$SelfProxyDetailImpl.fromJson;

  @override
  int get id; // 自增主键 ID
  @override
  String? get name; // 代理名称，用户自定义标识
  @override
  int get type; // 代理协议类型（1 HTTP, 2 HTTPS, 3 SOCKS5）
  @override
  String? get host; // 主机地址，可为 IP 或域名
  @override
  int get port; // 代理服务端口
  @override
  String? get username; // 可选的代理用户名
  @override
  String? get password; // 可选的代理密码
  @override
  @JsonKey(name: 'team_id')
  int? get teamId; // 代理所属团队 ID
  @override
  @JsonKey(name: 'environment_id')
  int? get environmentId;

  /// Create a copy of SelfProxyDetail
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SelfProxyDetailImplCopyWith<_$SelfProxyDetailImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PlatformProxyModel _$PlatformProxyModelFromJson(Map<String, dynamic> json) {
  return _PlatformProxyModel.fromJson(json);
}

/// @nodoc
mixin _$PlatformProxyModel {
  List<PlatformProxy> get proxies => throw _privateConstructorUsedError;
  int get total => throw _privateConstructorUsedError;

  /// Serializes this PlatformProxyModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PlatformProxyModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PlatformProxyModelCopyWith<PlatformProxyModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PlatformProxyModelCopyWith<$Res> {
  factory $PlatformProxyModelCopyWith(
          PlatformProxyModel value, $Res Function(PlatformProxyModel) then) =
      _$PlatformProxyModelCopyWithImpl<$Res, PlatformProxyModel>;
  @useResult
  $Res call({List<PlatformProxy> proxies, int total});
}

/// @nodoc
class _$PlatformProxyModelCopyWithImpl<$Res, $Val extends PlatformProxyModel>
    implements $PlatformProxyModelCopyWith<$Res> {
  _$PlatformProxyModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PlatformProxyModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? proxies = null,
    Object? total = null,
  }) {
    return _then(_value.copyWith(
      proxies: null == proxies
          ? _value.proxies
          : proxies // ignore: cast_nullable_to_non_nullable
              as List<PlatformProxy>,
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PlatformProxyModelImplCopyWith<$Res>
    implements $PlatformProxyModelCopyWith<$Res> {
  factory _$$PlatformProxyModelImplCopyWith(_$PlatformProxyModelImpl value,
          $Res Function(_$PlatformProxyModelImpl) then) =
      __$$PlatformProxyModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<PlatformProxy> proxies, int total});
}

/// @nodoc
class __$$PlatformProxyModelImplCopyWithImpl<$Res>
    extends _$PlatformProxyModelCopyWithImpl<$Res, _$PlatformProxyModelImpl>
    implements _$$PlatformProxyModelImplCopyWith<$Res> {
  __$$PlatformProxyModelImplCopyWithImpl(_$PlatformProxyModelImpl _value,
      $Res Function(_$PlatformProxyModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of PlatformProxyModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? proxies = null,
    Object? total = null,
  }) {
    return _then(_$PlatformProxyModelImpl(
      proxies: null == proxies
          ? _value._proxies
          : proxies // ignore: cast_nullable_to_non_nullable
              as List<PlatformProxy>,
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PlatformProxyModelImpl implements _PlatformProxyModel {
  const _$PlatformProxyModelImpl(
      {required final List<PlatformProxy> proxies, required this.total})
      : _proxies = proxies;

  factory _$PlatformProxyModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$PlatformProxyModelImplFromJson(json);

  final List<PlatformProxy> _proxies;
  @override
  List<PlatformProxy> get proxies {
    if (_proxies is EqualUnmodifiableListView) return _proxies;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_proxies);
  }

  @override
  final int total;

  @override
  String toString() {
    return 'PlatformProxyModel(proxies: $proxies, total: $total)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PlatformProxyModelImpl &&
            const DeepCollectionEquality().equals(other._proxies, _proxies) &&
            (identical(other.total, total) || other.total == total));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_proxies), total);

  /// Create a copy of PlatformProxyModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PlatformProxyModelImplCopyWith<_$PlatformProxyModelImpl> get copyWith =>
      __$$PlatformProxyModelImplCopyWithImpl<_$PlatformProxyModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PlatformProxyModelImplToJson(
      this,
    );
  }
}

abstract class _PlatformProxyModel implements PlatformProxyModel {
  const factory _PlatformProxyModel(
      {required final List<PlatformProxy> proxies,
      required final int total}) = _$PlatformProxyModelImpl;

  factory _PlatformProxyModel.fromJson(Map<String, dynamic> json) =
      _$PlatformProxyModelImpl.fromJson;

  @override
  List<PlatformProxy> get proxies;
  @override
  int get total;

  /// Create a copy of PlatformProxyModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PlatformProxyModelImplCopyWith<_$PlatformProxyModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PlatformProxy _$PlatformProxyFromJson(Map<String, dynamic> json) {
  return _PlatformProxy.fromJson(json);
}

/// @nodoc
mixin _$PlatformProxy {
  int get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError; // 代理名称
  @JsonKey(name: 'ip_address')
  String get ipAddress => throw _privateConstructorUsedError; // IP地址
  String get environment => throw _privateConstructorUsedError; // 环境
  String get region => throw _privateConstructorUsedError; // 地区
  @JsonKey(name: 'is_unlimited')
  bool get isUnlimited => throw _privateConstructorUsedError; // 是否无限制
  @JsonKey(name: 'auto_renew')
  bool get autoRenew => throw _privateConstructorUsedError; // 是否自动续期
  @JsonKey(name: 'expires_at')
  String get expiresAt => throw _privateConstructorUsedError;

  /// Serializes this PlatformProxy to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PlatformProxy
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PlatformProxyCopyWith<PlatformProxy> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PlatformProxyCopyWith<$Res> {
  factory $PlatformProxyCopyWith(
          PlatformProxy value, $Res Function(PlatformProxy) then) =
      _$PlatformProxyCopyWithImpl<$Res, PlatformProxy>;
  @useResult
  $Res call(
      {int id,
      String name,
      @JsonKey(name: 'ip_address') String ipAddress,
      String environment,
      String region,
      @JsonKey(name: 'is_unlimited') bool isUnlimited,
      @JsonKey(name: 'auto_renew') bool autoRenew,
      @JsonKey(name: 'expires_at') String expiresAt});
}

/// @nodoc
class _$PlatformProxyCopyWithImpl<$Res, $Val extends PlatformProxy>
    implements $PlatformProxyCopyWith<$Res> {
  _$PlatformProxyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PlatformProxy
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? ipAddress = null,
    Object? environment = null,
    Object? region = null,
    Object? isUnlimited = null,
    Object? autoRenew = null,
    Object? expiresAt = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      ipAddress: null == ipAddress
          ? _value.ipAddress
          : ipAddress // ignore: cast_nullable_to_non_nullable
              as String,
      environment: null == environment
          ? _value.environment
          : environment // ignore: cast_nullable_to_non_nullable
              as String,
      region: null == region
          ? _value.region
          : region // ignore: cast_nullable_to_non_nullable
              as String,
      isUnlimited: null == isUnlimited
          ? _value.isUnlimited
          : isUnlimited // ignore: cast_nullable_to_non_nullable
              as bool,
      autoRenew: null == autoRenew
          ? _value.autoRenew
          : autoRenew // ignore: cast_nullable_to_non_nullable
              as bool,
      expiresAt: null == expiresAt
          ? _value.expiresAt
          : expiresAt // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PlatformProxyImplCopyWith<$Res>
    implements $PlatformProxyCopyWith<$Res> {
  factory _$$PlatformProxyImplCopyWith(
          _$PlatformProxyImpl value, $Res Function(_$PlatformProxyImpl) then) =
      __$$PlatformProxyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      String name,
      @JsonKey(name: 'ip_address') String ipAddress,
      String environment,
      String region,
      @JsonKey(name: 'is_unlimited') bool isUnlimited,
      @JsonKey(name: 'auto_renew') bool autoRenew,
      @JsonKey(name: 'expires_at') String expiresAt});
}

/// @nodoc
class __$$PlatformProxyImplCopyWithImpl<$Res>
    extends _$PlatformProxyCopyWithImpl<$Res, _$PlatformProxyImpl>
    implements _$$PlatformProxyImplCopyWith<$Res> {
  __$$PlatformProxyImplCopyWithImpl(
      _$PlatformProxyImpl _value, $Res Function(_$PlatformProxyImpl) _then)
      : super(_value, _then);

  /// Create a copy of PlatformProxy
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? ipAddress = null,
    Object? environment = null,
    Object? region = null,
    Object? isUnlimited = null,
    Object? autoRenew = null,
    Object? expiresAt = null,
  }) {
    return _then(_$PlatformProxyImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      ipAddress: null == ipAddress
          ? _value.ipAddress
          : ipAddress // ignore: cast_nullable_to_non_nullable
              as String,
      environment: null == environment
          ? _value.environment
          : environment // ignore: cast_nullable_to_non_nullable
              as String,
      region: null == region
          ? _value.region
          : region // ignore: cast_nullable_to_non_nullable
              as String,
      isUnlimited: null == isUnlimited
          ? _value.isUnlimited
          : isUnlimited // ignore: cast_nullable_to_non_nullable
              as bool,
      autoRenew: null == autoRenew
          ? _value.autoRenew
          : autoRenew // ignore: cast_nullable_to_non_nullable
              as bool,
      expiresAt: null == expiresAt
          ? _value.expiresAt
          : expiresAt // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PlatformProxyImpl implements _PlatformProxy {
  const _$PlatformProxyImpl(
      {required this.id,
      required this.name,
      @JsonKey(name: 'ip_address') required this.ipAddress,
      required this.environment,
      required this.region,
      @JsonKey(name: 'is_unlimited') required this.isUnlimited,
      @JsonKey(name: 'auto_renew') required this.autoRenew,
      @JsonKey(name: 'expires_at') required this.expiresAt});

  factory _$PlatformProxyImpl.fromJson(Map<String, dynamic> json) =>
      _$$PlatformProxyImplFromJson(json);

  @override
  final int id;
  @override
  final String name;
// 代理名称
  @override
  @JsonKey(name: 'ip_address')
  final String ipAddress;
// IP地址
  @override
  final String environment;
// 环境
  @override
  final String region;
// 地区
  @override
  @JsonKey(name: 'is_unlimited')
  final bool isUnlimited;
// 是否无限制
  @override
  @JsonKey(name: 'auto_renew')
  final bool autoRenew;
// 是否自动续期
  @override
  @JsonKey(name: 'expires_at')
  final String expiresAt;

  @override
  String toString() {
    return 'PlatformProxy(id: $id, name: $name, ipAddress: $ipAddress, environment: $environment, region: $region, isUnlimited: $isUnlimited, autoRenew: $autoRenew, expiresAt: $expiresAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PlatformProxyImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.ipAddress, ipAddress) ||
                other.ipAddress == ipAddress) &&
            (identical(other.environment, environment) ||
                other.environment == environment) &&
            (identical(other.region, region) || other.region == region) &&
            (identical(other.isUnlimited, isUnlimited) ||
                other.isUnlimited == isUnlimited) &&
            (identical(other.autoRenew, autoRenew) ||
                other.autoRenew == autoRenew) &&
            (identical(other.expiresAt, expiresAt) ||
                other.expiresAt == expiresAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, name, ipAddress, environment,
      region, isUnlimited, autoRenew, expiresAt);

  /// Create a copy of PlatformProxy
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PlatformProxyImplCopyWith<_$PlatformProxyImpl> get copyWith =>
      __$$PlatformProxyImplCopyWithImpl<_$PlatformProxyImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PlatformProxyImplToJson(
      this,
    );
  }
}

abstract class _PlatformProxy implements PlatformProxy {
  const factory _PlatformProxy(
          {required final int id,
          required final String name,
          @JsonKey(name: 'ip_address') required final String ipAddress,
          required final String environment,
          required final String region,
          @JsonKey(name: 'is_unlimited') required final bool isUnlimited,
          @JsonKey(name: 'auto_renew') required final bool autoRenew,
          @JsonKey(name: 'expires_at') required final String expiresAt}) =
      _$PlatformProxyImpl;

  factory _PlatformProxy.fromJson(Map<String, dynamic> json) =
      _$PlatformProxyImpl.fromJson;

  @override
  int get id;
  @override
  String get name; // 代理名称
  @override
  @JsonKey(name: 'ip_address')
  String get ipAddress; // IP地址
  @override
  String get environment; // 环境
  @override
  String get region; // 地区
  @override
  @JsonKey(name: 'is_unlimited')
  bool get isUnlimited; // 是否无限制
  @override
  @JsonKey(name: 'auto_renew')
  bool get autoRenew; // 是否自动续期
  @override
  @JsonKey(name: 'expires_at')
  String get expiresAt;

  /// Create a copy of PlatformProxy
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PlatformProxyImplCopyWith<_$PlatformProxyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
