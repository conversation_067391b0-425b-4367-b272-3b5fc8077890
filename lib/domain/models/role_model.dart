import 'dart:convert';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'role_model.freezed.dart';
part 'role_model.g.dart';

/// 角色领域模型
@freezed
class RoleModel with _$RoleModel {
  const factory RoleModel({
    @JsonKey(name: 'id') required int id,             // 角色ID
    @JsonKey(name: 'name') required String name,      // 角色名称
    @Json<PERSON>ey(name: 'team_id') required int teamId,    // 团队ID
    @JsonKey(
      name: 'permissions',
      fromJson: _permissionsFromJson,
      toJson: _permissionsToJson,
    )
    required Map<String, dynamic> permissions,         // 权限配置
    @Json<PERSON>ey(name: 'created_at') DateTime? createdAt, // 创建时间（可空）
    @JsonKey(name: 'updated_at') DateTime? updatedAt, // 更新时间（可空）
    @JsonKey(name: 'secure') bool? secure,            // 安全登录标识（可空）
  }) = _RoleModel;

  /// 从 JSON 创建模型实例
  factory RoleModel.fromJson(Map<String, dynamic> json) =>
      _$RoleModelFromJson(json);
}

/// 创建角色请求模型
@freezed
class CreateRoleRequest with _$CreateRoleRequest {
  const factory CreateRoleRequest({
    @JsonKey(name: 'name') required String name,
    /// 权限配置（必须是有效的 JSON 字符串）
    /// 示例: '{"read": true, "write": true}'
    @JsonKey(name: 'permissions') required String permissions,
    @JsonKey(name: 'secure') required bool secure,
  }) = _CreateRoleRequest;

  factory CreateRoleRequest.fromJson(Map<String, dynamic> json) =>
      _$CreateRoleRequestFromJson(json);
}

/// 更新角色请求模型
@freezed
class UpdateRoleRequest with _$UpdateRoleRequest {
  const factory UpdateRoleRequest({
    @JsonKey(name: 'id') required int id,
    @JsonKey(name: 'name') required String name,
    @JsonKey(name: 'permissions') required String permissions,
    @JsonKey(name: 'secure') required bool secure,
  }) = _UpdateRoleRequest;

  factory UpdateRoleRequest.fromJson(Map<String, dynamic> json) =>
      _$UpdateRoleRequestFromJson(json);
}

/// 自定义方法，将 Permissions 字段从 JSON 字符串解析为 Map
/// 支持两种格式：
/// 1. 对象格式：{"logs:*": true, "roles:*": true}
/// 2. 数组格式：["users:profile_read", "users:profile_update"]
Map<String, dynamic> _permissionsFromJson(String jsonStr) {
  final decoded = jsonDecode(jsonStr);
  
  if (decoded is Map<String, dynamic>) {
    // 对象格式，直接返回
    return decoded;
  } else if (decoded is List) {
    // 数组格式，转换为对象格式（权限名为key，值为true）
    final Map<String, dynamic> permissionMap = {};
    for (final permission in decoded) {
      if (permission is String) {
        permissionMap[permission] = true;
      }
    }
    return permissionMap;
  } else {
    // 不支持的格式，返回空Map
    return {};
  }
}

/// 自定义方法，将 Permissions 字段从 Map 转换为 JSON 字符串
String _permissionsToJson(Map<String, dynamic> permissions) =>
    jsonEncode(permissions); 
