import 'package:freezed_annotation/freezed_annotation.dart';

part 'auth_model.freezed.dart';
part 'auth_model.g.dart';

// ==================== 请求模型 ====================

/// 登录请求模型
@freezed
class LoginRequest with _$LoginRequest {
  const factory LoginRequest({
    required String identifier, // 邮箱或用户名
    required String password,
  }) = _LoginRequest;

  factory LoginRequest.fromJson(Map<String, dynamic> json) =>
      _$LoginRequestFromJson(json);
}

/// 注册请求模型
@freezed
class RegisterRequest with _$RegisterRequest {
  const factory RegisterRequest({
    @JsonKey(name: 'user_name') required String username, // 必需，最大20字符
    required String password, // 必需字段，最大60字符
    String? email, // 可选，有效邮箱，最大100字符
    String? telephone, // 可选，最大15字符
    @JsonKey(name: 'invite_code') String? inviteCode, // 可选，最大15字符
    @JsonKey(name: 'email_code') String? emailCode, // 可选，最大15字符
  }) = _RegisterRequest;

  factory RegisterRequest.fromJson(Map<String, dynamic> json) =>
      _$RegisterRequestFromJson(json);
}

// ==================== 响应模型 ====================

/// 认证响应模型
@freezed
class AuthResponse with _$AuthResponse {
  const factory AuthResponse({
    required String message,
    String? token,
    User? user,
  }) = _AuthResponse;

  factory AuthResponse.fromJson(Map<String, dynamic> json) =>
      _$AuthResponseFromJson(json);
}

/// 用户信息模型
@freezed
class User with _$User {
  const factory User({
    required int id,
    required String username,
    String? email,
    String? telephone,
    String? avatar,
    @JsonKey(name: 'created_at') String? createdAt,
    @JsonKey(name: 'updated_at') String? updatedAt,
  }) = _User;

  factory User.fromJson(Map<String, dynamic> json) =>
      _$UserFromJson(json);
} 

/// 验证码响应模型
@freezed
class CaptchaResponse with _$CaptchaResponse {
  const factory CaptchaResponse({
    @JsonKey(name: 'captcha_image') required String captchaImage,
    required String seed,
    required String target,
  }) = _CaptchaResponse;

  factory CaptchaResponse.fromJson(Map<String, dynamic> json) =>
      _$CaptchaResponseFromJson(json);
}

/// 验证验证码请求模型
@freezed
class VerifyCaptchaRequest with _$VerifyCaptchaRequest {
  const factory VerifyCaptchaRequest({
    required String answer,
    required String nonce,
    required String seed,
    required String target,
  }) = _VerifyCaptchaRequest;

  factory VerifyCaptchaRequest.fromJson(Map<String, dynamic> json) =>
      _$VerifyCaptchaRequestFromJson(json);
}

/// 验证验证码响应模型
@freezed
class VerifyCaptchaResponse with _$VerifyCaptchaResponse {
  const factory VerifyCaptchaResponse({
    required bool valid,
  }) = _VerifyCaptchaResponse;

  factory VerifyCaptchaResponse.fromJson(Map<String, dynamic> json) =>
      _$VerifyCaptchaResponseFromJson(json);
}

/// 发送注册验证邮件请求模型
@freezed
class SendRegisterEmailRequest with _$SendRegisterEmailRequest {
  const factory SendRegisterEmailRequest({
    required String answer,
    required String email,
    required String nonce,
    required String seed,
    required String target,
  }) = _SendRegisterEmailRequest;

  factory SendRegisterEmailRequest.fromJson(Map<String, dynamic> json) =>
      _$SendRegisterEmailRequestFromJson(json);
}

/// 发送注册验证邮件响应模型
@freezed
class SendRegisterEmailResponse with _$SendRegisterEmailResponse {
  const factory SendRegisterEmailResponse({
    required String message,
    required bool sent,
  }) = _SendRegisterEmailResponse;

  factory SendRegisterEmailResponse.fromJson(Map<String, dynamic> json) =>
      _$SendRegisterEmailResponseFromJson(json);
}
