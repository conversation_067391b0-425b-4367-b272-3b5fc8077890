// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'common_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CommonResponse _$CommonResponseFromJson(Map<String, dynamic> json) {
  return _CommonResponse.fromJson(json);
}

/// @nodoc
mixin _$CommonResponse {
  int get code => throw _privateConstructorUsedError; // 状态码，默认为200
  String get message => throw _privateConstructorUsedError; // 消息
  Object? get data => throw _privateConstructorUsedError;

  /// Serializes this CommonResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CommonResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CommonResponseCopyWith<CommonResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CommonResponseCopyWith<$Res> {
  factory $CommonResponseCopyWith(
          CommonResponse value, $Res Function(CommonResponse) then) =
      _$CommonResponseCopyWithImpl<$Res, CommonResponse>;
  @useResult
  $Res call({int code, String message, Object? data});
}

/// @nodoc
class _$CommonResponseCopyWithImpl<$Res, $Val extends CommonResponse>
    implements $CommonResponseCopyWith<$Res> {
  _$CommonResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CommonResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? code = null,
    Object? message = null,
    Object? data = freezed,
  }) {
    return _then(_value.copyWith(
      code: null == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as int,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      data: freezed == data ? _value.data : data,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CommonResponseImplCopyWith<$Res>
    implements $CommonResponseCopyWith<$Res> {
  factory _$$CommonResponseImplCopyWith(_$CommonResponseImpl value,
          $Res Function(_$CommonResponseImpl) then) =
      __$$CommonResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int code, String message, Object? data});
}

/// @nodoc
class __$$CommonResponseImplCopyWithImpl<$Res>
    extends _$CommonResponseCopyWithImpl<$Res, _$CommonResponseImpl>
    implements _$$CommonResponseImplCopyWith<$Res> {
  __$$CommonResponseImplCopyWithImpl(
      _$CommonResponseImpl _value, $Res Function(_$CommonResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of CommonResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? code = null,
    Object? message = null,
    Object? data = freezed,
  }) {
    return _then(_$CommonResponseImpl(
      code: null == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as int,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      data: freezed == data ? _value.data : data,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CommonResponseImpl implements _CommonResponse {
  const _$CommonResponseImpl(
      {this.code = 200, required this.message, this.data});

  factory _$CommonResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$CommonResponseImplFromJson(json);

  @override
  @JsonKey()
  final int code;
// 状态码，默认为200
  @override
  final String message;
// 消息
  @override
  final Object? data;

  @override
  String toString() {
    return 'CommonResponse(code: $code, message: $message, data: $data)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CommonResponseImpl &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.message, message) || other.message == message) &&
            const DeepCollectionEquality().equals(other.data, data));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, code, message, const DeepCollectionEquality().hash(data));

  /// Create a copy of CommonResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CommonResponseImplCopyWith<_$CommonResponseImpl> get copyWith =>
      __$$CommonResponseImplCopyWithImpl<_$CommonResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CommonResponseImplToJson(
      this,
    );
  }
}

abstract class _CommonResponse implements CommonResponse {
  const factory _CommonResponse(
      {final int code,
      required final String message,
      final Object? data}) = _$CommonResponseImpl;

  factory _CommonResponse.fromJson(Map<String, dynamic> json) =
      _$CommonResponseImpl.fromJson;

  @override
  int get code; // 状态码，默认为200
  @override
  String get message; // 消息
  @override
  Object? get data;

  /// Create a copy of CommonResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CommonResponseImplCopyWith<_$CommonResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
