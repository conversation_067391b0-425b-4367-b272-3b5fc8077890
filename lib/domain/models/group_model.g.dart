// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'group_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$GroupListRequestImpl _$$GroupListRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$GroupListRequestImpl(
      id: (json['id'] as num?)?.toInt(),
      teamId: (json['teamId'] as num?)?.toInt(),
      name: json['name'] as String?,
      limit: (json['limit'] as num?)?.toInt(),
      offset: (json['offset'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$GroupListRequestImplToJson(
        _$GroupListRequestImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'teamId': instance.teamId,
      'name': instance.name,
      'limit': instance.limit,
      'offset': instance.offset,
    };

_$GroupResponseImpl _$$GroupResponseImplFromJson(Map<String, dynamic> json) =>
    _$GroupResponseImpl(
      groups: (json['groups'] as List<dynamic>)
          .map((e) => Group.fromJson(e as Map<String, dynamic>))
          .toList(),
      total: (json['total'] as num).toInt(),
    );

Map<String, dynamic> _$$GroupResponseImplToJson(_$GroupResponseImpl instance) =>
    <String, dynamic>{
      'groups': instance.groups,
      'total': instance.total,
    };

_$GroupImpl _$$GroupImplFromJson(Map<String, dynamic> json) => _$GroupImpl(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      teamId: (json['team_id'] as num).toInt(),
      userName: json['user_name'] as String,
      createdAt: json['created_at'] as String,
      updatedAt: json['updated_at'] as String,
    );

Map<String, dynamic> _$$GroupImplToJson(_$GroupImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'team_id': instance.teamId,
      'user_name': instance.userName,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
    };
