// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'team_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$TeamModelResponseImpl _$$TeamModelResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$TeamModelResponseImpl(
      total: (json['total'] as num).toInt(),
      users: (json['users'] as List<dynamic>)
          .map((e) => TeamUserResponse.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$TeamModelResponseImplToJson(
        _$TeamModelResponseImpl instance) =>
    <String, dynamic>{
      'total': instance.total,
      'users': instance.users,
    };

_$TeamRequestImpl _$$TeamRequestImplFromJson(Map<String, dynamic> json) =>
    _$TeamRequestImpl(
      username: json['username'] as String?,
      roleId: (json['role_id'] as num?)?.toInt(),
      limit: (json['limit'] as num).toInt(),
      offset: (json['offset'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$TeamRequestImplToJson(_$TeamRequestImpl instance) =>
    <String, dynamic>{
      'username': instance.username,
      'role_id': instance.roleId,
      'limit': instance.limit,
      'offset': instance.offset,
    };

_$TeamUserResponseImpl _$$TeamUserResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$TeamUserResponseImpl(
      id: (json['id'] as num).toInt(),
      username: json['username'] as String,
      email: json['email'] as String,
      isTwoFactorEnabled: json['is_two_factor_enabled'] as bool,
      isActive: json['is_active'] as bool,
      roleName: json['role_name'] as String,
      isOwner: json['is_owner'] as bool,
    );

Map<String, dynamic> _$$TeamUserResponseImplToJson(
        _$TeamUserResponseImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'username': instance.username,
      'email': instance.email,
      'is_two_factor_enabled': instance.isTwoFactorEnabled,
      'is_active': instance.isActive,
      'role_name': instance.roleName,
      'is_owner': instance.isOwner,
    };

_$AddTeamUserRequestImpl _$$AddTeamUserRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$AddTeamUserRequestImpl(
      isActive: json['is_active'] as bool?,
      password: json['password'] as String,
      roleId: (json['role_id'] as num).toInt(),
      userName: json['user_name'] as String,
    );

Map<String, dynamic> _$$AddTeamUserRequestImplToJson(
        _$AddTeamUserRequestImpl instance) =>
    <String, dynamic>{
      if (instance.isActive case final value?) 'is_active': value,
      'password': instance.password,
      'role_id': instance.roleId,
      'user_name': instance.userName,
    };

_$UpdateTeamUserRequestImpl _$$UpdateTeamUserRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$UpdateTeamUserRequestImpl(
      id: (json['id'] as num).toInt(),
      isActive: json['is_active'] as bool?,
      password: json['password'] as String?,
      roleId: (json['role_id'] as num?)?.toInt(),
      userName: json['user_name'] as String?,
    );

Map<String, dynamic> _$$UpdateTeamUserRequestImplToJson(
        _$UpdateTeamUserRequestImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      if (instance.isActive case final value?) 'is_active': value,
      if (instance.password case final value?) 'password': value,
      if (instance.roleId case final value?) 'role_id': value,
      if (instance.userName case final value?) 'user_name': value,
    };

_$LoginLogRequestImpl _$$LoginLogRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$LoginLogRequestImpl(
      userId: (json['user_id'] as num?)?.toInt(),
      limit: (json['limit'] as num?)?.toInt() ?? 10,
      offset: (json['offset'] as num?)?.toInt() ?? 0,
      startTime: json['start_time'],
      endTime: json['end_time'],
    );

Map<String, dynamic> _$$LoginLogRequestImplToJson(
        _$LoginLogRequestImpl instance) =>
    <String, dynamic>{
      'user_id': instance.userId,
      'limit': instance.limit,
      'offset': instance.offset,
      'start_time': instance.startTime,
      'end_time': instance.endTime,
    };

_$LoginLogModelImpl _$$LoginLogModelImplFromJson(Map<String, dynamic> json) =>
    _$LoginLogModelImpl(
      logs: (json['logs'] as List<dynamic>)
          .map((e) => LoginLogItem.fromJson(e as Map<String, dynamic>))
          .toList(),
      total: (json['total'] as num).toInt(),
    );

Map<String, dynamic> _$$LoginLogModelImplToJson(_$LoginLogModelImpl instance) =>
    <String, dynamic>{
      'logs': instance.logs,
      'total': instance.total,
    };

_$LoginLogItemImpl _$$LoginLogItemImplFromJson(Map<String, dynamic> json) =>
    _$LoginLogItemImpl(
      id: (json['id'] as num).toInt(),
      createdAt: json['created_at'] as String,
      ipLocation: json['ip_location'] as String,
      loginIp: json['login_ip'] as String,
      userName: json['user_name'] as String,
    );

Map<String, dynamic> _$$LoginLogItemImplToJson(_$LoginLogItemImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'created_at': instance.createdAt,
      'ip_location': instance.ipLocation,
      'login_ip': instance.loginIp,
      'user_name': instance.userName,
    };

_$OperationLogRequestImpl _$$OperationLogRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$OperationLogRequestImpl(
      userId: (json['user_id'] as num?)?.toInt(),
      action: (json['action'] as num?)?.toInt(),
      category: (json['category'] as num?)?.toInt(),
      target: json['target'] as String?,
      limit: (json['limit'] as num?)?.toInt(),
      offset: (json['offset'] as num?)?.toInt(),
      startTime: json['start_time'] as String?,
      endTime: json['end_time'] as String?,
    );

Map<String, dynamic> _$$OperationLogRequestImplToJson(
        _$OperationLogRequestImpl instance) =>
    <String, dynamic>{
      'user_id': instance.userId,
      'action': instance.action,
      'category': instance.category,
      'target': instance.target,
      'limit': instance.limit,
      'offset': instance.offset,
      'start_time': instance.startTime,
      'end_time': instance.endTime,
    };

_$OperationLogModelImpl _$$OperationLogModelImplFromJson(
        Map<String, dynamic> json) =>
    _$OperationLogModelImpl(
      logs: (json['logs'] as List<dynamic>)
          .map((e) => OperationLogItem.fromJson(e as Map<String, dynamic>))
          .toList(),
      total: (json['total'] as num).toInt(),
    );

Map<String, dynamic> _$$OperationLogModelImplToJson(
        _$OperationLogModelImpl instance) =>
    <String, dynamic>{
      'logs': instance.logs,
      'total': instance.total,
    };

_$OperationLogItemImpl _$$OperationLogItemImplFromJson(
        Map<String, dynamic> json) =>
    _$OperationLogItemImpl(
      id: (json['id'] as num).toInt(),
      userId: (json['user_id'] as num).toInt(),
      teamId: (json['team_id'] as num).toInt(),
      action: (json['action'] as num).toInt(),
      target: json['target'] as String,
      category: (json['category'] as num).toInt(),
      createdAt: json['created_at'] as String,
    );

Map<String, dynamic> _$$OperationLogItemImplToJson(
        _$OperationLogItemImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'user_id': instance.userId,
      'team_id': instance.teamId,
      'action': instance.action,
      'target': instance.target,
      'category': instance.category,
      'created_at': instance.createdAt,
    };
