// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'role_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$RoleModelImpl _$$RoleModelImplFromJson(Map<String, dynamic> json) =>
    _$RoleModelImpl(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      teamId: (json['team_id'] as num).toInt(),
      permissions: _permissionsFromJson(json['permissions'] as String),
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
      secure: json['secure'] as bool?,
    );

Map<String, dynamic> _$$RoleModelImplToJson(_$RoleModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'team_id': instance.teamId,
      'permissions': _permissionsToJson(instance.permissions),
      'created_at': instance.createdAt?.toIso8601String(),
      'updated_at': instance.updatedAt?.toIso8601String(),
      'secure': instance.secure,
    };

_$CreateRoleRequestImpl _$$CreateRoleRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$CreateRoleRequestImpl(
      name: json['name'] as String,
      permissions: json['permissions'] as String,
      secure: json['secure'] as bool,
    );

Map<String, dynamic> _$$CreateRoleRequestImplToJson(
        _$CreateRoleRequestImpl instance) =>
    <String, dynamic>{
      'name': instance.name,
      'permissions': instance.permissions,
      'secure': instance.secure,
    };

_$UpdateRoleRequestImpl _$$UpdateRoleRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$UpdateRoleRequestImpl(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      permissions: json['permissions'] as String,
      secure: json['secure'] as bool,
    );

Map<String, dynamic> _$$UpdateRoleRequestImplToJson(
        _$UpdateRoleRequestImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'permissions': instance.permissions,
      'secure': instance.secure,
    };
