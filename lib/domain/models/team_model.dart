import 'package:freezed_annotation/freezed_annotation.dart';

part 'team_model.freezed.dart';
part 'team_model.g.dart';

/// 团队领域模型
@freezed
class TeamModelResponse with _$TeamModelResponse {
  const factory TeamModelResponse({
    required int total,
    required List<TeamUserResponse> users,
  }) = _TeamModelResponse;

  factory TeamModelResponse.fromJson(Map<String, dynamic> json) =>
      _$TeamModelResponseFromJson(json);
}

/// 团队成员列表请求模型
@freezed
class TeamRequest with _$TeamRequest {
  const factory TeamRequest({
    @J<PERSON><PERSON><PERSON>(name: 'username') String? username,
    @Json<PERSON>ey(name: 'role_id') int? roleId,
    @JsonKey(name: 'limit') required int limit,
    @Json<PERSON>ey(name: 'offset') int? offset,
  }) = _TeamRequest;

  factory TeamRequest.fromJson(Map<String, dynamic> json) =>
      _$TeamRequestFromJ<PERSON>(json);
}

/// 团队用户领域模型
@freezed
class TeamUserResponse with _$TeamUserResponse {
  const factory TeamUserResponse({
    required int id,
    required String username,
    required String email,
    @JsonKey(name: 'is_two_factor_enabled') required bool isTwoFactorEnabled,
    @JsonKey(name: 'is_active') required bool isActive,
    @JsonKey(name: 'role_name') required String roleName,
    @JsonKey(name: 'is_owner') required bool isOwner,
  }) = _TeamUserResponse;

  factory TeamUserResponse.fromJson(Map<String, dynamic> json) =>
      _$TeamUserResponseFromJson(json);
}

/// 团队用户请求领域模型
@freezed
class AddTeamUserRequest with _$AddTeamUserRequest {
  const factory AddTeamUserRequest({
    @JsonKey(name: 'is_active', includeIfNull: false) bool? isActive,
    @JsonKey(name: 'password') required String password,
    @JsonKey(name: 'role_id') required int roleId,
    @JsonKey(name: 'user_name') required String userName,
  }) = _AddTeamUserRequest;

  factory AddTeamUserRequest.fromJson(Map<String, dynamic> json) =>
      _$AddTeamUserRequestFromJson(json);
}

/// 更新团队用户请求领域模型
@freezed
class UpdateTeamUserRequest with _$UpdateTeamUserRequest {
  const factory UpdateTeamUserRequest({
    @JsonKey(name: 'id') required int id,
    @JsonKey(name: 'is_active', includeIfNull: false) bool? isActive,
    @JsonKey(name: 'password', includeIfNull: false) String? password,
    @JsonKey(name: 'role_id', includeIfNull: false) int? roleId,
    @JsonKey(name: 'user_name', includeIfNull: false) String? userName,
  }) = _UpdateTeamUserRequest;

  factory UpdateTeamUserRequest.fromJson(Map<String, dynamic> json) =>
      _$UpdateTeamUserRequestFromJson(json);
}

/// 登录日志请求参数模型
@freezed
class LoginLogRequest with _$LoginLogRequest {
  const factory LoginLogRequest({
    @JsonKey(name: 'user_id') int? userId,
    @Default(10) int? limit,
    @Default(0) int? offset,
    @JsonKey(name: 'start_time') Object? startTime,
    @JsonKey(name: 'end_time') Object? endTime,
  }) = _LoginLogRequest;

  factory LoginLogRequest.fromJson(Map<String, dynamic> json) =>
      _$LoginLogRequestFromJson(json);
}

/// 登录日志响应模型
@freezed
class LoginLogModel with _$LoginLogModel {
  const factory LoginLogModel({
    required List<LoginLogItem> logs,
    required int total,
  }) = _LoginLogModel;

  factory LoginLogModel.fromJson(Map<String, dynamic> json) =>
      _$LoginLogModelFromJson(json);
}

/// 登录日志项模型
@freezed
class LoginLogItem with _$LoginLogItem {
  const factory LoginLogItem({
    required int id,
    @JsonKey(name: 'created_at') required String createdAt,
    @JsonKey(name: 'ip_location') required String ipLocation,
    @JsonKey(name: 'login_ip') required String loginIp, // IP地址的整型表示
    @JsonKey(name: 'user_name') required String userName,
  }) = _LoginLogItem;

  factory LoginLogItem.fromJson(Map<String, dynamic> json) =>
      _$LoginLogItemFromJson(json);
}

/// 操作日志请求模型
@freezed
class OperationLogRequest with _$OperationLogRequest {
  const factory OperationLogRequest({
    @JsonKey(name: 'user_id') int? userId, // 用户ID
    @JsonKey(name: 'action') int? action, // 操作类型筛选
    @JsonKey(name: 'category') int? category, // 操作类型筛选
    @JsonKey(name: 'target') String? target, // 操作目标筛选, 例如user_management
    @JsonKey(name: 'limit') int? limit, // 每页数量，默认100，最大500
    @JsonKey(name: 'offset') int? offset, // 偏移量，默认0
    @JsonKey(name: 'start_time') String? startTime, // 开始时间，RFC3339格式
    @JsonKey(name: 'end_time') String? endTime, // 结束时间，RFC3339格式
  }) = _OperationLogRequest;

  factory OperationLogRequest.fromJson(Map<String, dynamic> json) =>
      _$OperationLogRequestFromJson(json);
}

/// 操作日志响应模型
@freezed
class OperationLogModel with _$OperationLogModel {
  const factory OperationLogModel({
    required List<OperationLogItem> logs,
    required int total,
  }) = _OperationLogModel;

  factory OperationLogModel.fromJson(Map<String, dynamic> json) =>
      _$OperationLogModelFromJson(json);
}

@freezed
class OperationLogItem with _$OperationLogItem {
  const factory OperationLogItem({
    required int id,
    @JsonKey(name: 'user_id') required int userId,
    @JsonKey(name: 'team_id') required int teamId,
    @JsonKey(name: 'action') required int action,
    @JsonKey(name: 'target') required String target,
    @JsonKey(name: 'category') required int category,
    @JsonKey(name: 'created_at') required String createdAt,
  }) = _OperationLogItem;

  factory OperationLogItem.fromJson(Map<String, dynamic> json) =>
      _$OperationLogItemFromJson(json);

}
