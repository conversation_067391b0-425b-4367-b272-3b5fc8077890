// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'auth_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

LoginRequest _$LoginRequestFromJson(Map<String, dynamic> json) {
  return _LoginRequest.fromJson(json);
}

/// @nodoc
mixin _$LoginRequest {
  String get identifier => throw _privateConstructorUsedError; // 邮箱或用户名
  String get password => throw _privateConstructorUsedError;

  /// Serializes this LoginRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of LoginRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $LoginRequestCopyWith<LoginRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LoginRequestCopyWith<$Res> {
  factory $LoginRequestCopyWith(
          LoginRequest value, $Res Function(LoginRequest) then) =
      _$LoginRequestCopyWithImpl<$Res, LoginRequest>;
  @useResult
  $Res call({String identifier, String password});
}

/// @nodoc
class _$LoginRequestCopyWithImpl<$Res, $Val extends LoginRequest>
    implements $LoginRequestCopyWith<$Res> {
  _$LoginRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LoginRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? identifier = null,
    Object? password = null,
  }) {
    return _then(_value.copyWith(
      identifier: null == identifier
          ? _value.identifier
          : identifier // ignore: cast_nullable_to_non_nullable
              as String,
      password: null == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LoginRequestImplCopyWith<$Res>
    implements $LoginRequestCopyWith<$Res> {
  factory _$$LoginRequestImplCopyWith(
          _$LoginRequestImpl value, $Res Function(_$LoginRequestImpl) then) =
      __$$LoginRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String identifier, String password});
}

/// @nodoc
class __$$LoginRequestImplCopyWithImpl<$Res>
    extends _$LoginRequestCopyWithImpl<$Res, _$LoginRequestImpl>
    implements _$$LoginRequestImplCopyWith<$Res> {
  __$$LoginRequestImplCopyWithImpl(
      _$LoginRequestImpl _value, $Res Function(_$LoginRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of LoginRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? identifier = null,
    Object? password = null,
  }) {
    return _then(_$LoginRequestImpl(
      identifier: null == identifier
          ? _value.identifier
          : identifier // ignore: cast_nullable_to_non_nullable
              as String,
      password: null == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$LoginRequestImpl implements _LoginRequest {
  const _$LoginRequestImpl({required this.identifier, required this.password});

  factory _$LoginRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$LoginRequestImplFromJson(json);

  @override
  final String identifier;
// 邮箱或用户名
  @override
  final String password;

  @override
  String toString() {
    return 'LoginRequest(identifier: $identifier, password: $password)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoginRequestImpl &&
            (identical(other.identifier, identifier) ||
                other.identifier == identifier) &&
            (identical(other.password, password) ||
                other.password == password));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, identifier, password);

  /// Create a copy of LoginRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoginRequestImplCopyWith<_$LoginRequestImpl> get copyWith =>
      __$$LoginRequestImplCopyWithImpl<_$LoginRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LoginRequestImplToJson(
      this,
    );
  }
}

abstract class _LoginRequest implements LoginRequest {
  const factory _LoginRequest(
      {required final String identifier,
      required final String password}) = _$LoginRequestImpl;

  factory _LoginRequest.fromJson(Map<String, dynamic> json) =
      _$LoginRequestImpl.fromJson;

  @override
  String get identifier; // 邮箱或用户名
  @override
  String get password;

  /// Create a copy of LoginRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoginRequestImplCopyWith<_$LoginRequestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

RegisterRequest _$RegisterRequestFromJson(Map<String, dynamic> json) {
  return _RegisterRequest.fromJson(json);
}

/// @nodoc
mixin _$RegisterRequest {
  @JsonKey(name: 'user_name')
  String get username => throw _privateConstructorUsedError; // 必需，最大20字符
  String get password => throw _privateConstructorUsedError; // 必需字段，最大60字符
  String? get email => throw _privateConstructorUsedError; // 可选，有效邮箱，最大100字符
  String? get telephone => throw _privateConstructorUsedError; // 可选，最大15字符
  @JsonKey(name: 'invite_code')
  String? get inviteCode => throw _privateConstructorUsedError; // 可选，最大15字符
  @JsonKey(name: 'email_code')
  String? get emailCode => throw _privateConstructorUsedError;

  /// Serializes this RegisterRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of RegisterRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RegisterRequestCopyWith<RegisterRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RegisterRequestCopyWith<$Res> {
  factory $RegisterRequestCopyWith(
          RegisterRequest value, $Res Function(RegisterRequest) then) =
      _$RegisterRequestCopyWithImpl<$Res, RegisterRequest>;
  @useResult
  $Res call(
      {@JsonKey(name: 'user_name') String username,
      String password,
      String? email,
      String? telephone,
      @JsonKey(name: 'invite_code') String? inviteCode,
      @JsonKey(name: 'email_code') String? emailCode});
}

/// @nodoc
class _$RegisterRequestCopyWithImpl<$Res, $Val extends RegisterRequest>
    implements $RegisterRequestCopyWith<$Res> {
  _$RegisterRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RegisterRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? username = null,
    Object? password = null,
    Object? email = freezed,
    Object? telephone = freezed,
    Object? inviteCode = freezed,
    Object? emailCode = freezed,
  }) {
    return _then(_value.copyWith(
      username: null == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String,
      password: null == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      telephone: freezed == telephone
          ? _value.telephone
          : telephone // ignore: cast_nullable_to_non_nullable
              as String?,
      inviteCode: freezed == inviteCode
          ? _value.inviteCode
          : inviteCode // ignore: cast_nullable_to_non_nullable
              as String?,
      emailCode: freezed == emailCode
          ? _value.emailCode
          : emailCode // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RegisterRequestImplCopyWith<$Res>
    implements $RegisterRequestCopyWith<$Res> {
  factory _$$RegisterRequestImplCopyWith(_$RegisterRequestImpl value,
          $Res Function(_$RegisterRequestImpl) then) =
      __$$RegisterRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'user_name') String username,
      String password,
      String? email,
      String? telephone,
      @JsonKey(name: 'invite_code') String? inviteCode,
      @JsonKey(name: 'email_code') String? emailCode});
}

/// @nodoc
class __$$RegisterRequestImplCopyWithImpl<$Res>
    extends _$RegisterRequestCopyWithImpl<$Res, _$RegisterRequestImpl>
    implements _$$RegisterRequestImplCopyWith<$Res> {
  __$$RegisterRequestImplCopyWithImpl(
      _$RegisterRequestImpl _value, $Res Function(_$RegisterRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of RegisterRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? username = null,
    Object? password = null,
    Object? email = freezed,
    Object? telephone = freezed,
    Object? inviteCode = freezed,
    Object? emailCode = freezed,
  }) {
    return _then(_$RegisterRequestImpl(
      username: null == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String,
      password: null == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      telephone: freezed == telephone
          ? _value.telephone
          : telephone // ignore: cast_nullable_to_non_nullable
              as String?,
      inviteCode: freezed == inviteCode
          ? _value.inviteCode
          : inviteCode // ignore: cast_nullable_to_non_nullable
              as String?,
      emailCode: freezed == emailCode
          ? _value.emailCode
          : emailCode // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RegisterRequestImpl implements _RegisterRequest {
  const _$RegisterRequestImpl(
      {@JsonKey(name: 'user_name') required this.username,
      required this.password,
      this.email,
      this.telephone,
      @JsonKey(name: 'invite_code') this.inviteCode,
      @JsonKey(name: 'email_code') this.emailCode});

  factory _$RegisterRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$RegisterRequestImplFromJson(json);

  @override
  @JsonKey(name: 'user_name')
  final String username;
// 必需，最大20字符
  @override
  final String password;
// 必需字段，最大60字符
  @override
  final String? email;
// 可选，有效邮箱，最大100字符
  @override
  final String? telephone;
// 可选，最大15字符
  @override
  @JsonKey(name: 'invite_code')
  final String? inviteCode;
// 可选，最大15字符
  @override
  @JsonKey(name: 'email_code')
  final String? emailCode;

  @override
  String toString() {
    return 'RegisterRequest(username: $username, password: $password, email: $email, telephone: $telephone, inviteCode: $inviteCode, emailCode: $emailCode)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RegisterRequestImpl &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.password, password) ||
                other.password == password) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.telephone, telephone) ||
                other.telephone == telephone) &&
            (identical(other.inviteCode, inviteCode) ||
                other.inviteCode == inviteCode) &&
            (identical(other.emailCode, emailCode) ||
                other.emailCode == emailCode));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, username, password, email, telephone, inviteCode, emailCode);

  /// Create a copy of RegisterRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RegisterRequestImplCopyWith<_$RegisterRequestImpl> get copyWith =>
      __$$RegisterRequestImplCopyWithImpl<_$RegisterRequestImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RegisterRequestImplToJson(
      this,
    );
  }
}

abstract class _RegisterRequest implements RegisterRequest {
  const factory _RegisterRequest(
          {@JsonKey(name: 'user_name') required final String username,
          required final String password,
          final String? email,
          final String? telephone,
          @JsonKey(name: 'invite_code') final String? inviteCode,
          @JsonKey(name: 'email_code') final String? emailCode}) =
      _$RegisterRequestImpl;

  factory _RegisterRequest.fromJson(Map<String, dynamic> json) =
      _$RegisterRequestImpl.fromJson;

  @override
  @JsonKey(name: 'user_name')
  String get username; // 必需，最大20字符
  @override
  String get password; // 必需字段，最大60字符
  @override
  String? get email; // 可选，有效邮箱，最大100字符
  @override
  String? get telephone; // 可选，最大15字符
  @override
  @JsonKey(name: 'invite_code')
  String? get inviteCode; // 可选，最大15字符
  @override
  @JsonKey(name: 'email_code')
  String? get emailCode;

  /// Create a copy of RegisterRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RegisterRequestImplCopyWith<_$RegisterRequestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AuthResponse _$AuthResponseFromJson(Map<String, dynamic> json) {
  return _AuthResponse.fromJson(json);
}

/// @nodoc
mixin _$AuthResponse {
  String get message => throw _privateConstructorUsedError;
  String? get token => throw _privateConstructorUsedError;
  User? get user => throw _privateConstructorUsedError;

  /// Serializes this AuthResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AuthResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AuthResponseCopyWith<AuthResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AuthResponseCopyWith<$Res> {
  factory $AuthResponseCopyWith(
          AuthResponse value, $Res Function(AuthResponse) then) =
      _$AuthResponseCopyWithImpl<$Res, AuthResponse>;
  @useResult
  $Res call({String message, String? token, User? user});

  $UserCopyWith<$Res>? get user;
}

/// @nodoc
class _$AuthResponseCopyWithImpl<$Res, $Val extends AuthResponse>
    implements $AuthResponseCopyWith<$Res> {
  _$AuthResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AuthResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? token = freezed,
    Object? user = freezed,
  }) {
    return _then(_value.copyWith(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      token: freezed == token
          ? _value.token
          : token // ignore: cast_nullable_to_non_nullable
              as String?,
      user: freezed == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as User?,
    ) as $Val);
  }

  /// Create a copy of AuthResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserCopyWith<$Res>? get user {
    if (_value.user == null) {
      return null;
    }

    return $UserCopyWith<$Res>(_value.user!, (value) {
      return _then(_value.copyWith(user: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$AuthResponseImplCopyWith<$Res>
    implements $AuthResponseCopyWith<$Res> {
  factory _$$AuthResponseImplCopyWith(
          _$AuthResponseImpl value, $Res Function(_$AuthResponseImpl) then) =
      __$$AuthResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String message, String? token, User? user});

  @override
  $UserCopyWith<$Res>? get user;
}

/// @nodoc
class __$$AuthResponseImplCopyWithImpl<$Res>
    extends _$AuthResponseCopyWithImpl<$Res, _$AuthResponseImpl>
    implements _$$AuthResponseImplCopyWith<$Res> {
  __$$AuthResponseImplCopyWithImpl(
      _$AuthResponseImpl _value, $Res Function(_$AuthResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? token = freezed,
    Object? user = freezed,
  }) {
    return _then(_$AuthResponseImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      token: freezed == token
          ? _value.token
          : token // ignore: cast_nullable_to_non_nullable
              as String?,
      user: freezed == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as User?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AuthResponseImpl implements _AuthResponse {
  const _$AuthResponseImpl({required this.message, this.token, this.user});

  factory _$AuthResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$AuthResponseImplFromJson(json);

  @override
  final String message;
  @override
  final String? token;
  @override
  final User? user;

  @override
  String toString() {
    return 'AuthResponse(message: $message, token: $token, user: $user)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AuthResponseImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.token, token) || other.token == token) &&
            (identical(other.user, user) || other.user == user));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, message, token, user);

  /// Create a copy of AuthResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AuthResponseImplCopyWith<_$AuthResponseImpl> get copyWith =>
      __$$AuthResponseImplCopyWithImpl<_$AuthResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AuthResponseImplToJson(
      this,
    );
  }
}

abstract class _AuthResponse implements AuthResponse {
  const factory _AuthResponse(
      {required final String message,
      final String? token,
      final User? user}) = _$AuthResponseImpl;

  factory _AuthResponse.fromJson(Map<String, dynamic> json) =
      _$AuthResponseImpl.fromJson;

  @override
  String get message;
  @override
  String? get token;
  @override
  User? get user;

  /// Create a copy of AuthResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AuthResponseImplCopyWith<_$AuthResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

User _$UserFromJson(Map<String, dynamic> json) {
  return _User.fromJson(json);
}

/// @nodoc
mixin _$User {
  int get id => throw _privateConstructorUsedError;
  String get username => throw _privateConstructorUsedError;
  String? get email => throw _privateConstructorUsedError;
  String? get telephone => throw _privateConstructorUsedError;
  String? get avatar => throw _privateConstructorUsedError;
  @JsonKey(name: 'created_at')
  String? get createdAt => throw _privateConstructorUsedError;
  @JsonKey(name: 'updated_at')
  String? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this User to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserCopyWith<User> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserCopyWith<$Res> {
  factory $UserCopyWith(User value, $Res Function(User) then) =
      _$UserCopyWithImpl<$Res, User>;
  @useResult
  $Res call(
      {int id,
      String username,
      String? email,
      String? telephone,
      String? avatar,
      @JsonKey(name: 'created_at') String? createdAt,
      @JsonKey(name: 'updated_at') String? updatedAt});
}

/// @nodoc
class _$UserCopyWithImpl<$Res, $Val extends User>
    implements $UserCopyWith<$Res> {
  _$UserCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? username = null,
    Object? email = freezed,
    Object? telephone = freezed,
    Object? avatar = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      username: null == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      telephone: freezed == telephone
          ? _value.telephone
          : telephone // ignore: cast_nullable_to_non_nullable
              as String?,
      avatar: freezed == avatar
          ? _value.avatar
          : avatar // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as String?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UserImplCopyWith<$Res> implements $UserCopyWith<$Res> {
  factory _$$UserImplCopyWith(
          _$UserImpl value, $Res Function(_$UserImpl) then) =
      __$$UserImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      String username,
      String? email,
      String? telephone,
      String? avatar,
      @JsonKey(name: 'created_at') String? createdAt,
      @JsonKey(name: 'updated_at') String? updatedAt});
}

/// @nodoc
class __$$UserImplCopyWithImpl<$Res>
    extends _$UserCopyWithImpl<$Res, _$UserImpl>
    implements _$$UserImplCopyWith<$Res> {
  __$$UserImplCopyWithImpl(_$UserImpl _value, $Res Function(_$UserImpl) _then)
      : super(_value, _then);

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? username = null,
    Object? email = freezed,
    Object? telephone = freezed,
    Object? avatar = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_$UserImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      username: null == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      telephone: freezed == telephone
          ? _value.telephone
          : telephone // ignore: cast_nullable_to_non_nullable
              as String?,
      avatar: freezed == avatar
          ? _value.avatar
          : avatar // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as String?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserImpl implements _User {
  const _$UserImpl(
      {required this.id,
      required this.username,
      this.email,
      this.telephone,
      this.avatar,
      @JsonKey(name: 'created_at') this.createdAt,
      @JsonKey(name: 'updated_at') this.updatedAt});

  factory _$UserImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserImplFromJson(json);

  @override
  final int id;
  @override
  final String username;
  @override
  final String? email;
  @override
  final String? telephone;
  @override
  final String? avatar;
  @override
  @JsonKey(name: 'created_at')
  final String? createdAt;
  @override
  @JsonKey(name: 'updated_at')
  final String? updatedAt;

  @override
  String toString() {
    return 'User(id: $id, username: $username, email: $email, telephone: $telephone, avatar: $avatar, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.telephone, telephone) ||
                other.telephone == telephone) &&
            (identical(other.avatar, avatar) || other.avatar == avatar) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, username, email, telephone,
      avatar, createdAt, updatedAt);

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserImplCopyWith<_$UserImpl> get copyWith =>
      __$$UserImplCopyWithImpl<_$UserImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserImplToJson(
      this,
    );
  }
}

abstract class _User implements User {
  const factory _User(
      {required final int id,
      required final String username,
      final String? email,
      final String? telephone,
      final String? avatar,
      @JsonKey(name: 'created_at') final String? createdAt,
      @JsonKey(name: 'updated_at') final String? updatedAt}) = _$UserImpl;

  factory _User.fromJson(Map<String, dynamic> json) = _$UserImpl.fromJson;

  @override
  int get id;
  @override
  String get username;
  @override
  String? get email;
  @override
  String? get telephone;
  @override
  String? get avatar;
  @override
  @JsonKey(name: 'created_at')
  String? get createdAt;
  @override
  @JsonKey(name: 'updated_at')
  String? get updatedAt;

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserImplCopyWith<_$UserImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CaptchaResponse _$CaptchaResponseFromJson(Map<String, dynamic> json) {
  return _CaptchaResponse.fromJson(json);
}

/// @nodoc
mixin _$CaptchaResponse {
  @JsonKey(name: 'captcha_image')
  String get captchaImage => throw _privateConstructorUsedError;
  String get seed => throw _privateConstructorUsedError;
  String get target => throw _privateConstructorUsedError;

  /// Serializes this CaptchaResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CaptchaResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CaptchaResponseCopyWith<CaptchaResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CaptchaResponseCopyWith<$Res> {
  factory $CaptchaResponseCopyWith(
          CaptchaResponse value, $Res Function(CaptchaResponse) then) =
      _$CaptchaResponseCopyWithImpl<$Res, CaptchaResponse>;
  @useResult
  $Res call(
      {@JsonKey(name: 'captcha_image') String captchaImage,
      String seed,
      String target});
}

/// @nodoc
class _$CaptchaResponseCopyWithImpl<$Res, $Val extends CaptchaResponse>
    implements $CaptchaResponseCopyWith<$Res> {
  _$CaptchaResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CaptchaResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? captchaImage = null,
    Object? seed = null,
    Object? target = null,
  }) {
    return _then(_value.copyWith(
      captchaImage: null == captchaImage
          ? _value.captchaImage
          : captchaImage // ignore: cast_nullable_to_non_nullable
              as String,
      seed: null == seed
          ? _value.seed
          : seed // ignore: cast_nullable_to_non_nullable
              as String,
      target: null == target
          ? _value.target
          : target // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CaptchaResponseImplCopyWith<$Res>
    implements $CaptchaResponseCopyWith<$Res> {
  factory _$$CaptchaResponseImplCopyWith(_$CaptchaResponseImpl value,
          $Res Function(_$CaptchaResponseImpl) then) =
      __$$CaptchaResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'captcha_image') String captchaImage,
      String seed,
      String target});
}

/// @nodoc
class __$$CaptchaResponseImplCopyWithImpl<$Res>
    extends _$CaptchaResponseCopyWithImpl<$Res, _$CaptchaResponseImpl>
    implements _$$CaptchaResponseImplCopyWith<$Res> {
  __$$CaptchaResponseImplCopyWithImpl(
      _$CaptchaResponseImpl _value, $Res Function(_$CaptchaResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of CaptchaResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? captchaImage = null,
    Object? seed = null,
    Object? target = null,
  }) {
    return _then(_$CaptchaResponseImpl(
      captchaImage: null == captchaImage
          ? _value.captchaImage
          : captchaImage // ignore: cast_nullable_to_non_nullable
              as String,
      seed: null == seed
          ? _value.seed
          : seed // ignore: cast_nullable_to_non_nullable
              as String,
      target: null == target
          ? _value.target
          : target // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CaptchaResponseImpl implements _CaptchaResponse {
  const _$CaptchaResponseImpl(
      {@JsonKey(name: 'captcha_image') required this.captchaImage,
      required this.seed,
      required this.target});

  factory _$CaptchaResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$CaptchaResponseImplFromJson(json);

  @override
  @JsonKey(name: 'captcha_image')
  final String captchaImage;
  @override
  final String seed;
  @override
  final String target;

  @override
  String toString() {
    return 'CaptchaResponse(captchaImage: $captchaImage, seed: $seed, target: $target)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CaptchaResponseImpl &&
            (identical(other.captchaImage, captchaImage) ||
                other.captchaImage == captchaImage) &&
            (identical(other.seed, seed) || other.seed == seed) &&
            (identical(other.target, target) || other.target == target));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, captchaImage, seed, target);

  /// Create a copy of CaptchaResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CaptchaResponseImplCopyWith<_$CaptchaResponseImpl> get copyWith =>
      __$$CaptchaResponseImplCopyWithImpl<_$CaptchaResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CaptchaResponseImplToJson(
      this,
    );
  }
}

abstract class _CaptchaResponse implements CaptchaResponse {
  const factory _CaptchaResponse(
      {@JsonKey(name: 'captcha_image') required final String captchaImage,
      required final String seed,
      required final String target}) = _$CaptchaResponseImpl;

  factory _CaptchaResponse.fromJson(Map<String, dynamic> json) =
      _$CaptchaResponseImpl.fromJson;

  @override
  @JsonKey(name: 'captcha_image')
  String get captchaImage;
  @override
  String get seed;
  @override
  String get target;

  /// Create a copy of CaptchaResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CaptchaResponseImplCopyWith<_$CaptchaResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

VerifyCaptchaRequest _$VerifyCaptchaRequestFromJson(Map<String, dynamic> json) {
  return _VerifyCaptchaRequest.fromJson(json);
}

/// @nodoc
mixin _$VerifyCaptchaRequest {
  String get answer => throw _privateConstructorUsedError;
  String get nonce => throw _privateConstructorUsedError;
  String get seed => throw _privateConstructorUsedError;
  String get target => throw _privateConstructorUsedError;

  /// Serializes this VerifyCaptchaRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of VerifyCaptchaRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $VerifyCaptchaRequestCopyWith<VerifyCaptchaRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VerifyCaptchaRequestCopyWith<$Res> {
  factory $VerifyCaptchaRequestCopyWith(VerifyCaptchaRequest value,
          $Res Function(VerifyCaptchaRequest) then) =
      _$VerifyCaptchaRequestCopyWithImpl<$Res, VerifyCaptchaRequest>;
  @useResult
  $Res call({String answer, String nonce, String seed, String target});
}

/// @nodoc
class _$VerifyCaptchaRequestCopyWithImpl<$Res,
        $Val extends VerifyCaptchaRequest>
    implements $VerifyCaptchaRequestCopyWith<$Res> {
  _$VerifyCaptchaRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of VerifyCaptchaRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? answer = null,
    Object? nonce = null,
    Object? seed = null,
    Object? target = null,
  }) {
    return _then(_value.copyWith(
      answer: null == answer
          ? _value.answer
          : answer // ignore: cast_nullable_to_non_nullable
              as String,
      nonce: null == nonce
          ? _value.nonce
          : nonce // ignore: cast_nullable_to_non_nullable
              as String,
      seed: null == seed
          ? _value.seed
          : seed // ignore: cast_nullable_to_non_nullable
              as String,
      target: null == target
          ? _value.target
          : target // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$VerifyCaptchaRequestImplCopyWith<$Res>
    implements $VerifyCaptchaRequestCopyWith<$Res> {
  factory _$$VerifyCaptchaRequestImplCopyWith(_$VerifyCaptchaRequestImpl value,
          $Res Function(_$VerifyCaptchaRequestImpl) then) =
      __$$VerifyCaptchaRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String answer, String nonce, String seed, String target});
}

/// @nodoc
class __$$VerifyCaptchaRequestImplCopyWithImpl<$Res>
    extends _$VerifyCaptchaRequestCopyWithImpl<$Res, _$VerifyCaptchaRequestImpl>
    implements _$$VerifyCaptchaRequestImplCopyWith<$Res> {
  __$$VerifyCaptchaRequestImplCopyWithImpl(_$VerifyCaptchaRequestImpl _value,
      $Res Function(_$VerifyCaptchaRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of VerifyCaptchaRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? answer = null,
    Object? nonce = null,
    Object? seed = null,
    Object? target = null,
  }) {
    return _then(_$VerifyCaptchaRequestImpl(
      answer: null == answer
          ? _value.answer
          : answer // ignore: cast_nullable_to_non_nullable
              as String,
      nonce: null == nonce
          ? _value.nonce
          : nonce // ignore: cast_nullable_to_non_nullable
              as String,
      seed: null == seed
          ? _value.seed
          : seed // ignore: cast_nullable_to_non_nullable
              as String,
      target: null == target
          ? _value.target
          : target // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$VerifyCaptchaRequestImpl implements _VerifyCaptchaRequest {
  const _$VerifyCaptchaRequestImpl(
      {required this.answer,
      required this.nonce,
      required this.seed,
      required this.target});

  factory _$VerifyCaptchaRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$VerifyCaptchaRequestImplFromJson(json);

  @override
  final String answer;
  @override
  final String nonce;
  @override
  final String seed;
  @override
  final String target;

  @override
  String toString() {
    return 'VerifyCaptchaRequest(answer: $answer, nonce: $nonce, seed: $seed, target: $target)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VerifyCaptchaRequestImpl &&
            (identical(other.answer, answer) || other.answer == answer) &&
            (identical(other.nonce, nonce) || other.nonce == nonce) &&
            (identical(other.seed, seed) || other.seed == seed) &&
            (identical(other.target, target) || other.target == target));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, answer, nonce, seed, target);

  /// Create a copy of VerifyCaptchaRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VerifyCaptchaRequestImplCopyWith<_$VerifyCaptchaRequestImpl>
      get copyWith =>
          __$$VerifyCaptchaRequestImplCopyWithImpl<_$VerifyCaptchaRequestImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VerifyCaptchaRequestImplToJson(
      this,
    );
  }
}

abstract class _VerifyCaptchaRequest implements VerifyCaptchaRequest {
  const factory _VerifyCaptchaRequest(
      {required final String answer,
      required final String nonce,
      required final String seed,
      required final String target}) = _$VerifyCaptchaRequestImpl;

  factory _VerifyCaptchaRequest.fromJson(Map<String, dynamic> json) =
      _$VerifyCaptchaRequestImpl.fromJson;

  @override
  String get answer;
  @override
  String get nonce;
  @override
  String get seed;
  @override
  String get target;

  /// Create a copy of VerifyCaptchaRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VerifyCaptchaRequestImplCopyWith<_$VerifyCaptchaRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}

VerifyCaptchaResponse _$VerifyCaptchaResponseFromJson(
    Map<String, dynamic> json) {
  return _VerifyCaptchaResponse.fromJson(json);
}

/// @nodoc
mixin _$VerifyCaptchaResponse {
  bool get valid => throw _privateConstructorUsedError;

  /// Serializes this VerifyCaptchaResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of VerifyCaptchaResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $VerifyCaptchaResponseCopyWith<VerifyCaptchaResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VerifyCaptchaResponseCopyWith<$Res> {
  factory $VerifyCaptchaResponseCopyWith(VerifyCaptchaResponse value,
          $Res Function(VerifyCaptchaResponse) then) =
      _$VerifyCaptchaResponseCopyWithImpl<$Res, VerifyCaptchaResponse>;
  @useResult
  $Res call({bool valid});
}

/// @nodoc
class _$VerifyCaptchaResponseCopyWithImpl<$Res,
        $Val extends VerifyCaptchaResponse>
    implements $VerifyCaptchaResponseCopyWith<$Res> {
  _$VerifyCaptchaResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of VerifyCaptchaResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? valid = null,
  }) {
    return _then(_value.copyWith(
      valid: null == valid
          ? _value.valid
          : valid // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$VerifyCaptchaResponseImplCopyWith<$Res>
    implements $VerifyCaptchaResponseCopyWith<$Res> {
  factory _$$VerifyCaptchaResponseImplCopyWith(
          _$VerifyCaptchaResponseImpl value,
          $Res Function(_$VerifyCaptchaResponseImpl) then) =
      __$$VerifyCaptchaResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool valid});
}

/// @nodoc
class __$$VerifyCaptchaResponseImplCopyWithImpl<$Res>
    extends _$VerifyCaptchaResponseCopyWithImpl<$Res,
        _$VerifyCaptchaResponseImpl>
    implements _$$VerifyCaptchaResponseImplCopyWith<$Res> {
  __$$VerifyCaptchaResponseImplCopyWithImpl(_$VerifyCaptchaResponseImpl _value,
      $Res Function(_$VerifyCaptchaResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of VerifyCaptchaResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? valid = null,
  }) {
    return _then(_$VerifyCaptchaResponseImpl(
      valid: null == valid
          ? _value.valid
          : valid // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$VerifyCaptchaResponseImpl implements _VerifyCaptchaResponse {
  const _$VerifyCaptchaResponseImpl({required this.valid});

  factory _$VerifyCaptchaResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$VerifyCaptchaResponseImplFromJson(json);

  @override
  final bool valid;

  @override
  String toString() {
    return 'VerifyCaptchaResponse(valid: $valid)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VerifyCaptchaResponseImpl &&
            (identical(other.valid, valid) || other.valid == valid));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, valid);

  /// Create a copy of VerifyCaptchaResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VerifyCaptchaResponseImplCopyWith<_$VerifyCaptchaResponseImpl>
      get copyWith => __$$VerifyCaptchaResponseImplCopyWithImpl<
          _$VerifyCaptchaResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VerifyCaptchaResponseImplToJson(
      this,
    );
  }
}

abstract class _VerifyCaptchaResponse implements VerifyCaptchaResponse {
  const factory _VerifyCaptchaResponse({required final bool valid}) =
      _$VerifyCaptchaResponseImpl;

  factory _VerifyCaptchaResponse.fromJson(Map<String, dynamic> json) =
      _$VerifyCaptchaResponseImpl.fromJson;

  @override
  bool get valid;

  /// Create a copy of VerifyCaptchaResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VerifyCaptchaResponseImplCopyWith<_$VerifyCaptchaResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

SendRegisterEmailRequest _$SendRegisterEmailRequestFromJson(
    Map<String, dynamic> json) {
  return _SendRegisterEmailRequest.fromJson(json);
}

/// @nodoc
mixin _$SendRegisterEmailRequest {
  String get answer => throw _privateConstructorUsedError;
  String get email => throw _privateConstructorUsedError;
  String get nonce => throw _privateConstructorUsedError;
  String get seed => throw _privateConstructorUsedError;
  String get target => throw _privateConstructorUsedError;

  /// Serializes this SendRegisterEmailRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SendRegisterEmailRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SendRegisterEmailRequestCopyWith<SendRegisterEmailRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SendRegisterEmailRequestCopyWith<$Res> {
  factory $SendRegisterEmailRequestCopyWith(SendRegisterEmailRequest value,
          $Res Function(SendRegisterEmailRequest) then) =
      _$SendRegisterEmailRequestCopyWithImpl<$Res, SendRegisterEmailRequest>;
  @useResult
  $Res call(
      {String answer, String email, String nonce, String seed, String target});
}

/// @nodoc
class _$SendRegisterEmailRequestCopyWithImpl<$Res,
        $Val extends SendRegisterEmailRequest>
    implements $SendRegisterEmailRequestCopyWith<$Res> {
  _$SendRegisterEmailRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SendRegisterEmailRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? answer = null,
    Object? email = null,
    Object? nonce = null,
    Object? seed = null,
    Object? target = null,
  }) {
    return _then(_value.copyWith(
      answer: null == answer
          ? _value.answer
          : answer // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      nonce: null == nonce
          ? _value.nonce
          : nonce // ignore: cast_nullable_to_non_nullable
              as String,
      seed: null == seed
          ? _value.seed
          : seed // ignore: cast_nullable_to_non_nullable
              as String,
      target: null == target
          ? _value.target
          : target // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SendRegisterEmailRequestImplCopyWith<$Res>
    implements $SendRegisterEmailRequestCopyWith<$Res> {
  factory _$$SendRegisterEmailRequestImplCopyWith(
          _$SendRegisterEmailRequestImpl value,
          $Res Function(_$SendRegisterEmailRequestImpl) then) =
      __$$SendRegisterEmailRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String answer, String email, String nonce, String seed, String target});
}

/// @nodoc
class __$$SendRegisterEmailRequestImplCopyWithImpl<$Res>
    extends _$SendRegisterEmailRequestCopyWithImpl<$Res,
        _$SendRegisterEmailRequestImpl>
    implements _$$SendRegisterEmailRequestImplCopyWith<$Res> {
  __$$SendRegisterEmailRequestImplCopyWithImpl(
      _$SendRegisterEmailRequestImpl _value,
      $Res Function(_$SendRegisterEmailRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of SendRegisterEmailRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? answer = null,
    Object? email = null,
    Object? nonce = null,
    Object? seed = null,
    Object? target = null,
  }) {
    return _then(_$SendRegisterEmailRequestImpl(
      answer: null == answer
          ? _value.answer
          : answer // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      nonce: null == nonce
          ? _value.nonce
          : nonce // ignore: cast_nullable_to_non_nullable
              as String,
      seed: null == seed
          ? _value.seed
          : seed // ignore: cast_nullable_to_non_nullable
              as String,
      target: null == target
          ? _value.target
          : target // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SendRegisterEmailRequestImpl implements _SendRegisterEmailRequest {
  const _$SendRegisterEmailRequestImpl(
      {required this.answer,
      required this.email,
      required this.nonce,
      required this.seed,
      required this.target});

  factory _$SendRegisterEmailRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$SendRegisterEmailRequestImplFromJson(json);

  @override
  final String answer;
  @override
  final String email;
  @override
  final String nonce;
  @override
  final String seed;
  @override
  final String target;

  @override
  String toString() {
    return 'SendRegisterEmailRequest(answer: $answer, email: $email, nonce: $nonce, seed: $seed, target: $target)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SendRegisterEmailRequestImpl &&
            (identical(other.answer, answer) || other.answer == answer) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.nonce, nonce) || other.nonce == nonce) &&
            (identical(other.seed, seed) || other.seed == seed) &&
            (identical(other.target, target) || other.target == target));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, answer, email, nonce, seed, target);

  /// Create a copy of SendRegisterEmailRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SendRegisterEmailRequestImplCopyWith<_$SendRegisterEmailRequestImpl>
      get copyWith => __$$SendRegisterEmailRequestImplCopyWithImpl<
          _$SendRegisterEmailRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SendRegisterEmailRequestImplToJson(
      this,
    );
  }
}

abstract class _SendRegisterEmailRequest implements SendRegisterEmailRequest {
  const factory _SendRegisterEmailRequest(
      {required final String answer,
      required final String email,
      required final String nonce,
      required final String seed,
      required final String target}) = _$SendRegisterEmailRequestImpl;

  factory _SendRegisterEmailRequest.fromJson(Map<String, dynamic> json) =
      _$SendRegisterEmailRequestImpl.fromJson;

  @override
  String get answer;
  @override
  String get email;
  @override
  String get nonce;
  @override
  String get seed;
  @override
  String get target;

  /// Create a copy of SendRegisterEmailRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SendRegisterEmailRequestImplCopyWith<_$SendRegisterEmailRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}

SendRegisterEmailResponse _$SendRegisterEmailResponseFromJson(
    Map<String, dynamic> json) {
  return _SendRegisterEmailResponse.fromJson(json);
}

/// @nodoc
mixin _$SendRegisterEmailResponse {
  String get message => throw _privateConstructorUsedError;
  bool get sent => throw _privateConstructorUsedError;

  /// Serializes this SendRegisterEmailResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SendRegisterEmailResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SendRegisterEmailResponseCopyWith<SendRegisterEmailResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SendRegisterEmailResponseCopyWith<$Res> {
  factory $SendRegisterEmailResponseCopyWith(SendRegisterEmailResponse value,
          $Res Function(SendRegisterEmailResponse) then) =
      _$SendRegisterEmailResponseCopyWithImpl<$Res, SendRegisterEmailResponse>;
  @useResult
  $Res call({String message, bool sent});
}

/// @nodoc
class _$SendRegisterEmailResponseCopyWithImpl<$Res,
        $Val extends SendRegisterEmailResponse>
    implements $SendRegisterEmailResponseCopyWith<$Res> {
  _$SendRegisterEmailResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SendRegisterEmailResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? sent = null,
  }) {
    return _then(_value.copyWith(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      sent: null == sent
          ? _value.sent
          : sent // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SendRegisterEmailResponseImplCopyWith<$Res>
    implements $SendRegisterEmailResponseCopyWith<$Res> {
  factory _$$SendRegisterEmailResponseImplCopyWith(
          _$SendRegisterEmailResponseImpl value,
          $Res Function(_$SendRegisterEmailResponseImpl) then) =
      __$$SendRegisterEmailResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String message, bool sent});
}

/// @nodoc
class __$$SendRegisterEmailResponseImplCopyWithImpl<$Res>
    extends _$SendRegisterEmailResponseCopyWithImpl<$Res,
        _$SendRegisterEmailResponseImpl>
    implements _$$SendRegisterEmailResponseImplCopyWith<$Res> {
  __$$SendRegisterEmailResponseImplCopyWithImpl(
      _$SendRegisterEmailResponseImpl _value,
      $Res Function(_$SendRegisterEmailResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of SendRegisterEmailResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? sent = null,
  }) {
    return _then(_$SendRegisterEmailResponseImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      sent: null == sent
          ? _value.sent
          : sent // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SendRegisterEmailResponseImpl implements _SendRegisterEmailResponse {
  const _$SendRegisterEmailResponseImpl(
      {required this.message, required this.sent});

  factory _$SendRegisterEmailResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$SendRegisterEmailResponseImplFromJson(json);

  @override
  final String message;
  @override
  final bool sent;

  @override
  String toString() {
    return 'SendRegisterEmailResponse(message: $message, sent: $sent)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SendRegisterEmailResponseImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.sent, sent) || other.sent == sent));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, message, sent);

  /// Create a copy of SendRegisterEmailResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SendRegisterEmailResponseImplCopyWith<_$SendRegisterEmailResponseImpl>
      get copyWith => __$$SendRegisterEmailResponseImplCopyWithImpl<
          _$SendRegisterEmailResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SendRegisterEmailResponseImplToJson(
      this,
    );
  }
}

abstract class _SendRegisterEmailResponse implements SendRegisterEmailResponse {
  const factory _SendRegisterEmailResponse(
      {required final String message,
      required final bool sent}) = _$SendRegisterEmailResponseImpl;

  factory _SendRegisterEmailResponse.fromJson(Map<String, dynamic> json) =
      _$SendRegisterEmailResponseImpl.fromJson;

  @override
  String get message;
  @override
  bool get sent;

  /// Create a copy of SendRegisterEmailResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SendRegisterEmailResponseImplCopyWith<_$SendRegisterEmailResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
