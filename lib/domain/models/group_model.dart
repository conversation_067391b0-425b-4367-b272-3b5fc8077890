import 'package:freezed_annotation/freezed_annotation.dart';

part 'group_model.freezed.dart';
part 'group_model.g.dart';

// ==================== 请求模型 ====================

/// 分组列表请求模型
@freezed
class GroupListRequest with _$GroupListRequest {
  const factory GroupListRequest({
    int? id,
    int? teamId,
    String? name,
    int? limit,
    int? offset,
  }) = _GroupListRequest;

  factory GroupListRequest.fromJson(Map<String, dynamic> json) =>
      _$GroupListRequestFromJson(json);
}

// ==================== 响应模型 ====================

/// 分组响应模型
@freezed
class GroupResponse with _$GroupResponse {
  const factory GroupResponse({
    required List<Group> groups,
    required int total,
  }) = _GroupResponse;

  factory GroupResponse.fromJson(Map<String, dynamic> json) =>
      _$GroupResponseFromJson(json);
}

/// 分组模型
@freezed
class Group with _$Group {
  const factory Group({
    required int id,
    required String name,
    @JsonKey(name: 'team_id') required int teamId,
    @JsonKey(name: 'user_name') required String userName,
    @JsonKey(name: 'created_at') required String createdAt,
    @JsonKey(name: 'updated_at') required String updatedAt,
  }) = _Group;

  factory Group.fromJson(Map<String, dynamic> json) =>
      _$GroupFromJson(json);
}
