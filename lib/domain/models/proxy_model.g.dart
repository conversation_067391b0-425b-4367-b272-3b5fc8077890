// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'proxy_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ProxyListRequestImpl _$$ProxyListRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$ProxyListRequestImpl(
      id: (json['id'] as num?)?.toInt(),
      environmentId: (json['environmentId'] as num?)?.toInt(),
      type: (json['type'] as num?)?.toInt(),
      name: json['name'] as String?,
      limit: (json['limit'] as num?)?.toInt(),
      offset: (json['offset'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$ProxyListRequestImplToJson(
        _$ProxyListRequestImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'environmentId': instance.environmentId,
      'type': instance.type,
      'name': instance.name,
      'limit': instance.limit,
      'offset': instance.offset,
    };

_$SelfProxyCreateRequestImpl _$$SelfProxyCreateRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$SelfProxyCreateRequestImpl(
      name: json['name'] as String,
      type: (json['type'] as num).toInt(),
      host: json['host'] as String,
      port: (json['port'] as num).toInt(),
      username: json['username'] as String?,
      password: json['password'] as String?,
      environmentId: (json['environmentId'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$SelfProxyCreateRequestImplToJson(
        _$SelfProxyCreateRequestImpl instance) =>
    <String, dynamic>{
      'name': instance.name,
      'type': instance.type,
      'host': instance.host,
      'port': instance.port,
      if (instance.username case final value?) 'username': value,
      if (instance.password case final value?) 'password': value,
      if (instance.environmentId case final value?) 'environmentId': value,
    };

_$SelfProxyUpdateRequestImpl _$$SelfProxyUpdateRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$SelfProxyUpdateRequestImpl(
      id: (json['id'] as num?)?.toInt(),
      name: json['name'] as String,
      type: (json['type'] as num).toInt(),
      host: json['host'] as String,
      port: (json['port'] as num).toInt(),
      username: json['username'] as String?,
      password: json['password'] as String?,
      environmentId: (json['environmentId'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$SelfProxyUpdateRequestImplToJson(
        _$SelfProxyUpdateRequestImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'type': instance.type,
      'host': instance.host,
      'port': instance.port,
      if (instance.username case final value?) 'username': value,
      if (instance.password case final value?) 'password': value,
      if (instance.environmentId case final value?) 'environmentId': value,
    };

_$SelfProxyResponseImpl _$$SelfProxyResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$SelfProxyResponseImpl(
      proxies: (json['proxies'] as List<dynamic>)
          .map((e) => SelfProxyItem.fromJson(e as Map<String, dynamic>))
          .toList(),
      total: (json['total'] as num).toInt(),
    );

Map<String, dynamic> _$$SelfProxyResponseImplToJson(
        _$SelfProxyResponseImpl instance) =>
    <String, dynamic>{
      'proxies': instance.proxies,
      'total': instance.total,
    };

_$SelfProxyItemImpl _$$SelfProxyItemImplFromJson(Map<String, dynamic> json) =>
    _$SelfProxyItemImpl(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      type: (json['type'] as num).toInt(),
      host: json['host'] as String,
      port: (json['port'] as num).toInt(),
      environmentName: json['environment_name'] as String,
      teamName: json['team_name'] as String,
    );

Map<String, dynamic> _$$SelfProxyItemImplToJson(_$SelfProxyItemImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'type': instance.type,
      'host': instance.host,
      'port': instance.port,
      'environment_name': instance.environmentName,
      'team_name': instance.teamName,
    };

_$SelfProxyDetailImpl _$$SelfProxyDetailImplFromJson(
        Map<String, dynamic> json) =>
    _$SelfProxyDetailImpl(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String?,
      type: (json['type'] as num).toInt(),
      host: json['host'] as String?,
      port: (json['port'] as num).toInt(),
      username: json['username'] as String?,
      password: json['password'] as String?,
      teamId: (json['team_id'] as num?)?.toInt(),
      environmentId: (json['environment_id'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$SelfProxyDetailImplToJson(
        _$SelfProxyDetailImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'type': instance.type,
      'host': instance.host,
      'port': instance.port,
      'username': instance.username,
      'password': instance.password,
      'team_id': instance.teamId,
      'environment_id': instance.environmentId,
    };

_$PlatformProxyModelImpl _$$PlatformProxyModelImplFromJson(
        Map<String, dynamic> json) =>
    _$PlatformProxyModelImpl(
      proxies: (json['proxies'] as List<dynamic>)
          .map((e) => PlatformProxy.fromJson(e as Map<String, dynamic>))
          .toList(),
      total: (json['total'] as num).toInt(),
    );

Map<String, dynamic> _$$PlatformProxyModelImplToJson(
        _$PlatformProxyModelImpl instance) =>
    <String, dynamic>{
      'proxies': instance.proxies,
      'total': instance.total,
    };

_$PlatformProxyImpl _$$PlatformProxyImplFromJson(Map<String, dynamic> json) =>
    _$PlatformProxyImpl(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      ipAddress: json['ip_address'] as String,
      environment: json['environment'] as String,
      region: json['region'] as String,
      isUnlimited: json['is_unlimited'] as bool,
      autoRenew: json['auto_renew'] as bool,
      expiresAt: json['expires_at'] as String,
    );

Map<String, dynamic> _$$PlatformProxyImplToJson(_$PlatformProxyImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'ip_address': instance.ipAddress,
      'environment': instance.environment,
      'region': instance.region,
      'is_unlimited': instance.isUnlimited,
      'auto_renew': instance.autoRenew,
      'expires_at': instance.expiresAt,
    };
