import 'package:freezed_annotation/freezed_annotation.dart';

part 'user_model.freezed.dart';
part 'user_model.g.dart';

@freezed
class UserModel with _$UserModel {
  const factory UserModel({
    @<PERSON><PERSON><PERSON><PERSON>(name: 'user_name') String? username,
    String? email,
    String? telephone,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'is_active') bool? isActive,
    @<PERSON>son<PERSON><PERSON>(name: 'is_deleted') bool? isDeleted,
    @<PERSON>son<PERSON><PERSON>(name: 'is_two_factor_enabled') bool? isTwoFactorEnabled,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'two_factor_secret') String? twoFactorSecret,
    @<PERSON>son<PERSON><PERSON>(name: 'real_name_type') int? realNameType,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'real_name') String? realName,
    @Json<PERSON><PERSON>(name: 'id_card_number') String? idCardNumber,
    @<PERSON>son<PERSON><PERSON>(name: 'company_name') String? companyName,
    @JsonKey(name: 'company_unified_social_code') String? companyUnifiedSocialCode,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'invite_user_id') int? inviteUserId,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'commission_type') int? commissionType,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'commission_rate') double? commissionRate,
    @<PERSON>son<PERSON>ey(name: 'invite_code') String? inviteCode,
    @JsonKey(name: 'team') Team? team,
    @JsonKey(name: 'role') Role? role,
    @JsonKey(name: 'wallet_amount') double? walletAmount,
    @JsonKey(name: 'subscription') dynamic subscription,
    @JsonKey(name: 'environment_size_sum') int? environmentSizeSum,
  }) = _UserModel;

  factory UserModel.fromJson(Map<String, dynamic> json) =>
      _$UserModelFromJson(json);
}

@freezed
class Team with _$Team {
  const factory Team({
    required String name,
    @JsonKey(name: 'owner_id') required int ownerId,
  }) = _Team;

  factory Team.fromJson(Map<String, dynamic> json) =>
      _$TeamFromJson(json);
}

@freezed
class Role with _$Role {
  const factory Role({
    required String name,
    required String permissions,
    bool? secure,
  }) = _Role;

  factory Role.fromJson(Map<String, dynamic> json) =>
      _$RoleFromJson(json);
}

/// 更新用户个人信息请求模型
@freezed
class UpdateUserInfoRequest with _$UpdateUserInfoRequest {
  const factory UpdateUserInfoRequest({
    String? email,         // 新邮箱地址（可选）
    String? emailCode,    // 邮箱验证码（可选）
    String? telephone,     // 新手机号码（可选）
    String? telephoneCode, // 手机验证码（可选）
    bool? enable2fa,   // 启用/禁用双因子认证
  }) = _UpdateUserInfoRequest;
}

// 用户基础信息，用于首页的展示
// @freezed
// class UserInfoModel with _$UserInfoModel {
//   const factory UserInfoModel({
//     required UserModel user,
//     required TeamModel team,
//   }) = _UserInfoModel;
// }
