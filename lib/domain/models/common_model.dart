import 'package:freezed_annotation/freezed_annotation.dart';

part 'common_model.freezed.dart';
part 'common_model.g.dart';

/// 通用响应模型
/// 支持您的API格式: {"code": 200, "message": "success", "data": {...}}
/// 也支持简化格式: {"message": "success"}
@freezed
class CommonResponse with _$CommonResponse {
  const factory CommonResponse({
    @Default(200) int code, // 状态码，默认为200
    required String message, // 消息
    Object? data, // 数据，可以是任意类型的模型
  }) = _CommonResponse;

  factory CommonResponse.fromJson(Map<String, dynamic> json) =>
      _$CommonResponseFromJson(json);
}

/// 扩展方法，方便解析特定类型的data
extension CommonResponseExtension on CommonResponse {
  /// 解析data为指定类型
  T? parseData<T>(T Function(Map<String, dynamic>) fromJson) {
    if (data == null) return null;
    if (data is Map<String, dynamic>) {
      return fromJson(data as Map<String, dynamic>);
    }
    return null;
  }

  /// 安全解析data为指定类型，处理null值字段
  T? parseDataSafely<T>(
    T Function(Map<String, dynamic>) fromJson,
    Map<String, dynamic> defaultFields,
  ) {
    if (data == null) return null;
    if (data is Map<String, dynamic>) {
      final dataMap = Map<String, dynamic>.from(data as Map<String, dynamic>);
      
      // 为null的字段设置默认值
      defaultFields.forEach((key, defaultValue) {
        if (dataMap[key] == null) {
          dataMap[key] = defaultValue;
        }
      });
      
      try {
        return fromJson(dataMap);
      } catch (e) {
        // 解析失败时返回null
        return null;
      }
    }
    return null;
  }

  /// 解析data为List类型
  List<T>? parseDataList<T>(T Function(Map<String, dynamic>) fromJson) {
    if (data == null) return null;
    if (data is List) {
      return (data as List).map((item) {
        if (item is Map<String, dynamic>) {
          return fromJson(item);
        }
        throw Exception('Invalid data format for list parsing');
      }).toList();
    }
    return null;
  }

  /// 获取原始Map数据
  Map<String, dynamic>? get dataAsMap {
    if (data is Map<String, dynamic>) {
      return data as Map<String, dynamic>;
    }
    return null;
  }

  /// 获取原始List数据
  List<dynamic>? get dataAsList {
    if (data is List) {
      return data as List<dynamic>;
    }
    return null;
  }

  /// 检查是否成功响应 (支持200状态码)
  bool get isSuccess => code == 200;

  /// 检查是否有数据
  bool get hasData => data != null;

  /// 获取最合适的消息
  /// 优先使用data中的message，如果没有则使用外层message
  String get bestMessage {
    final dataMap = dataAsMap;
    if (dataMap != null && dataMap['message'] is String) {
      return dataMap['message'] as String;
    }
    return message;
  }

  /// 从data中获取指定字段的值
  T? getDataField<T>(String fieldName) {
    final dataMap = dataAsMap;
    if (dataMap != null && dataMap.containsKey(fieldName)) {
      return dataMap[fieldName] as T?;
    }
    return null;
  }

  /// 检查data中是否包含message字段
  bool get hasDataMessage {
    final dataMap = dataAsMap;
    return dataMap != null && dataMap.containsKey('message') && dataMap['message'] is String;
  }
}
