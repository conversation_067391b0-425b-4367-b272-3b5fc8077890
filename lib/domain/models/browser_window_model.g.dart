// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'browser_window_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$BrowserWindowModelImpl _$$BrowserWindowModelImplFromJson(
        Map<String, dynamic> json) =>
    _$BrowserWindowModelImpl(
      windows: (json['environments'] as List<dynamic>?)
          ?.map((e) => BrowserWindowItem.fromJson(e as Map<String, dynamic>))
          .toList(),
      total: (json['total'] as num).toInt(),
    );

Map<String, dynamic> _$$BrowserWindowModelImplToJson(
        _$BrowserWindowModelImpl instance) =>
    <String, dynamic>{
      'environments': instance.windows,
      'total': instance.total,
    };

_$BrowserWindowRequestImpl _$$BrowserWindowRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$BrowserWindowRequestImpl(
      name: json['name'] as String?,
      userId: (json['user_id'] as num?)?.toInt(),
      groupId: (json['group_id'] as num?)?.toInt(),
      limit: (json['limit'] as num?)?.toInt(),
      offset: (json['offset'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$BrowserWindowRequestImplToJson(
        _$BrowserWindowRequestImpl instance) =>
    <String, dynamic>{
      if (instance.name case final value?) 'name': value,
      if (instance.userId case final value?) 'user_id': value,
      if (instance.groupId case final value?) 'group_id': value,
      if (instance.limit case final value?) 'limit': value,
      if (instance.offset case final value?) 'offset': value,
    };

_$DeletedBrowserWindowRequestImpl _$$DeletedBrowserWindowRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$DeletedBrowserWindowRequestImpl(
      groupId: (json['group_id'] as num?)?.toInt(),
      limit: (json['limit'] as num?)?.toInt(),
      offset: (json['offset'] as num?)?.toInt(),
      name: json['name'] as String?,
      userId: (json['user_id'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$DeletedBrowserWindowRequestImplToJson(
        _$DeletedBrowserWindowRequestImpl instance) =>
    <String, dynamic>{
      if (instance.groupId case final value?) 'group_id': value,
      if (instance.limit case final value?) 'limit': value,
      if (instance.offset case final value?) 'offset': value,
      if (instance.name case final value?) 'name': value,
      if (instance.userId case final value?) 'user_id': value,
    };

_$BrowserWindowItemImpl _$$BrowserWindowItemImplFromJson(
        Map<String, dynamic> json) =>
    _$BrowserWindowItemImpl(
      id: (json['id'] as num).toInt(),
      teamId: (json['team_id'] as num).toInt(),
      name: json['name'] as String,
      userId: (json['user_id'] as num).toInt(),
      groupId: (json['group_id'] as num).toInt(),
      groupName: json['group_name'] as String,
      proxy: ProxyInfo.fromJson(json['proxy'] as Map<String, dynamic>),
      proxyType: (json['proxy_type'] as num).toInt(),
      platform: json['platform'] as String,
      storage: json['storage'] as String,
      tag: json['tag'] as String,
      comment: json['comment'] as String?,
      sort: (json['sort'] as num?)?.toInt(),
      size: (json['size'] as num?)?.toInt(),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$$BrowserWindowItemImplToJson(
        _$BrowserWindowItemImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'team_id': instance.teamId,
      'name': instance.name,
      'user_id': instance.userId,
      'group_id': instance.groupId,
      'group_name': instance.groupName,
      'proxy': instance.proxy,
      'proxy_type': instance.proxyType,
      'platform': instance.platform,
      'storage': instance.storage,
      'tag': instance.tag,
      'comment': instance.comment,
      'sort': instance.sort,
      'size': instance.size,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
    };

_$BrowserWindowImpl _$$BrowserWindowImplFromJson(Map<String, dynamic> json) =>
    _$BrowserWindowImpl(
      id: (json['id'] as num).toInt(),
      teamId: (json['team_id'] as num).toInt(),
      name: json['name'] as String,
      userId: (json['user_id'] as num).toInt(),
      groupId: (json['group_id'] as num).toInt(),
      groupName: json['group_name'] as String,
      proxy: ProxyInfo.fromJson(json['proxy'] as Map<String, dynamic>),
      proxyType: (json['proxy_type'] as num).toInt(),
      platform: json['platform'] as String,
      parameters: json['parameters'] as String,
      storage: json['storage'] as String,
      tag: json['tag'] as String,
      comment: json['comment'] as String?,
      sort: (json['sort'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$BrowserWindowImplToJson(_$BrowserWindowImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'team_id': instance.teamId,
      'name': instance.name,
      'user_id': instance.userId,
      'group_id': instance.groupId,
      'group_name': instance.groupName,
      'proxy': instance.proxy,
      'proxy_type': instance.proxyType,
      'platform': instance.platform,
      'parameters': instance.parameters,
      'storage': instance.storage,
      'tag': instance.tag,
      'comment': instance.comment,
      'sort': instance.sort,
    };

_$ProxyInfoImpl _$$ProxyInfoImplFromJson(Map<String, dynamic> json) =>
    _$ProxyInfoImpl(
      name: json['name'] as String,
      type: (json['type'] as num).toInt(),
      address: json['address'] as String,
      port: (json['port'] as num).toInt(),
      username: json['username'] as String,
      password: json['password'] as String,
    );

Map<String, dynamic> _$$ProxyInfoImplToJson(_$ProxyInfoImpl instance) =>
    <String, dynamic>{
      'name': instance.name,
      'type': instance.type,
      'address': instance.address,
      'port': instance.port,
      'username': instance.username,
      'password': instance.password,
    };

_$BrowserWindowUpdateRequestImpl _$$BrowserWindowUpdateRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$BrowserWindowUpdateRequestImpl(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String?,
      groupId: (json['group_id'] as num?)?.toInt(),
      proxyId: (json['proxy_id'] as num?)?.toInt(),
      proxyType: (json['proxy_type'] as num?)?.toInt(),
      platform: json['platform'] as String?,
      parameters: json['parameters'] as String?,
      storage: json['storage'] as String?,
      tag: json['tag'] as String?,
      comment: json['comment'] as String?,
      sort: (json['sort'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$BrowserWindowUpdateRequestImplToJson(
        _$BrowserWindowUpdateRequestImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      if (instance.name case final value?) 'name': value,
      if (instance.groupId case final value?) 'group_id': value,
      if (instance.proxyId case final value?) 'proxy_id': value,
      if (instance.proxyType case final value?) 'proxy_type': value,
      if (instance.platform case final value?) 'platform': value,
      if (instance.parameters case final value?) 'parameters': value,
      if (instance.storage case final value?) 'storage': value,
      if (instance.tag case final value?) 'tag': value,
      if (instance.comment case final value?) 'comment': value,
      if (instance.sort case final value?) 'sort': value,
    };

_$BrowserCreateRequestImpl _$$BrowserCreateRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$BrowserCreateRequestImpl(
      name: json['name'] as String,
      groupId: (json['group_id'] as num).toInt(),
      proxyId: (json['proxy_id'] as num).toInt(),
      proxyType: (json['proxy_type'] as num).toInt(),
      platform: json['platform'] as String,
      parameters: json['parameters'] as String,
      storage: json['storage'] as String?,
      tag: json['tag'] as String?,
      comment: json['comment'] as String?,
      sort: (json['sort'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$BrowserCreateRequestImplToJson(
        _$BrowserCreateRequestImpl instance) =>
    <String, dynamic>{
      'name': instance.name,
      'group_id': instance.groupId,
      'proxy_id': instance.proxyId,
      'proxy_type': instance.proxyType,
      'platform': instance.platform,
      'parameters': instance.parameters,
      if (instance.storage case final value?) 'storage': value,
      if (instance.tag case final value?) 'tag': value,
      if (instance.comment case final value?) 'comment': value,
      if (instance.sort case final value?) 'sort': value,
    };

_$BrowserUpdateRequestImpl _$$BrowserUpdateRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$BrowserUpdateRequestImpl(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      groupId: (json['group_id'] as num).toInt(),
      proxyId: (json['proxy_id'] as num).toInt(),
      proxyType: (json['proxy_type'] as num?)?.toInt() ?? 2,
      platform: json['platform'] as String,
      parameters: json['parameters'] as String,
      storage: json['storage'] as String?,
      tag: json['tag'] as String?,
      comment: json['comment'] as String?,
      sort: (json['sort'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$BrowserUpdateRequestImplToJson(
        _$BrowserUpdateRequestImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'group_id': instance.groupId,
      'proxy_id': instance.proxyId,
      'proxy_type': instance.proxyType,
      'platform': instance.platform,
      'parameters': instance.parameters,
      'storage': instance.storage,
      'tag': instance.tag,
      'comment': instance.comment,
      'sort': instance.sort,
    };
