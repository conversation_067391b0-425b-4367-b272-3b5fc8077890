// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'browser_instance_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

BrowserInstanceModel _$BrowserInstanceModelFromJson(Map<String, dynamic> json) {
  return _BrowserInstanceModel.fromJson(json);
}

/// @nodoc
mixin _$BrowserInstanceModel {
  String get id => throw _privateConstructorUsedError; // 唯一标识符
  String get name => throw _privateConstructorUsedError; // 浏览器名称
  String get configPath => throw _privateConstructorUsedError; // 配置文件路径
  Map<String, dynamic> get config => throw _privateConstructorUsedError; // 配置信息
  BrowserStatus get status => throw _privateConstructorUsedError; // 当前状态
  int? get pid => throw _privateConstructorUsedError; // 进程ID
  DateTime? get startTime => throw _privateConstructorUsedError; // 启动时间
  DateTime? get lastActivity => throw _privateConstructorUsedError;

  /// Serializes this BrowserInstanceModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of BrowserInstanceModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BrowserInstanceModelCopyWith<BrowserInstanceModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BrowserInstanceModelCopyWith<$Res> {
  factory $BrowserInstanceModelCopyWith(BrowserInstanceModel value,
          $Res Function(BrowserInstanceModel) then) =
      _$BrowserInstanceModelCopyWithImpl<$Res, BrowserInstanceModel>;
  @useResult
  $Res call(
      {String id,
      String name,
      String configPath,
      Map<String, dynamic> config,
      BrowserStatus status,
      int? pid,
      DateTime? startTime,
      DateTime? lastActivity});
}

/// @nodoc
class _$BrowserInstanceModelCopyWithImpl<$Res,
        $Val extends BrowserInstanceModel>
    implements $BrowserInstanceModelCopyWith<$Res> {
  _$BrowserInstanceModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BrowserInstanceModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? configPath = null,
    Object? config = null,
    Object? status = null,
    Object? pid = freezed,
    Object? startTime = freezed,
    Object? lastActivity = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      configPath: null == configPath
          ? _value.configPath
          : configPath // ignore: cast_nullable_to_non_nullable
              as String,
      config: null == config
          ? _value.config
          : config // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as BrowserStatus,
      pid: freezed == pid
          ? _value.pid
          : pid // ignore: cast_nullable_to_non_nullable
              as int?,
      startTime: freezed == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      lastActivity: freezed == lastActivity
          ? _value.lastActivity
          : lastActivity // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BrowserInstanceModelImplCopyWith<$Res>
    implements $BrowserInstanceModelCopyWith<$Res> {
  factory _$$BrowserInstanceModelImplCopyWith(_$BrowserInstanceModelImpl value,
          $Res Function(_$BrowserInstanceModelImpl) then) =
      __$$BrowserInstanceModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String name,
      String configPath,
      Map<String, dynamic> config,
      BrowserStatus status,
      int? pid,
      DateTime? startTime,
      DateTime? lastActivity});
}

/// @nodoc
class __$$BrowserInstanceModelImplCopyWithImpl<$Res>
    extends _$BrowserInstanceModelCopyWithImpl<$Res, _$BrowserInstanceModelImpl>
    implements _$$BrowserInstanceModelImplCopyWith<$Res> {
  __$$BrowserInstanceModelImplCopyWithImpl(_$BrowserInstanceModelImpl _value,
      $Res Function(_$BrowserInstanceModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of BrowserInstanceModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? configPath = null,
    Object? config = null,
    Object? status = null,
    Object? pid = freezed,
    Object? startTime = freezed,
    Object? lastActivity = freezed,
  }) {
    return _then(_$BrowserInstanceModelImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      configPath: null == configPath
          ? _value.configPath
          : configPath // ignore: cast_nullable_to_non_nullable
              as String,
      config: null == config
          ? _value._config
          : config // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as BrowserStatus,
      pid: freezed == pid
          ? _value.pid
          : pid // ignore: cast_nullable_to_non_nullable
              as int?,
      startTime: freezed == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      lastActivity: freezed == lastActivity
          ? _value.lastActivity
          : lastActivity // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BrowserInstanceModelImpl extends _BrowserInstanceModel {
  const _$BrowserInstanceModelImpl(
      {required this.id,
      required this.name,
      required this.configPath,
      required final Map<String, dynamic> config,
      this.status = BrowserStatus.stopped,
      this.pid,
      this.startTime,
      this.lastActivity})
      : _config = config,
        super._();

  factory _$BrowserInstanceModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$BrowserInstanceModelImplFromJson(json);

  @override
  final String id;
// 唯一标识符
  @override
  final String name;
// 浏览器名称
  @override
  final String configPath;
// 配置文件路径
  final Map<String, dynamic> _config;
// 配置文件路径
  @override
  Map<String, dynamic> get config {
    if (_config is EqualUnmodifiableMapView) return _config;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_config);
  }

// 配置信息
  @override
  @JsonKey()
  final BrowserStatus status;
// 当前状态
  @override
  final int? pid;
// 进程ID
  @override
  final DateTime? startTime;
// 启动时间
  @override
  final DateTime? lastActivity;

  @override
  String toString() {
    return 'BrowserInstanceModel(id: $id, name: $name, configPath: $configPath, config: $config, status: $status, pid: $pid, startTime: $startTime, lastActivity: $lastActivity)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BrowserInstanceModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.configPath, configPath) ||
                other.configPath == configPath) &&
            const DeepCollectionEquality().equals(other._config, _config) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.pid, pid) || other.pid == pid) &&
            (identical(other.startTime, startTime) ||
                other.startTime == startTime) &&
            (identical(other.lastActivity, lastActivity) ||
                other.lastActivity == lastActivity));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      configPath,
      const DeepCollectionEquality().hash(_config),
      status,
      pid,
      startTime,
      lastActivity);

  /// Create a copy of BrowserInstanceModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BrowserInstanceModelImplCopyWith<_$BrowserInstanceModelImpl>
      get copyWith =>
          __$$BrowserInstanceModelImplCopyWithImpl<_$BrowserInstanceModelImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BrowserInstanceModelImplToJson(
      this,
    );
  }
}

abstract class _BrowserInstanceModel extends BrowserInstanceModel {
  const factory _BrowserInstanceModel(
      {required final String id,
      required final String name,
      required final String configPath,
      required final Map<String, dynamic> config,
      final BrowserStatus status,
      final int? pid,
      final DateTime? startTime,
      final DateTime? lastActivity}) = _$BrowserInstanceModelImpl;
  const _BrowserInstanceModel._() : super._();

  factory _BrowserInstanceModel.fromJson(Map<String, dynamic> json) =
      _$BrowserInstanceModelImpl.fromJson;

  @override
  String get id; // 唯一标识符
  @override
  String get name; // 浏览器名称
  @override
  String get configPath; // 配置文件路径
  @override
  Map<String, dynamic> get config; // 配置信息
  @override
  BrowserStatus get status; // 当前状态
  @override
  int? get pid; // 进程ID
  @override
  DateTime? get startTime; // 启动时间
  @override
  DateTime? get lastActivity;

  /// Create a copy of BrowserInstanceModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BrowserInstanceModelImplCopyWith<_$BrowserInstanceModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}
