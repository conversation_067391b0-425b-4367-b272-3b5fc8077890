// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$OrderListRequestImpl _$$OrderListRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$OrderListRequestImpl(
      orderType: (json['order_type'] as num?)?.toInt(),
      status: (json['status'] as num?)?.toInt(),
      paymentMethod: (json['payment_method'] as num?)?.toInt(),
      limit: (json['limit'] as num?)?.toInt(),
      offset: (json['offset'] as num?)?.toInt(),
      startTime: json['start_time'] as String?,
      endTime: json['end_time'] as String?,
    );

Map<String, dynamic> _$$OrderListRequestImplToJson(
        _$OrderListRequestImpl instance) =>
    <String, dynamic>{
      if (instance.orderType case final value?) 'order_type': value,
      if (instance.status case final value?) 'status': value,
      if (instance.paymentMethod case final value?) 'payment_method': value,
      if (instance.limit case final value?) 'limit': value,
      if (instance.offset case final value?) 'offset': value,
      if (instance.startTime case final value?) 'start_time': value,
      if (instance.endTime case final value?) 'end_time': value,
    };

_$CreateOrderRequestImpl _$$CreateOrderRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$CreateOrderRequestImpl(
      duration: (json['duration'] as num?)?.toInt(),
      environmentCount: (json['environment_count'] as num?)?.toInt(),
      memberCount: (json['member_count'] as num?)?.toInt(),
      orderType: (json['order_type'] as num).toInt(),
      paymentMethod: (json['payment_method'] as num).toInt(),
      proxyCount: (json['proxy_count'] as num?)?.toInt(),
      proxyId: (json['proxy_id'] as num?)?.toInt(),
      recharge: (json['recharge'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$CreateOrderRequestImplToJson(
        _$CreateOrderRequestImpl instance) =>
    <String, dynamic>{
      if (instance.duration case final value?) 'duration': value,
      if (instance.environmentCount case final value?)
        'environment_count': value,
      if (instance.memberCount case final value?) 'member_count': value,
      'order_type': instance.orderType,
      'payment_method': instance.paymentMethod,
      if (instance.proxyCount case final value?) 'proxy_count': value,
      if (instance.proxyId case final value?) 'proxy_id': value,
      if (instance.recharge case final value?) 'recharge': value,
    };

_$CalculateOrderPriceRequestImpl _$$CalculateOrderPriceRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$CalculateOrderPriceRequestImpl(
      duration: (json['duration'] as num?)?.toInt(),
      environmentCount: (json['environment_count'] as num?)?.toInt(),
      memberCount: (json['member_count'] as num?)?.toInt(),
      orderType: (json['order_type'] as num).toInt(),
      proxyCount: (json['proxy_count'] as num?)?.toInt(),
      proxyId: (json['proxy_id'] as num?)?.toInt(),
      recharge: (json['recharge'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$CalculateOrderPriceRequestImplToJson(
        _$CalculateOrderPriceRequestImpl instance) =>
    <String, dynamic>{
      if (instance.duration case final value?) 'duration': value,
      if (instance.environmentCount case final value?)
        'environment_count': value,
      if (instance.memberCount case final value?) 'member_count': value,
      'order_type': instance.orderType,
      if (instance.proxyCount case final value?) 'proxy_count': value,
      if (instance.proxyId case final value?) 'proxy_id': value,
      if (instance.recharge case final value?) 'recharge': value,
    };

_$OrderListResponseImpl _$$OrderListResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$OrderListResponseImpl(
      orders: (json['orders'] as List<dynamic>)
          .map((e) => Order.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$OrderListResponseImplToJson(
        _$OrderListResponseImpl instance) =>
    <String, dynamic>{
      'orders': instance.orders,
    };

_$OrderImpl _$$OrderImplFromJson(Map<String, dynamic> json) => _$OrderImpl(
      amount: (json['amount'] as num?)?.toInt(),
      balanceAmount: (json['balance_amount'] as num?)?.toInt(),
      couponId: (json['coupon_id'] as num?)?.toInt(),
      createdAt: json['created_at'] as String?,
      currency: json['currency'] as String?,
      expiresAt: json['expires_at'] as String?,
      id: (json['id'] as num?)?.toInt(),
      orderContent: json['order_content'] as String?,
      orderNumber: json['order_number'] as String?,
      orderType: (json['order_type'] as num?)?.toInt(),
      paymentMethod: (json['payment_method'] as num?)?.toInt(),
      realAmount: (json['real_amount'] as num?)?.toInt(),
      status: (json['status'] as num?)?.toInt(),
      teamId: (json['team_id'] as num?)?.toInt(),
      updatedAt: json['updated_at'] as String?,
      url: json['url'] as String?,
      userId: (json['user_id'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$OrderImplToJson(_$OrderImpl instance) =>
    <String, dynamic>{
      'amount': instance.amount,
      'balance_amount': instance.balanceAmount,
      'coupon_id': instance.couponId,
      'created_at': instance.createdAt,
      'currency': instance.currency,
      'expires_at': instance.expiresAt,
      'id': instance.id,
      'order_content': instance.orderContent,
      'order_number': instance.orderNumber,
      'order_type': instance.orderType,
      'payment_method': instance.paymentMethod,
      'real_amount': instance.realAmount,
      'status': instance.status,
      'team_id': instance.teamId,
      'updated_at': instance.updatedAt,
      'url': instance.url,
      'user_id': instance.userId,
    };

_$CreateOrderResponseImpl _$$CreateOrderResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$CreateOrderResponseImpl(
      amount: (json['amount'] as num?)?.toInt(),
      orderId: (json['order_id'] as num?)?.toInt(),
      orderNumber: json['order_number'] as String?,
      paymentUrl: json['payment_url'] as String?,
    );

Map<String, dynamic> _$$CreateOrderResponseImplToJson(
        _$CreateOrderResponseImpl instance) =>
    <String, dynamic>{
      'amount': instance.amount,
      'order_id': instance.orderId,
      'order_number': instance.orderNumber,
      'payment_url': instance.paymentUrl,
    };
