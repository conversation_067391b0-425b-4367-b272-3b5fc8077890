import 'package:freezed_annotation/freezed_annotation.dart';

part 'order_model.freezed.dart';
part 'order_model.g.dart';

// ==================== 请求模型 ====================

/// 订单列表请求模型
@freezed
class OrderListRequest with _$OrderListRequest {
  const factory OrderListRequest({
    @JsonKey(name: 'order_type', includeIfNull: false) int? orderType, // 订单类型
    @Json<PERSON>ey(name: 'status', includeIfNull: false) int? status, // 订单状态
    @Json<PERSON>ey(name: 'payment_method', includeIfNull: false) int? paymentMethod, // 支付方式
    @Json<PERSON>ey(name: 'limit', includeIfNull: false) int? limit, // 每页条数
    @JsonKey(name: 'offset', includeIfNull: false) int? offset, // 偏移量
    @<PERSON><PERSON><PERSON><PERSON>(name: 'start_time', includeIfNull: false) String? startTime, // 开始时间
    @Json<PERSON>ey(name: 'end_time', includeIfNull: false) String? endTime, // 结束时间
  }) = _OrderListRequest;

  factory OrderListRequest.fromJson(Map<String, dynamic> json) =>
      _$OrderListRequestFromJson(json);
}

/// 创建订单请求模型
@freezed
class CreateOrderRequest with _$CreateOrderRequest {
  const factory CreateOrderRequest({
    @JsonKey(name: 'duration', includeIfNull: false) int? duration,
    @JsonKey(name: 'environment_count', includeIfNull: false) int? environmentCount,
    @JsonKey(name: 'member_count', includeIfNull: false) int? memberCount,
    @JsonKey(name: 'order_type', includeIfNull: false) required int orderType,
    @JsonKey(name: 'payment_method', includeIfNull: false) required int paymentMethod,
    @JsonKey(name: 'proxy_count', includeIfNull: false) int? proxyCount,
    @JsonKey(name: 'proxy_id', includeIfNull: false) int? proxyId,
    @JsonKey(name: 'recharge', includeIfNull: false) int? recharge,
  }) = _CreateOrderRequest;

  factory CreateOrderRequest.fromJson(Map<String, dynamic> json) =>
      _$CreateOrderRequestFromJson(json);
}

/// 计算订单价格请求模型
@freezed
class CalculateOrderPriceRequest with _$CalculateOrderPriceRequest {
  const factory CalculateOrderPriceRequest({
    @JsonKey(name: 'duration', includeIfNull: false) int? duration,
    @JsonKey(name: 'environment_count', includeIfNull: false) int? environmentCount,
    @JsonKey(name: 'member_count', includeIfNull: false) int? memberCount,
    @JsonKey(name: 'order_type', includeIfNull: false) required int orderType,
    @JsonKey(name: 'proxy_count', includeIfNull: false) int? proxyCount,
    @JsonKey(name: 'proxy_id', includeIfNull: false) int? proxyId,
    @JsonKey(name: 'recharge', includeIfNull: false) int? recharge,
  }) = _CalculateOrderPriceRequest;

  factory CalculateOrderPriceRequest.fromJson(Map<String, dynamic> json) =>
      _$CalculateOrderPriceRequestFromJson(json);
}
// ==================== 响应模型 ====================

/// 订单列表响应模型
@freezed
class OrderListResponse with _$OrderListResponse {
  const factory OrderListResponse({
    @JsonKey(name: 'orders') required List<Order> orders,
  }) = _OrderListResponse;

  factory OrderListResponse.fromJson(Map<String, dynamic> json) =>
      _$OrderListResponseFromJson(json);
}


/// 订单模型
@freezed
class Order with _$Order {
  const factory Order({
    @JsonKey(name: 'amount') int? amount,
    @JsonKey(name: 'balance_amount') int? balanceAmount,
    @JsonKey(name: 'coupon_id') int? couponId,
    @JsonKey(name: 'created_at') String? createdAt,
    @JsonKey(name: 'currency') String? currency,
    @JsonKey(name: 'expires_at') String? expiresAt,
    @JsonKey(name: 'id') int? id,
    @JsonKey(name: 'order_content') String? orderContent,
    @JsonKey(name: 'order_number') String? orderNumber,
    @JsonKey(name: 'order_type') int? orderType,
    @JsonKey(name: 'payment_method') int? paymentMethod,
    @JsonKey(name: 'real_amount') int? realAmount,
    @JsonKey(name: 'status') int? status,
    @JsonKey(name: 'team_id') int? teamId,
    @JsonKey(name: 'updated_at') String? updatedAt,
    @JsonKey(name: 'url') String? url,
    @JsonKey(name: 'user_id') int? userId,
  }) = _Order;

  factory Order.fromJson(Map<String, dynamic> json) => _$OrderFromJson(json);
}

/// 创建订单响应模型
@freezed
class CreateOrderResponse with _$CreateOrderResponse {
  const factory CreateOrderResponse({
    @JsonKey(name: 'amount') int? amount,
    @JsonKey(name: 'order_id') int? orderId,
    @JsonKey(name: 'order_number') String? orderNumber,
    @JsonKey(name: 'payment_url') String? paymentUrl,
  }) = _CreateOrderResponse;

  factory CreateOrderResponse.fromJson(Map<String, dynamic> json) =>
      _$CreateOrderResponseFromJson(json);
}


