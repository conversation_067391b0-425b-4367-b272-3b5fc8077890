// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'role_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

RoleModel _$RoleModelFromJson(Map<String, dynamic> json) {
  return _RoleModel.fromJson(json);
}

/// @nodoc
mixin _$RoleModel {
  @JsonKey(name: 'id')
  int get id => throw _privateConstructorUsedError; // 角色ID
  @JsonKey(name: 'name')
  String get name => throw _privateConstructorUsedError; // 角色名称
  @JsonKey(name: 'team_id')
  int get teamId => throw _privateConstructorUsedError; // 团队ID
  @JsonKey(
      name: 'permissions',
      fromJson: _permissionsFromJson,
      toJson: _permissionsToJson)
  Map<String, dynamic> get permissions =>
      throw _privateConstructorUsedError; // 权限配置
  @JsonKey(name: 'created_at')
  DateTime? get createdAt => throw _privateConstructorUsedError; // 创建时间（可空）
  @JsonKey(name: 'updated_at')
  DateTime? get updatedAt => throw _privateConstructorUsedError; // 更新时间（可空）
  @JsonKey(name: 'secure')
  bool? get secure => throw _privateConstructorUsedError;

  /// Serializes this RoleModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of RoleModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RoleModelCopyWith<RoleModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RoleModelCopyWith<$Res> {
  factory $RoleModelCopyWith(RoleModel value, $Res Function(RoleModel) then) =
      _$RoleModelCopyWithImpl<$Res, RoleModel>;
  @useResult
  $Res call(
      {@JsonKey(name: 'id') int id,
      @JsonKey(name: 'name') String name,
      @JsonKey(name: 'team_id') int teamId,
      @JsonKey(
          name: 'permissions',
          fromJson: _permissionsFromJson,
          toJson: _permissionsToJson)
      Map<String, dynamic> permissions,
      @JsonKey(name: 'created_at') DateTime? createdAt,
      @JsonKey(name: 'updated_at') DateTime? updatedAt,
      @JsonKey(name: 'secure') bool? secure});
}

/// @nodoc
class _$RoleModelCopyWithImpl<$Res, $Val extends RoleModel>
    implements $RoleModelCopyWith<$Res> {
  _$RoleModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RoleModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? teamId = null,
    Object? permissions = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? secure = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      teamId: null == teamId
          ? _value.teamId
          : teamId // ignore: cast_nullable_to_non_nullable
              as int,
      permissions: null == permissions
          ? _value.permissions
          : permissions // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      secure: freezed == secure
          ? _value.secure
          : secure // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RoleModelImplCopyWith<$Res>
    implements $RoleModelCopyWith<$Res> {
  factory _$$RoleModelImplCopyWith(
          _$RoleModelImpl value, $Res Function(_$RoleModelImpl) then) =
      __$$RoleModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'id') int id,
      @JsonKey(name: 'name') String name,
      @JsonKey(name: 'team_id') int teamId,
      @JsonKey(
          name: 'permissions',
          fromJson: _permissionsFromJson,
          toJson: _permissionsToJson)
      Map<String, dynamic> permissions,
      @JsonKey(name: 'created_at') DateTime? createdAt,
      @JsonKey(name: 'updated_at') DateTime? updatedAt,
      @JsonKey(name: 'secure') bool? secure});
}

/// @nodoc
class __$$RoleModelImplCopyWithImpl<$Res>
    extends _$RoleModelCopyWithImpl<$Res, _$RoleModelImpl>
    implements _$$RoleModelImplCopyWith<$Res> {
  __$$RoleModelImplCopyWithImpl(
      _$RoleModelImpl _value, $Res Function(_$RoleModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of RoleModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? teamId = null,
    Object? permissions = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? secure = freezed,
  }) {
    return _then(_$RoleModelImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      teamId: null == teamId
          ? _value.teamId
          : teamId // ignore: cast_nullable_to_non_nullable
              as int,
      permissions: null == permissions
          ? _value._permissions
          : permissions // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      secure: freezed == secure
          ? _value.secure
          : secure // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RoleModelImpl implements _RoleModel {
  const _$RoleModelImpl(
      {@JsonKey(name: 'id') required this.id,
      @JsonKey(name: 'name') required this.name,
      @JsonKey(name: 'team_id') required this.teamId,
      @JsonKey(
          name: 'permissions',
          fromJson: _permissionsFromJson,
          toJson: _permissionsToJson)
      required final Map<String, dynamic> permissions,
      @JsonKey(name: 'created_at') this.createdAt,
      @JsonKey(name: 'updated_at') this.updatedAt,
      @JsonKey(name: 'secure') this.secure})
      : _permissions = permissions;

  factory _$RoleModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$RoleModelImplFromJson(json);

  @override
  @JsonKey(name: 'id')
  final int id;
// 角色ID
  @override
  @JsonKey(name: 'name')
  final String name;
// 角色名称
  @override
  @JsonKey(name: 'team_id')
  final int teamId;
// 团队ID
  final Map<String, dynamic> _permissions;
// 团队ID
  @override
  @JsonKey(
      name: 'permissions',
      fromJson: _permissionsFromJson,
      toJson: _permissionsToJson)
  Map<String, dynamic> get permissions {
    if (_permissions is EqualUnmodifiableMapView) return _permissions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_permissions);
  }

// 权限配置
  @override
  @JsonKey(name: 'created_at')
  final DateTime? createdAt;
// 创建时间（可空）
  @override
  @JsonKey(name: 'updated_at')
  final DateTime? updatedAt;
// 更新时间（可空）
  @override
  @JsonKey(name: 'secure')
  final bool? secure;

  @override
  String toString() {
    return 'RoleModel(id: $id, name: $name, teamId: $teamId, permissions: $permissions, createdAt: $createdAt, updatedAt: $updatedAt, secure: $secure)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RoleModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.teamId, teamId) || other.teamId == teamId) &&
            const DeepCollectionEquality()
                .equals(other._permissions, _permissions) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.secure, secure) || other.secure == secure));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      teamId,
      const DeepCollectionEquality().hash(_permissions),
      createdAt,
      updatedAt,
      secure);

  /// Create a copy of RoleModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RoleModelImplCopyWith<_$RoleModelImpl> get copyWith =>
      __$$RoleModelImplCopyWithImpl<_$RoleModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RoleModelImplToJson(
      this,
    );
  }
}

abstract class _RoleModel implements RoleModel {
  const factory _RoleModel(
      {@JsonKey(name: 'id') required final int id,
      @JsonKey(name: 'name') required final String name,
      @JsonKey(name: 'team_id') required final int teamId,
      @JsonKey(
          name: 'permissions',
          fromJson: _permissionsFromJson,
          toJson: _permissionsToJson)
      required final Map<String, dynamic> permissions,
      @JsonKey(name: 'created_at') final DateTime? createdAt,
      @JsonKey(name: 'updated_at') final DateTime? updatedAt,
      @JsonKey(name: 'secure') final bool? secure}) = _$RoleModelImpl;

  factory _RoleModel.fromJson(Map<String, dynamic> json) =
      _$RoleModelImpl.fromJson;

  @override
  @JsonKey(name: 'id')
  int get id; // 角色ID
  @override
  @JsonKey(name: 'name')
  String get name; // 角色名称
  @override
  @JsonKey(name: 'team_id')
  int get teamId; // 团队ID
  @override
  @JsonKey(
      name: 'permissions',
      fromJson: _permissionsFromJson,
      toJson: _permissionsToJson)
  Map<String, dynamic> get permissions; // 权限配置
  @override
  @JsonKey(name: 'created_at')
  DateTime? get createdAt; // 创建时间（可空）
  @override
  @JsonKey(name: 'updated_at')
  DateTime? get updatedAt; // 更新时间（可空）
  @override
  @JsonKey(name: 'secure')
  bool? get secure;

  /// Create a copy of RoleModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RoleModelImplCopyWith<_$RoleModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CreateRoleRequest _$CreateRoleRequestFromJson(Map<String, dynamic> json) {
  return _CreateRoleRequest.fromJson(json);
}

/// @nodoc
mixin _$CreateRoleRequest {
  @JsonKey(name: 'name')
  String get name => throw _privateConstructorUsedError;

  /// 权限配置（必须是有效的 JSON 字符串）
  /// 示例: '{"read": true, "write": true}'
  @JsonKey(name: 'permissions')
  String get permissions => throw _privateConstructorUsedError;
  @JsonKey(name: 'secure')
  bool get secure => throw _privateConstructorUsedError;

  /// Serializes this CreateRoleRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CreateRoleRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CreateRoleRequestCopyWith<CreateRoleRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreateRoleRequestCopyWith<$Res> {
  factory $CreateRoleRequestCopyWith(
          CreateRoleRequest value, $Res Function(CreateRoleRequest) then) =
      _$CreateRoleRequestCopyWithImpl<$Res, CreateRoleRequest>;
  @useResult
  $Res call(
      {@JsonKey(name: 'name') String name,
      @JsonKey(name: 'permissions') String permissions,
      @JsonKey(name: 'secure') bool secure});
}

/// @nodoc
class _$CreateRoleRequestCopyWithImpl<$Res, $Val extends CreateRoleRequest>
    implements $CreateRoleRequestCopyWith<$Res> {
  _$CreateRoleRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CreateRoleRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? permissions = null,
    Object? secure = null,
  }) {
    return _then(_value.copyWith(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      permissions: null == permissions
          ? _value.permissions
          : permissions // ignore: cast_nullable_to_non_nullable
              as String,
      secure: null == secure
          ? _value.secure
          : secure // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CreateRoleRequestImplCopyWith<$Res>
    implements $CreateRoleRequestCopyWith<$Res> {
  factory _$$CreateRoleRequestImplCopyWith(_$CreateRoleRequestImpl value,
          $Res Function(_$CreateRoleRequestImpl) then) =
      __$$CreateRoleRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'name') String name,
      @JsonKey(name: 'permissions') String permissions,
      @JsonKey(name: 'secure') bool secure});
}

/// @nodoc
class __$$CreateRoleRequestImplCopyWithImpl<$Res>
    extends _$CreateRoleRequestCopyWithImpl<$Res, _$CreateRoleRequestImpl>
    implements _$$CreateRoleRequestImplCopyWith<$Res> {
  __$$CreateRoleRequestImplCopyWithImpl(_$CreateRoleRequestImpl _value,
      $Res Function(_$CreateRoleRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of CreateRoleRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? permissions = null,
    Object? secure = null,
  }) {
    return _then(_$CreateRoleRequestImpl(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      permissions: null == permissions
          ? _value.permissions
          : permissions // ignore: cast_nullable_to_non_nullable
              as String,
      secure: null == secure
          ? _value.secure
          : secure // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CreateRoleRequestImpl implements _CreateRoleRequest {
  const _$CreateRoleRequestImpl(
      {@JsonKey(name: 'name') required this.name,
      @JsonKey(name: 'permissions') required this.permissions,
      @JsonKey(name: 'secure') required this.secure});

  factory _$CreateRoleRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$CreateRoleRequestImplFromJson(json);

  @override
  @JsonKey(name: 'name')
  final String name;

  /// 权限配置（必须是有效的 JSON 字符串）
  /// 示例: '{"read": true, "write": true}'
  @override
  @JsonKey(name: 'permissions')
  final String permissions;
  @override
  @JsonKey(name: 'secure')
  final bool secure;

  @override
  String toString() {
    return 'CreateRoleRequest(name: $name, permissions: $permissions, secure: $secure)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreateRoleRequestImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.permissions, permissions) ||
                other.permissions == permissions) &&
            (identical(other.secure, secure) || other.secure == secure));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, name, permissions, secure);

  /// Create a copy of CreateRoleRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreateRoleRequestImplCopyWith<_$CreateRoleRequestImpl> get copyWith =>
      __$$CreateRoleRequestImplCopyWithImpl<_$CreateRoleRequestImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CreateRoleRequestImplToJson(
      this,
    );
  }
}

abstract class _CreateRoleRequest implements CreateRoleRequest {
  const factory _CreateRoleRequest(
          {@JsonKey(name: 'name') required final String name,
          @JsonKey(name: 'permissions') required final String permissions,
          @JsonKey(name: 'secure') required final bool secure}) =
      _$CreateRoleRequestImpl;

  factory _CreateRoleRequest.fromJson(Map<String, dynamic> json) =
      _$CreateRoleRequestImpl.fromJson;

  @override
  @JsonKey(name: 'name')
  String get name;

  /// 权限配置（必须是有效的 JSON 字符串）
  /// 示例: '{"read": true, "write": true}'
  @override
  @JsonKey(name: 'permissions')
  String get permissions;
  @override
  @JsonKey(name: 'secure')
  bool get secure;

  /// Create a copy of CreateRoleRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreateRoleRequestImplCopyWith<_$CreateRoleRequestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

UpdateRoleRequest _$UpdateRoleRequestFromJson(Map<String, dynamic> json) {
  return _UpdateRoleRequest.fromJson(json);
}

/// @nodoc
mixin _$UpdateRoleRequest {
  @JsonKey(name: 'id')
  int get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'name')
  String get name => throw _privateConstructorUsedError;
  @JsonKey(name: 'permissions')
  String get permissions => throw _privateConstructorUsedError;
  @JsonKey(name: 'secure')
  bool get secure => throw _privateConstructorUsedError;

  /// Serializes this UpdateRoleRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UpdateRoleRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UpdateRoleRequestCopyWith<UpdateRoleRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UpdateRoleRequestCopyWith<$Res> {
  factory $UpdateRoleRequestCopyWith(
          UpdateRoleRequest value, $Res Function(UpdateRoleRequest) then) =
      _$UpdateRoleRequestCopyWithImpl<$Res, UpdateRoleRequest>;
  @useResult
  $Res call(
      {@JsonKey(name: 'id') int id,
      @JsonKey(name: 'name') String name,
      @JsonKey(name: 'permissions') String permissions,
      @JsonKey(name: 'secure') bool secure});
}

/// @nodoc
class _$UpdateRoleRequestCopyWithImpl<$Res, $Val extends UpdateRoleRequest>
    implements $UpdateRoleRequestCopyWith<$Res> {
  _$UpdateRoleRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UpdateRoleRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? permissions = null,
    Object? secure = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      permissions: null == permissions
          ? _value.permissions
          : permissions // ignore: cast_nullable_to_non_nullable
              as String,
      secure: null == secure
          ? _value.secure
          : secure // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UpdateRoleRequestImplCopyWith<$Res>
    implements $UpdateRoleRequestCopyWith<$Res> {
  factory _$$UpdateRoleRequestImplCopyWith(_$UpdateRoleRequestImpl value,
          $Res Function(_$UpdateRoleRequestImpl) then) =
      __$$UpdateRoleRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'id') int id,
      @JsonKey(name: 'name') String name,
      @JsonKey(name: 'permissions') String permissions,
      @JsonKey(name: 'secure') bool secure});
}

/// @nodoc
class __$$UpdateRoleRequestImplCopyWithImpl<$Res>
    extends _$UpdateRoleRequestCopyWithImpl<$Res, _$UpdateRoleRequestImpl>
    implements _$$UpdateRoleRequestImplCopyWith<$Res> {
  __$$UpdateRoleRequestImplCopyWithImpl(_$UpdateRoleRequestImpl _value,
      $Res Function(_$UpdateRoleRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of UpdateRoleRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? permissions = null,
    Object? secure = null,
  }) {
    return _then(_$UpdateRoleRequestImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      permissions: null == permissions
          ? _value.permissions
          : permissions // ignore: cast_nullable_to_non_nullable
              as String,
      secure: null == secure
          ? _value.secure
          : secure // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UpdateRoleRequestImpl implements _UpdateRoleRequest {
  const _$UpdateRoleRequestImpl(
      {@JsonKey(name: 'id') required this.id,
      @JsonKey(name: 'name') required this.name,
      @JsonKey(name: 'permissions') required this.permissions,
      @JsonKey(name: 'secure') required this.secure});

  factory _$UpdateRoleRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$UpdateRoleRequestImplFromJson(json);

  @override
  @JsonKey(name: 'id')
  final int id;
  @override
  @JsonKey(name: 'name')
  final String name;
  @override
  @JsonKey(name: 'permissions')
  final String permissions;
  @override
  @JsonKey(name: 'secure')
  final bool secure;

  @override
  String toString() {
    return 'UpdateRoleRequest(id: $id, name: $name, permissions: $permissions, secure: $secure)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateRoleRequestImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.permissions, permissions) ||
                other.permissions == permissions) &&
            (identical(other.secure, secure) || other.secure == secure));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, name, permissions, secure);

  /// Create a copy of UpdateRoleRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateRoleRequestImplCopyWith<_$UpdateRoleRequestImpl> get copyWith =>
      __$$UpdateRoleRequestImplCopyWithImpl<_$UpdateRoleRequestImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UpdateRoleRequestImplToJson(
      this,
    );
  }
}

abstract class _UpdateRoleRequest implements UpdateRoleRequest {
  const factory _UpdateRoleRequest(
          {@JsonKey(name: 'id') required final int id,
          @JsonKey(name: 'name') required final String name,
          @JsonKey(name: 'permissions') required final String permissions,
          @JsonKey(name: 'secure') required final bool secure}) =
      _$UpdateRoleRequestImpl;

  factory _UpdateRoleRequest.fromJson(Map<String, dynamic> json) =
      _$UpdateRoleRequestImpl.fromJson;

  @override
  @JsonKey(name: 'id')
  int get id;
  @override
  @JsonKey(name: 'name')
  String get name;
  @override
  @JsonKey(name: 'permissions')
  String get permissions;
  @override
  @JsonKey(name: 'secure')
  bool get secure;

  /// Create a copy of UpdateRoleRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateRoleRequestImplCopyWith<_$UpdateRoleRequestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
