// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'team_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

TeamModelResponse _$TeamModelResponseFromJson(Map<String, dynamic> json) {
  return _TeamModelResponse.fromJson(json);
}

/// @nodoc
mixin _$TeamModelResponse {
  int get total => throw _privateConstructorUsedError;
  List<TeamUserResponse> get users => throw _privateConstructorUsedError;

  /// Serializes this TeamModelResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TeamModelResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TeamModelResponseCopyWith<TeamModelResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TeamModelResponseCopyWith<$Res> {
  factory $TeamModelResponseCopyWith(
          TeamModelResponse value, $Res Function(TeamModelResponse) then) =
      _$TeamModelResponseCopyWithImpl<$Res, TeamModelResponse>;
  @useResult
  $Res call({int total, List<TeamUserResponse> users});
}

/// @nodoc
class _$TeamModelResponseCopyWithImpl<$Res, $Val extends TeamModelResponse>
    implements $TeamModelResponseCopyWith<$Res> {
  _$TeamModelResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TeamModelResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? total = null,
    Object? users = null,
  }) {
    return _then(_value.copyWith(
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      users: null == users
          ? _value.users
          : users // ignore: cast_nullable_to_non_nullable
              as List<TeamUserResponse>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TeamModelResponseImplCopyWith<$Res>
    implements $TeamModelResponseCopyWith<$Res> {
  factory _$$TeamModelResponseImplCopyWith(_$TeamModelResponseImpl value,
          $Res Function(_$TeamModelResponseImpl) then) =
      __$$TeamModelResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int total, List<TeamUserResponse> users});
}

/// @nodoc
class __$$TeamModelResponseImplCopyWithImpl<$Res>
    extends _$TeamModelResponseCopyWithImpl<$Res, _$TeamModelResponseImpl>
    implements _$$TeamModelResponseImplCopyWith<$Res> {
  __$$TeamModelResponseImplCopyWithImpl(_$TeamModelResponseImpl _value,
      $Res Function(_$TeamModelResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of TeamModelResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? total = null,
    Object? users = null,
  }) {
    return _then(_$TeamModelResponseImpl(
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      users: null == users
          ? _value._users
          : users // ignore: cast_nullable_to_non_nullable
              as List<TeamUserResponse>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TeamModelResponseImpl implements _TeamModelResponse {
  const _$TeamModelResponseImpl(
      {required this.total, required final List<TeamUserResponse> users})
      : _users = users;

  factory _$TeamModelResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$TeamModelResponseImplFromJson(json);

  @override
  final int total;
  final List<TeamUserResponse> _users;
  @override
  List<TeamUserResponse> get users {
    if (_users is EqualUnmodifiableListView) return _users;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_users);
  }

  @override
  String toString() {
    return 'TeamModelResponse(total: $total, users: $users)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TeamModelResponseImpl &&
            (identical(other.total, total) || other.total == total) &&
            const DeepCollectionEquality().equals(other._users, _users));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, total, const DeepCollectionEquality().hash(_users));

  /// Create a copy of TeamModelResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TeamModelResponseImplCopyWith<_$TeamModelResponseImpl> get copyWith =>
      __$$TeamModelResponseImplCopyWithImpl<_$TeamModelResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TeamModelResponseImplToJson(
      this,
    );
  }
}

abstract class _TeamModelResponse implements TeamModelResponse {
  const factory _TeamModelResponse(
      {required final int total,
      required final List<TeamUserResponse> users}) = _$TeamModelResponseImpl;

  factory _TeamModelResponse.fromJson(Map<String, dynamic> json) =
      _$TeamModelResponseImpl.fromJson;

  @override
  int get total;
  @override
  List<TeamUserResponse> get users;

  /// Create a copy of TeamModelResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TeamModelResponseImplCopyWith<_$TeamModelResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

TeamRequest _$TeamRequestFromJson(Map<String, dynamic> json) {
  return _TeamRequest.fromJson(json);
}

/// @nodoc
mixin _$TeamRequest {
  @JsonKey(name: 'username')
  String? get username => throw _privateConstructorUsedError;
  @JsonKey(name: 'role_id')
  int? get roleId => throw _privateConstructorUsedError;
  @JsonKey(name: 'limit')
  int get limit => throw _privateConstructorUsedError;
  @JsonKey(name: 'offset')
  int? get offset => throw _privateConstructorUsedError;

  /// Serializes this TeamRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TeamRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TeamRequestCopyWith<TeamRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TeamRequestCopyWith<$Res> {
  factory $TeamRequestCopyWith(
          TeamRequest value, $Res Function(TeamRequest) then) =
      _$TeamRequestCopyWithImpl<$Res, TeamRequest>;
  @useResult
  $Res call(
      {@JsonKey(name: 'username') String? username,
      @JsonKey(name: 'role_id') int? roleId,
      @JsonKey(name: 'limit') int limit,
      @JsonKey(name: 'offset') int? offset});
}

/// @nodoc
class _$TeamRequestCopyWithImpl<$Res, $Val extends TeamRequest>
    implements $TeamRequestCopyWith<$Res> {
  _$TeamRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TeamRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? username = freezed,
    Object? roleId = freezed,
    Object? limit = null,
    Object? offset = freezed,
  }) {
    return _then(_value.copyWith(
      username: freezed == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String?,
      roleId: freezed == roleId
          ? _value.roleId
          : roleId // ignore: cast_nullable_to_non_nullable
              as int?,
      limit: null == limit
          ? _value.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int,
      offset: freezed == offset
          ? _value.offset
          : offset // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TeamRequestImplCopyWith<$Res>
    implements $TeamRequestCopyWith<$Res> {
  factory _$$TeamRequestImplCopyWith(
          _$TeamRequestImpl value, $Res Function(_$TeamRequestImpl) then) =
      __$$TeamRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'username') String? username,
      @JsonKey(name: 'role_id') int? roleId,
      @JsonKey(name: 'limit') int limit,
      @JsonKey(name: 'offset') int? offset});
}

/// @nodoc
class __$$TeamRequestImplCopyWithImpl<$Res>
    extends _$TeamRequestCopyWithImpl<$Res, _$TeamRequestImpl>
    implements _$$TeamRequestImplCopyWith<$Res> {
  __$$TeamRequestImplCopyWithImpl(
      _$TeamRequestImpl _value, $Res Function(_$TeamRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of TeamRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? username = freezed,
    Object? roleId = freezed,
    Object? limit = null,
    Object? offset = freezed,
  }) {
    return _then(_$TeamRequestImpl(
      username: freezed == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String?,
      roleId: freezed == roleId
          ? _value.roleId
          : roleId // ignore: cast_nullable_to_non_nullable
              as int?,
      limit: null == limit
          ? _value.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int,
      offset: freezed == offset
          ? _value.offset
          : offset // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TeamRequestImpl implements _TeamRequest {
  const _$TeamRequestImpl(
      {@JsonKey(name: 'username') this.username,
      @JsonKey(name: 'role_id') this.roleId,
      @JsonKey(name: 'limit') required this.limit,
      @JsonKey(name: 'offset') this.offset});

  factory _$TeamRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$TeamRequestImplFromJson(json);

  @override
  @JsonKey(name: 'username')
  final String? username;
  @override
  @JsonKey(name: 'role_id')
  final int? roleId;
  @override
  @JsonKey(name: 'limit')
  final int limit;
  @override
  @JsonKey(name: 'offset')
  final int? offset;

  @override
  String toString() {
    return 'TeamRequest(username: $username, roleId: $roleId, limit: $limit, offset: $offset)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TeamRequestImpl &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.roleId, roleId) || other.roleId == roleId) &&
            (identical(other.limit, limit) || other.limit == limit) &&
            (identical(other.offset, offset) || other.offset == offset));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, username, roleId, limit, offset);

  /// Create a copy of TeamRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TeamRequestImplCopyWith<_$TeamRequestImpl> get copyWith =>
      __$$TeamRequestImplCopyWithImpl<_$TeamRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TeamRequestImplToJson(
      this,
    );
  }
}

abstract class _TeamRequest implements TeamRequest {
  const factory _TeamRequest(
      {@JsonKey(name: 'username') final String? username,
      @JsonKey(name: 'role_id') final int? roleId,
      @JsonKey(name: 'limit') required final int limit,
      @JsonKey(name: 'offset') final int? offset}) = _$TeamRequestImpl;

  factory _TeamRequest.fromJson(Map<String, dynamic> json) =
      _$TeamRequestImpl.fromJson;

  @override
  @JsonKey(name: 'username')
  String? get username;
  @override
  @JsonKey(name: 'role_id')
  int? get roleId;
  @override
  @JsonKey(name: 'limit')
  int get limit;
  @override
  @JsonKey(name: 'offset')
  int? get offset;

  /// Create a copy of TeamRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TeamRequestImplCopyWith<_$TeamRequestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

TeamUserResponse _$TeamUserResponseFromJson(Map<String, dynamic> json) {
  return _TeamUserResponse.fromJson(json);
}

/// @nodoc
mixin _$TeamUserResponse {
  int get id => throw _privateConstructorUsedError;
  String get username => throw _privateConstructorUsedError;
  String get email => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_two_factor_enabled')
  bool get isTwoFactorEnabled => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_active')
  bool get isActive => throw _privateConstructorUsedError;
  @JsonKey(name: 'role_name')
  String get roleName => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_owner')
  bool get isOwner => throw _privateConstructorUsedError;

  /// Serializes this TeamUserResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TeamUserResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TeamUserResponseCopyWith<TeamUserResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TeamUserResponseCopyWith<$Res> {
  factory $TeamUserResponseCopyWith(
          TeamUserResponse value, $Res Function(TeamUserResponse) then) =
      _$TeamUserResponseCopyWithImpl<$Res, TeamUserResponse>;
  @useResult
  $Res call(
      {int id,
      String username,
      String email,
      @JsonKey(name: 'is_two_factor_enabled') bool isTwoFactorEnabled,
      @JsonKey(name: 'is_active') bool isActive,
      @JsonKey(name: 'role_name') String roleName,
      @JsonKey(name: 'is_owner') bool isOwner});
}

/// @nodoc
class _$TeamUserResponseCopyWithImpl<$Res, $Val extends TeamUserResponse>
    implements $TeamUserResponseCopyWith<$Res> {
  _$TeamUserResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TeamUserResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? username = null,
    Object? email = null,
    Object? isTwoFactorEnabled = null,
    Object? isActive = null,
    Object? roleName = null,
    Object? isOwner = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      username: null == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      isTwoFactorEnabled: null == isTwoFactorEnabled
          ? _value.isTwoFactorEnabled
          : isTwoFactorEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      roleName: null == roleName
          ? _value.roleName
          : roleName // ignore: cast_nullable_to_non_nullable
              as String,
      isOwner: null == isOwner
          ? _value.isOwner
          : isOwner // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TeamUserResponseImplCopyWith<$Res>
    implements $TeamUserResponseCopyWith<$Res> {
  factory _$$TeamUserResponseImplCopyWith(_$TeamUserResponseImpl value,
          $Res Function(_$TeamUserResponseImpl) then) =
      __$$TeamUserResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      String username,
      String email,
      @JsonKey(name: 'is_two_factor_enabled') bool isTwoFactorEnabled,
      @JsonKey(name: 'is_active') bool isActive,
      @JsonKey(name: 'role_name') String roleName,
      @JsonKey(name: 'is_owner') bool isOwner});
}

/// @nodoc
class __$$TeamUserResponseImplCopyWithImpl<$Res>
    extends _$TeamUserResponseCopyWithImpl<$Res, _$TeamUserResponseImpl>
    implements _$$TeamUserResponseImplCopyWith<$Res> {
  __$$TeamUserResponseImplCopyWithImpl(_$TeamUserResponseImpl _value,
      $Res Function(_$TeamUserResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of TeamUserResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? username = null,
    Object? email = null,
    Object? isTwoFactorEnabled = null,
    Object? isActive = null,
    Object? roleName = null,
    Object? isOwner = null,
  }) {
    return _then(_$TeamUserResponseImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      username: null == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      isTwoFactorEnabled: null == isTwoFactorEnabled
          ? _value.isTwoFactorEnabled
          : isTwoFactorEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      roleName: null == roleName
          ? _value.roleName
          : roleName // ignore: cast_nullable_to_non_nullable
              as String,
      isOwner: null == isOwner
          ? _value.isOwner
          : isOwner // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TeamUserResponseImpl implements _TeamUserResponse {
  const _$TeamUserResponseImpl(
      {required this.id,
      required this.username,
      required this.email,
      @JsonKey(name: 'is_two_factor_enabled') required this.isTwoFactorEnabled,
      @JsonKey(name: 'is_active') required this.isActive,
      @JsonKey(name: 'role_name') required this.roleName,
      @JsonKey(name: 'is_owner') required this.isOwner});

  factory _$TeamUserResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$TeamUserResponseImplFromJson(json);

  @override
  final int id;
  @override
  final String username;
  @override
  final String email;
  @override
  @JsonKey(name: 'is_two_factor_enabled')
  final bool isTwoFactorEnabled;
  @override
  @JsonKey(name: 'is_active')
  final bool isActive;
  @override
  @JsonKey(name: 'role_name')
  final String roleName;
  @override
  @JsonKey(name: 'is_owner')
  final bool isOwner;

  @override
  String toString() {
    return 'TeamUserResponse(id: $id, username: $username, email: $email, isTwoFactorEnabled: $isTwoFactorEnabled, isActive: $isActive, roleName: $roleName, isOwner: $isOwner)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TeamUserResponseImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.isTwoFactorEnabled, isTwoFactorEnabled) ||
                other.isTwoFactorEnabled == isTwoFactorEnabled) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.roleName, roleName) ||
                other.roleName == roleName) &&
            (identical(other.isOwner, isOwner) || other.isOwner == isOwner));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, username, email,
      isTwoFactorEnabled, isActive, roleName, isOwner);

  /// Create a copy of TeamUserResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TeamUserResponseImplCopyWith<_$TeamUserResponseImpl> get copyWith =>
      __$$TeamUserResponseImplCopyWithImpl<_$TeamUserResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TeamUserResponseImplToJson(
      this,
    );
  }
}

abstract class _TeamUserResponse implements TeamUserResponse {
  const factory _TeamUserResponse(
          {required final int id,
          required final String username,
          required final String email,
          @JsonKey(name: 'is_two_factor_enabled')
          required final bool isTwoFactorEnabled,
          @JsonKey(name: 'is_active') required final bool isActive,
          @JsonKey(name: 'role_name') required final String roleName,
          @JsonKey(name: 'is_owner') required final bool isOwner}) =
      _$TeamUserResponseImpl;

  factory _TeamUserResponse.fromJson(Map<String, dynamic> json) =
      _$TeamUserResponseImpl.fromJson;

  @override
  int get id;
  @override
  String get username;
  @override
  String get email;
  @override
  @JsonKey(name: 'is_two_factor_enabled')
  bool get isTwoFactorEnabled;
  @override
  @JsonKey(name: 'is_active')
  bool get isActive;
  @override
  @JsonKey(name: 'role_name')
  String get roleName;
  @override
  @JsonKey(name: 'is_owner')
  bool get isOwner;

  /// Create a copy of TeamUserResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TeamUserResponseImplCopyWith<_$TeamUserResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AddTeamUserRequest _$AddTeamUserRequestFromJson(Map<String, dynamic> json) {
  return _AddTeamUserRequest.fromJson(json);
}

/// @nodoc
mixin _$AddTeamUserRequest {
  @JsonKey(name: 'is_active', includeIfNull: false)
  bool? get isActive => throw _privateConstructorUsedError;
  @JsonKey(name: 'password')
  String get password => throw _privateConstructorUsedError;
  @JsonKey(name: 'role_id')
  int get roleId => throw _privateConstructorUsedError;
  @JsonKey(name: 'user_name')
  String get userName => throw _privateConstructorUsedError;

  /// Serializes this AddTeamUserRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AddTeamUserRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AddTeamUserRequestCopyWith<AddTeamUserRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AddTeamUserRequestCopyWith<$Res> {
  factory $AddTeamUserRequestCopyWith(
          AddTeamUserRequest value, $Res Function(AddTeamUserRequest) then) =
      _$AddTeamUserRequestCopyWithImpl<$Res, AddTeamUserRequest>;
  @useResult
  $Res call(
      {@JsonKey(name: 'is_active', includeIfNull: false) bool? isActive,
      @JsonKey(name: 'password') String password,
      @JsonKey(name: 'role_id') int roleId,
      @JsonKey(name: 'user_name') String userName});
}

/// @nodoc
class _$AddTeamUserRequestCopyWithImpl<$Res, $Val extends AddTeamUserRequest>
    implements $AddTeamUserRequestCopyWith<$Res> {
  _$AddTeamUserRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AddTeamUserRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isActive = freezed,
    Object? password = null,
    Object? roleId = null,
    Object? userName = null,
  }) {
    return _then(_value.copyWith(
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      password: null == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String,
      roleId: null == roleId
          ? _value.roleId
          : roleId // ignore: cast_nullable_to_non_nullable
              as int,
      userName: null == userName
          ? _value.userName
          : userName // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AddTeamUserRequestImplCopyWith<$Res>
    implements $AddTeamUserRequestCopyWith<$Res> {
  factory _$$AddTeamUserRequestImplCopyWith(_$AddTeamUserRequestImpl value,
          $Res Function(_$AddTeamUserRequestImpl) then) =
      __$$AddTeamUserRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'is_active', includeIfNull: false) bool? isActive,
      @JsonKey(name: 'password') String password,
      @JsonKey(name: 'role_id') int roleId,
      @JsonKey(name: 'user_name') String userName});
}

/// @nodoc
class __$$AddTeamUserRequestImplCopyWithImpl<$Res>
    extends _$AddTeamUserRequestCopyWithImpl<$Res, _$AddTeamUserRequestImpl>
    implements _$$AddTeamUserRequestImplCopyWith<$Res> {
  __$$AddTeamUserRequestImplCopyWithImpl(_$AddTeamUserRequestImpl _value,
      $Res Function(_$AddTeamUserRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of AddTeamUserRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isActive = freezed,
    Object? password = null,
    Object? roleId = null,
    Object? userName = null,
  }) {
    return _then(_$AddTeamUserRequestImpl(
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      password: null == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String,
      roleId: null == roleId
          ? _value.roleId
          : roleId // ignore: cast_nullable_to_non_nullable
              as int,
      userName: null == userName
          ? _value.userName
          : userName // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AddTeamUserRequestImpl implements _AddTeamUserRequest {
  const _$AddTeamUserRequestImpl(
      {@JsonKey(name: 'is_active', includeIfNull: false) this.isActive,
      @JsonKey(name: 'password') required this.password,
      @JsonKey(name: 'role_id') required this.roleId,
      @JsonKey(name: 'user_name') required this.userName});

  factory _$AddTeamUserRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$AddTeamUserRequestImplFromJson(json);

  @override
  @JsonKey(name: 'is_active', includeIfNull: false)
  final bool? isActive;
  @override
  @JsonKey(name: 'password')
  final String password;
  @override
  @JsonKey(name: 'role_id')
  final int roleId;
  @override
  @JsonKey(name: 'user_name')
  final String userName;

  @override
  String toString() {
    return 'AddTeamUserRequest(isActive: $isActive, password: $password, roleId: $roleId, userName: $userName)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AddTeamUserRequestImpl &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.password, password) ||
                other.password == password) &&
            (identical(other.roleId, roleId) || other.roleId == roleId) &&
            (identical(other.userName, userName) ||
                other.userName == userName));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, isActive, password, roleId, userName);

  /// Create a copy of AddTeamUserRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AddTeamUserRequestImplCopyWith<_$AddTeamUserRequestImpl> get copyWith =>
      __$$AddTeamUserRequestImplCopyWithImpl<_$AddTeamUserRequestImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AddTeamUserRequestImplToJson(
      this,
    );
  }
}

abstract class _AddTeamUserRequest implements AddTeamUserRequest {
  const factory _AddTeamUserRequest(
      {@JsonKey(name: 'is_active', includeIfNull: false) final bool? isActive,
      @JsonKey(name: 'password') required final String password,
      @JsonKey(name: 'role_id') required final int roleId,
      @JsonKey(name: 'user_name')
      required final String userName}) = _$AddTeamUserRequestImpl;

  factory _AddTeamUserRequest.fromJson(Map<String, dynamic> json) =
      _$AddTeamUserRequestImpl.fromJson;

  @override
  @JsonKey(name: 'is_active', includeIfNull: false)
  bool? get isActive;
  @override
  @JsonKey(name: 'password')
  String get password;
  @override
  @JsonKey(name: 'role_id')
  int get roleId;
  @override
  @JsonKey(name: 'user_name')
  String get userName;

  /// Create a copy of AddTeamUserRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AddTeamUserRequestImplCopyWith<_$AddTeamUserRequestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

UpdateTeamUserRequest _$UpdateTeamUserRequestFromJson(
    Map<String, dynamic> json) {
  return _UpdateTeamUserRequest.fromJson(json);
}

/// @nodoc
mixin _$UpdateTeamUserRequest {
  @JsonKey(name: 'id')
  int get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_active', includeIfNull: false)
  bool? get isActive => throw _privateConstructorUsedError;
  @JsonKey(name: 'password', includeIfNull: false)
  String? get password => throw _privateConstructorUsedError;
  @JsonKey(name: 'role_id', includeIfNull: false)
  int? get roleId => throw _privateConstructorUsedError;
  @JsonKey(name: 'user_name', includeIfNull: false)
  String? get userName => throw _privateConstructorUsedError;

  /// Serializes this UpdateTeamUserRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UpdateTeamUserRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UpdateTeamUserRequestCopyWith<UpdateTeamUserRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UpdateTeamUserRequestCopyWith<$Res> {
  factory $UpdateTeamUserRequestCopyWith(UpdateTeamUserRequest value,
          $Res Function(UpdateTeamUserRequest) then) =
      _$UpdateTeamUserRequestCopyWithImpl<$Res, UpdateTeamUserRequest>;
  @useResult
  $Res call(
      {@JsonKey(name: 'id') int id,
      @JsonKey(name: 'is_active', includeIfNull: false) bool? isActive,
      @JsonKey(name: 'password', includeIfNull: false) String? password,
      @JsonKey(name: 'role_id', includeIfNull: false) int? roleId,
      @JsonKey(name: 'user_name', includeIfNull: false) String? userName});
}

/// @nodoc
class _$UpdateTeamUserRequestCopyWithImpl<$Res,
        $Val extends UpdateTeamUserRequest>
    implements $UpdateTeamUserRequestCopyWith<$Res> {
  _$UpdateTeamUserRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UpdateTeamUserRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? isActive = freezed,
    Object? password = freezed,
    Object? roleId = freezed,
    Object? userName = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      password: freezed == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String?,
      roleId: freezed == roleId
          ? _value.roleId
          : roleId // ignore: cast_nullable_to_non_nullable
              as int?,
      userName: freezed == userName
          ? _value.userName
          : userName // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UpdateTeamUserRequestImplCopyWith<$Res>
    implements $UpdateTeamUserRequestCopyWith<$Res> {
  factory _$$UpdateTeamUserRequestImplCopyWith(
          _$UpdateTeamUserRequestImpl value,
          $Res Function(_$UpdateTeamUserRequestImpl) then) =
      __$$UpdateTeamUserRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'id') int id,
      @JsonKey(name: 'is_active', includeIfNull: false) bool? isActive,
      @JsonKey(name: 'password', includeIfNull: false) String? password,
      @JsonKey(name: 'role_id', includeIfNull: false) int? roleId,
      @JsonKey(name: 'user_name', includeIfNull: false) String? userName});
}

/// @nodoc
class __$$UpdateTeamUserRequestImplCopyWithImpl<$Res>
    extends _$UpdateTeamUserRequestCopyWithImpl<$Res,
        _$UpdateTeamUserRequestImpl>
    implements _$$UpdateTeamUserRequestImplCopyWith<$Res> {
  __$$UpdateTeamUserRequestImplCopyWithImpl(_$UpdateTeamUserRequestImpl _value,
      $Res Function(_$UpdateTeamUserRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of UpdateTeamUserRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? isActive = freezed,
    Object? password = freezed,
    Object? roleId = freezed,
    Object? userName = freezed,
  }) {
    return _then(_$UpdateTeamUserRequestImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      password: freezed == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String?,
      roleId: freezed == roleId
          ? _value.roleId
          : roleId // ignore: cast_nullable_to_non_nullable
              as int?,
      userName: freezed == userName
          ? _value.userName
          : userName // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UpdateTeamUserRequestImpl implements _UpdateTeamUserRequest {
  const _$UpdateTeamUserRequestImpl(
      {@JsonKey(name: 'id') required this.id,
      @JsonKey(name: 'is_active', includeIfNull: false) this.isActive,
      @JsonKey(name: 'password', includeIfNull: false) this.password,
      @JsonKey(name: 'role_id', includeIfNull: false) this.roleId,
      @JsonKey(name: 'user_name', includeIfNull: false) this.userName});

  factory _$UpdateTeamUserRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$UpdateTeamUserRequestImplFromJson(json);

  @override
  @JsonKey(name: 'id')
  final int id;
  @override
  @JsonKey(name: 'is_active', includeIfNull: false)
  final bool? isActive;
  @override
  @JsonKey(name: 'password', includeIfNull: false)
  final String? password;
  @override
  @JsonKey(name: 'role_id', includeIfNull: false)
  final int? roleId;
  @override
  @JsonKey(name: 'user_name', includeIfNull: false)
  final String? userName;

  @override
  String toString() {
    return 'UpdateTeamUserRequest(id: $id, isActive: $isActive, password: $password, roleId: $roleId, userName: $userName)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateTeamUserRequestImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.password, password) ||
                other.password == password) &&
            (identical(other.roleId, roleId) || other.roleId == roleId) &&
            (identical(other.userName, userName) ||
                other.userName == userName));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, isActive, password, roleId, userName);

  /// Create a copy of UpdateTeamUserRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateTeamUserRequestImplCopyWith<_$UpdateTeamUserRequestImpl>
      get copyWith => __$$UpdateTeamUserRequestImplCopyWithImpl<
          _$UpdateTeamUserRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UpdateTeamUserRequestImplToJson(
      this,
    );
  }
}

abstract class _UpdateTeamUserRequest implements UpdateTeamUserRequest {
  const factory _UpdateTeamUserRequest(
      {@JsonKey(name: 'id') required final int id,
      @JsonKey(name: 'is_active', includeIfNull: false) final bool? isActive,
      @JsonKey(name: 'password', includeIfNull: false) final String? password,
      @JsonKey(name: 'role_id', includeIfNull: false) final int? roleId,
      @JsonKey(name: 'user_name', includeIfNull: false)
      final String? userName}) = _$UpdateTeamUserRequestImpl;

  factory _UpdateTeamUserRequest.fromJson(Map<String, dynamic> json) =
      _$UpdateTeamUserRequestImpl.fromJson;

  @override
  @JsonKey(name: 'id')
  int get id;
  @override
  @JsonKey(name: 'is_active', includeIfNull: false)
  bool? get isActive;
  @override
  @JsonKey(name: 'password', includeIfNull: false)
  String? get password;
  @override
  @JsonKey(name: 'role_id', includeIfNull: false)
  int? get roleId;
  @override
  @JsonKey(name: 'user_name', includeIfNull: false)
  String? get userName;

  /// Create a copy of UpdateTeamUserRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateTeamUserRequestImplCopyWith<_$UpdateTeamUserRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}

LoginLogRequest _$LoginLogRequestFromJson(Map<String, dynamic> json) {
  return _LoginLogRequest.fromJson(json);
}

/// @nodoc
mixin _$LoginLogRequest {
  @JsonKey(name: 'user_id')
  int? get userId => throw _privateConstructorUsedError;
  int? get limit => throw _privateConstructorUsedError;
  int? get offset => throw _privateConstructorUsedError;
  @JsonKey(name: 'start_time')
  Object? get startTime => throw _privateConstructorUsedError;
  @JsonKey(name: 'end_time')
  Object? get endTime => throw _privateConstructorUsedError;

  /// Serializes this LoginLogRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of LoginLogRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $LoginLogRequestCopyWith<LoginLogRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LoginLogRequestCopyWith<$Res> {
  factory $LoginLogRequestCopyWith(
          LoginLogRequest value, $Res Function(LoginLogRequest) then) =
      _$LoginLogRequestCopyWithImpl<$Res, LoginLogRequest>;
  @useResult
  $Res call(
      {@JsonKey(name: 'user_id') int? userId,
      int? limit,
      int? offset,
      @JsonKey(name: 'start_time') Object? startTime,
      @JsonKey(name: 'end_time') Object? endTime});
}

/// @nodoc
class _$LoginLogRequestCopyWithImpl<$Res, $Val extends LoginLogRequest>
    implements $LoginLogRequestCopyWith<$Res> {
  _$LoginLogRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LoginLogRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = freezed,
    Object? limit = freezed,
    Object? offset = freezed,
    Object? startTime = freezed,
    Object? endTime = freezed,
  }) {
    return _then(_value.copyWith(
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int?,
      limit: freezed == limit
          ? _value.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int?,
      offset: freezed == offset
          ? _value.offset
          : offset // ignore: cast_nullable_to_non_nullable
              as int?,
      startTime: freezed == startTime ? _value.startTime : startTime,
      endTime: freezed == endTime ? _value.endTime : endTime,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LoginLogRequestImplCopyWith<$Res>
    implements $LoginLogRequestCopyWith<$Res> {
  factory _$$LoginLogRequestImplCopyWith(_$LoginLogRequestImpl value,
          $Res Function(_$LoginLogRequestImpl) then) =
      __$$LoginLogRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'user_id') int? userId,
      int? limit,
      int? offset,
      @JsonKey(name: 'start_time') Object? startTime,
      @JsonKey(name: 'end_time') Object? endTime});
}

/// @nodoc
class __$$LoginLogRequestImplCopyWithImpl<$Res>
    extends _$LoginLogRequestCopyWithImpl<$Res, _$LoginLogRequestImpl>
    implements _$$LoginLogRequestImplCopyWith<$Res> {
  __$$LoginLogRequestImplCopyWithImpl(
      _$LoginLogRequestImpl _value, $Res Function(_$LoginLogRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of LoginLogRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = freezed,
    Object? limit = freezed,
    Object? offset = freezed,
    Object? startTime = freezed,
    Object? endTime = freezed,
  }) {
    return _then(_$LoginLogRequestImpl(
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int?,
      limit: freezed == limit
          ? _value.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int?,
      offset: freezed == offset
          ? _value.offset
          : offset // ignore: cast_nullable_to_non_nullable
              as int?,
      startTime: freezed == startTime ? _value.startTime : startTime,
      endTime: freezed == endTime ? _value.endTime : endTime,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$LoginLogRequestImpl implements _LoginLogRequest {
  const _$LoginLogRequestImpl(
      {@JsonKey(name: 'user_id') this.userId,
      this.limit = 10,
      this.offset = 0,
      @JsonKey(name: 'start_time') this.startTime,
      @JsonKey(name: 'end_time') this.endTime});

  factory _$LoginLogRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$LoginLogRequestImplFromJson(json);

  @override
  @JsonKey(name: 'user_id')
  final int? userId;
  @override
  @JsonKey()
  final int? limit;
  @override
  @JsonKey()
  final int? offset;
  @override
  @JsonKey(name: 'start_time')
  final Object? startTime;
  @override
  @JsonKey(name: 'end_time')
  final Object? endTime;

  @override
  String toString() {
    return 'LoginLogRequest(userId: $userId, limit: $limit, offset: $offset, startTime: $startTime, endTime: $endTime)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoginLogRequestImpl &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.limit, limit) || other.limit == limit) &&
            (identical(other.offset, offset) || other.offset == offset) &&
            const DeepCollectionEquality().equals(other.startTime, startTime) &&
            const DeepCollectionEquality().equals(other.endTime, endTime));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      userId,
      limit,
      offset,
      const DeepCollectionEquality().hash(startTime),
      const DeepCollectionEquality().hash(endTime));

  /// Create a copy of LoginLogRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoginLogRequestImplCopyWith<_$LoginLogRequestImpl> get copyWith =>
      __$$LoginLogRequestImplCopyWithImpl<_$LoginLogRequestImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LoginLogRequestImplToJson(
      this,
    );
  }
}

abstract class _LoginLogRequest implements LoginLogRequest {
  const factory _LoginLogRequest(
          {@JsonKey(name: 'user_id') final int? userId,
          final int? limit,
          final int? offset,
          @JsonKey(name: 'start_time') final Object? startTime,
          @JsonKey(name: 'end_time') final Object? endTime}) =
      _$LoginLogRequestImpl;

  factory _LoginLogRequest.fromJson(Map<String, dynamic> json) =
      _$LoginLogRequestImpl.fromJson;

  @override
  @JsonKey(name: 'user_id')
  int? get userId;
  @override
  int? get limit;
  @override
  int? get offset;
  @override
  @JsonKey(name: 'start_time')
  Object? get startTime;
  @override
  @JsonKey(name: 'end_time')
  Object? get endTime;

  /// Create a copy of LoginLogRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoginLogRequestImplCopyWith<_$LoginLogRequestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

LoginLogModel _$LoginLogModelFromJson(Map<String, dynamic> json) {
  return _LoginLogModel.fromJson(json);
}

/// @nodoc
mixin _$LoginLogModel {
  List<LoginLogItem> get logs => throw _privateConstructorUsedError;
  int get total => throw _privateConstructorUsedError;

  /// Serializes this LoginLogModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of LoginLogModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $LoginLogModelCopyWith<LoginLogModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LoginLogModelCopyWith<$Res> {
  factory $LoginLogModelCopyWith(
          LoginLogModel value, $Res Function(LoginLogModel) then) =
      _$LoginLogModelCopyWithImpl<$Res, LoginLogModel>;
  @useResult
  $Res call({List<LoginLogItem> logs, int total});
}

/// @nodoc
class _$LoginLogModelCopyWithImpl<$Res, $Val extends LoginLogModel>
    implements $LoginLogModelCopyWith<$Res> {
  _$LoginLogModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LoginLogModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? logs = null,
    Object? total = null,
  }) {
    return _then(_value.copyWith(
      logs: null == logs
          ? _value.logs
          : logs // ignore: cast_nullable_to_non_nullable
              as List<LoginLogItem>,
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LoginLogModelImplCopyWith<$Res>
    implements $LoginLogModelCopyWith<$Res> {
  factory _$$LoginLogModelImplCopyWith(
          _$LoginLogModelImpl value, $Res Function(_$LoginLogModelImpl) then) =
      __$$LoginLogModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<LoginLogItem> logs, int total});
}

/// @nodoc
class __$$LoginLogModelImplCopyWithImpl<$Res>
    extends _$LoginLogModelCopyWithImpl<$Res, _$LoginLogModelImpl>
    implements _$$LoginLogModelImplCopyWith<$Res> {
  __$$LoginLogModelImplCopyWithImpl(
      _$LoginLogModelImpl _value, $Res Function(_$LoginLogModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of LoginLogModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? logs = null,
    Object? total = null,
  }) {
    return _then(_$LoginLogModelImpl(
      logs: null == logs
          ? _value._logs
          : logs // ignore: cast_nullable_to_non_nullable
              as List<LoginLogItem>,
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$LoginLogModelImpl implements _LoginLogModel {
  const _$LoginLogModelImpl(
      {required final List<LoginLogItem> logs, required this.total})
      : _logs = logs;

  factory _$LoginLogModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$LoginLogModelImplFromJson(json);

  final List<LoginLogItem> _logs;
  @override
  List<LoginLogItem> get logs {
    if (_logs is EqualUnmodifiableListView) return _logs;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_logs);
  }

  @override
  final int total;

  @override
  String toString() {
    return 'LoginLogModel(logs: $logs, total: $total)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoginLogModelImpl &&
            const DeepCollectionEquality().equals(other._logs, _logs) &&
            (identical(other.total, total) || other.total == total));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_logs), total);

  /// Create a copy of LoginLogModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoginLogModelImplCopyWith<_$LoginLogModelImpl> get copyWith =>
      __$$LoginLogModelImplCopyWithImpl<_$LoginLogModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LoginLogModelImplToJson(
      this,
    );
  }
}

abstract class _LoginLogModel implements LoginLogModel {
  const factory _LoginLogModel(
      {required final List<LoginLogItem> logs,
      required final int total}) = _$LoginLogModelImpl;

  factory _LoginLogModel.fromJson(Map<String, dynamic> json) =
      _$LoginLogModelImpl.fromJson;

  @override
  List<LoginLogItem> get logs;
  @override
  int get total;

  /// Create a copy of LoginLogModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoginLogModelImplCopyWith<_$LoginLogModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

LoginLogItem _$LoginLogItemFromJson(Map<String, dynamic> json) {
  return _LoginLogItem.fromJson(json);
}

/// @nodoc
mixin _$LoginLogItem {
  int get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'created_at')
  String get createdAt => throw _privateConstructorUsedError;
  @JsonKey(name: 'ip_location')
  String get ipLocation => throw _privateConstructorUsedError;
  @JsonKey(name: 'login_ip')
  String get loginIp => throw _privateConstructorUsedError; // IP地址的整型表示
  @JsonKey(name: 'user_name')
  String get userName => throw _privateConstructorUsedError;

  /// Serializes this LoginLogItem to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of LoginLogItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $LoginLogItemCopyWith<LoginLogItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LoginLogItemCopyWith<$Res> {
  factory $LoginLogItemCopyWith(
          LoginLogItem value, $Res Function(LoginLogItem) then) =
      _$LoginLogItemCopyWithImpl<$Res, LoginLogItem>;
  @useResult
  $Res call(
      {int id,
      @JsonKey(name: 'created_at') String createdAt,
      @JsonKey(name: 'ip_location') String ipLocation,
      @JsonKey(name: 'login_ip') String loginIp,
      @JsonKey(name: 'user_name') String userName});
}

/// @nodoc
class _$LoginLogItemCopyWithImpl<$Res, $Val extends LoginLogItem>
    implements $LoginLogItemCopyWith<$Res> {
  _$LoginLogItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LoginLogItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? createdAt = null,
    Object? ipLocation = null,
    Object? loginIp = null,
    Object? userName = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as String,
      ipLocation: null == ipLocation
          ? _value.ipLocation
          : ipLocation // ignore: cast_nullable_to_non_nullable
              as String,
      loginIp: null == loginIp
          ? _value.loginIp
          : loginIp // ignore: cast_nullable_to_non_nullable
              as String,
      userName: null == userName
          ? _value.userName
          : userName // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LoginLogItemImplCopyWith<$Res>
    implements $LoginLogItemCopyWith<$Res> {
  factory _$$LoginLogItemImplCopyWith(
          _$LoginLogItemImpl value, $Res Function(_$LoginLogItemImpl) then) =
      __$$LoginLogItemImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      @JsonKey(name: 'created_at') String createdAt,
      @JsonKey(name: 'ip_location') String ipLocation,
      @JsonKey(name: 'login_ip') String loginIp,
      @JsonKey(name: 'user_name') String userName});
}

/// @nodoc
class __$$LoginLogItemImplCopyWithImpl<$Res>
    extends _$LoginLogItemCopyWithImpl<$Res, _$LoginLogItemImpl>
    implements _$$LoginLogItemImplCopyWith<$Res> {
  __$$LoginLogItemImplCopyWithImpl(
      _$LoginLogItemImpl _value, $Res Function(_$LoginLogItemImpl) _then)
      : super(_value, _then);

  /// Create a copy of LoginLogItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? createdAt = null,
    Object? ipLocation = null,
    Object? loginIp = null,
    Object? userName = null,
  }) {
    return _then(_$LoginLogItemImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as String,
      ipLocation: null == ipLocation
          ? _value.ipLocation
          : ipLocation // ignore: cast_nullable_to_non_nullable
              as String,
      loginIp: null == loginIp
          ? _value.loginIp
          : loginIp // ignore: cast_nullable_to_non_nullable
              as String,
      userName: null == userName
          ? _value.userName
          : userName // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$LoginLogItemImpl implements _LoginLogItem {
  const _$LoginLogItemImpl(
      {required this.id,
      @JsonKey(name: 'created_at') required this.createdAt,
      @JsonKey(name: 'ip_location') required this.ipLocation,
      @JsonKey(name: 'login_ip') required this.loginIp,
      @JsonKey(name: 'user_name') required this.userName});

  factory _$LoginLogItemImpl.fromJson(Map<String, dynamic> json) =>
      _$$LoginLogItemImplFromJson(json);

  @override
  final int id;
  @override
  @JsonKey(name: 'created_at')
  final String createdAt;
  @override
  @JsonKey(name: 'ip_location')
  final String ipLocation;
  @override
  @JsonKey(name: 'login_ip')
  final String loginIp;
// IP地址的整型表示
  @override
  @JsonKey(name: 'user_name')
  final String userName;

  @override
  String toString() {
    return 'LoginLogItem(id: $id, createdAt: $createdAt, ipLocation: $ipLocation, loginIp: $loginIp, userName: $userName)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoginLogItemImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.ipLocation, ipLocation) ||
                other.ipLocation == ipLocation) &&
            (identical(other.loginIp, loginIp) || other.loginIp == loginIp) &&
            (identical(other.userName, userName) ||
                other.userName == userName));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, createdAt, ipLocation, loginIp, userName);

  /// Create a copy of LoginLogItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoginLogItemImplCopyWith<_$LoginLogItemImpl> get copyWith =>
      __$$LoginLogItemImplCopyWithImpl<_$LoginLogItemImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LoginLogItemImplToJson(
      this,
    );
  }
}

abstract class _LoginLogItem implements LoginLogItem {
  const factory _LoginLogItem(
          {required final int id,
          @JsonKey(name: 'created_at') required final String createdAt,
          @JsonKey(name: 'ip_location') required final String ipLocation,
          @JsonKey(name: 'login_ip') required final String loginIp,
          @JsonKey(name: 'user_name') required final String userName}) =
      _$LoginLogItemImpl;

  factory _LoginLogItem.fromJson(Map<String, dynamic> json) =
      _$LoginLogItemImpl.fromJson;

  @override
  int get id;
  @override
  @JsonKey(name: 'created_at')
  String get createdAt;
  @override
  @JsonKey(name: 'ip_location')
  String get ipLocation;
  @override
  @JsonKey(name: 'login_ip')
  String get loginIp; // IP地址的整型表示
  @override
  @JsonKey(name: 'user_name')
  String get userName;

  /// Create a copy of LoginLogItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoginLogItemImplCopyWith<_$LoginLogItemImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

OperationLogRequest _$OperationLogRequestFromJson(Map<String, dynamic> json) {
  return _OperationLogRequest.fromJson(json);
}

/// @nodoc
mixin _$OperationLogRequest {
  @JsonKey(name: 'user_id')
  int? get userId => throw _privateConstructorUsedError; // 用户ID
  @JsonKey(name: 'action')
  int? get action => throw _privateConstructorUsedError; // 操作类型筛选
  @JsonKey(name: 'category')
  int? get category => throw _privateConstructorUsedError; // 操作类型筛选
  @JsonKey(name: 'target')
  String? get target =>
      throw _privateConstructorUsedError; // 操作目标筛选, 例如user_management
  @JsonKey(name: 'limit')
  int? get limit => throw _privateConstructorUsedError; // 每页数量，默认100，最大500
  @JsonKey(name: 'offset')
  int? get offset => throw _privateConstructorUsedError; // 偏移量，默认0
  @JsonKey(name: 'start_time')
  String? get startTime => throw _privateConstructorUsedError; // 开始时间，RFC3339格式
  @JsonKey(name: 'end_time')
  String? get endTime => throw _privateConstructorUsedError;

  /// Serializes this OperationLogRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of OperationLogRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OperationLogRequestCopyWith<OperationLogRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OperationLogRequestCopyWith<$Res> {
  factory $OperationLogRequestCopyWith(
          OperationLogRequest value, $Res Function(OperationLogRequest) then) =
      _$OperationLogRequestCopyWithImpl<$Res, OperationLogRequest>;
  @useResult
  $Res call(
      {@JsonKey(name: 'user_id') int? userId,
      @JsonKey(name: 'action') int? action,
      @JsonKey(name: 'category') int? category,
      @JsonKey(name: 'target') String? target,
      @JsonKey(name: 'limit') int? limit,
      @JsonKey(name: 'offset') int? offset,
      @JsonKey(name: 'start_time') String? startTime,
      @JsonKey(name: 'end_time') String? endTime});
}

/// @nodoc
class _$OperationLogRequestCopyWithImpl<$Res, $Val extends OperationLogRequest>
    implements $OperationLogRequestCopyWith<$Res> {
  _$OperationLogRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OperationLogRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = freezed,
    Object? action = freezed,
    Object? category = freezed,
    Object? target = freezed,
    Object? limit = freezed,
    Object? offset = freezed,
    Object? startTime = freezed,
    Object? endTime = freezed,
  }) {
    return _then(_value.copyWith(
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int?,
      action: freezed == action
          ? _value.action
          : action // ignore: cast_nullable_to_non_nullable
              as int?,
      category: freezed == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as int?,
      target: freezed == target
          ? _value.target
          : target // ignore: cast_nullable_to_non_nullable
              as String?,
      limit: freezed == limit
          ? _value.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int?,
      offset: freezed == offset
          ? _value.offset
          : offset // ignore: cast_nullable_to_non_nullable
              as int?,
      startTime: freezed == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as String?,
      endTime: freezed == endTime
          ? _value.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OperationLogRequestImplCopyWith<$Res>
    implements $OperationLogRequestCopyWith<$Res> {
  factory _$$OperationLogRequestImplCopyWith(_$OperationLogRequestImpl value,
          $Res Function(_$OperationLogRequestImpl) then) =
      __$$OperationLogRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'user_id') int? userId,
      @JsonKey(name: 'action') int? action,
      @JsonKey(name: 'category') int? category,
      @JsonKey(name: 'target') String? target,
      @JsonKey(name: 'limit') int? limit,
      @JsonKey(name: 'offset') int? offset,
      @JsonKey(name: 'start_time') String? startTime,
      @JsonKey(name: 'end_time') String? endTime});
}

/// @nodoc
class __$$OperationLogRequestImplCopyWithImpl<$Res>
    extends _$OperationLogRequestCopyWithImpl<$Res, _$OperationLogRequestImpl>
    implements _$$OperationLogRequestImplCopyWith<$Res> {
  __$$OperationLogRequestImplCopyWithImpl(_$OperationLogRequestImpl _value,
      $Res Function(_$OperationLogRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of OperationLogRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = freezed,
    Object? action = freezed,
    Object? category = freezed,
    Object? target = freezed,
    Object? limit = freezed,
    Object? offset = freezed,
    Object? startTime = freezed,
    Object? endTime = freezed,
  }) {
    return _then(_$OperationLogRequestImpl(
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int?,
      action: freezed == action
          ? _value.action
          : action // ignore: cast_nullable_to_non_nullable
              as int?,
      category: freezed == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as int?,
      target: freezed == target
          ? _value.target
          : target // ignore: cast_nullable_to_non_nullable
              as String?,
      limit: freezed == limit
          ? _value.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int?,
      offset: freezed == offset
          ? _value.offset
          : offset // ignore: cast_nullable_to_non_nullable
              as int?,
      startTime: freezed == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as String?,
      endTime: freezed == endTime
          ? _value.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OperationLogRequestImpl implements _OperationLogRequest {
  const _$OperationLogRequestImpl(
      {@JsonKey(name: 'user_id') this.userId,
      @JsonKey(name: 'action') this.action,
      @JsonKey(name: 'category') this.category,
      @JsonKey(name: 'target') this.target,
      @JsonKey(name: 'limit') this.limit,
      @JsonKey(name: 'offset') this.offset,
      @JsonKey(name: 'start_time') this.startTime,
      @JsonKey(name: 'end_time') this.endTime});

  factory _$OperationLogRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$OperationLogRequestImplFromJson(json);

  @override
  @JsonKey(name: 'user_id')
  final int? userId;
// 用户ID
  @override
  @JsonKey(name: 'action')
  final int? action;
// 操作类型筛选
  @override
  @JsonKey(name: 'category')
  final int? category;
// 操作类型筛选
  @override
  @JsonKey(name: 'target')
  final String? target;
// 操作目标筛选, 例如user_management
  @override
  @JsonKey(name: 'limit')
  final int? limit;
// 每页数量，默认100，最大500
  @override
  @JsonKey(name: 'offset')
  final int? offset;
// 偏移量，默认0
  @override
  @JsonKey(name: 'start_time')
  final String? startTime;
// 开始时间，RFC3339格式
  @override
  @JsonKey(name: 'end_time')
  final String? endTime;

  @override
  String toString() {
    return 'OperationLogRequest(userId: $userId, action: $action, category: $category, target: $target, limit: $limit, offset: $offset, startTime: $startTime, endTime: $endTime)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OperationLogRequestImpl &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.action, action) || other.action == action) &&
            (identical(other.category, category) ||
                other.category == category) &&
            (identical(other.target, target) || other.target == target) &&
            (identical(other.limit, limit) || other.limit == limit) &&
            (identical(other.offset, offset) || other.offset == offset) &&
            (identical(other.startTime, startTime) ||
                other.startTime == startTime) &&
            (identical(other.endTime, endTime) || other.endTime == endTime));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, userId, action, category, target,
      limit, offset, startTime, endTime);

  /// Create a copy of OperationLogRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OperationLogRequestImplCopyWith<_$OperationLogRequestImpl> get copyWith =>
      __$$OperationLogRequestImplCopyWithImpl<_$OperationLogRequestImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OperationLogRequestImplToJson(
      this,
    );
  }
}

abstract class _OperationLogRequest implements OperationLogRequest {
  const factory _OperationLogRequest(
          {@JsonKey(name: 'user_id') final int? userId,
          @JsonKey(name: 'action') final int? action,
          @JsonKey(name: 'category') final int? category,
          @JsonKey(name: 'target') final String? target,
          @JsonKey(name: 'limit') final int? limit,
          @JsonKey(name: 'offset') final int? offset,
          @JsonKey(name: 'start_time') final String? startTime,
          @JsonKey(name: 'end_time') final String? endTime}) =
      _$OperationLogRequestImpl;

  factory _OperationLogRequest.fromJson(Map<String, dynamic> json) =
      _$OperationLogRequestImpl.fromJson;

  @override
  @JsonKey(name: 'user_id')
  int? get userId; // 用户ID
  @override
  @JsonKey(name: 'action')
  int? get action; // 操作类型筛选
  @override
  @JsonKey(name: 'category')
  int? get category; // 操作类型筛选
  @override
  @JsonKey(name: 'target')
  String? get target; // 操作目标筛选, 例如user_management
  @override
  @JsonKey(name: 'limit')
  int? get limit; // 每页数量，默认100，最大500
  @override
  @JsonKey(name: 'offset')
  int? get offset; // 偏移量，默认0
  @override
  @JsonKey(name: 'start_time')
  String? get startTime; // 开始时间，RFC3339格式
  @override
  @JsonKey(name: 'end_time')
  String? get endTime;

  /// Create a copy of OperationLogRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OperationLogRequestImplCopyWith<_$OperationLogRequestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

OperationLogModel _$OperationLogModelFromJson(Map<String, dynamic> json) {
  return _OperationLogModel.fromJson(json);
}

/// @nodoc
mixin _$OperationLogModel {
  List<OperationLogItem> get logs => throw _privateConstructorUsedError;
  int get total => throw _privateConstructorUsedError;

  /// Serializes this OperationLogModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of OperationLogModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OperationLogModelCopyWith<OperationLogModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OperationLogModelCopyWith<$Res> {
  factory $OperationLogModelCopyWith(
          OperationLogModel value, $Res Function(OperationLogModel) then) =
      _$OperationLogModelCopyWithImpl<$Res, OperationLogModel>;
  @useResult
  $Res call({List<OperationLogItem> logs, int total});
}

/// @nodoc
class _$OperationLogModelCopyWithImpl<$Res, $Val extends OperationLogModel>
    implements $OperationLogModelCopyWith<$Res> {
  _$OperationLogModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OperationLogModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? logs = null,
    Object? total = null,
  }) {
    return _then(_value.copyWith(
      logs: null == logs
          ? _value.logs
          : logs // ignore: cast_nullable_to_non_nullable
              as List<OperationLogItem>,
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OperationLogModelImplCopyWith<$Res>
    implements $OperationLogModelCopyWith<$Res> {
  factory _$$OperationLogModelImplCopyWith(_$OperationLogModelImpl value,
          $Res Function(_$OperationLogModelImpl) then) =
      __$$OperationLogModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<OperationLogItem> logs, int total});
}

/// @nodoc
class __$$OperationLogModelImplCopyWithImpl<$Res>
    extends _$OperationLogModelCopyWithImpl<$Res, _$OperationLogModelImpl>
    implements _$$OperationLogModelImplCopyWith<$Res> {
  __$$OperationLogModelImplCopyWithImpl(_$OperationLogModelImpl _value,
      $Res Function(_$OperationLogModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of OperationLogModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? logs = null,
    Object? total = null,
  }) {
    return _then(_$OperationLogModelImpl(
      logs: null == logs
          ? _value._logs
          : logs // ignore: cast_nullable_to_non_nullable
              as List<OperationLogItem>,
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OperationLogModelImpl implements _OperationLogModel {
  const _$OperationLogModelImpl(
      {required final List<OperationLogItem> logs, required this.total})
      : _logs = logs;

  factory _$OperationLogModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$OperationLogModelImplFromJson(json);

  final List<OperationLogItem> _logs;
  @override
  List<OperationLogItem> get logs {
    if (_logs is EqualUnmodifiableListView) return _logs;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_logs);
  }

  @override
  final int total;

  @override
  String toString() {
    return 'OperationLogModel(logs: $logs, total: $total)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OperationLogModelImpl &&
            const DeepCollectionEquality().equals(other._logs, _logs) &&
            (identical(other.total, total) || other.total == total));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_logs), total);

  /// Create a copy of OperationLogModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OperationLogModelImplCopyWith<_$OperationLogModelImpl> get copyWith =>
      __$$OperationLogModelImplCopyWithImpl<_$OperationLogModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OperationLogModelImplToJson(
      this,
    );
  }
}

abstract class _OperationLogModel implements OperationLogModel {
  const factory _OperationLogModel(
      {required final List<OperationLogItem> logs,
      required final int total}) = _$OperationLogModelImpl;

  factory _OperationLogModel.fromJson(Map<String, dynamic> json) =
      _$OperationLogModelImpl.fromJson;

  @override
  List<OperationLogItem> get logs;
  @override
  int get total;

  /// Create a copy of OperationLogModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OperationLogModelImplCopyWith<_$OperationLogModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

OperationLogItem _$OperationLogItemFromJson(Map<String, dynamic> json) {
  return _OperationLogItem.fromJson(json);
}

/// @nodoc
mixin _$OperationLogItem {
  int get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'user_id')
  int get userId => throw _privateConstructorUsedError;
  @JsonKey(name: 'team_id')
  int get teamId => throw _privateConstructorUsedError;
  @JsonKey(name: 'action')
  int get action => throw _privateConstructorUsedError;
  @JsonKey(name: 'target')
  String get target => throw _privateConstructorUsedError;
  @JsonKey(name: 'category')
  int get category => throw _privateConstructorUsedError;
  @JsonKey(name: 'created_at')
  String get createdAt => throw _privateConstructorUsedError;

  /// Serializes this OperationLogItem to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of OperationLogItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OperationLogItemCopyWith<OperationLogItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OperationLogItemCopyWith<$Res> {
  factory $OperationLogItemCopyWith(
          OperationLogItem value, $Res Function(OperationLogItem) then) =
      _$OperationLogItemCopyWithImpl<$Res, OperationLogItem>;
  @useResult
  $Res call(
      {int id,
      @JsonKey(name: 'user_id') int userId,
      @JsonKey(name: 'team_id') int teamId,
      @JsonKey(name: 'action') int action,
      @JsonKey(name: 'target') String target,
      @JsonKey(name: 'category') int category,
      @JsonKey(name: 'created_at') String createdAt});
}

/// @nodoc
class _$OperationLogItemCopyWithImpl<$Res, $Val extends OperationLogItem>
    implements $OperationLogItemCopyWith<$Res> {
  _$OperationLogItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OperationLogItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? teamId = null,
    Object? action = null,
    Object? target = null,
    Object? category = null,
    Object? createdAt = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int,
      teamId: null == teamId
          ? _value.teamId
          : teamId // ignore: cast_nullable_to_non_nullable
              as int,
      action: null == action
          ? _value.action
          : action // ignore: cast_nullable_to_non_nullable
              as int,
      target: null == target
          ? _value.target
          : target // ignore: cast_nullable_to_non_nullable
              as String,
      category: null == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as int,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OperationLogItemImplCopyWith<$Res>
    implements $OperationLogItemCopyWith<$Res> {
  factory _$$OperationLogItemImplCopyWith(_$OperationLogItemImpl value,
          $Res Function(_$OperationLogItemImpl) then) =
      __$$OperationLogItemImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      @JsonKey(name: 'user_id') int userId,
      @JsonKey(name: 'team_id') int teamId,
      @JsonKey(name: 'action') int action,
      @JsonKey(name: 'target') String target,
      @JsonKey(name: 'category') int category,
      @JsonKey(name: 'created_at') String createdAt});
}

/// @nodoc
class __$$OperationLogItemImplCopyWithImpl<$Res>
    extends _$OperationLogItemCopyWithImpl<$Res, _$OperationLogItemImpl>
    implements _$$OperationLogItemImplCopyWith<$Res> {
  __$$OperationLogItemImplCopyWithImpl(_$OperationLogItemImpl _value,
      $Res Function(_$OperationLogItemImpl) _then)
      : super(_value, _then);

  /// Create a copy of OperationLogItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? teamId = null,
    Object? action = null,
    Object? target = null,
    Object? category = null,
    Object? createdAt = null,
  }) {
    return _then(_$OperationLogItemImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int,
      teamId: null == teamId
          ? _value.teamId
          : teamId // ignore: cast_nullable_to_non_nullable
              as int,
      action: null == action
          ? _value.action
          : action // ignore: cast_nullable_to_non_nullable
              as int,
      target: null == target
          ? _value.target
          : target // ignore: cast_nullable_to_non_nullable
              as String,
      category: null == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as int,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OperationLogItemImpl implements _OperationLogItem {
  const _$OperationLogItemImpl(
      {required this.id,
      @JsonKey(name: 'user_id') required this.userId,
      @JsonKey(name: 'team_id') required this.teamId,
      @JsonKey(name: 'action') required this.action,
      @JsonKey(name: 'target') required this.target,
      @JsonKey(name: 'category') required this.category,
      @JsonKey(name: 'created_at') required this.createdAt});

  factory _$OperationLogItemImpl.fromJson(Map<String, dynamic> json) =>
      _$$OperationLogItemImplFromJson(json);

  @override
  final int id;
  @override
  @JsonKey(name: 'user_id')
  final int userId;
  @override
  @JsonKey(name: 'team_id')
  final int teamId;
  @override
  @JsonKey(name: 'action')
  final int action;
  @override
  @JsonKey(name: 'target')
  final String target;
  @override
  @JsonKey(name: 'category')
  final int category;
  @override
  @JsonKey(name: 'created_at')
  final String createdAt;

  @override
  String toString() {
    return 'OperationLogItem(id: $id, userId: $userId, teamId: $teamId, action: $action, target: $target, category: $category, createdAt: $createdAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OperationLogItemImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.teamId, teamId) || other.teamId == teamId) &&
            (identical(other.action, action) || other.action == action) &&
            (identical(other.target, target) || other.target == target) &&
            (identical(other.category, category) ||
                other.category == category) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, userId, teamId, action, target, category, createdAt);

  /// Create a copy of OperationLogItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OperationLogItemImplCopyWith<_$OperationLogItemImpl> get copyWith =>
      __$$OperationLogItemImplCopyWithImpl<_$OperationLogItemImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OperationLogItemImplToJson(
      this,
    );
  }
}

abstract class _OperationLogItem implements OperationLogItem {
  const factory _OperationLogItem(
          {required final int id,
          @JsonKey(name: 'user_id') required final int userId,
          @JsonKey(name: 'team_id') required final int teamId,
          @JsonKey(name: 'action') required final int action,
          @JsonKey(name: 'target') required final String target,
          @JsonKey(name: 'category') required final int category,
          @JsonKey(name: 'created_at') required final String createdAt}) =
      _$OperationLogItemImpl;

  factory _OperationLogItem.fromJson(Map<String, dynamic> json) =
      _$OperationLogItemImpl.fromJson;

  @override
  int get id;
  @override
  @JsonKey(name: 'user_id')
  int get userId;
  @override
  @JsonKey(name: 'team_id')
  int get teamId;
  @override
  @JsonKey(name: 'action')
  int get action;
  @override
  @JsonKey(name: 'target')
  String get target;
  @override
  @JsonKey(name: 'category')
  int get category;
  @override
  @JsonKey(name: 'created_at')
  String get createdAt;

  /// Create a copy of OperationLogItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OperationLogItemImplCopyWith<_$OperationLogItemImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
