// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'browser_window_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

BrowserWindowModel _$BrowserWindowModelFromJson(Map<String, dynamic> json) {
  return _BrowserWindowModel.fromJson(json);
}

/// @nodoc
mixin _$BrowserWindowModel {
  @JsonKey(name: 'environments')
  List<BrowserWindowItem>? get windows =>
      throw _privateConstructorUsedError; // 浏览器窗口列表
  int get total => throw _privateConstructorUsedError;

  /// Serializes this BrowserWindowModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of BrowserWindowModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BrowserWindowModelCopyWith<BrowserWindowModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BrowserWindowModelCopyWith<$Res> {
  factory $BrowserWindowModelCopyWith(
          BrowserWindowModel value, $Res Function(BrowserWindowModel) then) =
      _$BrowserWindowModelCopyWithImpl<$Res, BrowserWindowModel>;
  @useResult
  $Res call(
      {@JsonKey(name: 'environments') List<BrowserWindowItem>? windows,
      int total});
}

/// @nodoc
class _$BrowserWindowModelCopyWithImpl<$Res, $Val extends BrowserWindowModel>
    implements $BrowserWindowModelCopyWith<$Res> {
  _$BrowserWindowModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BrowserWindowModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? windows = freezed,
    Object? total = null,
  }) {
    return _then(_value.copyWith(
      windows: freezed == windows
          ? _value.windows
          : windows // ignore: cast_nullable_to_non_nullable
              as List<BrowserWindowItem>?,
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BrowserWindowModelImplCopyWith<$Res>
    implements $BrowserWindowModelCopyWith<$Res> {
  factory _$$BrowserWindowModelImplCopyWith(_$BrowserWindowModelImpl value,
          $Res Function(_$BrowserWindowModelImpl) then) =
      __$$BrowserWindowModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'environments') List<BrowserWindowItem>? windows,
      int total});
}

/// @nodoc
class __$$BrowserWindowModelImplCopyWithImpl<$Res>
    extends _$BrowserWindowModelCopyWithImpl<$Res, _$BrowserWindowModelImpl>
    implements _$$BrowserWindowModelImplCopyWith<$Res> {
  __$$BrowserWindowModelImplCopyWithImpl(_$BrowserWindowModelImpl _value,
      $Res Function(_$BrowserWindowModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of BrowserWindowModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? windows = freezed,
    Object? total = null,
  }) {
    return _then(_$BrowserWindowModelImpl(
      windows: freezed == windows
          ? _value._windows
          : windows // ignore: cast_nullable_to_non_nullable
              as List<BrowserWindowItem>?,
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BrowserWindowModelImpl implements _BrowserWindowModel {
  const _$BrowserWindowModelImpl(
      {@JsonKey(name: 'environments') final List<BrowserWindowItem>? windows,
      required this.total})
      : _windows = windows;

  factory _$BrowserWindowModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$BrowserWindowModelImplFromJson(json);

  final List<BrowserWindowItem>? _windows;
  @override
  @JsonKey(name: 'environments')
  List<BrowserWindowItem>? get windows {
    final value = _windows;
    if (value == null) return null;
    if (_windows is EqualUnmodifiableListView) return _windows;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

// 浏览器窗口列表
  @override
  final int total;

  @override
  String toString() {
    return 'BrowserWindowModel(windows: $windows, total: $total)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BrowserWindowModelImpl &&
            const DeepCollectionEquality().equals(other._windows, _windows) &&
            (identical(other.total, total) || other.total == total));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_windows), total);

  /// Create a copy of BrowserWindowModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BrowserWindowModelImplCopyWith<_$BrowserWindowModelImpl> get copyWith =>
      __$$BrowserWindowModelImplCopyWithImpl<_$BrowserWindowModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BrowserWindowModelImplToJson(
      this,
    );
  }
}

abstract class _BrowserWindowModel implements BrowserWindowModel {
  const factory _BrowserWindowModel(
      {@JsonKey(name: 'environments') final List<BrowserWindowItem>? windows,
      required final int total}) = _$BrowserWindowModelImpl;

  factory _BrowserWindowModel.fromJson(Map<String, dynamic> json) =
      _$BrowserWindowModelImpl.fromJson;

  @override
  @JsonKey(name: 'environments')
  List<BrowserWindowItem>? get windows; // 浏览器窗口列表
  @override
  int get total;

  /// Create a copy of BrowserWindowModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BrowserWindowModelImplCopyWith<_$BrowserWindowModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

BrowserWindowRequest _$BrowserWindowRequestFromJson(Map<String, dynamic> json) {
  return _BrowserWindowRequest.fromJson(json);
}

/// @nodoc
mixin _$BrowserWindowRequest {
  @JsonKey(includeIfNull: false)
  String? get name => throw _privateConstructorUsedError; // 环境名称
  @JsonKey(name: 'user_id', includeIfNull: false)
  int? get userId => throw _privateConstructorUsedError; // 用户ID
  @JsonKey(name: 'group_id', includeIfNull: false)
  int? get groupId => throw _privateConstructorUsedError; // 分组ID
  @JsonKey(includeIfNull: false)
  int? get limit => throw _privateConstructorUsedError; // 限制数量，范围1-500
  @JsonKey(includeIfNull: false)
  int? get offset => throw _privateConstructorUsedError;

  /// Serializes this BrowserWindowRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of BrowserWindowRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BrowserWindowRequestCopyWith<BrowserWindowRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BrowserWindowRequestCopyWith<$Res> {
  factory $BrowserWindowRequestCopyWith(BrowserWindowRequest value,
          $Res Function(BrowserWindowRequest) then) =
      _$BrowserWindowRequestCopyWithImpl<$Res, BrowserWindowRequest>;
  @useResult
  $Res call(
      {@JsonKey(includeIfNull: false) String? name,
      @JsonKey(name: 'user_id', includeIfNull: false) int? userId,
      @JsonKey(name: 'group_id', includeIfNull: false) int? groupId,
      @JsonKey(includeIfNull: false) int? limit,
      @JsonKey(includeIfNull: false) int? offset});
}

/// @nodoc
class _$BrowserWindowRequestCopyWithImpl<$Res,
        $Val extends BrowserWindowRequest>
    implements $BrowserWindowRequestCopyWith<$Res> {
  _$BrowserWindowRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BrowserWindowRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = freezed,
    Object? userId = freezed,
    Object? groupId = freezed,
    Object? limit = freezed,
    Object? offset = freezed,
  }) {
    return _then(_value.copyWith(
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int?,
      groupId: freezed == groupId
          ? _value.groupId
          : groupId // ignore: cast_nullable_to_non_nullable
              as int?,
      limit: freezed == limit
          ? _value.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int?,
      offset: freezed == offset
          ? _value.offset
          : offset // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BrowserWindowRequestImplCopyWith<$Res>
    implements $BrowserWindowRequestCopyWith<$Res> {
  factory _$$BrowserWindowRequestImplCopyWith(_$BrowserWindowRequestImpl value,
          $Res Function(_$BrowserWindowRequestImpl) then) =
      __$$BrowserWindowRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(includeIfNull: false) String? name,
      @JsonKey(name: 'user_id', includeIfNull: false) int? userId,
      @JsonKey(name: 'group_id', includeIfNull: false) int? groupId,
      @JsonKey(includeIfNull: false) int? limit,
      @JsonKey(includeIfNull: false) int? offset});
}

/// @nodoc
class __$$BrowserWindowRequestImplCopyWithImpl<$Res>
    extends _$BrowserWindowRequestCopyWithImpl<$Res, _$BrowserWindowRequestImpl>
    implements _$$BrowserWindowRequestImplCopyWith<$Res> {
  __$$BrowserWindowRequestImplCopyWithImpl(_$BrowserWindowRequestImpl _value,
      $Res Function(_$BrowserWindowRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of BrowserWindowRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = freezed,
    Object? userId = freezed,
    Object? groupId = freezed,
    Object? limit = freezed,
    Object? offset = freezed,
  }) {
    return _then(_$BrowserWindowRequestImpl(
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int?,
      groupId: freezed == groupId
          ? _value.groupId
          : groupId // ignore: cast_nullable_to_non_nullable
              as int?,
      limit: freezed == limit
          ? _value.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int?,
      offset: freezed == offset
          ? _value.offset
          : offset // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BrowserWindowRequestImpl implements _BrowserWindowRequest {
  const _$BrowserWindowRequestImpl(
      {@JsonKey(includeIfNull: false) this.name,
      @JsonKey(name: 'user_id', includeIfNull: false) this.userId,
      @JsonKey(name: 'group_id', includeIfNull: false) this.groupId,
      @JsonKey(includeIfNull: false) this.limit,
      @JsonKey(includeIfNull: false) this.offset});

  factory _$BrowserWindowRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$BrowserWindowRequestImplFromJson(json);

  @override
  @JsonKey(includeIfNull: false)
  final String? name;
// 环境名称
  @override
  @JsonKey(name: 'user_id', includeIfNull: false)
  final int? userId;
// 用户ID
  @override
  @JsonKey(name: 'group_id', includeIfNull: false)
  final int? groupId;
// 分组ID
  @override
  @JsonKey(includeIfNull: false)
  final int? limit;
// 限制数量，范围1-500
  @override
  @JsonKey(includeIfNull: false)
  final int? offset;

  @override
  String toString() {
    return 'BrowserWindowRequest(name: $name, userId: $userId, groupId: $groupId, limit: $limit, offset: $offset)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BrowserWindowRequestImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.groupId, groupId) || other.groupId == groupId) &&
            (identical(other.limit, limit) || other.limit == limit) &&
            (identical(other.offset, offset) || other.offset == offset));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, name, userId, groupId, limit, offset);

  /// Create a copy of BrowserWindowRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BrowserWindowRequestImplCopyWith<_$BrowserWindowRequestImpl>
      get copyWith =>
          __$$BrowserWindowRequestImplCopyWithImpl<_$BrowserWindowRequestImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BrowserWindowRequestImplToJson(
      this,
    );
  }
}

abstract class _BrowserWindowRequest implements BrowserWindowRequest {
  const factory _BrowserWindowRequest(
          {@JsonKey(includeIfNull: false) final String? name,
          @JsonKey(name: 'user_id', includeIfNull: false) final int? userId,
          @JsonKey(name: 'group_id', includeIfNull: false) final int? groupId,
          @JsonKey(includeIfNull: false) final int? limit,
          @JsonKey(includeIfNull: false) final int? offset}) =
      _$BrowserWindowRequestImpl;

  factory _BrowserWindowRequest.fromJson(Map<String, dynamic> json) =
      _$BrowserWindowRequestImpl.fromJson;

  @override
  @JsonKey(includeIfNull: false)
  String? get name; // 环境名称
  @override
  @JsonKey(name: 'user_id', includeIfNull: false)
  int? get userId; // 用户ID
  @override
  @JsonKey(name: 'group_id', includeIfNull: false)
  int? get groupId; // 分组ID
  @override
  @JsonKey(includeIfNull: false)
  int? get limit; // 限制数量，范围1-500
  @override
  @JsonKey(includeIfNull: false)
  int? get offset;

  /// Create a copy of BrowserWindowRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BrowserWindowRequestImplCopyWith<_$BrowserWindowRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}

DeletedBrowserWindowRequest _$DeletedBrowserWindowRequestFromJson(
    Map<String, dynamic> json) {
  return _DeletedBrowserWindowRequest.fromJson(json);
}

/// @nodoc
mixin _$DeletedBrowserWindowRequest {
  @JsonKey(name: 'group_id', includeIfNull: false)
  int? get groupId => throw _privateConstructorUsedError; // 分组ID
  @JsonKey(includeIfNull: false)
  int? get limit => throw _privateConstructorUsedError; // 限制数量，范围1-500
  @JsonKey(includeIfNull: false)
  int? get offset => throw _privateConstructorUsedError; // 偏移量，最小为0
  @JsonKey(includeIfNull: false)
  String? get name => throw _privateConstructorUsedError; // 环境名称
  @JsonKey(name: 'user_id', includeIfNull: false)
  int? get userId => throw _privateConstructorUsedError;

  /// Serializes this DeletedBrowserWindowRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DeletedBrowserWindowRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DeletedBrowserWindowRequestCopyWith<DeletedBrowserWindowRequest>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DeletedBrowserWindowRequestCopyWith<$Res> {
  factory $DeletedBrowserWindowRequestCopyWith(
          DeletedBrowserWindowRequest value,
          $Res Function(DeletedBrowserWindowRequest) then) =
      _$DeletedBrowserWindowRequestCopyWithImpl<$Res,
          DeletedBrowserWindowRequest>;
  @useResult
  $Res call(
      {@JsonKey(name: 'group_id', includeIfNull: false) int? groupId,
      @JsonKey(includeIfNull: false) int? limit,
      @JsonKey(includeIfNull: false) int? offset,
      @JsonKey(includeIfNull: false) String? name,
      @JsonKey(name: 'user_id', includeIfNull: false) int? userId});
}

/// @nodoc
class _$DeletedBrowserWindowRequestCopyWithImpl<$Res,
        $Val extends DeletedBrowserWindowRequest>
    implements $DeletedBrowserWindowRequestCopyWith<$Res> {
  _$DeletedBrowserWindowRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DeletedBrowserWindowRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? groupId = freezed,
    Object? limit = freezed,
    Object? offset = freezed,
    Object? name = freezed,
    Object? userId = freezed,
  }) {
    return _then(_value.copyWith(
      groupId: freezed == groupId
          ? _value.groupId
          : groupId // ignore: cast_nullable_to_non_nullable
              as int?,
      limit: freezed == limit
          ? _value.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int?,
      offset: freezed == offset
          ? _value.offset
          : offset // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DeletedBrowserWindowRequestImplCopyWith<$Res>
    implements $DeletedBrowserWindowRequestCopyWith<$Res> {
  factory _$$DeletedBrowserWindowRequestImplCopyWith(
          _$DeletedBrowserWindowRequestImpl value,
          $Res Function(_$DeletedBrowserWindowRequestImpl) then) =
      __$$DeletedBrowserWindowRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'group_id', includeIfNull: false) int? groupId,
      @JsonKey(includeIfNull: false) int? limit,
      @JsonKey(includeIfNull: false) int? offset,
      @JsonKey(includeIfNull: false) String? name,
      @JsonKey(name: 'user_id', includeIfNull: false) int? userId});
}

/// @nodoc
class __$$DeletedBrowserWindowRequestImplCopyWithImpl<$Res>
    extends _$DeletedBrowserWindowRequestCopyWithImpl<$Res,
        _$DeletedBrowserWindowRequestImpl>
    implements _$$DeletedBrowserWindowRequestImplCopyWith<$Res> {
  __$$DeletedBrowserWindowRequestImplCopyWithImpl(
      _$DeletedBrowserWindowRequestImpl _value,
      $Res Function(_$DeletedBrowserWindowRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeletedBrowserWindowRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? groupId = freezed,
    Object? limit = freezed,
    Object? offset = freezed,
    Object? name = freezed,
    Object? userId = freezed,
  }) {
    return _then(_$DeletedBrowserWindowRequestImpl(
      groupId: freezed == groupId
          ? _value.groupId
          : groupId // ignore: cast_nullable_to_non_nullable
              as int?,
      limit: freezed == limit
          ? _value.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int?,
      offset: freezed == offset
          ? _value.offset
          : offset // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DeletedBrowserWindowRequestImpl
    implements _DeletedBrowserWindowRequest {
  const _$DeletedBrowserWindowRequestImpl(
      {@JsonKey(name: 'group_id', includeIfNull: false) this.groupId,
      @JsonKey(includeIfNull: false) this.limit,
      @JsonKey(includeIfNull: false) this.offset,
      @JsonKey(includeIfNull: false) this.name,
      @JsonKey(name: 'user_id', includeIfNull: false) this.userId});

  factory _$DeletedBrowserWindowRequestImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$DeletedBrowserWindowRequestImplFromJson(json);

  @override
  @JsonKey(name: 'group_id', includeIfNull: false)
  final int? groupId;
// 分组ID
  @override
  @JsonKey(includeIfNull: false)
  final int? limit;
// 限制数量，范围1-500
  @override
  @JsonKey(includeIfNull: false)
  final int? offset;
// 偏移量，最小为0
  @override
  @JsonKey(includeIfNull: false)
  final String? name;
// 环境名称
  @override
  @JsonKey(name: 'user_id', includeIfNull: false)
  final int? userId;

  @override
  String toString() {
    return 'DeletedBrowserWindowRequest(groupId: $groupId, limit: $limit, offset: $offset, name: $name, userId: $userId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeletedBrowserWindowRequestImpl &&
            (identical(other.groupId, groupId) || other.groupId == groupId) &&
            (identical(other.limit, limit) || other.limit == limit) &&
            (identical(other.offset, offset) || other.offset == offset) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.userId, userId) || other.userId == userId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, groupId, limit, offset, name, userId);

  /// Create a copy of DeletedBrowserWindowRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeletedBrowserWindowRequestImplCopyWith<_$DeletedBrowserWindowRequestImpl>
      get copyWith => __$$DeletedBrowserWindowRequestImplCopyWithImpl<
          _$DeletedBrowserWindowRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DeletedBrowserWindowRequestImplToJson(
      this,
    );
  }
}

abstract class _DeletedBrowserWindowRequest
    implements DeletedBrowserWindowRequest {
  const factory _DeletedBrowserWindowRequest(
          {@JsonKey(name: 'group_id', includeIfNull: false) final int? groupId,
          @JsonKey(includeIfNull: false) final int? limit,
          @JsonKey(includeIfNull: false) final int? offset,
          @JsonKey(includeIfNull: false) final String? name,
          @JsonKey(name: 'user_id', includeIfNull: false) final int? userId}) =
      _$DeletedBrowserWindowRequestImpl;

  factory _DeletedBrowserWindowRequest.fromJson(Map<String, dynamic> json) =
      _$DeletedBrowserWindowRequestImpl.fromJson;

  @override
  @JsonKey(name: 'group_id', includeIfNull: false)
  int? get groupId; // 分组ID
  @override
  @JsonKey(includeIfNull: false)
  int? get limit; // 限制数量，范围1-500
  @override
  @JsonKey(includeIfNull: false)
  int? get offset; // 偏移量，最小为0
  @override
  @JsonKey(includeIfNull: false)
  String? get name; // 环境名称
  @override
  @JsonKey(name: 'user_id', includeIfNull: false)
  int? get userId;

  /// Create a copy of DeletedBrowserWindowRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeletedBrowserWindowRequestImplCopyWith<_$DeletedBrowserWindowRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}

BrowserWindowItem _$BrowserWindowItemFromJson(Map<String, dynamic> json) {
  return _BrowserWindowItem.fromJson(json);
}

/// @nodoc
mixin _$BrowserWindowItem {
  int get id => throw _privateConstructorUsedError; // 编号
  @JsonKey(name: 'team_id')
  int get teamId => throw _privateConstructorUsedError; // 团队ID
  String get name => throw _privateConstructorUsedError; // 窗口名称
  @JsonKey(name: 'user_id')
  int get userId => throw _privateConstructorUsedError; // 用户ID
  @JsonKey(name: 'group_id')
  int get groupId => throw _privateConstructorUsedError; // 分组ID
  @JsonKey(name: 'group_name')
  String get groupName => throw _privateConstructorUsedError; // 所属分组名称
  ProxyInfo get proxy => throw _privateConstructorUsedError; // 代理信息
  @JsonKey(name: 'proxy_type')
  int get proxyType => throw _privateConstructorUsedError; // 代理类型
  String get platform => throw _privateConstructorUsedError; // 所属平台
  String get storage => throw _privateConstructorUsedError; // 存储信息
  String get tag => throw _privateConstructorUsedError; // 标签
  String? get comment => throw _privateConstructorUsedError; // 备注
  int? get sort => throw _privateConstructorUsedError; // 排序（可选）
  int? get size => throw _privateConstructorUsedError; // 大小（可选）
  @JsonKey(name: 'created_at')
  DateTime get createdAt => throw _privateConstructorUsedError; // 创建时间（必需）
  @JsonKey(name: 'updated_at')
  DateTime get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this BrowserWindowItem to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of BrowserWindowItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BrowserWindowItemCopyWith<BrowserWindowItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BrowserWindowItemCopyWith<$Res> {
  factory $BrowserWindowItemCopyWith(
          BrowserWindowItem value, $Res Function(BrowserWindowItem) then) =
      _$BrowserWindowItemCopyWithImpl<$Res, BrowserWindowItem>;
  @useResult
  $Res call(
      {int id,
      @JsonKey(name: 'team_id') int teamId,
      String name,
      @JsonKey(name: 'user_id') int userId,
      @JsonKey(name: 'group_id') int groupId,
      @JsonKey(name: 'group_name') String groupName,
      ProxyInfo proxy,
      @JsonKey(name: 'proxy_type') int proxyType,
      String platform,
      String storage,
      String tag,
      String? comment,
      int? sort,
      int? size,
      @JsonKey(name: 'created_at') DateTime createdAt,
      @JsonKey(name: 'updated_at') DateTime updatedAt});

  $ProxyInfoCopyWith<$Res> get proxy;
}

/// @nodoc
class _$BrowserWindowItemCopyWithImpl<$Res, $Val extends BrowserWindowItem>
    implements $BrowserWindowItemCopyWith<$Res> {
  _$BrowserWindowItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BrowserWindowItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? teamId = null,
    Object? name = null,
    Object? userId = null,
    Object? groupId = null,
    Object? groupName = null,
    Object? proxy = null,
    Object? proxyType = null,
    Object? platform = null,
    Object? storage = null,
    Object? tag = null,
    Object? comment = freezed,
    Object? sort = freezed,
    Object? size = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      teamId: null == teamId
          ? _value.teamId
          : teamId // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int,
      groupId: null == groupId
          ? _value.groupId
          : groupId // ignore: cast_nullable_to_non_nullable
              as int,
      groupName: null == groupName
          ? _value.groupName
          : groupName // ignore: cast_nullable_to_non_nullable
              as String,
      proxy: null == proxy
          ? _value.proxy
          : proxy // ignore: cast_nullable_to_non_nullable
              as ProxyInfo,
      proxyType: null == proxyType
          ? _value.proxyType
          : proxyType // ignore: cast_nullable_to_non_nullable
              as int,
      platform: null == platform
          ? _value.platform
          : platform // ignore: cast_nullable_to_non_nullable
              as String,
      storage: null == storage
          ? _value.storage
          : storage // ignore: cast_nullable_to_non_nullable
              as String,
      tag: null == tag
          ? _value.tag
          : tag // ignore: cast_nullable_to_non_nullable
              as String,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
      sort: freezed == sort
          ? _value.sort
          : sort // ignore: cast_nullable_to_non_nullable
              as int?,
      size: freezed == size
          ? _value.size
          : size // ignore: cast_nullable_to_non_nullable
              as int?,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ) as $Val);
  }

  /// Create a copy of BrowserWindowItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ProxyInfoCopyWith<$Res> get proxy {
    return $ProxyInfoCopyWith<$Res>(_value.proxy, (value) {
      return _then(_value.copyWith(proxy: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$BrowserWindowItemImplCopyWith<$Res>
    implements $BrowserWindowItemCopyWith<$Res> {
  factory _$$BrowserWindowItemImplCopyWith(_$BrowserWindowItemImpl value,
          $Res Function(_$BrowserWindowItemImpl) then) =
      __$$BrowserWindowItemImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      @JsonKey(name: 'team_id') int teamId,
      String name,
      @JsonKey(name: 'user_id') int userId,
      @JsonKey(name: 'group_id') int groupId,
      @JsonKey(name: 'group_name') String groupName,
      ProxyInfo proxy,
      @JsonKey(name: 'proxy_type') int proxyType,
      String platform,
      String storage,
      String tag,
      String? comment,
      int? sort,
      int? size,
      @JsonKey(name: 'created_at') DateTime createdAt,
      @JsonKey(name: 'updated_at') DateTime updatedAt});

  @override
  $ProxyInfoCopyWith<$Res> get proxy;
}

/// @nodoc
class __$$BrowserWindowItemImplCopyWithImpl<$Res>
    extends _$BrowserWindowItemCopyWithImpl<$Res, _$BrowserWindowItemImpl>
    implements _$$BrowserWindowItemImplCopyWith<$Res> {
  __$$BrowserWindowItemImplCopyWithImpl(_$BrowserWindowItemImpl _value,
      $Res Function(_$BrowserWindowItemImpl) _then)
      : super(_value, _then);

  /// Create a copy of BrowserWindowItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? teamId = null,
    Object? name = null,
    Object? userId = null,
    Object? groupId = null,
    Object? groupName = null,
    Object? proxy = null,
    Object? proxyType = null,
    Object? platform = null,
    Object? storage = null,
    Object? tag = null,
    Object? comment = freezed,
    Object? sort = freezed,
    Object? size = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_$BrowserWindowItemImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      teamId: null == teamId
          ? _value.teamId
          : teamId // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int,
      groupId: null == groupId
          ? _value.groupId
          : groupId // ignore: cast_nullable_to_non_nullable
              as int,
      groupName: null == groupName
          ? _value.groupName
          : groupName // ignore: cast_nullable_to_non_nullable
              as String,
      proxy: null == proxy
          ? _value.proxy
          : proxy // ignore: cast_nullable_to_non_nullable
              as ProxyInfo,
      proxyType: null == proxyType
          ? _value.proxyType
          : proxyType // ignore: cast_nullable_to_non_nullable
              as int,
      platform: null == platform
          ? _value.platform
          : platform // ignore: cast_nullable_to_non_nullable
              as String,
      storage: null == storage
          ? _value.storage
          : storage // ignore: cast_nullable_to_non_nullable
              as String,
      tag: null == tag
          ? _value.tag
          : tag // ignore: cast_nullable_to_non_nullable
              as String,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
      sort: freezed == sort
          ? _value.sort
          : sort // ignore: cast_nullable_to_non_nullable
              as int?,
      size: freezed == size
          ? _value.size
          : size // ignore: cast_nullable_to_non_nullable
              as int?,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BrowserWindowItemImpl implements _BrowserWindowItem {
  const _$BrowserWindowItemImpl(
      {required this.id,
      @JsonKey(name: 'team_id') required this.teamId,
      required this.name,
      @JsonKey(name: 'user_id') required this.userId,
      @JsonKey(name: 'group_id') required this.groupId,
      @JsonKey(name: 'group_name') required this.groupName,
      required this.proxy,
      @JsonKey(name: 'proxy_type') required this.proxyType,
      required this.platform,
      required this.storage,
      required this.tag,
      this.comment,
      this.sort,
      this.size,
      @JsonKey(name: 'created_at') required this.createdAt,
      @JsonKey(name: 'updated_at') required this.updatedAt});

  factory _$BrowserWindowItemImpl.fromJson(Map<String, dynamic> json) =>
      _$$BrowserWindowItemImplFromJson(json);

  @override
  final int id;
// 编号
  @override
  @JsonKey(name: 'team_id')
  final int teamId;
// 团队ID
  @override
  final String name;
// 窗口名称
  @override
  @JsonKey(name: 'user_id')
  final int userId;
// 用户ID
  @override
  @JsonKey(name: 'group_id')
  final int groupId;
// 分组ID
  @override
  @JsonKey(name: 'group_name')
  final String groupName;
// 所属分组名称
  @override
  final ProxyInfo proxy;
// 代理信息
  @override
  @JsonKey(name: 'proxy_type')
  final int proxyType;
// 代理类型
  @override
  final String platform;
// 所属平台
  @override
  final String storage;
// 存储信息
  @override
  final String tag;
// 标签
  @override
  final String? comment;
// 备注
  @override
  final int? sort;
// 排序（可选）
  @override
  final int? size;
// 大小（可选）
  @override
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
// 创建时间（必需）
  @override
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;

  @override
  String toString() {
    return 'BrowserWindowItem(id: $id, teamId: $teamId, name: $name, userId: $userId, groupId: $groupId, groupName: $groupName, proxy: $proxy, proxyType: $proxyType, platform: $platform, storage: $storage, tag: $tag, comment: $comment, sort: $sort, size: $size, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BrowserWindowItemImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.teamId, teamId) || other.teamId == teamId) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.groupId, groupId) || other.groupId == groupId) &&
            (identical(other.groupName, groupName) ||
                other.groupName == groupName) &&
            (identical(other.proxy, proxy) || other.proxy == proxy) &&
            (identical(other.proxyType, proxyType) ||
                other.proxyType == proxyType) &&
            (identical(other.platform, platform) ||
                other.platform == platform) &&
            (identical(other.storage, storage) || other.storage == storage) &&
            (identical(other.tag, tag) || other.tag == tag) &&
            (identical(other.comment, comment) || other.comment == comment) &&
            (identical(other.sort, sort) || other.sort == sort) &&
            (identical(other.size, size) || other.size == size) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      teamId,
      name,
      userId,
      groupId,
      groupName,
      proxy,
      proxyType,
      platform,
      storage,
      tag,
      comment,
      sort,
      size,
      createdAt,
      updatedAt);

  /// Create a copy of BrowserWindowItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BrowserWindowItemImplCopyWith<_$BrowserWindowItemImpl> get copyWith =>
      __$$BrowserWindowItemImplCopyWithImpl<_$BrowserWindowItemImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BrowserWindowItemImplToJson(
      this,
    );
  }
}

abstract class _BrowserWindowItem implements BrowserWindowItem {
  const factory _BrowserWindowItem(
          {required final int id,
          @JsonKey(name: 'team_id') required final int teamId,
          required final String name,
          @JsonKey(name: 'user_id') required final int userId,
          @JsonKey(name: 'group_id') required final int groupId,
          @JsonKey(name: 'group_name') required final String groupName,
          required final ProxyInfo proxy,
          @JsonKey(name: 'proxy_type') required final int proxyType,
          required final String platform,
          required final String storage,
          required final String tag,
          final String? comment,
          final int? sort,
          final int? size,
          @JsonKey(name: 'created_at') required final DateTime createdAt,
          @JsonKey(name: 'updated_at') required final DateTime updatedAt}) =
      _$BrowserWindowItemImpl;

  factory _BrowserWindowItem.fromJson(Map<String, dynamic> json) =
      _$BrowserWindowItemImpl.fromJson;

  @override
  int get id; // 编号
  @override
  @JsonKey(name: 'team_id')
  int get teamId; // 团队ID
  @override
  String get name; // 窗口名称
  @override
  @JsonKey(name: 'user_id')
  int get userId; // 用户ID
  @override
  @JsonKey(name: 'group_id')
  int get groupId; // 分组ID
  @override
  @JsonKey(name: 'group_name')
  String get groupName; // 所属分组名称
  @override
  ProxyInfo get proxy; // 代理信息
  @override
  @JsonKey(name: 'proxy_type')
  int get proxyType; // 代理类型
  @override
  String get platform; // 所属平台
  @override
  String get storage; // 存储信息
  @override
  String get tag; // 标签
  @override
  String? get comment; // 备注
  @override
  int? get sort; // 排序（可选）
  @override
  int? get size; // 大小（可选）
  @override
  @JsonKey(name: 'created_at')
  DateTime get createdAt; // 创建时间（必需）
  @override
  @JsonKey(name: 'updated_at')
  DateTime get updatedAt;

  /// Create a copy of BrowserWindowItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BrowserWindowItemImplCopyWith<_$BrowserWindowItemImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

BrowserWindow _$BrowserWindowFromJson(Map<String, dynamic> json) {
  return _BrowserWindow.fromJson(json);
}

/// @nodoc
mixin _$BrowserWindow {
  int get id => throw _privateConstructorUsedError; // 编号
  @JsonKey(name: 'team_id')
  int get teamId => throw _privateConstructorUsedError; // 团队ID
  String get name => throw _privateConstructorUsedError; // 窗口名称
  @JsonKey(name: 'user_id')
  int get userId => throw _privateConstructorUsedError; // 用户ID
  @JsonKey(name: 'group_id')
  int get groupId => throw _privateConstructorUsedError; // 分组ID
  @JsonKey(name: 'group_name')
  String get groupName => throw _privateConstructorUsedError; // 所属分组名称
  ProxyInfo get proxy => throw _privateConstructorUsedError; // 代理信息
  @JsonKey(name: 'proxy_type')
  int get proxyType => throw _privateConstructorUsedError; // 代理类型
  String get platform => throw _privateConstructorUsedError; // 所属平台
  String get parameters => throw _privateConstructorUsedError; // 参数（详情中包含）
  String get storage => throw _privateConstructorUsedError; // 存储信息
  String get tag => throw _privateConstructorUsedError; // 标签
  String? get comment => throw _privateConstructorUsedError; // 备注（详情中包含，可选）
  int? get sort => throw _privateConstructorUsedError;

  /// Serializes this BrowserWindow to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of BrowserWindow
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BrowserWindowCopyWith<BrowserWindow> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BrowserWindowCopyWith<$Res> {
  factory $BrowserWindowCopyWith(
          BrowserWindow value, $Res Function(BrowserWindow) then) =
      _$BrowserWindowCopyWithImpl<$Res, BrowserWindow>;
  @useResult
  $Res call(
      {int id,
      @JsonKey(name: 'team_id') int teamId,
      String name,
      @JsonKey(name: 'user_id') int userId,
      @JsonKey(name: 'group_id') int groupId,
      @JsonKey(name: 'group_name') String groupName,
      ProxyInfo proxy,
      @JsonKey(name: 'proxy_type') int proxyType,
      String platform,
      String parameters,
      String storage,
      String tag,
      String? comment,
      int? sort});

  $ProxyInfoCopyWith<$Res> get proxy;
}

/// @nodoc
class _$BrowserWindowCopyWithImpl<$Res, $Val extends BrowserWindow>
    implements $BrowserWindowCopyWith<$Res> {
  _$BrowserWindowCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BrowserWindow
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? teamId = null,
    Object? name = null,
    Object? userId = null,
    Object? groupId = null,
    Object? groupName = null,
    Object? proxy = null,
    Object? proxyType = null,
    Object? platform = null,
    Object? parameters = null,
    Object? storage = null,
    Object? tag = null,
    Object? comment = freezed,
    Object? sort = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      teamId: null == teamId
          ? _value.teamId
          : teamId // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int,
      groupId: null == groupId
          ? _value.groupId
          : groupId // ignore: cast_nullable_to_non_nullable
              as int,
      groupName: null == groupName
          ? _value.groupName
          : groupName // ignore: cast_nullable_to_non_nullable
              as String,
      proxy: null == proxy
          ? _value.proxy
          : proxy // ignore: cast_nullable_to_non_nullable
              as ProxyInfo,
      proxyType: null == proxyType
          ? _value.proxyType
          : proxyType // ignore: cast_nullable_to_non_nullable
              as int,
      platform: null == platform
          ? _value.platform
          : platform // ignore: cast_nullable_to_non_nullable
              as String,
      parameters: null == parameters
          ? _value.parameters
          : parameters // ignore: cast_nullable_to_non_nullable
              as String,
      storage: null == storage
          ? _value.storage
          : storage // ignore: cast_nullable_to_non_nullable
              as String,
      tag: null == tag
          ? _value.tag
          : tag // ignore: cast_nullable_to_non_nullable
              as String,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
      sort: freezed == sort
          ? _value.sort
          : sort // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }

  /// Create a copy of BrowserWindow
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ProxyInfoCopyWith<$Res> get proxy {
    return $ProxyInfoCopyWith<$Res>(_value.proxy, (value) {
      return _then(_value.copyWith(proxy: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$BrowserWindowImplCopyWith<$Res>
    implements $BrowserWindowCopyWith<$Res> {
  factory _$$BrowserWindowImplCopyWith(
          _$BrowserWindowImpl value, $Res Function(_$BrowserWindowImpl) then) =
      __$$BrowserWindowImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      @JsonKey(name: 'team_id') int teamId,
      String name,
      @JsonKey(name: 'user_id') int userId,
      @JsonKey(name: 'group_id') int groupId,
      @JsonKey(name: 'group_name') String groupName,
      ProxyInfo proxy,
      @JsonKey(name: 'proxy_type') int proxyType,
      String platform,
      String parameters,
      String storage,
      String tag,
      String? comment,
      int? sort});

  @override
  $ProxyInfoCopyWith<$Res> get proxy;
}

/// @nodoc
class __$$BrowserWindowImplCopyWithImpl<$Res>
    extends _$BrowserWindowCopyWithImpl<$Res, _$BrowserWindowImpl>
    implements _$$BrowserWindowImplCopyWith<$Res> {
  __$$BrowserWindowImplCopyWithImpl(
      _$BrowserWindowImpl _value, $Res Function(_$BrowserWindowImpl) _then)
      : super(_value, _then);

  /// Create a copy of BrowserWindow
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? teamId = null,
    Object? name = null,
    Object? userId = null,
    Object? groupId = null,
    Object? groupName = null,
    Object? proxy = null,
    Object? proxyType = null,
    Object? platform = null,
    Object? parameters = null,
    Object? storage = null,
    Object? tag = null,
    Object? comment = freezed,
    Object? sort = freezed,
  }) {
    return _then(_$BrowserWindowImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      teamId: null == teamId
          ? _value.teamId
          : teamId // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int,
      groupId: null == groupId
          ? _value.groupId
          : groupId // ignore: cast_nullable_to_non_nullable
              as int,
      groupName: null == groupName
          ? _value.groupName
          : groupName // ignore: cast_nullable_to_non_nullable
              as String,
      proxy: null == proxy
          ? _value.proxy
          : proxy // ignore: cast_nullable_to_non_nullable
              as ProxyInfo,
      proxyType: null == proxyType
          ? _value.proxyType
          : proxyType // ignore: cast_nullable_to_non_nullable
              as int,
      platform: null == platform
          ? _value.platform
          : platform // ignore: cast_nullable_to_non_nullable
              as String,
      parameters: null == parameters
          ? _value.parameters
          : parameters // ignore: cast_nullable_to_non_nullable
              as String,
      storage: null == storage
          ? _value.storage
          : storage // ignore: cast_nullable_to_non_nullable
              as String,
      tag: null == tag
          ? _value.tag
          : tag // ignore: cast_nullable_to_non_nullable
              as String,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
      sort: freezed == sort
          ? _value.sort
          : sort // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BrowserWindowImpl implements _BrowserWindow {
  const _$BrowserWindowImpl(
      {required this.id,
      @JsonKey(name: 'team_id') required this.teamId,
      required this.name,
      @JsonKey(name: 'user_id') required this.userId,
      @JsonKey(name: 'group_id') required this.groupId,
      @JsonKey(name: 'group_name') required this.groupName,
      required this.proxy,
      @JsonKey(name: 'proxy_type') required this.proxyType,
      required this.platform,
      required this.parameters,
      required this.storage,
      required this.tag,
      this.comment,
      this.sort});

  factory _$BrowserWindowImpl.fromJson(Map<String, dynamic> json) =>
      _$$BrowserWindowImplFromJson(json);

  @override
  final int id;
// 编号
  @override
  @JsonKey(name: 'team_id')
  final int teamId;
// 团队ID
  @override
  final String name;
// 窗口名称
  @override
  @JsonKey(name: 'user_id')
  final int userId;
// 用户ID
  @override
  @JsonKey(name: 'group_id')
  final int groupId;
// 分组ID
  @override
  @JsonKey(name: 'group_name')
  final String groupName;
// 所属分组名称
  @override
  final ProxyInfo proxy;
// 代理信息
  @override
  @JsonKey(name: 'proxy_type')
  final int proxyType;
// 代理类型
  @override
  final String platform;
// 所属平台
  @override
  final String parameters;
// 参数（详情中包含）
  @override
  final String storage;
// 存储信息
  @override
  final String tag;
// 标签
  @override
  final String? comment;
// 备注（详情中包含，可选）
  @override
  final int? sort;

  @override
  String toString() {
    return 'BrowserWindow(id: $id, teamId: $teamId, name: $name, userId: $userId, groupId: $groupId, groupName: $groupName, proxy: $proxy, proxyType: $proxyType, platform: $platform, parameters: $parameters, storage: $storage, tag: $tag, comment: $comment, sort: $sort)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BrowserWindowImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.teamId, teamId) || other.teamId == teamId) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.groupId, groupId) || other.groupId == groupId) &&
            (identical(other.groupName, groupName) ||
                other.groupName == groupName) &&
            (identical(other.proxy, proxy) || other.proxy == proxy) &&
            (identical(other.proxyType, proxyType) ||
                other.proxyType == proxyType) &&
            (identical(other.platform, platform) ||
                other.platform == platform) &&
            (identical(other.parameters, parameters) ||
                other.parameters == parameters) &&
            (identical(other.storage, storage) || other.storage == storage) &&
            (identical(other.tag, tag) || other.tag == tag) &&
            (identical(other.comment, comment) || other.comment == comment) &&
            (identical(other.sort, sort) || other.sort == sort));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      teamId,
      name,
      userId,
      groupId,
      groupName,
      proxy,
      proxyType,
      platform,
      parameters,
      storage,
      tag,
      comment,
      sort);

  /// Create a copy of BrowserWindow
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BrowserWindowImplCopyWith<_$BrowserWindowImpl> get copyWith =>
      __$$BrowserWindowImplCopyWithImpl<_$BrowserWindowImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BrowserWindowImplToJson(
      this,
    );
  }
}

abstract class _BrowserWindow implements BrowserWindow {
  const factory _BrowserWindow(
      {required final int id,
      @JsonKey(name: 'team_id') required final int teamId,
      required final String name,
      @JsonKey(name: 'user_id') required final int userId,
      @JsonKey(name: 'group_id') required final int groupId,
      @JsonKey(name: 'group_name') required final String groupName,
      required final ProxyInfo proxy,
      @JsonKey(name: 'proxy_type') required final int proxyType,
      required final String platform,
      required final String parameters,
      required final String storage,
      required final String tag,
      final String? comment,
      final int? sort}) = _$BrowserWindowImpl;

  factory _BrowserWindow.fromJson(Map<String, dynamic> json) =
      _$BrowserWindowImpl.fromJson;

  @override
  int get id; // 编号
  @override
  @JsonKey(name: 'team_id')
  int get teamId; // 团队ID
  @override
  String get name; // 窗口名称
  @override
  @JsonKey(name: 'user_id')
  int get userId; // 用户ID
  @override
  @JsonKey(name: 'group_id')
  int get groupId; // 分组ID
  @override
  @JsonKey(name: 'group_name')
  String get groupName; // 所属分组名称
  @override
  ProxyInfo get proxy; // 代理信息
  @override
  @JsonKey(name: 'proxy_type')
  int get proxyType; // 代理类型
  @override
  String get platform; // 所属平台
  @override
  String get parameters; // 参数（详情中包含）
  @override
  String get storage; // 存储信息
  @override
  String get tag; // 标签
  @override
  String? get comment; // 备注（详情中包含，可选）
  @override
  int? get sort;

  /// Create a copy of BrowserWindow
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BrowserWindowImplCopyWith<_$BrowserWindowImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ProxyInfo _$ProxyInfoFromJson(Map<String, dynamic> json) {
  return _ProxyInfo.fromJson(json);
}

/// @nodoc
mixin _$ProxyInfo {
  String get name => throw _privateConstructorUsedError; // 地址
  int get type => throw _privateConstructorUsedError; // 代理类型
  String get address => throw _privateConstructorUsedError; // 地址
  int get port => throw _privateConstructorUsedError; // 端口
  String get username => throw _privateConstructorUsedError; // 用户名
  String get password => throw _privateConstructorUsedError;

  /// Serializes this ProxyInfo to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProxyInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProxyInfoCopyWith<ProxyInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProxyInfoCopyWith<$Res> {
  factory $ProxyInfoCopyWith(ProxyInfo value, $Res Function(ProxyInfo) then) =
      _$ProxyInfoCopyWithImpl<$Res, ProxyInfo>;
  @useResult
  $Res call(
      {String name,
      int type,
      String address,
      int port,
      String username,
      String password});
}

/// @nodoc
class _$ProxyInfoCopyWithImpl<$Res, $Val extends ProxyInfo>
    implements $ProxyInfoCopyWith<$Res> {
  _$ProxyInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProxyInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? type = null,
    Object? address = null,
    Object? port = null,
    Object? username = null,
    Object? password = null,
  }) {
    return _then(_value.copyWith(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int,
      address: null == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String,
      port: null == port
          ? _value.port
          : port // ignore: cast_nullable_to_non_nullable
              as int,
      username: null == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String,
      password: null == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProxyInfoImplCopyWith<$Res>
    implements $ProxyInfoCopyWith<$Res> {
  factory _$$ProxyInfoImplCopyWith(
          _$ProxyInfoImpl value, $Res Function(_$ProxyInfoImpl) then) =
      __$$ProxyInfoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String name,
      int type,
      String address,
      int port,
      String username,
      String password});
}

/// @nodoc
class __$$ProxyInfoImplCopyWithImpl<$Res>
    extends _$ProxyInfoCopyWithImpl<$Res, _$ProxyInfoImpl>
    implements _$$ProxyInfoImplCopyWith<$Res> {
  __$$ProxyInfoImplCopyWithImpl(
      _$ProxyInfoImpl _value, $Res Function(_$ProxyInfoImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProxyInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? type = null,
    Object? address = null,
    Object? port = null,
    Object? username = null,
    Object? password = null,
  }) {
    return _then(_$ProxyInfoImpl(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int,
      address: null == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String,
      port: null == port
          ? _value.port
          : port // ignore: cast_nullable_to_non_nullable
              as int,
      username: null == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String,
      password: null == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ProxyInfoImpl implements _ProxyInfo {
  const _$ProxyInfoImpl(
      {required this.name,
      required this.type,
      required this.address,
      required this.port,
      required this.username,
      required this.password});

  factory _$ProxyInfoImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProxyInfoImplFromJson(json);

  @override
  final String name;
// 地址
  @override
  final int type;
// 代理类型
  @override
  final String address;
// 地址
  @override
  final int port;
// 端口
  @override
  final String username;
// 用户名
  @override
  final String password;

  @override
  String toString() {
    return 'ProxyInfo(name: $name, type: $type, address: $address, port: $port, username: $username, password: $password)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProxyInfoImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.port, port) || other.port == port) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.password, password) ||
                other.password == password));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, name, type, address, port, username, password);

  /// Create a copy of ProxyInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProxyInfoImplCopyWith<_$ProxyInfoImpl> get copyWith =>
      __$$ProxyInfoImplCopyWithImpl<_$ProxyInfoImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProxyInfoImplToJson(
      this,
    );
  }
}

abstract class _ProxyInfo implements ProxyInfo {
  const factory _ProxyInfo(
      {required final String name,
      required final int type,
      required final String address,
      required final int port,
      required final String username,
      required final String password}) = _$ProxyInfoImpl;

  factory _ProxyInfo.fromJson(Map<String, dynamic> json) =
      _$ProxyInfoImpl.fromJson;

  @override
  String get name; // 地址
  @override
  int get type; // 代理类型
  @override
  String get address; // 地址
  @override
  int get port; // 端口
  @override
  String get username; // 用户名
  @override
  String get password;

  /// Create a copy of ProxyInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProxyInfoImplCopyWith<_$ProxyInfoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

BrowserWindowUpdateRequest _$BrowserWindowUpdateRequestFromJson(
    Map<String, dynamic> json) {
  return _BrowserWindowUpdateRequest.fromJson(json);
}

/// @nodoc
mixin _$BrowserWindowUpdateRequest {
  int get id => throw _privateConstructorUsedError; // 编号，必需
  @JsonKey(includeIfNull: false)
  String? get name => throw _privateConstructorUsedError; // 窗口名称，可选
  @JsonKey(name: 'group_id', includeIfNull: false)
  int? get groupId => throw _privateConstructorUsedError; // 分组ID，可选
  @JsonKey(name: 'proxy_id', includeIfNull: false)
  int? get proxyId => throw _privateConstructorUsedError; // 代理ID，可选
  @JsonKey(name: 'proxy_type', includeIfNull: false)
  int? get proxyType => throw _privateConstructorUsedError; // 代理类型，可选
  @JsonKey(includeIfNull: false)
  String? get platform => throw _privateConstructorUsedError; // 平台，可选
  @JsonKey(includeIfNull: false)
  String? get parameters => throw _privateConstructorUsedError; // JSON字符串参数，可选
  @JsonKey(includeIfNull: false)
  String? get storage => throw _privateConstructorUsedError; // 存储信息，可选
  @JsonKey(includeIfNull: false)
  String? get tag => throw _privateConstructorUsedError; // 标签，可选
  @JsonKey(includeIfNull: false)
  String? get comment => throw _privateConstructorUsedError; // 备注，可选
  @JsonKey(includeIfNull: false)
  int? get sort => throw _privateConstructorUsedError;

  /// Serializes this BrowserWindowUpdateRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of BrowserWindowUpdateRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BrowserWindowUpdateRequestCopyWith<BrowserWindowUpdateRequest>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BrowserWindowUpdateRequestCopyWith<$Res> {
  factory $BrowserWindowUpdateRequestCopyWith(BrowserWindowUpdateRequest value,
          $Res Function(BrowserWindowUpdateRequest) then) =
      _$BrowserWindowUpdateRequestCopyWithImpl<$Res,
          BrowserWindowUpdateRequest>;
  @useResult
  $Res call(
      {int id,
      @JsonKey(includeIfNull: false) String? name,
      @JsonKey(name: 'group_id', includeIfNull: false) int? groupId,
      @JsonKey(name: 'proxy_id', includeIfNull: false) int? proxyId,
      @JsonKey(name: 'proxy_type', includeIfNull: false) int? proxyType,
      @JsonKey(includeIfNull: false) String? platform,
      @JsonKey(includeIfNull: false) String? parameters,
      @JsonKey(includeIfNull: false) String? storage,
      @JsonKey(includeIfNull: false) String? tag,
      @JsonKey(includeIfNull: false) String? comment,
      @JsonKey(includeIfNull: false) int? sort});
}

/// @nodoc
class _$BrowserWindowUpdateRequestCopyWithImpl<$Res,
        $Val extends BrowserWindowUpdateRequest>
    implements $BrowserWindowUpdateRequestCopyWith<$Res> {
  _$BrowserWindowUpdateRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BrowserWindowUpdateRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = freezed,
    Object? groupId = freezed,
    Object? proxyId = freezed,
    Object? proxyType = freezed,
    Object? platform = freezed,
    Object? parameters = freezed,
    Object? storage = freezed,
    Object? tag = freezed,
    Object? comment = freezed,
    Object? sort = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      groupId: freezed == groupId
          ? _value.groupId
          : groupId // ignore: cast_nullable_to_non_nullable
              as int?,
      proxyId: freezed == proxyId
          ? _value.proxyId
          : proxyId // ignore: cast_nullable_to_non_nullable
              as int?,
      proxyType: freezed == proxyType
          ? _value.proxyType
          : proxyType // ignore: cast_nullable_to_non_nullable
              as int?,
      platform: freezed == platform
          ? _value.platform
          : platform // ignore: cast_nullable_to_non_nullable
              as String?,
      parameters: freezed == parameters
          ? _value.parameters
          : parameters // ignore: cast_nullable_to_non_nullable
              as String?,
      storage: freezed == storage
          ? _value.storage
          : storage // ignore: cast_nullable_to_non_nullable
              as String?,
      tag: freezed == tag
          ? _value.tag
          : tag // ignore: cast_nullable_to_non_nullable
              as String?,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
      sort: freezed == sort
          ? _value.sort
          : sort // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BrowserWindowUpdateRequestImplCopyWith<$Res>
    implements $BrowserWindowUpdateRequestCopyWith<$Res> {
  factory _$$BrowserWindowUpdateRequestImplCopyWith(
          _$BrowserWindowUpdateRequestImpl value,
          $Res Function(_$BrowserWindowUpdateRequestImpl) then) =
      __$$BrowserWindowUpdateRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      @JsonKey(includeIfNull: false) String? name,
      @JsonKey(name: 'group_id', includeIfNull: false) int? groupId,
      @JsonKey(name: 'proxy_id', includeIfNull: false) int? proxyId,
      @JsonKey(name: 'proxy_type', includeIfNull: false) int? proxyType,
      @JsonKey(includeIfNull: false) String? platform,
      @JsonKey(includeIfNull: false) String? parameters,
      @JsonKey(includeIfNull: false) String? storage,
      @JsonKey(includeIfNull: false) String? tag,
      @JsonKey(includeIfNull: false) String? comment,
      @JsonKey(includeIfNull: false) int? sort});
}

/// @nodoc
class __$$BrowserWindowUpdateRequestImplCopyWithImpl<$Res>
    extends _$BrowserWindowUpdateRequestCopyWithImpl<$Res,
        _$BrowserWindowUpdateRequestImpl>
    implements _$$BrowserWindowUpdateRequestImplCopyWith<$Res> {
  __$$BrowserWindowUpdateRequestImplCopyWithImpl(
      _$BrowserWindowUpdateRequestImpl _value,
      $Res Function(_$BrowserWindowUpdateRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of BrowserWindowUpdateRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = freezed,
    Object? groupId = freezed,
    Object? proxyId = freezed,
    Object? proxyType = freezed,
    Object? platform = freezed,
    Object? parameters = freezed,
    Object? storage = freezed,
    Object? tag = freezed,
    Object? comment = freezed,
    Object? sort = freezed,
  }) {
    return _then(_$BrowserWindowUpdateRequestImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      groupId: freezed == groupId
          ? _value.groupId
          : groupId // ignore: cast_nullable_to_non_nullable
              as int?,
      proxyId: freezed == proxyId
          ? _value.proxyId
          : proxyId // ignore: cast_nullable_to_non_nullable
              as int?,
      proxyType: freezed == proxyType
          ? _value.proxyType
          : proxyType // ignore: cast_nullable_to_non_nullable
              as int?,
      platform: freezed == platform
          ? _value.platform
          : platform // ignore: cast_nullable_to_non_nullable
              as String?,
      parameters: freezed == parameters
          ? _value.parameters
          : parameters // ignore: cast_nullable_to_non_nullable
              as String?,
      storage: freezed == storage
          ? _value.storage
          : storage // ignore: cast_nullable_to_non_nullable
              as String?,
      tag: freezed == tag
          ? _value.tag
          : tag // ignore: cast_nullable_to_non_nullable
              as String?,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
      sort: freezed == sort
          ? _value.sort
          : sort // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BrowserWindowUpdateRequestImpl implements _BrowserWindowUpdateRequest {
  const _$BrowserWindowUpdateRequestImpl(
      {required this.id,
      @JsonKey(includeIfNull: false) this.name,
      @JsonKey(name: 'group_id', includeIfNull: false) this.groupId,
      @JsonKey(name: 'proxy_id', includeIfNull: false) this.proxyId,
      @JsonKey(name: 'proxy_type', includeIfNull: false) this.proxyType,
      @JsonKey(includeIfNull: false) this.platform,
      @JsonKey(includeIfNull: false) this.parameters,
      @JsonKey(includeIfNull: false) this.storage,
      @JsonKey(includeIfNull: false) this.tag,
      @JsonKey(includeIfNull: false) this.comment,
      @JsonKey(includeIfNull: false) this.sort});

  factory _$BrowserWindowUpdateRequestImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$BrowserWindowUpdateRequestImplFromJson(json);

  @override
  final int id;
// 编号，必需
  @override
  @JsonKey(includeIfNull: false)
  final String? name;
// 窗口名称，可选
  @override
  @JsonKey(name: 'group_id', includeIfNull: false)
  final int? groupId;
// 分组ID，可选
  @override
  @JsonKey(name: 'proxy_id', includeIfNull: false)
  final int? proxyId;
// 代理ID，可选
  @override
  @JsonKey(name: 'proxy_type', includeIfNull: false)
  final int? proxyType;
// 代理类型，可选
  @override
  @JsonKey(includeIfNull: false)
  final String? platform;
// 平台，可选
  @override
  @JsonKey(includeIfNull: false)
  final String? parameters;
// JSON字符串参数，可选
  @override
  @JsonKey(includeIfNull: false)
  final String? storage;
// 存储信息，可选
  @override
  @JsonKey(includeIfNull: false)
  final String? tag;
// 标签，可选
  @override
  @JsonKey(includeIfNull: false)
  final String? comment;
// 备注，可选
  @override
  @JsonKey(includeIfNull: false)
  final int? sort;

  @override
  String toString() {
    return 'BrowserWindowUpdateRequest(id: $id, name: $name, groupId: $groupId, proxyId: $proxyId, proxyType: $proxyType, platform: $platform, parameters: $parameters, storage: $storage, tag: $tag, comment: $comment, sort: $sort)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BrowserWindowUpdateRequestImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.groupId, groupId) || other.groupId == groupId) &&
            (identical(other.proxyId, proxyId) || other.proxyId == proxyId) &&
            (identical(other.proxyType, proxyType) ||
                other.proxyType == proxyType) &&
            (identical(other.platform, platform) ||
                other.platform == platform) &&
            (identical(other.parameters, parameters) ||
                other.parameters == parameters) &&
            (identical(other.storage, storage) || other.storage == storage) &&
            (identical(other.tag, tag) || other.tag == tag) &&
            (identical(other.comment, comment) || other.comment == comment) &&
            (identical(other.sort, sort) || other.sort == sort));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, name, groupId, proxyId,
      proxyType, platform, parameters, storage, tag, comment, sort);

  /// Create a copy of BrowserWindowUpdateRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BrowserWindowUpdateRequestImplCopyWith<_$BrowserWindowUpdateRequestImpl>
      get copyWith => __$$BrowserWindowUpdateRequestImplCopyWithImpl<
          _$BrowserWindowUpdateRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BrowserWindowUpdateRequestImplToJson(
      this,
    );
  }
}

abstract class _BrowserWindowUpdateRequest
    implements BrowserWindowUpdateRequest {
  const factory _BrowserWindowUpdateRequest(
      {required final int id,
      @JsonKey(includeIfNull: false) final String? name,
      @JsonKey(name: 'group_id', includeIfNull: false) final int? groupId,
      @JsonKey(name: 'proxy_id', includeIfNull: false) final int? proxyId,
      @JsonKey(name: 'proxy_type', includeIfNull: false) final int? proxyType,
      @JsonKey(includeIfNull: false) final String? platform,
      @JsonKey(includeIfNull: false) final String? parameters,
      @JsonKey(includeIfNull: false) final String? storage,
      @JsonKey(includeIfNull: false) final String? tag,
      @JsonKey(includeIfNull: false) final String? comment,
      @JsonKey(includeIfNull: false)
      final int? sort}) = _$BrowserWindowUpdateRequestImpl;

  factory _BrowserWindowUpdateRequest.fromJson(Map<String, dynamic> json) =
      _$BrowserWindowUpdateRequestImpl.fromJson;

  @override
  int get id; // 编号，必需
  @override
  @JsonKey(includeIfNull: false)
  String? get name; // 窗口名称，可选
  @override
  @JsonKey(name: 'group_id', includeIfNull: false)
  int? get groupId; // 分组ID，可选
  @override
  @JsonKey(name: 'proxy_id', includeIfNull: false)
  int? get proxyId; // 代理ID，可选
  @override
  @JsonKey(name: 'proxy_type', includeIfNull: false)
  int? get proxyType; // 代理类型，可选
  @override
  @JsonKey(includeIfNull: false)
  String? get platform; // 平台，可选
  @override
  @JsonKey(includeIfNull: false)
  String? get parameters; // JSON字符串参数，可选
  @override
  @JsonKey(includeIfNull: false)
  String? get storage; // 存储信息，可选
  @override
  @JsonKey(includeIfNull: false)
  String? get tag; // 标签，可选
  @override
  @JsonKey(includeIfNull: false)
  String? get comment; // 备注，可选
  @override
  @JsonKey(includeIfNull: false)
  int? get sort;

  /// Create a copy of BrowserWindowUpdateRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BrowserWindowUpdateRequestImplCopyWith<_$BrowserWindowUpdateRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}

BrowserCreateRequest _$BrowserCreateRequestFromJson(Map<String, dynamic> json) {
  return _BrowserCreateRequest.fromJson(json);
}

/// @nodoc
mixin _$BrowserCreateRequest {
  String get name => throw _privateConstructorUsedError; // 窗口名称，必需，最大100字符
  @JsonKey(name: 'group_id')
  int get groupId => throw _privateConstructorUsedError; // 分组ID，必需
  @JsonKey(name: 'proxy_id')
  int get proxyId => throw _privateConstructorUsedError; // 代理ID，必需
  @JsonKey(name: 'proxy_type')
  int get proxyType => throw _privateConstructorUsedError; // 代理类型，必需
  String get platform => throw _privateConstructorUsedError; // 所属平台，必需
  String get parameters => throw _privateConstructorUsedError; // JSON字符串参数，必需
  @JsonKey(includeIfNull: false)
  String? get storage => throw _privateConstructorUsedError; // 存储信息，可选
  @JsonKey(includeIfNull: false)
  String? get tag => throw _privateConstructorUsedError; // 标签，可选
  @JsonKey(includeIfNull: false)
  String? get comment => throw _privateConstructorUsedError; // 备注，可选
  @JsonKey(includeIfNull: false)
  int? get sort => throw _privateConstructorUsedError;

  /// Serializes this BrowserCreateRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of BrowserCreateRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BrowserCreateRequestCopyWith<BrowserCreateRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BrowserCreateRequestCopyWith<$Res> {
  factory $BrowserCreateRequestCopyWith(BrowserCreateRequest value,
          $Res Function(BrowserCreateRequest) then) =
      _$BrowserCreateRequestCopyWithImpl<$Res, BrowserCreateRequest>;
  @useResult
  $Res call(
      {String name,
      @JsonKey(name: 'group_id') int groupId,
      @JsonKey(name: 'proxy_id') int proxyId,
      @JsonKey(name: 'proxy_type') int proxyType,
      String platform,
      String parameters,
      @JsonKey(includeIfNull: false) String? storage,
      @JsonKey(includeIfNull: false) String? tag,
      @JsonKey(includeIfNull: false) String? comment,
      @JsonKey(includeIfNull: false) int? sort});
}

/// @nodoc
class _$BrowserCreateRequestCopyWithImpl<$Res,
        $Val extends BrowserCreateRequest>
    implements $BrowserCreateRequestCopyWith<$Res> {
  _$BrowserCreateRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BrowserCreateRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? groupId = null,
    Object? proxyId = null,
    Object? proxyType = null,
    Object? platform = null,
    Object? parameters = null,
    Object? storage = freezed,
    Object? tag = freezed,
    Object? comment = freezed,
    Object? sort = freezed,
  }) {
    return _then(_value.copyWith(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      groupId: null == groupId
          ? _value.groupId
          : groupId // ignore: cast_nullable_to_non_nullable
              as int,
      proxyId: null == proxyId
          ? _value.proxyId
          : proxyId // ignore: cast_nullable_to_non_nullable
              as int,
      proxyType: null == proxyType
          ? _value.proxyType
          : proxyType // ignore: cast_nullable_to_non_nullable
              as int,
      platform: null == platform
          ? _value.platform
          : platform // ignore: cast_nullable_to_non_nullable
              as String,
      parameters: null == parameters
          ? _value.parameters
          : parameters // ignore: cast_nullable_to_non_nullable
              as String,
      storage: freezed == storage
          ? _value.storage
          : storage // ignore: cast_nullable_to_non_nullable
              as String?,
      tag: freezed == tag
          ? _value.tag
          : tag // ignore: cast_nullable_to_non_nullable
              as String?,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
      sort: freezed == sort
          ? _value.sort
          : sort // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BrowserCreateRequestImplCopyWith<$Res>
    implements $BrowserCreateRequestCopyWith<$Res> {
  factory _$$BrowserCreateRequestImplCopyWith(_$BrowserCreateRequestImpl value,
          $Res Function(_$BrowserCreateRequestImpl) then) =
      __$$BrowserCreateRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String name,
      @JsonKey(name: 'group_id') int groupId,
      @JsonKey(name: 'proxy_id') int proxyId,
      @JsonKey(name: 'proxy_type') int proxyType,
      String platform,
      String parameters,
      @JsonKey(includeIfNull: false) String? storage,
      @JsonKey(includeIfNull: false) String? tag,
      @JsonKey(includeIfNull: false) String? comment,
      @JsonKey(includeIfNull: false) int? sort});
}

/// @nodoc
class __$$BrowserCreateRequestImplCopyWithImpl<$Res>
    extends _$BrowserCreateRequestCopyWithImpl<$Res, _$BrowserCreateRequestImpl>
    implements _$$BrowserCreateRequestImplCopyWith<$Res> {
  __$$BrowserCreateRequestImplCopyWithImpl(_$BrowserCreateRequestImpl _value,
      $Res Function(_$BrowserCreateRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of BrowserCreateRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? groupId = null,
    Object? proxyId = null,
    Object? proxyType = null,
    Object? platform = null,
    Object? parameters = null,
    Object? storage = freezed,
    Object? tag = freezed,
    Object? comment = freezed,
    Object? sort = freezed,
  }) {
    return _then(_$BrowserCreateRequestImpl(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      groupId: null == groupId
          ? _value.groupId
          : groupId // ignore: cast_nullable_to_non_nullable
              as int,
      proxyId: null == proxyId
          ? _value.proxyId
          : proxyId // ignore: cast_nullable_to_non_nullable
              as int,
      proxyType: null == proxyType
          ? _value.proxyType
          : proxyType // ignore: cast_nullable_to_non_nullable
              as int,
      platform: null == platform
          ? _value.platform
          : platform // ignore: cast_nullable_to_non_nullable
              as String,
      parameters: null == parameters
          ? _value.parameters
          : parameters // ignore: cast_nullable_to_non_nullable
              as String,
      storage: freezed == storage
          ? _value.storage
          : storage // ignore: cast_nullable_to_non_nullable
              as String?,
      tag: freezed == tag
          ? _value.tag
          : tag // ignore: cast_nullable_to_non_nullable
              as String?,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
      sort: freezed == sort
          ? _value.sort
          : sort // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BrowserCreateRequestImpl implements _BrowserCreateRequest {
  const _$BrowserCreateRequestImpl(
      {required this.name,
      @JsonKey(name: 'group_id') required this.groupId,
      @JsonKey(name: 'proxy_id') required this.proxyId,
      @JsonKey(name: 'proxy_type') required this.proxyType,
      required this.platform,
      required this.parameters,
      @JsonKey(includeIfNull: false) this.storage,
      @JsonKey(includeIfNull: false) this.tag,
      @JsonKey(includeIfNull: false) this.comment,
      @JsonKey(includeIfNull: false) this.sort});

  factory _$BrowserCreateRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$BrowserCreateRequestImplFromJson(json);

  @override
  final String name;
// 窗口名称，必需，最大100字符
  @override
  @JsonKey(name: 'group_id')
  final int groupId;
// 分组ID，必需
  @override
  @JsonKey(name: 'proxy_id')
  final int proxyId;
// 代理ID，必需
  @override
  @JsonKey(name: 'proxy_type')
  final int proxyType;
// 代理类型，必需
  @override
  final String platform;
// 所属平台，必需
  @override
  final String parameters;
// JSON字符串参数，必需
  @override
  @JsonKey(includeIfNull: false)
  final String? storage;
// 存储信息，可选
  @override
  @JsonKey(includeIfNull: false)
  final String? tag;
// 标签，可选
  @override
  @JsonKey(includeIfNull: false)
  final String? comment;
// 备注，可选
  @override
  @JsonKey(includeIfNull: false)
  final int? sort;

  @override
  String toString() {
    return 'BrowserCreateRequest(name: $name, groupId: $groupId, proxyId: $proxyId, proxyType: $proxyType, platform: $platform, parameters: $parameters, storage: $storage, tag: $tag, comment: $comment, sort: $sort)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BrowserCreateRequestImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.groupId, groupId) || other.groupId == groupId) &&
            (identical(other.proxyId, proxyId) || other.proxyId == proxyId) &&
            (identical(other.proxyType, proxyType) ||
                other.proxyType == proxyType) &&
            (identical(other.platform, platform) ||
                other.platform == platform) &&
            (identical(other.parameters, parameters) ||
                other.parameters == parameters) &&
            (identical(other.storage, storage) || other.storage == storage) &&
            (identical(other.tag, tag) || other.tag == tag) &&
            (identical(other.comment, comment) || other.comment == comment) &&
            (identical(other.sort, sort) || other.sort == sort));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, name, groupId, proxyId,
      proxyType, platform, parameters, storage, tag, comment, sort);

  /// Create a copy of BrowserCreateRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BrowserCreateRequestImplCopyWith<_$BrowserCreateRequestImpl>
      get copyWith =>
          __$$BrowserCreateRequestImplCopyWithImpl<_$BrowserCreateRequestImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BrowserCreateRequestImplToJson(
      this,
    );
  }
}

abstract class _BrowserCreateRequest implements BrowserCreateRequest {
  const factory _BrowserCreateRequest(
          {required final String name,
          @JsonKey(name: 'group_id') required final int groupId,
          @JsonKey(name: 'proxy_id') required final int proxyId,
          @JsonKey(name: 'proxy_type') required final int proxyType,
          required final String platform,
          required final String parameters,
          @JsonKey(includeIfNull: false) final String? storage,
          @JsonKey(includeIfNull: false) final String? tag,
          @JsonKey(includeIfNull: false) final String? comment,
          @JsonKey(includeIfNull: false) final int? sort}) =
      _$BrowserCreateRequestImpl;

  factory _BrowserCreateRequest.fromJson(Map<String, dynamic> json) =
      _$BrowserCreateRequestImpl.fromJson;

  @override
  String get name; // 窗口名称，必需，最大100字符
  @override
  @JsonKey(name: 'group_id')
  int get groupId; // 分组ID，必需
  @override
  @JsonKey(name: 'proxy_id')
  int get proxyId; // 代理ID，必需
  @override
  @JsonKey(name: 'proxy_type')
  int get proxyType; // 代理类型，必需
  @override
  String get platform; // 所属平台，必需
  @override
  String get parameters; // JSON字符串参数，必需
  @override
  @JsonKey(includeIfNull: false)
  String? get storage; // 存储信息，可选
  @override
  @JsonKey(includeIfNull: false)
  String? get tag; // 标签，可选
  @override
  @JsonKey(includeIfNull: false)
  String? get comment; // 备注，可选
  @override
  @JsonKey(includeIfNull: false)
  int? get sort;

  /// Create a copy of BrowserCreateRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BrowserCreateRequestImplCopyWith<_$BrowserCreateRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}

BrowserUpdateRequest _$BrowserUpdateRequestFromJson(Map<String, dynamic> json) {
  return _BrowserUpdateRequest.fromJson(json);
}

/// @nodoc
mixin _$BrowserUpdateRequest {
  int get id => throw _privateConstructorUsedError; // 编号，必需
  String get name => throw _privateConstructorUsedError; // 窗口名称，必需
  @JsonKey(name: 'group_id')
  int get groupId => throw _privateConstructorUsedError; // 分组ID，必需
  @JsonKey(name: 'proxy_id')
  int get proxyId => throw _privateConstructorUsedError; // 代理ID，必需
  @JsonKey(name: 'proxy_type')
  int get proxyType => throw _privateConstructorUsedError; // 代理类型，默认自有代理(2)
  String get platform => throw _privateConstructorUsedError; // 平台，必需
  String get parameters => throw _privateConstructorUsedError; // JSON字符串参数，必需
  String? get storage => throw _privateConstructorUsedError; // 存储信息，可选
  String? get tag => throw _privateConstructorUsedError; // 标签，可选
  String? get comment => throw _privateConstructorUsedError; // 备注，可选
  int? get sort => throw _privateConstructorUsedError;

  /// Serializes this BrowserUpdateRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of BrowserUpdateRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BrowserUpdateRequestCopyWith<BrowserUpdateRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BrowserUpdateRequestCopyWith<$Res> {
  factory $BrowserUpdateRequestCopyWith(BrowserUpdateRequest value,
          $Res Function(BrowserUpdateRequest) then) =
      _$BrowserUpdateRequestCopyWithImpl<$Res, BrowserUpdateRequest>;
  @useResult
  $Res call(
      {int id,
      String name,
      @JsonKey(name: 'group_id') int groupId,
      @JsonKey(name: 'proxy_id') int proxyId,
      @JsonKey(name: 'proxy_type') int proxyType,
      String platform,
      String parameters,
      String? storage,
      String? tag,
      String? comment,
      int? sort});
}

/// @nodoc
class _$BrowserUpdateRequestCopyWithImpl<$Res,
        $Val extends BrowserUpdateRequest>
    implements $BrowserUpdateRequestCopyWith<$Res> {
  _$BrowserUpdateRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BrowserUpdateRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? groupId = null,
    Object? proxyId = null,
    Object? proxyType = null,
    Object? platform = null,
    Object? parameters = null,
    Object? storage = freezed,
    Object? tag = freezed,
    Object? comment = freezed,
    Object? sort = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      groupId: null == groupId
          ? _value.groupId
          : groupId // ignore: cast_nullable_to_non_nullable
              as int,
      proxyId: null == proxyId
          ? _value.proxyId
          : proxyId // ignore: cast_nullable_to_non_nullable
              as int,
      proxyType: null == proxyType
          ? _value.proxyType
          : proxyType // ignore: cast_nullable_to_non_nullable
              as int,
      platform: null == platform
          ? _value.platform
          : platform // ignore: cast_nullable_to_non_nullable
              as String,
      parameters: null == parameters
          ? _value.parameters
          : parameters // ignore: cast_nullable_to_non_nullable
              as String,
      storage: freezed == storage
          ? _value.storage
          : storage // ignore: cast_nullable_to_non_nullable
              as String?,
      tag: freezed == tag
          ? _value.tag
          : tag // ignore: cast_nullable_to_non_nullable
              as String?,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
      sort: freezed == sort
          ? _value.sort
          : sort // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BrowserUpdateRequestImplCopyWith<$Res>
    implements $BrowserUpdateRequestCopyWith<$Res> {
  factory _$$BrowserUpdateRequestImplCopyWith(_$BrowserUpdateRequestImpl value,
          $Res Function(_$BrowserUpdateRequestImpl) then) =
      __$$BrowserUpdateRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      String name,
      @JsonKey(name: 'group_id') int groupId,
      @JsonKey(name: 'proxy_id') int proxyId,
      @JsonKey(name: 'proxy_type') int proxyType,
      String platform,
      String parameters,
      String? storage,
      String? tag,
      String? comment,
      int? sort});
}

/// @nodoc
class __$$BrowserUpdateRequestImplCopyWithImpl<$Res>
    extends _$BrowserUpdateRequestCopyWithImpl<$Res, _$BrowserUpdateRequestImpl>
    implements _$$BrowserUpdateRequestImplCopyWith<$Res> {
  __$$BrowserUpdateRequestImplCopyWithImpl(_$BrowserUpdateRequestImpl _value,
      $Res Function(_$BrowserUpdateRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of BrowserUpdateRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? groupId = null,
    Object? proxyId = null,
    Object? proxyType = null,
    Object? platform = null,
    Object? parameters = null,
    Object? storage = freezed,
    Object? tag = freezed,
    Object? comment = freezed,
    Object? sort = freezed,
  }) {
    return _then(_$BrowserUpdateRequestImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      groupId: null == groupId
          ? _value.groupId
          : groupId // ignore: cast_nullable_to_non_nullable
              as int,
      proxyId: null == proxyId
          ? _value.proxyId
          : proxyId // ignore: cast_nullable_to_non_nullable
              as int,
      proxyType: null == proxyType
          ? _value.proxyType
          : proxyType // ignore: cast_nullable_to_non_nullable
              as int,
      platform: null == platform
          ? _value.platform
          : platform // ignore: cast_nullable_to_non_nullable
              as String,
      parameters: null == parameters
          ? _value.parameters
          : parameters // ignore: cast_nullable_to_non_nullable
              as String,
      storage: freezed == storage
          ? _value.storage
          : storage // ignore: cast_nullable_to_non_nullable
              as String?,
      tag: freezed == tag
          ? _value.tag
          : tag // ignore: cast_nullable_to_non_nullable
              as String?,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
      sort: freezed == sort
          ? _value.sort
          : sort // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BrowserUpdateRequestImpl implements _BrowserUpdateRequest {
  const _$BrowserUpdateRequestImpl(
      {required this.id,
      required this.name,
      @JsonKey(name: 'group_id') required this.groupId,
      @JsonKey(name: 'proxy_id') required this.proxyId,
      @JsonKey(name: 'proxy_type') this.proxyType = 2,
      required this.platform,
      required this.parameters,
      this.storage,
      this.tag,
      this.comment,
      this.sort});

  factory _$BrowserUpdateRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$BrowserUpdateRequestImplFromJson(json);

  @override
  final int id;
// 编号，必需
  @override
  final String name;
// 窗口名称，必需
  @override
  @JsonKey(name: 'group_id')
  final int groupId;
// 分组ID，必需
  @override
  @JsonKey(name: 'proxy_id')
  final int proxyId;
// 代理ID，必需
  @override
  @JsonKey(name: 'proxy_type')
  final int proxyType;
// 代理类型，默认自有代理(2)
  @override
  final String platform;
// 平台，必需
  @override
  final String parameters;
// JSON字符串参数，必需
  @override
  final String? storage;
// 存储信息，可选
  @override
  final String? tag;
// 标签，可选
  @override
  final String? comment;
// 备注，可选
  @override
  final int? sort;

  @override
  String toString() {
    return 'BrowserUpdateRequest(id: $id, name: $name, groupId: $groupId, proxyId: $proxyId, proxyType: $proxyType, platform: $platform, parameters: $parameters, storage: $storage, tag: $tag, comment: $comment, sort: $sort)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BrowserUpdateRequestImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.groupId, groupId) || other.groupId == groupId) &&
            (identical(other.proxyId, proxyId) || other.proxyId == proxyId) &&
            (identical(other.proxyType, proxyType) ||
                other.proxyType == proxyType) &&
            (identical(other.platform, platform) ||
                other.platform == platform) &&
            (identical(other.parameters, parameters) ||
                other.parameters == parameters) &&
            (identical(other.storage, storage) || other.storage == storage) &&
            (identical(other.tag, tag) || other.tag == tag) &&
            (identical(other.comment, comment) || other.comment == comment) &&
            (identical(other.sort, sort) || other.sort == sort));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, name, groupId, proxyId,
      proxyType, platform, parameters, storage, tag, comment, sort);

  /// Create a copy of BrowserUpdateRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BrowserUpdateRequestImplCopyWith<_$BrowserUpdateRequestImpl>
      get copyWith =>
          __$$BrowserUpdateRequestImplCopyWithImpl<_$BrowserUpdateRequestImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BrowserUpdateRequestImplToJson(
      this,
    );
  }
}

abstract class _BrowserUpdateRequest implements BrowserUpdateRequest {
  const factory _BrowserUpdateRequest(
      {required final int id,
      required final String name,
      @JsonKey(name: 'group_id') required final int groupId,
      @JsonKey(name: 'proxy_id') required final int proxyId,
      @JsonKey(name: 'proxy_type') final int proxyType,
      required final String platform,
      required final String parameters,
      final String? storage,
      final String? tag,
      final String? comment,
      final int? sort}) = _$BrowserUpdateRequestImpl;

  factory _BrowserUpdateRequest.fromJson(Map<String, dynamic> json) =
      _$BrowserUpdateRequestImpl.fromJson;

  @override
  int get id; // 编号，必需
  @override
  String get name; // 窗口名称，必需
  @override
  @JsonKey(name: 'group_id')
  int get groupId; // 分组ID，必需
  @override
  @JsonKey(name: 'proxy_id')
  int get proxyId; // 代理ID，必需
  @override
  @JsonKey(name: 'proxy_type')
  int get proxyType; // 代理类型，默认自有代理(2)
  @override
  String get platform; // 平台，必需
  @override
  String get parameters; // JSON字符串参数，必需
  @override
  String? get storage; // 存储信息，可选
  @override
  String? get tag; // 标签，可选
  @override
  String? get comment; // 备注，可选
  @override
  int? get sort;

  /// Create a copy of BrowserUpdateRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BrowserUpdateRequestImplCopyWith<_$BrowserUpdateRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}
