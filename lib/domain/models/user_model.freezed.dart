// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

UserModel _$UserModelFromJson(Map<String, dynamic> json) {
  return _UserModel.fromJson(json);
}

/// @nodoc
mixin _$UserModel {
  @JsonKey(name: 'user_name')
  String? get username => throw _privateConstructorUsedError;
  String? get email => throw _privateConstructorUsedError;
  String? get telephone => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_active')
  bool? get isActive => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_deleted')
  bool? get isDeleted => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_two_factor_enabled')
  bool? get isTwoFactorEnabled => throw _privateConstructorUsedError;
  @JsonKey(name: 'two_factor_secret')
  String? get twoFactorSecret => throw _privateConstructorUsedError;
  @JsonKey(name: 'real_name_type')
  int? get realNameType => throw _privateConstructorUsedError;
  @JsonKey(name: 'real_name')
  String? get realName => throw _privateConstructorUsedError;
  @JsonKey(name: 'id_card_number')
  String? get idCardNumber => throw _privateConstructorUsedError;
  @JsonKey(name: 'company_name')
  String? get companyName => throw _privateConstructorUsedError;
  @JsonKey(name: 'company_unified_social_code')
  String? get companyUnifiedSocialCode => throw _privateConstructorUsedError;
  @JsonKey(name: 'invite_user_id')
  int? get inviteUserId => throw _privateConstructorUsedError;
  @JsonKey(name: 'commission_type')
  int? get commissionType => throw _privateConstructorUsedError;
  @JsonKey(name: 'commission_rate')
  double? get commissionRate => throw _privateConstructorUsedError;
  @JsonKey(name: 'invite_code')
  String? get inviteCode => throw _privateConstructorUsedError;
  @JsonKey(name: 'team')
  Team? get team => throw _privateConstructorUsedError;
  @JsonKey(name: 'role')
  Role? get role => throw _privateConstructorUsedError;
  @JsonKey(name: 'wallet_amount')
  double? get walletAmount => throw _privateConstructorUsedError;
  @JsonKey(name: 'subscription')
  dynamic get subscription => throw _privateConstructorUsedError;
  @JsonKey(name: 'environment_size_sum')
  int? get environmentSizeSum => throw _privateConstructorUsedError;

  /// Serializes this UserModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UserModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserModelCopyWith<UserModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserModelCopyWith<$Res> {
  factory $UserModelCopyWith(UserModel value, $Res Function(UserModel) then) =
      _$UserModelCopyWithImpl<$Res, UserModel>;
  @useResult
  $Res call(
      {@JsonKey(name: 'user_name') String? username,
      String? email,
      String? telephone,
      @JsonKey(name: 'is_active') bool? isActive,
      @JsonKey(name: 'is_deleted') bool? isDeleted,
      @JsonKey(name: 'is_two_factor_enabled') bool? isTwoFactorEnabled,
      @JsonKey(name: 'two_factor_secret') String? twoFactorSecret,
      @JsonKey(name: 'real_name_type') int? realNameType,
      @JsonKey(name: 'real_name') String? realName,
      @JsonKey(name: 'id_card_number') String? idCardNumber,
      @JsonKey(name: 'company_name') String? companyName,
      @JsonKey(name: 'company_unified_social_code')
      String? companyUnifiedSocialCode,
      @JsonKey(name: 'invite_user_id') int? inviteUserId,
      @JsonKey(name: 'commission_type') int? commissionType,
      @JsonKey(name: 'commission_rate') double? commissionRate,
      @JsonKey(name: 'invite_code') String? inviteCode,
      @JsonKey(name: 'team') Team? team,
      @JsonKey(name: 'role') Role? role,
      @JsonKey(name: 'wallet_amount') double? walletAmount,
      @JsonKey(name: 'subscription') dynamic subscription,
      @JsonKey(name: 'environment_size_sum') int? environmentSizeSum});

  $TeamCopyWith<$Res>? get team;
  $RoleCopyWith<$Res>? get role;
}

/// @nodoc
class _$UserModelCopyWithImpl<$Res, $Val extends UserModel>
    implements $UserModelCopyWith<$Res> {
  _$UserModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? username = freezed,
    Object? email = freezed,
    Object? telephone = freezed,
    Object? isActive = freezed,
    Object? isDeleted = freezed,
    Object? isTwoFactorEnabled = freezed,
    Object? twoFactorSecret = freezed,
    Object? realNameType = freezed,
    Object? realName = freezed,
    Object? idCardNumber = freezed,
    Object? companyName = freezed,
    Object? companyUnifiedSocialCode = freezed,
    Object? inviteUserId = freezed,
    Object? commissionType = freezed,
    Object? commissionRate = freezed,
    Object? inviteCode = freezed,
    Object? team = freezed,
    Object? role = freezed,
    Object? walletAmount = freezed,
    Object? subscription = freezed,
    Object? environmentSizeSum = freezed,
  }) {
    return _then(_value.copyWith(
      username: freezed == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      telephone: freezed == telephone
          ? _value.telephone
          : telephone // ignore: cast_nullable_to_non_nullable
              as String?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      isDeleted: freezed == isDeleted
          ? _value.isDeleted
          : isDeleted // ignore: cast_nullable_to_non_nullable
              as bool?,
      isTwoFactorEnabled: freezed == isTwoFactorEnabled
          ? _value.isTwoFactorEnabled
          : isTwoFactorEnabled // ignore: cast_nullable_to_non_nullable
              as bool?,
      twoFactorSecret: freezed == twoFactorSecret
          ? _value.twoFactorSecret
          : twoFactorSecret // ignore: cast_nullable_to_non_nullable
              as String?,
      realNameType: freezed == realNameType
          ? _value.realNameType
          : realNameType // ignore: cast_nullable_to_non_nullable
              as int?,
      realName: freezed == realName
          ? _value.realName
          : realName // ignore: cast_nullable_to_non_nullable
              as String?,
      idCardNumber: freezed == idCardNumber
          ? _value.idCardNumber
          : idCardNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      companyName: freezed == companyName
          ? _value.companyName
          : companyName // ignore: cast_nullable_to_non_nullable
              as String?,
      companyUnifiedSocialCode: freezed == companyUnifiedSocialCode
          ? _value.companyUnifiedSocialCode
          : companyUnifiedSocialCode // ignore: cast_nullable_to_non_nullable
              as String?,
      inviteUserId: freezed == inviteUserId
          ? _value.inviteUserId
          : inviteUserId // ignore: cast_nullable_to_non_nullable
              as int?,
      commissionType: freezed == commissionType
          ? _value.commissionType
          : commissionType // ignore: cast_nullable_to_non_nullable
              as int?,
      commissionRate: freezed == commissionRate
          ? _value.commissionRate
          : commissionRate // ignore: cast_nullable_to_non_nullable
              as double?,
      inviteCode: freezed == inviteCode
          ? _value.inviteCode
          : inviteCode // ignore: cast_nullable_to_non_nullable
              as String?,
      team: freezed == team
          ? _value.team
          : team // ignore: cast_nullable_to_non_nullable
              as Team?,
      role: freezed == role
          ? _value.role
          : role // ignore: cast_nullable_to_non_nullable
              as Role?,
      walletAmount: freezed == walletAmount
          ? _value.walletAmount
          : walletAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      subscription: freezed == subscription
          ? _value.subscription
          : subscription // ignore: cast_nullable_to_non_nullable
              as dynamic,
      environmentSizeSum: freezed == environmentSizeSum
          ? _value.environmentSizeSum
          : environmentSizeSum // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }

  /// Create a copy of UserModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TeamCopyWith<$Res>? get team {
    if (_value.team == null) {
      return null;
    }

    return $TeamCopyWith<$Res>(_value.team!, (value) {
      return _then(_value.copyWith(team: value) as $Val);
    });
  }

  /// Create a copy of UserModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $RoleCopyWith<$Res>? get role {
    if (_value.role == null) {
      return null;
    }

    return $RoleCopyWith<$Res>(_value.role!, (value) {
      return _then(_value.copyWith(role: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$UserModelImplCopyWith<$Res>
    implements $UserModelCopyWith<$Res> {
  factory _$$UserModelImplCopyWith(
          _$UserModelImpl value, $Res Function(_$UserModelImpl) then) =
      __$$UserModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'user_name') String? username,
      String? email,
      String? telephone,
      @JsonKey(name: 'is_active') bool? isActive,
      @JsonKey(name: 'is_deleted') bool? isDeleted,
      @JsonKey(name: 'is_two_factor_enabled') bool? isTwoFactorEnabled,
      @JsonKey(name: 'two_factor_secret') String? twoFactorSecret,
      @JsonKey(name: 'real_name_type') int? realNameType,
      @JsonKey(name: 'real_name') String? realName,
      @JsonKey(name: 'id_card_number') String? idCardNumber,
      @JsonKey(name: 'company_name') String? companyName,
      @JsonKey(name: 'company_unified_social_code')
      String? companyUnifiedSocialCode,
      @JsonKey(name: 'invite_user_id') int? inviteUserId,
      @JsonKey(name: 'commission_type') int? commissionType,
      @JsonKey(name: 'commission_rate') double? commissionRate,
      @JsonKey(name: 'invite_code') String? inviteCode,
      @JsonKey(name: 'team') Team? team,
      @JsonKey(name: 'role') Role? role,
      @JsonKey(name: 'wallet_amount') double? walletAmount,
      @JsonKey(name: 'subscription') dynamic subscription,
      @JsonKey(name: 'environment_size_sum') int? environmentSizeSum});

  @override
  $TeamCopyWith<$Res>? get team;
  @override
  $RoleCopyWith<$Res>? get role;
}

/// @nodoc
class __$$UserModelImplCopyWithImpl<$Res>
    extends _$UserModelCopyWithImpl<$Res, _$UserModelImpl>
    implements _$$UserModelImplCopyWith<$Res> {
  __$$UserModelImplCopyWithImpl(
      _$UserModelImpl _value, $Res Function(_$UserModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? username = freezed,
    Object? email = freezed,
    Object? telephone = freezed,
    Object? isActive = freezed,
    Object? isDeleted = freezed,
    Object? isTwoFactorEnabled = freezed,
    Object? twoFactorSecret = freezed,
    Object? realNameType = freezed,
    Object? realName = freezed,
    Object? idCardNumber = freezed,
    Object? companyName = freezed,
    Object? companyUnifiedSocialCode = freezed,
    Object? inviteUserId = freezed,
    Object? commissionType = freezed,
    Object? commissionRate = freezed,
    Object? inviteCode = freezed,
    Object? team = freezed,
    Object? role = freezed,
    Object? walletAmount = freezed,
    Object? subscription = freezed,
    Object? environmentSizeSum = freezed,
  }) {
    return _then(_$UserModelImpl(
      username: freezed == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      telephone: freezed == telephone
          ? _value.telephone
          : telephone // ignore: cast_nullable_to_non_nullable
              as String?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      isDeleted: freezed == isDeleted
          ? _value.isDeleted
          : isDeleted // ignore: cast_nullable_to_non_nullable
              as bool?,
      isTwoFactorEnabled: freezed == isTwoFactorEnabled
          ? _value.isTwoFactorEnabled
          : isTwoFactorEnabled // ignore: cast_nullable_to_non_nullable
              as bool?,
      twoFactorSecret: freezed == twoFactorSecret
          ? _value.twoFactorSecret
          : twoFactorSecret // ignore: cast_nullable_to_non_nullable
              as String?,
      realNameType: freezed == realNameType
          ? _value.realNameType
          : realNameType // ignore: cast_nullable_to_non_nullable
              as int?,
      realName: freezed == realName
          ? _value.realName
          : realName // ignore: cast_nullable_to_non_nullable
              as String?,
      idCardNumber: freezed == idCardNumber
          ? _value.idCardNumber
          : idCardNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      companyName: freezed == companyName
          ? _value.companyName
          : companyName // ignore: cast_nullable_to_non_nullable
              as String?,
      companyUnifiedSocialCode: freezed == companyUnifiedSocialCode
          ? _value.companyUnifiedSocialCode
          : companyUnifiedSocialCode // ignore: cast_nullable_to_non_nullable
              as String?,
      inviteUserId: freezed == inviteUserId
          ? _value.inviteUserId
          : inviteUserId // ignore: cast_nullable_to_non_nullable
              as int?,
      commissionType: freezed == commissionType
          ? _value.commissionType
          : commissionType // ignore: cast_nullable_to_non_nullable
              as int?,
      commissionRate: freezed == commissionRate
          ? _value.commissionRate
          : commissionRate // ignore: cast_nullable_to_non_nullable
              as double?,
      inviteCode: freezed == inviteCode
          ? _value.inviteCode
          : inviteCode // ignore: cast_nullable_to_non_nullable
              as String?,
      team: freezed == team
          ? _value.team
          : team // ignore: cast_nullable_to_non_nullable
              as Team?,
      role: freezed == role
          ? _value.role
          : role // ignore: cast_nullable_to_non_nullable
              as Role?,
      walletAmount: freezed == walletAmount
          ? _value.walletAmount
          : walletAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      subscription: freezed == subscription
          ? _value.subscription
          : subscription // ignore: cast_nullable_to_non_nullable
              as dynamic,
      environmentSizeSum: freezed == environmentSizeSum
          ? _value.environmentSizeSum
          : environmentSizeSum // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserModelImpl implements _UserModel {
  const _$UserModelImpl(
      {@JsonKey(name: 'user_name') this.username,
      this.email,
      this.telephone,
      @JsonKey(name: 'is_active') this.isActive,
      @JsonKey(name: 'is_deleted') this.isDeleted,
      @JsonKey(name: 'is_two_factor_enabled') this.isTwoFactorEnabled,
      @JsonKey(name: 'two_factor_secret') this.twoFactorSecret,
      @JsonKey(name: 'real_name_type') this.realNameType,
      @JsonKey(name: 'real_name') this.realName,
      @JsonKey(name: 'id_card_number') this.idCardNumber,
      @JsonKey(name: 'company_name') this.companyName,
      @JsonKey(name: 'company_unified_social_code')
      this.companyUnifiedSocialCode,
      @JsonKey(name: 'invite_user_id') this.inviteUserId,
      @JsonKey(name: 'commission_type') this.commissionType,
      @JsonKey(name: 'commission_rate') this.commissionRate,
      @JsonKey(name: 'invite_code') this.inviteCode,
      @JsonKey(name: 'team') this.team,
      @JsonKey(name: 'role') this.role,
      @JsonKey(name: 'wallet_amount') this.walletAmount,
      @JsonKey(name: 'subscription') this.subscription,
      @JsonKey(name: 'environment_size_sum') this.environmentSizeSum});

  factory _$UserModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserModelImplFromJson(json);

  @override
  @JsonKey(name: 'user_name')
  final String? username;
  @override
  final String? email;
  @override
  final String? telephone;
  @override
  @JsonKey(name: 'is_active')
  final bool? isActive;
  @override
  @JsonKey(name: 'is_deleted')
  final bool? isDeleted;
  @override
  @JsonKey(name: 'is_two_factor_enabled')
  final bool? isTwoFactorEnabled;
  @override
  @JsonKey(name: 'two_factor_secret')
  final String? twoFactorSecret;
  @override
  @JsonKey(name: 'real_name_type')
  final int? realNameType;
  @override
  @JsonKey(name: 'real_name')
  final String? realName;
  @override
  @JsonKey(name: 'id_card_number')
  final String? idCardNumber;
  @override
  @JsonKey(name: 'company_name')
  final String? companyName;
  @override
  @JsonKey(name: 'company_unified_social_code')
  final String? companyUnifiedSocialCode;
  @override
  @JsonKey(name: 'invite_user_id')
  final int? inviteUserId;
  @override
  @JsonKey(name: 'commission_type')
  final int? commissionType;
  @override
  @JsonKey(name: 'commission_rate')
  final double? commissionRate;
  @override
  @JsonKey(name: 'invite_code')
  final String? inviteCode;
  @override
  @JsonKey(name: 'team')
  final Team? team;
  @override
  @JsonKey(name: 'role')
  final Role? role;
  @override
  @JsonKey(name: 'wallet_amount')
  final double? walletAmount;
  @override
  @JsonKey(name: 'subscription')
  final dynamic subscription;
  @override
  @JsonKey(name: 'environment_size_sum')
  final int? environmentSizeSum;

  @override
  String toString() {
    return 'UserModel(username: $username, email: $email, telephone: $telephone, isActive: $isActive, isDeleted: $isDeleted, isTwoFactorEnabled: $isTwoFactorEnabled, twoFactorSecret: $twoFactorSecret, realNameType: $realNameType, realName: $realName, idCardNumber: $idCardNumber, companyName: $companyName, companyUnifiedSocialCode: $companyUnifiedSocialCode, inviteUserId: $inviteUserId, commissionType: $commissionType, commissionRate: $commissionRate, inviteCode: $inviteCode, team: $team, role: $role, walletAmount: $walletAmount, subscription: $subscription, environmentSizeSum: $environmentSizeSum)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserModelImpl &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.telephone, telephone) ||
                other.telephone == telephone) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.isDeleted, isDeleted) ||
                other.isDeleted == isDeleted) &&
            (identical(other.isTwoFactorEnabled, isTwoFactorEnabled) ||
                other.isTwoFactorEnabled == isTwoFactorEnabled) &&
            (identical(other.twoFactorSecret, twoFactorSecret) ||
                other.twoFactorSecret == twoFactorSecret) &&
            (identical(other.realNameType, realNameType) ||
                other.realNameType == realNameType) &&
            (identical(other.realName, realName) ||
                other.realName == realName) &&
            (identical(other.idCardNumber, idCardNumber) ||
                other.idCardNumber == idCardNumber) &&
            (identical(other.companyName, companyName) ||
                other.companyName == companyName) &&
            (identical(
                    other.companyUnifiedSocialCode, companyUnifiedSocialCode) ||
                other.companyUnifiedSocialCode == companyUnifiedSocialCode) &&
            (identical(other.inviteUserId, inviteUserId) ||
                other.inviteUserId == inviteUserId) &&
            (identical(other.commissionType, commissionType) ||
                other.commissionType == commissionType) &&
            (identical(other.commissionRate, commissionRate) ||
                other.commissionRate == commissionRate) &&
            (identical(other.inviteCode, inviteCode) ||
                other.inviteCode == inviteCode) &&
            (identical(other.team, team) || other.team == team) &&
            (identical(other.role, role) || other.role == role) &&
            (identical(other.walletAmount, walletAmount) ||
                other.walletAmount == walletAmount) &&
            const DeepCollectionEquality()
                .equals(other.subscription, subscription) &&
            (identical(other.environmentSizeSum, environmentSizeSum) ||
                other.environmentSizeSum == environmentSizeSum));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        username,
        email,
        telephone,
        isActive,
        isDeleted,
        isTwoFactorEnabled,
        twoFactorSecret,
        realNameType,
        realName,
        idCardNumber,
        companyName,
        companyUnifiedSocialCode,
        inviteUserId,
        commissionType,
        commissionRate,
        inviteCode,
        team,
        role,
        walletAmount,
        const DeepCollectionEquality().hash(subscription),
        environmentSizeSum
      ]);

  /// Create a copy of UserModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserModelImplCopyWith<_$UserModelImpl> get copyWith =>
      __$$UserModelImplCopyWithImpl<_$UserModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserModelImplToJson(
      this,
    );
  }
}

abstract class _UserModel implements UserModel {
  const factory _UserModel(
      {@JsonKey(name: 'user_name') final String? username,
      final String? email,
      final String? telephone,
      @JsonKey(name: 'is_active') final bool? isActive,
      @JsonKey(name: 'is_deleted') final bool? isDeleted,
      @JsonKey(name: 'is_two_factor_enabled') final bool? isTwoFactorEnabled,
      @JsonKey(name: 'two_factor_secret') final String? twoFactorSecret,
      @JsonKey(name: 'real_name_type') final int? realNameType,
      @JsonKey(name: 'real_name') final String? realName,
      @JsonKey(name: 'id_card_number') final String? idCardNumber,
      @JsonKey(name: 'company_name') final String? companyName,
      @JsonKey(name: 'company_unified_social_code')
      final String? companyUnifiedSocialCode,
      @JsonKey(name: 'invite_user_id') final int? inviteUserId,
      @JsonKey(name: 'commission_type') final int? commissionType,
      @JsonKey(name: 'commission_rate') final double? commissionRate,
      @JsonKey(name: 'invite_code') final String? inviteCode,
      @JsonKey(name: 'team') final Team? team,
      @JsonKey(name: 'role') final Role? role,
      @JsonKey(name: 'wallet_amount') final double? walletAmount,
      @JsonKey(name: 'subscription') final dynamic subscription,
      @JsonKey(name: 'environment_size_sum')
      final int? environmentSizeSum}) = _$UserModelImpl;

  factory _UserModel.fromJson(Map<String, dynamic> json) =
      _$UserModelImpl.fromJson;

  @override
  @JsonKey(name: 'user_name')
  String? get username;
  @override
  String? get email;
  @override
  String? get telephone;
  @override
  @JsonKey(name: 'is_active')
  bool? get isActive;
  @override
  @JsonKey(name: 'is_deleted')
  bool? get isDeleted;
  @override
  @JsonKey(name: 'is_two_factor_enabled')
  bool? get isTwoFactorEnabled;
  @override
  @JsonKey(name: 'two_factor_secret')
  String? get twoFactorSecret;
  @override
  @JsonKey(name: 'real_name_type')
  int? get realNameType;
  @override
  @JsonKey(name: 'real_name')
  String? get realName;
  @override
  @JsonKey(name: 'id_card_number')
  String? get idCardNumber;
  @override
  @JsonKey(name: 'company_name')
  String? get companyName;
  @override
  @JsonKey(name: 'company_unified_social_code')
  String? get companyUnifiedSocialCode;
  @override
  @JsonKey(name: 'invite_user_id')
  int? get inviteUserId;
  @override
  @JsonKey(name: 'commission_type')
  int? get commissionType;
  @override
  @JsonKey(name: 'commission_rate')
  double? get commissionRate;
  @override
  @JsonKey(name: 'invite_code')
  String? get inviteCode;
  @override
  @JsonKey(name: 'team')
  Team? get team;
  @override
  @JsonKey(name: 'role')
  Role? get role;
  @override
  @JsonKey(name: 'wallet_amount')
  double? get walletAmount;
  @override
  @JsonKey(name: 'subscription')
  dynamic get subscription;
  @override
  @JsonKey(name: 'environment_size_sum')
  int? get environmentSizeSum;

  /// Create a copy of UserModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserModelImplCopyWith<_$UserModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Team _$TeamFromJson(Map<String, dynamic> json) {
  return _Team.fromJson(json);
}

/// @nodoc
mixin _$Team {
  String get name => throw _privateConstructorUsedError;
  @JsonKey(name: 'owner_id')
  int get ownerId => throw _privateConstructorUsedError;

  /// Serializes this Team to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Team
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TeamCopyWith<Team> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TeamCopyWith<$Res> {
  factory $TeamCopyWith(Team value, $Res Function(Team) then) =
      _$TeamCopyWithImpl<$Res, Team>;
  @useResult
  $Res call({String name, @JsonKey(name: 'owner_id') int ownerId});
}

/// @nodoc
class _$TeamCopyWithImpl<$Res, $Val extends Team>
    implements $TeamCopyWith<$Res> {
  _$TeamCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Team
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? ownerId = null,
  }) {
    return _then(_value.copyWith(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      ownerId: null == ownerId
          ? _value.ownerId
          : ownerId // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TeamImplCopyWith<$Res> implements $TeamCopyWith<$Res> {
  factory _$$TeamImplCopyWith(
          _$TeamImpl value, $Res Function(_$TeamImpl) then) =
      __$$TeamImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String name, @JsonKey(name: 'owner_id') int ownerId});
}

/// @nodoc
class __$$TeamImplCopyWithImpl<$Res>
    extends _$TeamCopyWithImpl<$Res, _$TeamImpl>
    implements _$$TeamImplCopyWith<$Res> {
  __$$TeamImplCopyWithImpl(_$TeamImpl _value, $Res Function(_$TeamImpl) _then)
      : super(_value, _then);

  /// Create a copy of Team
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? ownerId = null,
  }) {
    return _then(_$TeamImpl(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      ownerId: null == ownerId
          ? _value.ownerId
          : ownerId // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TeamImpl implements _Team {
  const _$TeamImpl(
      {required this.name, @JsonKey(name: 'owner_id') required this.ownerId});

  factory _$TeamImpl.fromJson(Map<String, dynamic> json) =>
      _$$TeamImplFromJson(json);

  @override
  final String name;
  @override
  @JsonKey(name: 'owner_id')
  final int ownerId;

  @override
  String toString() {
    return 'Team(name: $name, ownerId: $ownerId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TeamImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.ownerId, ownerId) || other.ownerId == ownerId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, name, ownerId);

  /// Create a copy of Team
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TeamImplCopyWith<_$TeamImpl> get copyWith =>
      __$$TeamImplCopyWithImpl<_$TeamImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TeamImplToJson(
      this,
    );
  }
}

abstract class _Team implements Team {
  const factory _Team(
      {required final String name,
      @JsonKey(name: 'owner_id') required final int ownerId}) = _$TeamImpl;

  factory _Team.fromJson(Map<String, dynamic> json) = _$TeamImpl.fromJson;

  @override
  String get name;
  @override
  @JsonKey(name: 'owner_id')
  int get ownerId;

  /// Create a copy of Team
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TeamImplCopyWith<_$TeamImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Role _$RoleFromJson(Map<String, dynamic> json) {
  return _Role.fromJson(json);
}

/// @nodoc
mixin _$Role {
  String get name => throw _privateConstructorUsedError;
  String get permissions => throw _privateConstructorUsedError;
  bool? get secure => throw _privateConstructorUsedError;

  /// Serializes this Role to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Role
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RoleCopyWith<Role> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RoleCopyWith<$Res> {
  factory $RoleCopyWith(Role value, $Res Function(Role) then) =
      _$RoleCopyWithImpl<$Res, Role>;
  @useResult
  $Res call({String name, String permissions, bool? secure});
}

/// @nodoc
class _$RoleCopyWithImpl<$Res, $Val extends Role>
    implements $RoleCopyWith<$Res> {
  _$RoleCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Role
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? permissions = null,
    Object? secure = freezed,
  }) {
    return _then(_value.copyWith(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      permissions: null == permissions
          ? _value.permissions
          : permissions // ignore: cast_nullable_to_non_nullable
              as String,
      secure: freezed == secure
          ? _value.secure
          : secure // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RoleImplCopyWith<$Res> implements $RoleCopyWith<$Res> {
  factory _$$RoleImplCopyWith(
          _$RoleImpl value, $Res Function(_$RoleImpl) then) =
      __$$RoleImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String name, String permissions, bool? secure});
}

/// @nodoc
class __$$RoleImplCopyWithImpl<$Res>
    extends _$RoleCopyWithImpl<$Res, _$RoleImpl>
    implements _$$RoleImplCopyWith<$Res> {
  __$$RoleImplCopyWithImpl(_$RoleImpl _value, $Res Function(_$RoleImpl) _then)
      : super(_value, _then);

  /// Create a copy of Role
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? permissions = null,
    Object? secure = freezed,
  }) {
    return _then(_$RoleImpl(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      permissions: null == permissions
          ? _value.permissions
          : permissions // ignore: cast_nullable_to_non_nullable
              as String,
      secure: freezed == secure
          ? _value.secure
          : secure // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RoleImpl implements _Role {
  const _$RoleImpl(
      {required this.name, required this.permissions, this.secure});

  factory _$RoleImpl.fromJson(Map<String, dynamic> json) =>
      _$$RoleImplFromJson(json);

  @override
  final String name;
  @override
  final String permissions;
  @override
  final bool? secure;

  @override
  String toString() {
    return 'Role(name: $name, permissions: $permissions, secure: $secure)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RoleImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.permissions, permissions) ||
                other.permissions == permissions) &&
            (identical(other.secure, secure) || other.secure == secure));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, name, permissions, secure);

  /// Create a copy of Role
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RoleImplCopyWith<_$RoleImpl> get copyWith =>
      __$$RoleImplCopyWithImpl<_$RoleImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RoleImplToJson(
      this,
    );
  }
}

abstract class _Role implements Role {
  const factory _Role(
      {required final String name,
      required final String permissions,
      final bool? secure}) = _$RoleImpl;

  factory _Role.fromJson(Map<String, dynamic> json) = _$RoleImpl.fromJson;

  @override
  String get name;
  @override
  String get permissions;
  @override
  bool? get secure;

  /// Create a copy of Role
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RoleImplCopyWith<_$RoleImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$UpdateUserInfoRequest {
  String? get email => throw _privateConstructorUsedError; // 新邮箱地址（可选）
  String? get emailCode => throw _privateConstructorUsedError; // 邮箱验证码（可选）
  String? get telephone => throw _privateConstructorUsedError; // 新手机号码（可选）
  String? get telephoneCode => throw _privateConstructorUsedError; // 手机验证码（可选）
  bool? get enable2fa => throw _privateConstructorUsedError;

  /// Create a copy of UpdateUserInfoRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UpdateUserInfoRequestCopyWith<UpdateUserInfoRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UpdateUserInfoRequestCopyWith<$Res> {
  factory $UpdateUserInfoRequestCopyWith(UpdateUserInfoRequest value,
          $Res Function(UpdateUserInfoRequest) then) =
      _$UpdateUserInfoRequestCopyWithImpl<$Res, UpdateUserInfoRequest>;
  @useResult
  $Res call(
      {String? email,
      String? emailCode,
      String? telephone,
      String? telephoneCode,
      bool? enable2fa});
}

/// @nodoc
class _$UpdateUserInfoRequestCopyWithImpl<$Res,
        $Val extends UpdateUserInfoRequest>
    implements $UpdateUserInfoRequestCopyWith<$Res> {
  _$UpdateUserInfoRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UpdateUserInfoRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? email = freezed,
    Object? emailCode = freezed,
    Object? telephone = freezed,
    Object? telephoneCode = freezed,
    Object? enable2fa = freezed,
  }) {
    return _then(_value.copyWith(
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      emailCode: freezed == emailCode
          ? _value.emailCode
          : emailCode // ignore: cast_nullable_to_non_nullable
              as String?,
      telephone: freezed == telephone
          ? _value.telephone
          : telephone // ignore: cast_nullable_to_non_nullable
              as String?,
      telephoneCode: freezed == telephoneCode
          ? _value.telephoneCode
          : telephoneCode // ignore: cast_nullable_to_non_nullable
              as String?,
      enable2fa: freezed == enable2fa
          ? _value.enable2fa
          : enable2fa // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UpdateUserInfoRequestImplCopyWith<$Res>
    implements $UpdateUserInfoRequestCopyWith<$Res> {
  factory _$$UpdateUserInfoRequestImplCopyWith(
          _$UpdateUserInfoRequestImpl value,
          $Res Function(_$UpdateUserInfoRequestImpl) then) =
      __$$UpdateUserInfoRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? email,
      String? emailCode,
      String? telephone,
      String? telephoneCode,
      bool? enable2fa});
}

/// @nodoc
class __$$UpdateUserInfoRequestImplCopyWithImpl<$Res>
    extends _$UpdateUserInfoRequestCopyWithImpl<$Res,
        _$UpdateUserInfoRequestImpl>
    implements _$$UpdateUserInfoRequestImplCopyWith<$Res> {
  __$$UpdateUserInfoRequestImplCopyWithImpl(_$UpdateUserInfoRequestImpl _value,
      $Res Function(_$UpdateUserInfoRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of UpdateUserInfoRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? email = freezed,
    Object? emailCode = freezed,
    Object? telephone = freezed,
    Object? telephoneCode = freezed,
    Object? enable2fa = freezed,
  }) {
    return _then(_$UpdateUserInfoRequestImpl(
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      emailCode: freezed == emailCode
          ? _value.emailCode
          : emailCode // ignore: cast_nullable_to_non_nullable
              as String?,
      telephone: freezed == telephone
          ? _value.telephone
          : telephone // ignore: cast_nullable_to_non_nullable
              as String?,
      telephoneCode: freezed == telephoneCode
          ? _value.telephoneCode
          : telephoneCode // ignore: cast_nullable_to_non_nullable
              as String?,
      enable2fa: freezed == enable2fa
          ? _value.enable2fa
          : enable2fa // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc

class _$UpdateUserInfoRequestImpl implements _UpdateUserInfoRequest {
  const _$UpdateUserInfoRequestImpl(
      {this.email,
      this.emailCode,
      this.telephone,
      this.telephoneCode,
      this.enable2fa});

  @override
  final String? email;
// 新邮箱地址（可选）
  @override
  final String? emailCode;
// 邮箱验证码（可选）
  @override
  final String? telephone;
// 新手机号码（可选）
  @override
  final String? telephoneCode;
// 手机验证码（可选）
  @override
  final bool? enable2fa;

  @override
  String toString() {
    return 'UpdateUserInfoRequest(email: $email, emailCode: $emailCode, telephone: $telephone, telephoneCode: $telephoneCode, enable2fa: $enable2fa)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateUserInfoRequestImpl &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.emailCode, emailCode) ||
                other.emailCode == emailCode) &&
            (identical(other.telephone, telephone) ||
                other.telephone == telephone) &&
            (identical(other.telephoneCode, telephoneCode) ||
                other.telephoneCode == telephoneCode) &&
            (identical(other.enable2fa, enable2fa) ||
                other.enable2fa == enable2fa));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, email, emailCode, telephone, telephoneCode, enable2fa);

  /// Create a copy of UpdateUserInfoRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateUserInfoRequestImplCopyWith<_$UpdateUserInfoRequestImpl>
      get copyWith => __$$UpdateUserInfoRequestImplCopyWithImpl<
          _$UpdateUserInfoRequestImpl>(this, _$identity);
}

abstract class _UpdateUserInfoRequest implements UpdateUserInfoRequest {
  const factory _UpdateUserInfoRequest(
      {final String? email,
      final String? emailCode,
      final String? telephone,
      final String? telephoneCode,
      final bool? enable2fa}) = _$UpdateUserInfoRequestImpl;

  @override
  String? get email; // 新邮箱地址（可选）
  @override
  String? get emailCode; // 邮箱验证码（可选）
  @override
  String? get telephone; // 新手机号码（可选）
  @override
  String? get telephoneCode; // 手机验证码（可选）
  @override
  bool? get enable2fa;

  /// Create a copy of UpdateUserInfoRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateUserInfoRequestImplCopyWith<_$UpdateUserInfoRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}
