// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$UserModelImpl _$$UserModelImplFromJson(Map<String, dynamic> json) =>
    _$UserModelImpl(
      username: json['user_name'] as String?,
      email: json['email'] as String?,
      telephone: json['telephone'] as String?,
      isActive: json['is_active'] as bool?,
      isDeleted: json['is_deleted'] as bool?,
      isTwoFactorEnabled: json['is_two_factor_enabled'] as bool?,
      twoFactorSecret: json['two_factor_secret'] as String?,
      realNameType: (json['real_name_type'] as num?)?.toInt(),
      realName: json['real_name'] as String?,
      idCardNumber: json['id_card_number'] as String?,
      companyName: json['company_name'] as String?,
      companyUnifiedSocialCode: json['company_unified_social_code'] as String?,
      inviteUserId: (json['invite_user_id'] as num?)?.toInt(),
      commissionType: (json['commission_type'] as num?)?.toInt(),
      commissionRate: (json['commission_rate'] as num?)?.toDouble(),
      inviteCode: json['invite_code'] as String?,
      team: json['team'] == null
          ? null
          : Team.fromJson(json['team'] as Map<String, dynamic>),
      role: json['role'] == null
          ? null
          : Role.fromJson(json['role'] as Map<String, dynamic>),
      walletAmount: (json['wallet_amount'] as num?)?.toDouble(),
      subscription: json['subscription'],
      environmentSizeSum: (json['environment_size_sum'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$UserModelImplToJson(_$UserModelImpl instance) =>
    <String, dynamic>{
      'user_name': instance.username,
      'email': instance.email,
      'telephone': instance.telephone,
      'is_active': instance.isActive,
      'is_deleted': instance.isDeleted,
      'is_two_factor_enabled': instance.isTwoFactorEnabled,
      'two_factor_secret': instance.twoFactorSecret,
      'real_name_type': instance.realNameType,
      'real_name': instance.realName,
      'id_card_number': instance.idCardNumber,
      'company_name': instance.companyName,
      'company_unified_social_code': instance.companyUnifiedSocialCode,
      'invite_user_id': instance.inviteUserId,
      'commission_type': instance.commissionType,
      'commission_rate': instance.commissionRate,
      'invite_code': instance.inviteCode,
      'team': instance.team,
      'role': instance.role,
      'wallet_amount': instance.walletAmount,
      'subscription': instance.subscription,
      'environment_size_sum': instance.environmentSizeSum,
    };

_$TeamImpl _$$TeamImplFromJson(Map<String, dynamic> json) => _$TeamImpl(
      name: json['name'] as String,
      ownerId: (json['owner_id'] as num).toInt(),
    );

Map<String, dynamic> _$$TeamImplToJson(_$TeamImpl instance) =>
    <String, dynamic>{
      'name': instance.name,
      'owner_id': instance.ownerId,
    };

_$RoleImpl _$$RoleImplFromJson(Map<String, dynamic> json) => _$RoleImpl(
      name: json['name'] as String,
      permissions: json['permissions'] as String,
      secure: json['secure'] as bool?,
    );

Map<String, dynamic> _$$RoleImplToJson(_$RoleImpl instance) =>
    <String, dynamic>{
      'name': instance.name,
      'permissions': instance.permissions,
      'secure': instance.secure,
    };
