import 'package:freezed_annotation/freezed_annotation.dart';

part 'browser_window_model.freezed.dart';
part 'browser_window_model.g.dart';

/// 浏览器窗口数据模型
@freezed
class BrowserWindowModel with _$BrowserWindowModel {
  const factory BrowserWindowModel({
    @JsonKey(name: 'environments') List<BrowserWindowItem>? windows, // 浏览器窗口列表
    required int total, // 总数据量
  }) = _BrowserWindowModel;

  /// 从 JSON 创建模型实例
  factory BrowserWindowModel.fromJson(Map<String, dynamic> json) =>
      _$BrowserWindowModelFromJson(json);
}

/// 浏览器环境查询请求模型
@freezed
class BrowserWindowRequest with _$BrowserWindowRequest {
  const factory BrowserWindowRequest({
    @JsonKey(includeIfNull: false) String? name, // 环境名称
    @JsonKey(name: 'user_id', includeIfNull: false) int? userId, // 用户ID
    @JsonKey(name: 'group_id', includeIfNull: false) int? groupId, // 分组ID
    @JsonKey(includeIfNull: false) int? limit, // 限制数量，范围1-500
    @JsonKey(includeIfNull: false) int? offset, // 偏移量，最小为0
  }) = _BrowserWindowRequest;

  factory BrowserWindowRequest.fromJson(Map<String, dynamic> json) =>
      _$BrowserWindowRequestFromJson(json);
}

/// 已删除浏览器查询请求模型
@freezed
class DeletedBrowserWindowRequest with _$DeletedBrowserWindowRequest {
  const factory DeletedBrowserWindowRequest({
    @JsonKey(name: 'group_id', includeIfNull: false) int? groupId, // 分组ID
    @JsonKey(includeIfNull: false) int? limit, // 限制数量，范围1-500
    @JsonKey(includeIfNull: false) int? offset, // 偏移量，最小为0
    @JsonKey(includeIfNull: false) String? name, // 环境名称
    @JsonKey(name: 'user_id', includeIfNull: false) int? userId, // 用户ID
  }) = _DeletedBrowserWindowRequest;

  factory DeletedBrowserWindowRequest.fromJson(Map<String, dynamic> json) =>
      _$DeletedBrowserWindowRequestFromJson(json);
}

/// 浏览器列表下单个窗口数据
@freezed
class BrowserWindowItem with _$BrowserWindowItem {
  const factory BrowserWindowItem({
    required int id, // 编号
    @JsonKey(name: 'team_id') required int teamId, // 团队ID
    required String name, // 窗口名称
    @JsonKey(name: 'user_id') required int userId, // 用户ID
    @JsonKey(name: 'group_id') required int groupId, // 分组ID
    @JsonKey(name: 'group_name') required String groupName, // 所属分组名称
    required ProxyInfo proxy, // 代理信息
    @JsonKey(name: 'proxy_type') required int proxyType, // 代理类型
    required String platform, // 所属平台
    required String storage, // 存储信息
    required String tag, // 标签
    String? comment, // 备注
    int? sort, // 排序（可选）
    int? size, // 大小（可选）
    @JsonKey(name: 'created_at') required DateTime createdAt, // 创建时间（必需）
    @JsonKey(name: 'updated_at') required DateTime updatedAt, // 更新时间（必需）
  }) = _BrowserWindowItem;

  /// 从 JSON 创建模型实例
  factory BrowserWindowItem.fromJson(Map<String, dynamic> json) =>
      _$BrowserWindowItemFromJson(json);
}

/// 独立浏览器窗口数据（详情页面使用，包含更多信息）
@freezed
class BrowserWindow with _$BrowserWindow {
  const factory BrowserWindow({
    required int id, // 编号
    @JsonKey(name: 'team_id') required int teamId, // 团队ID
    required String name, // 窗口名称
    @JsonKey(name: 'user_id') required int userId, // 用户ID
    @JsonKey(name: 'group_id') required int groupId, // 分组ID
    @JsonKey(name: 'group_name') required String groupName, // 所属分组名称
    required ProxyInfo proxy, // 代理信息
    @JsonKey(name: 'proxy_type') required int proxyType, // 代理类型
    required String platform, // 所属平台
    required String parameters, // 参数（详情中包含）
    required String storage, // 存储信息
    required String tag, // 标签
    String? comment, // 备注（详情中包含，可选）
    int? sort, // 编号
  }) = _BrowserWindow;

  /// 从 JSON 创建模型实例
  factory BrowserWindow.fromJson(Map<String, dynamic> json) =>
      _$BrowserWindowFromJson(json);
}

/// 代理信息数据模型
@freezed
class ProxyInfo with _$ProxyInfo {
  const factory ProxyInfo({
    required String name, // 地址
    required int type, // 代理类型
    required String address, // 地址
    required int port, // 端口
    required String username, // 用户名
    required String password, // 密码
  }) = _ProxyInfo;

  /// 从 JSON 创建模型实例
  factory ProxyInfo.fromJson(Map<String, dynamic> json) =>
      _$ProxyInfoFromJson(json);
}

/// 浏览器窗口更新请求模型
@freezed
class BrowserWindowUpdateRequest with _$BrowserWindowUpdateRequest {
  const factory BrowserWindowUpdateRequest({
    required int id, // 编号，必需
    @JsonKey(includeIfNull: false) String? name, // 窗口名称，可选
    @JsonKey(name: 'group_id', includeIfNull: false) int? groupId, // 分组ID，可选
    @JsonKey(name: 'proxy_id', includeIfNull: false) int? proxyId, // 代理ID，可选
    @JsonKey(name: 'proxy_type', includeIfNull: false) int? proxyType, // 代理类型，可选
    @JsonKey(includeIfNull: false) String? platform, // 平台，可选
    @JsonKey(includeIfNull: false) String? parameters, // JSON字符串参数，可选
    @JsonKey(includeIfNull: false) String? storage, // 存储信息，可选
    @JsonKey(includeIfNull: false) String? tag, // 标签，可选
    @JsonKey(includeIfNull: false) String? comment, // 备注，可选
    @JsonKey(includeIfNull: false) int? sort, // 排序，可选
  }) = _BrowserWindowUpdateRequest;

  /// 从 JSON 创建模型实例
  factory BrowserWindowUpdateRequest.fromJson(Map<String, dynamic> json) =>
      _$BrowserWindowUpdateRequestFromJson(json);
}

/// 浏览器窗口创建请求领域模型
@freezed
class BrowserCreateRequest with _$BrowserCreateRequest {
  const factory BrowserCreateRequest({
    required String name, // 窗口名称，必需，最大100字符
    @JsonKey(name: 'group_id') required int groupId, // 分组ID，必需
    @JsonKey(name: 'proxy_id') required int proxyId, // 代理ID，必需
    @JsonKey(name: 'proxy_type') required int proxyType, // 代理类型，必需
    required String platform, // 所属平台，必需
    required String parameters, // JSON字符串参数，必需
    @JsonKey(includeIfNull: false) String? storage, // 存储信息，可选
    @JsonKey(includeIfNull: false) String? tag, // 标签，可选
    @JsonKey(includeIfNull: false) String? comment, // 备注，可选
    @JsonKey(includeIfNull: false) int? sort, // 排序，可选
  }) = _BrowserCreateRequest;

  factory BrowserCreateRequest.fromJson(Map<String, dynamic> json) =>
      _$BrowserCreateRequestFromJson(json);
}

/// 浏览器更新模型
@freezed
class BrowserUpdateRequest with _$BrowserUpdateRequest {
  const factory BrowserUpdateRequest({
    required int id, // 编号，必需
    required String name, // 窗口名称，必需
    @JsonKey(name: 'group_id') required int groupId, // 分组ID，必需
    @JsonKey(name: 'proxy_id') required int proxyId, // 代理ID，必需
    @JsonKey(name: 'proxy_type') @Default(2) int proxyType, // 代理类型，默认自有代理(2)
    required String platform, // 平台，必需
    required String parameters, // JSON字符串参数，必需
    String? storage, // 存储信息，可选
    String? tag, // 标签，可选
    String? comment, // 备注，可选
    int? sort, // 排序，可选
  }) = _BrowserUpdateRequest;

  factory BrowserUpdateRequest.fromJson(Map<String, dynamic> json) =>
      _$BrowserUpdateRequestFromJson(json);
}
