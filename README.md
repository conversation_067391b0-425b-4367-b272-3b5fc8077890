# Suiyu浏览器项目

一个使用Flutter构建的现代浏览器应用程序。

## 项目架构

Suiyu浏览器采用了基于功能模块化的架构设计，结合了领域驱动设计(DDD)和传统分层架构的优势。项目以功能特性为核心组织代码，同时在全局层面维护清晰的职责分离。

### 核心架构理念

项目采用**混合分层架构**，既保持了层次的清晰性，又兼顾了功能模块的内聚性：

1. **全局共享层** (domain + repositories): 处理跨功能的核心业务逻辑和数据访问
2. **功能模块层** (features): 按业务功能组织，包含各自的控制器、模型和视图
3. **基础设施层** (core): 提供底层技术支持和工具服务

## 目录结构

```
lib/
├── core/                       # 🔧 核心基础设施层
│   ├── browser/                # 浏览器核心功能和工具
│   ├── constants/              # 应用级常量定义
│   ├── network/                # 网络相关工具和HTTP客户端
│   ├── relay/                  # 代理中继核心功能
│   ├── storage/                # 存储相关工具和缓存管理
│   └── utils/                  # 通用工具函数库
│
├── domain/                     # 🧠 领域层 - 核心业务逻辑
│   ├── models/                 # 全局领域模型
│   │   ├── auth_model.dart     # 认证相关模型
│   │   ├── user_model.dart     # 用户核心模型
│   │   ├── team_model.dart     # 团队相关模型
│   │   ├── proxy_model.dart    # 代理核心模型
│   │   ├── browser_window_model.dart # 浏览器窗口模型
│   │   └── role_model.dart     # 角色权限模型
│   │
│   └── services/               # 领域服务 - 跨功能业务逻辑
│       ├── auth_service.dart   # 认证业务服务
│       ├── user_info_notifier.dart # 用户信息状态管理
│       ├── team_service.dart   # 团队管理服务
│       ├── proxy_service.dart  # 代理管理服务
│       ├── browser_service.dart # 浏览器核心服务
│       └── locale_service.dart # 国际化服务
│
├── repositories/               # 📦 数据访问层 - 统一数据操作
│   ├── auth_repository.dart    # 认证数据访问
│   ├── user_repository.dart    # 用户数据访问
│   ├── team_repository.dart    # 团队数据访问
│   ├── proxy_repository.dart   # 代理数据访问
│   └── browser_window_repository.dart # 浏览器窗口数据访问
│
├── features/                   # 🎨 功能模块层 - 按业务功能组织
│   ├── login/                  # 登录功能模块
│   │   ├── controllers/        # 登录相关状态控制器
│   │   ├── models/             # 登录特定业务模型
│   │   └── views/              # 登录UI界面组件
│   │
│   ├── workbench/              # 工作台功能模块
│   │   ├── controllers/        # 工作台状态管理
│   │   ├── models/             # 工作台特定模型
│   │   └── views/              # 工作台UI组件和页面
│   │
│   ├── proxy/                  # 代理管理功能模块
│   │   ├── proxy_self/         # 自建代理子模块
│   │   ├── proxy_platform/     # 平台代理子模块
│   │   ├── proxy_store/        # 代理商店子模块
│   │   ├── proxy_cart/         # 代理购物车子模块
│   │   ├── proxy_api/          # 代理API子模块
│   │   └── views/              # 代理管理通用视图
│   │
│   ├── team/                   # 团队管理功能模块
│   ├── automation/             # 自动化功能模块
│   ├── financial/              # 财务管理功能模块
│   ├── titlebar/               # 标题栏功能模块
│   └── IndexPage.dart          # 应用主页面入口
│
├── widgets/                    # 🔗 全局共享UI组件
│   ├── custom_text_field.dart  # 自定义输入框
│   ├── custom_solid_button.dart # 自定义实心按钮
│   ├── custom_border_button.dart # 自定义边框按钮
│   ├── custom_switch.dart      # 自定义开关
│   ├── data_table_widget.dart  # 数据表格组件
│   ├── custom_dropdown_menu.dart # 自定义下拉菜单
│   └── custom_date_picker.dart # 自定义日期选择器
│
├── router/                     # 🧭 路由配置
│   └── go_router_provider.dart # 应用路由定义和配置
│
├── theme/                      # 🎨 应用主题定义
│
├── l10n/                       # 🌐 国际化资源
│
└── main.dart                   # 🚀 应用入口点
```

## 🔄 架构数据流与设计图

### 整体架构分层图

```
┌─────────────────────────────────────────────────────────────┐
│                    🎨 Features 功能模块层                      │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │  Workbench  │  │    Proxy    │  │    Team     │  ...     │
│  │   Module    │  │   Module    │  │   Module    │          │
│  │             │  │             │  │             │          │
│  │ Controllers │  │ Controllers │  │ Controllers │          │
│  │   Models    │  │   Models    │  │   Models    │          │
│  │   Views     │  │   Views     │  │   Views     │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
                              ▲ ▼
                        使用 Riverpod Provider
                              ▲ ▼
┌─────────────────────────────────────────────────────────────┐
│                      🧠 Domain 领域层                         │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │   Auth      │  │    Proxy    │  │    Team     │          │
│  │   Service   │  │   Service   │  │   Service   │  ...     │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
│                                                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              核心领域模型 (Domain Models)                 │ │
│  │  UserModel | ProxyModel | BrowserWindowModel | ...      │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ▲ ▼
                         依赖注入调用
                              ▲ ▼
┌─────────────────────────────────────────────────────────────┐
│                    📦 Repositories 数据访问层                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │   Auth      │  │    Proxy    │  │    Team     │          │
│  │ Repository  │  │ Repository  │  │ Repository  │  ...     │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
│                                                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │            API请求/响应数据处理                            │ │
│  │  JSON序列化 | 网络请求 | 数据转换 | 错误处理               │ │
│  │  (Repository)                                          │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ▲ ▼
                           HTTP 请求
                              ▲ ▼
┌─────────────────────────────────────────────────────────────┐
│                      ⚙️ Core 基础设施层                       │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │ HttpService │  │ Storage     │  │   Utils     │  ...     │
│  │ (Network)   │  │ Service     │  │  Browser    │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
│                                                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              基础设施服务                                 │ │
│  │  网络服务 | 存储服务 | 浏览器工具 | 代理中继               │ │
│  │  (Core/Network)                                      │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ▲ ▼
                           外部 API
                              ▲ ▼
                         🌐 后端服务器
```

### 数据流向图

#### 📥 **读取数据流程 (API → UI)**

```
🌐 后端 API
     ▼ HTTP Response
┌─────────────────┐
│  Core/Network   │ ◄── 统一网络服务，处理认证、错误
└─────────────────┘
     ▼ Raw JSON
┌─────────────────┐
│  Repository     │ ◄── 1. 接收API响应
│                 │     2. JSON反序列化
│                 │     3. 转换为领域模型
└─────────────────┘
     ▼ Domain Model
┌─────────────────┐
│   Service       │ ◄── 1. 处理跨功能业务逻辑
│                 │     2. 数据聚合和处理
│                 │     3. 返回处理后的领域模型
└─────────────────┘
     ▼ Processed Model
┌─────────────────┐
│  Controller     │ ◄── 1. 管理UI状态
│  (Riverpod)     │     2. 缓存数据
│                 │     3. 提供给UI使用
└─────────────────┘
     ▼ UI State
┌─────────────────┐
│     View        │ ◄── 监听状态变化，自动重建UI
│   (Widget)      │
└─────────────────┘
```

#### 📤 **提交数据流程 (UI → API)**

```
┌─────────────────┐
│     View        │ ◄── 用户操作：点击、输入等
│   (Widget)      │
└─────────────────┘
     ▼ User Action
┌─────────────────┐
│  Controller     │ ◄── 1. 接收用户操作
│  (Riverpod)     │     2. 构建领域模型
│                 │     3. 调用Service
└─────────────────┘
     ▼ Domain Model
┌─────────────────┐
│   Service       │ ◄── 1. 验证业务规则
│                 │     2. 处理业务逻辑
│                 │     3. 调用Repository
└─────────────────┘
     ▼ Domain Model
┌─────────────────┐
│  Repository     │ ◄── 1. 领域模型序列化为JSON
│                 │     2. 调用网络服务
│                 │     3. 处理响应并转换回领域模型
└─────────────────┘
     ▼ JSON Data
┌─────────────────┐
│  Core/Network   │ ◄── 发送HTTP请求
└─────────────────┘
     ▼ HTTP Request
🌐 后端 API
```

### Riverpod Provider 连接图

```
                    🎯 UI Widget
                         ▲
                   ref.watch()
                         ▲
    ┌──────────────────────────────────────────┐
    │                                          │
    ▼                    ▼                     ▼
┌─────────┐        ┌─────────┐          ┌─────────┐
│Browser  │        │ Proxy   │          │  Team   │
│Controller│        │Controller│          │Controller│
│Provider │        │Provider │          │Provider │
└─────────┘        └─────────┘          └─────────┘
    ▲                    ▲                     ▲
    │ ref.read()         │ ref.read()          │ ref.read()
    ▼                    ▼                     ▼
┌─────────┐        ┌─────────┐          ┌─────────┐
│Browser  │        │ Proxy   │          │  Team   │
│Service  │        │Service  │          │Service  │
│Provider │        │Provider │          │Provider │
└─────────┘        └─────────┘          └─────────┘
    ▲                    ▲                     ▲
    │ ref.read()         │ ref.read()          │ ref.read()
    ▼                    ▼                     ▼
┌─────────┐        ┌─────────┐          ┌─────────┐
│Browser  │        │ Proxy   │          │  Team   │
│Repository│        │Repository│          │Repository│
│Provider │        │Provider │          │Provider │
└─────────┘        └─────────┘          └─────────┘
    ▲                    ▲                     ▲
    │ ref.read()         │ ref.read()          │ ref.read()
    ▼                    ▼                     ▼
┌─────────────────────────────────────────────────┐
│           Core/Network Service Provider         │
│        (Singleton - 全局网络服务)                │
└─────────────────────────────────────────────────┘
```

## 自定义组件库

项目包含丰富的自定义UI组件，以保证界面风格统一和开发效率：

### 按钮组件

- **CustomSolidButton**: 自适应内容宽度的实心按钮，支持图标、文字、悬停效果
- **CustomBorderButton**: 边框按钮，适用于次要操作
- **LoadingButton**: 带加载状态的按钮
- **PrimaryButton**: 主要操作按钮
- **SecondaryButton**: 次要操作按钮
- **CustomEditButton**: 编辑操作专用按钮

### 输入组件

- **CustomTextField**: 统一样式的文本输入框
- **CustomDropdownMenu**: 下拉选择菜单
- **UnderlineDropdownWidget**: 下划线样式的下拉菜单
- **TwoLevelDropdownMenu**: 两级联动下拉菜单
- **InfiniteDropdownWidget**: 支持无限加载的下拉菜单
- **UnderlineDropdownWithPagination**: 带分页的下拉菜单

### 选择组件

- **CustomCheckboxGroup**: 复选框组
- **CustomRadioGroup**: 单选按钮组
- **CustomSwitch**: 开关组件
- **CustomSwitchButton**: 按钮式开关
- **CustomDatePicker**: 日期选择器

### 数据展示组件

- **DataTableWidget**: 高级数据表格，支持排序、筛选
- **CustomTableWidget**: 自定义表格组件
- **StateBar**: 状态指示条
- **CustomCard**: 卡片容器
- **ProxyTableBtn**: 代理表格专用按钮
- **ConfirmDialog**: 确认对话框

### 关键设计原则

#### 1. **清晰的依赖方向**
```
Features → Domain → Repositories → Core
    ❌        ❌         ❌
   反向依赖需要仔细控制
```

#### 2. **模型转换流程**
```
API Response → Repository → Domain Model → Service → Controller → UI
API Request  ← Repository ← Domain Model ← Service ← Controller ← UI
```

#### 3. **错误处理链**
```
Core/Network → Repository → Service → Controller → UI
    ▲              ▲          ▲         ▲         ▲
 网络错误        数据错误   业务错误  状态错误   UI错误
```

#### 4. **状态管理流**
```
Riverpod Provider: 依赖注入 + 状态管理
    ▼
Service: 业务逻辑处理
    ▼  
Repository: 数据访问
    ▼
Core/Network: 网络请求
```

## 设计原则

### 模型分类与职责

项目中的模型按照职责和使用范围分为不同类型：

#### **核心领域模型** (`domain/models/`)
- **职责**：代表核心业务概念，包含核心业务逻辑
- **特点**：被多个功能模块使用，相对稳定
- **示例**：
  - `UserModel`: 用户核心信息
  - `ProxyModel`: 代理相关业务模型
  - `BrowserWindowModel`: 浏览器窗口业务模型
  - `TeamModel`: 团队管理业务模型

#### **功能特定模型** (`features/xxx/models/`)
- **职责**：只被单个功能模块使用的业务模型
- **特点**：与特定功能界面密切相关，可能随功能需求变化
- **示例**：工作台配置模型、浏览器表格状态等

#### **API数据传输** (直接在Repository中处理)
- **职责**：处理API请求/响应的JSON序列化
- **特点**：使用`json_annotation`和`freezed`进行序列化
- **流程**：Repository层直接处理JSON与领域模型的转换

### 数据流转换原则

#### **简化的转换流程**
由于当前架构特点，我们采用了简化的数据转换流程：

1. **API响应处理**:
   ```dart
   // 在Repository中直接转换
   Future<UserModel> getUser() async {
     final response = await httpClient.get('/user');
     return UserModel.fromJson(response.data);
   }
   ```

2. **API请求处理**:
   ```dart
   // 领域模型直接序列化
   Future<void> updateUser(UserModel user) async {
     await httpClient.post('/user', data: user.toJson());
   }
   ```

3. **业务逻辑处理**:
   ```dart
   // Service层专注业务逻辑
   Future<void> validateAndUpdateUser(UserModel user) async {
     // 业务验证逻辑
     if (user.email.isEmpty) throw ValidationException();
     
     // 调用Repository
     await repository.updateUser(user);
   }
   ```

### 功能模块组织原则

#### **自包含原则**
- 每个功能模块应尽可能自包含
- 模块内部的控制器、模型、视图紧密协作
- 跨模块通信通过Domain层的Service进行

#### **代理模块示例**
代理功能采用了子模块化设计：
- `proxy_self/`: 自建代理管理
- `proxy_platform/`: 平台代理集成
- `proxy_store/`: 代理商店功能
- `proxy_cart/`: 购物车功能
- `proxy_api/`: API代理功能

### 依赖注入与状态管理

#### **Riverpod架构**
项目使用Riverpod进行依赖注入和状态管理：

1. **Provider层次**:
   ```dart
   // Repository Provider
   final userRepositoryProvider = Provider((ref) => UserRepository());
   
   // Service Provider
   final userServiceProvider = Provider((ref) {
     final repository = ref.read(userRepositoryProvider);
     return UserService(repository);
   });
   
   // Controller Provider  
   final userControllerProvider = StateNotifierProvider((ref) {
     final service = ref.read(userServiceProvider);
     return UserController(service);
   });
   ```

2. **状态消费**:
   ```dart
   // 在Widget中使用
   class UserProfilePage extends ConsumerWidget {
     @override
     Widget build(BuildContext context, WidgetRef ref) {
       final userState = ref.watch(userControllerProvider);
       return Text(userState.user.name);
     }
   }
   ```

### 跨模块通信规范

#### **推荐方式：通过Service层**
```dart
// ✅ 推荐 - 通过Service层通信
class WorkbenchController extends StateNotifier<WorkbenchState> {
  Future<void> configureProxy() async {
    final proxyService = ref.read(proxyServiceProvider);
    await proxyService.updateConfiguration(config);
  }
}
```

#### **避免方式：直接控制器调用**
```dart
// ❌ 避免 - 直接调用其他模块控制器
final proxyController = ref.read(proxyControllerProvider.notifier);
await proxyController.updateConfiguration(config);
```

## 技术栈

- **UI框架**: Flutter 3.5.4+
- **状态管理**: Riverpod (hooks_riverpod ^2.5.0)
- **路由管理**: go_router ^14.3.0
- **网络请求**: dio ^5.4.1
- **本地存储**: shared_preferences ^2.3.2
- **国际化**: flutter_localizations + intl ^0.19.0
- **序列化**: freezed + json_annotation
- **屏幕适配**: flutter_screenutil ^5.9.3
- **窗口管理**: window_manager ^0.3.9
- **函数式编程**: flutter_hooks ^0.20.5
- **SVG支持**: flutter_svg ^2.1.0
- **高级下拉菜单**: dropdown_button2 ^2.3.9
- **数据表格**: data_table_2 ^2.6.0

## 代码生成

项目使用多种代码生成器提高开发效率：

```bash
# 生成所有代码（模型、状态管理等）
flutter pub run build_runner build

# 清理并重新生成
flutter pub run build_runner build --delete-conflicting-outputs
```

### 生成器类型
- **freezed**: 不可变数据类生成
- **json_serializable**: JSON序列化代码
- **riverpod_generator**: Riverpod Provider代码

## 架构优势

- ✅ **模块化清晰**：功能按业务模块组织，职责明确
- ✅ **状态管理统一**：Riverpod提供一致的状态管理模式
- ✅ **可测试性强**：每层都可以独立测试
- ✅ **可维护性好**：修改一个模块影响范围可控
- ✅ **可扩展性强**：新功能按照相同模式添加
- ✅ **代码复用**：Domain层模型和服务可跨功能使用
- ✅ **组件库丰富**：提供完善的自定义组件，提高开发效率

## 开发规范

### 1. 新功能开发流程
1. 在`domain/models/`中定义核心业务模型（如需要）
2. 在`repositories/`中创建数据访问层
3. 在`domain/services/`中实现业务服务（如需要）
4. 在`features/`中创建功能模块
5. 配置Riverpod Provider依赖关系

### 2. 命名规范
- **模型文件**: `xxx_model.dart`
- **服务文件**: `xxx_service.dart`  
- **Repository文件**: `xxx_repository.dart`
- **控制器文件**: `xxx_controller.dart`
- **组件文件**: `custom_xxx.dart`

### 3. 导入规范
- 优先使用相对导入
- 按功能分组导入语句
- 避免循环依赖

## 项目启动

1. 确保Flutter环境已安装（SDK ≥ 3.5.4）
2. 获取依赖：`flutter pub get`
3. 生成代码：`flutter pub run build_runner build`
4. 运行项目：`flutter run`

## 入门资源

- [Flutter官方文档](https://docs.flutter.dev/)
- [Riverpod状态管理](https://riverpod.dev/)
- [Go Router路由管理](https://pub.dev/packages/go_router)
- [Freezed数据类生成](https://pub.dev/packages/freezed)
