name: frontend_re
description: "一个新的Flutter项目"
# 下面这行防止软件包被意外发布到 pub.dev
# 如果你想发布到 pub.dev，请删除这行
publish_to: "none"

# 以下定义了应用程序的版本和构建号
# 版本号由三个数字组成，用点分隔，如 1.2.43
# 后面可以跟一个可选的构建号，用 + 分隔
# 版本和构建号都可以在 flutter build 时通过指定 --build-name 和 --build-number 来覆盖
# 在Android中，build-name 用作 versionName，而 build-number 用作 versionCode
# 在iOS中，build-name 用作 CFBundleShortVersionString，而 build-number 用作 CFBundleVersion
# 在Windows中，build-name 用作产品和文件版本的主要、次要和补丁部分，而 build-number 用作构建后缀
version: 1.0.0+1

environment:
    sdk: ^3.5.4

# dependencies 指定你的包正常工作所需的其他包
# 要自动将包依赖升级到最新版本，请考虑运行 `flutter pub upgrade --major-versions`
# 或者，可以通过更改下面的版本号手动更新依赖
dependencies:
    flutter:
        sdk: flutter

    # 下面添加了 Cupertino Icons 字体到你的应用中
    # 配合 CupertinoIcons 类使用，用于 iOS 风格图标
    cupertino_icons: ^1.0.8 # iOS风格图标库
    go_router: ^14.3.0 # 路由导航管理
    shared_preferences: ^2.3.2 # 本地数据持久化存储
    flutter_screenutil: ^5.9.3 # 屏幕适配工具
    window_manager: ^0.3.9 # 窗口管理器
    flutter_hooks: ^0.20.5 # Hook 状态管理工具
    hooks_riverpod: ^2.5.0 # Riverpod 状态管理框架
    flutter_localizations: # Flutter国际化支持
        sdk: flutter
    dio: ^5.4.1 # HTTP 客户端，用于网络请求
    json_annotation: ^4.9.0 # JSON 序列化支持
    freezed_annotation: ^2.4.1 # 用于生成不可变对象
    riverpod_annotation: ^2.6.1 # Riverpod 注解
    animated_tree_view: ^2.3.0
    path_provider: ^2.1.5
    flutter_svg: ^2.1.0
    dropdown_button2: ^2.3.9
    data_table_2: ^2.6.0
    crypto: ^3.0.6
    ffi: ^2.1.3
    archive: ^4.0.7
    browser_tool:
        path: ./browser_tool
    synchronizer:
        path: ./synchronizer
    icon:
        path: ./icon

dev_dependencies:
    flutter_test:
        sdk: flutter

    # 下面的 "flutter_lints" 包包含一组推荐的 lint 规则
    # 用于鼓励良好的编码实践
    # 该 lint 集在位于包根目录的 `analysis_options.yaml` 文件中激活
    # 查看该文件以了解如何禁用特定 lint 规则和激活其他规则
    flutter_lints: ^4.0.0 # 代码规范检查工具
    # 记得使用这个命令生成代码flutter pub run build_runner build
    build_runner: ^2.4.8 # 代码生成器
    json_serializable: ^6.0.0 # JSON 序列化代码生成
    freezed: ^2.4.7 # 不可变对象生成器
    riverpod_generator: ^2.6.3
    custom_lint: ^0.6.2

# Flutter 相关的配置部分
flutter:
    # 下面这行确保 Material Icons 字体包含在你的应用中
    # 这样你就可以使用 material Icons 类中的图标
    uses-material-design: true
    # 资源文件配置部分
    assets:
        - assets/images/ # 图片资源目录，存放应用中使用的所有图片文件
        - assets/tool.dll
        - assets/relay.exe
        - assets/119.tar.xz
        - assets/images/login/
        - assets/images/workbench/
        - assets/images/proxy/
        - assets/images/common/
        - assets/images/platform/
        - assets/svg/
        - assets/svg/platform/
        # - assets/chrome2/
        # - assets/chrome2/119.0.6045.123/
        # - assets/chrome2/119.0.6045.123/Extensions/
        # - assets/chrome2/119.0.6045.123/Locales/
        # - assets/chrome2/119.0.6045.123/MEIPreload/
        # - assets/chrome2/119.0.6045.123/VisualElements/

    # 要向应用程序添加资源，请添加 assets 部分，如下所示：
    # assets:
    #   - images/a_dot_burr.jpeg
    #   - images/a_dot_ham.jpeg

    # 图像资源可以引用一个或多个特定分辨率的变体
    # 详见 https://flutter.dev/to/resolution-aware-images

    # 关于从包依赖添加资源的详细信息，请参见
    # https://flutter.dev/to/asset-from-package

    # 要向应用程序添加自定义字体，请在这里添加 fonts 部分
    # 在这个 "flutter" 部分中。列表中的每个条目都应该有一个
    # "family" 键，其中包含字体系列名称，以及一个 "fonts" 键，其中包含
    # 字体的资源和其他描述符的列表。例如：
    fonts:
       - family: PingFang SC
         fonts:
           - asset: assets/fonts/PingFangSC-Regular.otf
#       - family: Trajan Pro
#         fonts:
#           - asset: fonts/TrajanPro.ttf
#           - asset: fonts/TrajanPro_Bold.ttf
#             weight: 700
    #
    # 关于包依赖中字体的详细信息，
    # 请参见 https://flutter.dev/to/font-from-package
analyzer:
    plugins:
        - custom_lint
    errors:
        invalid_annotation_target: ignore
