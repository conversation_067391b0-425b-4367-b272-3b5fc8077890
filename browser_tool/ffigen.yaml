# Run with `dart run ffigen --config ffigen.yaml`.
name: BrowserToolBindings
description: |
  Bindings for `src/browser-tool.h`.

  Regenerate bindings with `dart run ffigen --config ffigen.yaml`.
output: 'lib/browser_tool_bindings_generated.dart'
headers:
  entry-points:
    - 'src/browser-tool.h'
preamble: |
  // ignore_for_file: always_specify_types
  // ignore_for_file: camel_case_types
  // ignore_for_file: non_constant_identifier_names
comments:
  style: any
  length: full