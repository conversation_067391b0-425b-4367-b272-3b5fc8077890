import 'dart:ffi';
import 'dart:io';
import 'package:ffi/ffi.dart';

import 'browser_tool_bindings_generated.dart';

const String _libName = 'browser-tool';

/// The dynamic library in which the symbols for [BrowserToolBindings] can be found.
final DynamicLibrary _dylib = () {
  if (Platform.isMacOS || Platform.isIOS) {
    // 对于macOS，直接使用dylib文件名
    return DynamicLibrary.open('$_libName.dylib');
  }
  if (Platform.isAndroid || Platform.isLinux) {
    return DynamicLibrary.open('lib$_libName.so');
  }
  if (Platform.isWindows) {
    return DynamicLibrary.open('$_libName.dll');
  }
  throw UnsupportedError('Unknown platform: ${Platform.operatingSystem}');
}();

/// The bindings to the native functions in [_dylib].
final BrowserToolBindings _bindings = BrowserToolBindings(_dylib);

/// Browser Tool Plugin 主类
class BrowserTool {

  /// 修改密码
  ///
  /// [url] - 网站URL
  /// [username] - 用户名
  /// [password] - 新密码
  /// [path] - 配置文件路径
  ///
  /// 返回操作结果字符串，失败时返回null
  static String? changePassword(String url, String username, String password, String path) {
    try {
      final urlPtr = url.toNativeUtf8().cast<Char>();
      final usernamePtr = username.toNativeUtf8().cast<Char>();
      final passwordPtr = password.toNativeUtf8().cast<Char>();
      final pathPtr = path.toNativeUtf8().cast<Char>();

      final result = _bindings.changePassword(urlPtr, usernamePtr, passwordPtr, pathPtr);

      // 清理内存
      malloc.free(urlPtr);
      malloc.free(usernamePtr);
      malloc.free(passwordPtr);
      malloc.free(pathPtr);

      if (result == nullptr) return null;

      final resultString = result.cast<Utf8>().toDartString();
      // Go分配的内存需要在Go端释放，这里不能free
      return resultString;
    } catch (e) {
      print('changePassword error: $e');
      return null;
    }
  }

  /// 获取Cookie
  ///
  /// [path] - Cookie文件路径
  ///
  /// 返回Cookie字符串，失败时返回null
  static String? getCookie(String path) {
    try {
      final pathPtr = path.toNativeUtf8().cast<Char>();
      final result = _bindings.getCookie(pathPtr);
      malloc.free(pathPtr);

      if (result == nullptr) return null;

      final resultString = result.cast<Utf8>().toDartString();
      return resultString;
    } catch (e) {
      print('getCookie error: $e');
      return null;
    }
  }

  /// 更新Cookie
  ///
  /// [cookie] - Cookie内容
  /// [path] - Cookie文件路径
  ///
  /// 返回操作结果字符串，失败时返回null
  static String? updateCookie(String cookie, String path) {
    try {
      final cookiePtr = cookie.toNativeUtf8().cast<Char>();
      final pathPtr = path.toNativeUtf8().cast<Char>();
      final result = _bindings.updateCookie(cookiePtr, pathPtr);

      malloc.free(cookiePtr);
      malloc.free(pathPtr);

      if (result == nullptr) return null;

      final resultString = result.cast<Utf8>().toDartString();
      return resultString;
    } catch (e) {
      print('updateCookie error: $e');
      return null;
    }
  }

  /// 获取地理信息
  ///
  /// [proxyType] - 代理类型 (如: "http", "socks5")
  /// [proxyAddress] - 代理地址 (如: "127.0.0.1:8080")
  ///
  /// 返回地理信息JSON字符串，失败时返回null
  static String? fetchGeoInfo(String proxyType, String proxyAddress) {
    try {
      final proxyTypePtr = proxyType.toNativeUtf8().cast<Char>();
      final proxyAddressPtr = proxyAddress.toNativeUtf8().cast<Char>();
      final result = _bindings.fetchGeoInfo(proxyTypePtr, proxyAddressPtr);

      malloc.free(proxyTypePtr);
      malloc.free(proxyAddressPtr);

      if (result == nullptr) return null;

      final resultString = result.cast<Utf8>().toDartString();
      return resultString;
    } catch (e) {
      print('fetchGeoInfo error: $e');
      return null;
    }
  }

  /// 写入固定密钥
  ///
  /// [path] - 密钥文件路径
  ///
  /// 返回操作结果字符串，失败时返回null
  static String? writeFixedKey(String path) {
    try {
      final pathPtr = path.toNativeUtf8().cast<Char>();
      final result = _bindings.writeFixedKey(pathPtr);
      malloc.free(pathPtr);

      if (result == nullptr) return null;

      final resultString = result.cast<Utf8>().toDartString();
      return resultString;
    } catch (e) {
      print('writeFixedKey error: $e');
      return null;
    }
  }
}