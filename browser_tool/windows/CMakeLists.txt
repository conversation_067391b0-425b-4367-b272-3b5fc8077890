# The Flutter tooling requires that developers have a version of Visual Studio
# installed that includes CMake 3.14 or later. You should not increase this
# version, as doing so will cause the plugin to fail to compile for some
# customers of the plugin.
cmake_minimum_required(VERSION 3.14)

# Project-level configuration.
set(PROJECT_NAME "browser_tool")
project(${PROJECT_NAME} LANGUAGES CXX)

# 注释掉原来的构建子目录，因为我们使用预编译的Go动态库
# add_subdirectory("${CMAKE_CURRENT_SOURCE_DIR}/../src" "${CMAKE_CURRENT_BINARY_DIR}/shared")

# List of absolute paths to libraries that should be bundled with the plugin.
# 这里指定你的Go编译的DLL文件路径
set(browser_tool_bundled_libraries
  # 指向你的Go编译的DLL文件
  "${CMAKE_CURRENT_SOURCE_DIR}/../src/windows/browser-tool.dll"
  PARENT_SCOPE
)