#
# To learn more about a Podspec see http://guides.cocoapods.org/syntax/podspec.html.
# Run `pod lib lint browser_tool.podspec` to validate before publishing.
#
Pod::Spec.new do |s|
  s.name             = 'browser_tool'
  s.version          = '0.0.1'
  s.summary          = '指纹浏览器插件'
  s.description      = <<-DESC
指纹浏览器插件
                       DESC
  s.homepage         = 'http://example.com'
  s.license          = { :file => '../LICENSE' }
  s.author           = { 'Your Company' => '<EMAIL>' }

  # 由于我们使用预编译的Go动态库，不需要再编译C源文件
  s.source           = { :path => '.' }

  # 注释掉对C源文件的编译，因为我们使用预编译的dylib
  # s.source_files = 'Classes/**/*'

  # 如果你的Classes目录下有必要的Objective-C或Swift文件（比如插件注册代码），
  # 可以有选择性地包含：
  # s.source_files = 'Classes/**/*.{h,m,mm,swift}'

  # 指定你的Go编译的动态库
  s.vendored_libraries = '../src/macos/browser-tool.dylib'

  # If your plugin requires a privacy manifest, for example if it collects user
  # data, update the PrivacyInfo.xcprivacy file to describe your plugin's
  # privacy impact, and then uncomment this line. For more information,
  # see https://developer.apple.com/documentation/bundleresources/privacy_manifest_files
  # s.resource_bundles = {'browser_tool_privacy' => ['Resources/PrivacyInfo.xcprivacy']}

  s.dependency 'FlutterMacOS'

  s.platform = :osx, '10.11'
  s.pod_target_xcconfig = { 'DEFINES_MODULE' => 'YES' }
  s.swift_version = '5.0'
end