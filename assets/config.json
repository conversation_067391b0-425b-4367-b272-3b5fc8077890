{"log": {"disabled": false, "level": "info", "timestamp": true}, "dns": {"servers": [{"type": "udp", "tag": "local", "server": "************", "server_port": 53}], "final": "local", "strategy": "prefer_ipv4", "reverse_mapping": true}, "ntp": {"enabled": true, "server": "time.apple.com", "server_port": 123, "interval": "30m"}, "inbounds": [{"type": "socks", "tag": "socks1", "listen": "127.0.0.1", "listen_port": 20001}], "outbounds": [{"type": "direct", "tag": "direct"}, {"server": "*******", "server_port": 45889, "version": "5", "username": "sekai", "password": "admin", "tag": "socks1", "type": "socks"}], "route": {"default_domain_resolver": {"server": "local", "rewrite_ttl": 60, "client_subnet": "***************"}, "rules": [{"inbound": "socks1", "action": "route", "outbound": "socks1"}]}}