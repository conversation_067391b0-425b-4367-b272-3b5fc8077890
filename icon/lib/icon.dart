import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/services.dart';
import 'package:image/image.dart' as img;
import 'package:path/path.dart' as path;
import 'icon_platform_interface.dart';
import 'resize.dart'; // 添加这行导入

class IconManager {
  // 缓存自定义字体
  static img.BitmapFont? _customFont;

  /// 加载自定义字体（417像素大小）
  Future<img.BitmapFont> _loadCustomFont() async {
    if (_customFont != null) {
      return _customFont!;
    }

    try {
      // 加载自定义字体zip文件（417像素大小）
      final ByteData fontData = await rootBundle.load('packages/icon/assets/chrome.zip');
      final Uint8List fontBytes = fontData.buffer.asUint8List();
      _customFont = img.BitmapFont.fromZip(fontBytes);
      return _customFont!;
    } catch (e) {
      // 如果自定义字体加载失败，使用默认字体
      print('Warning: Failed to load custom font, using default: $e');
      return img.arial48;
    }
  }

  /// 设置进程图标，在chrome.png基础上添加数字序号
  ///
  /// [pid] - 目标进程ID
  /// [number] - 要显示的数字序号
  Future<String?> setIconWithNumber(int pid, int number) async {
    try {
      // 1. 先确定文件路径
      final String currentDir = Directory.current.path;
      final String icoDir = path.join(currentDir, 'ico');
      final String fileName = '$number.ico';
      final String filePath = path.join(icoDir, fileName);
      final File icoFile = File(filePath);

      // 2. 检查ICO文件是否已存在
      if (await icoFile.exists()) {
        print('ICO file already exists: $filePath, skipping generation');
        // 直接调用平台代码设置图标
        final result = await IconPlatform.instance.setIcon(pid, filePath);
        return result;
      }

      // 3. 文件不存在，需要生成ICO文件
      print('ICO file does not exist, generating: $filePath');

      // 4. 加载原始图片
      final ByteData assetData = await rootBundle.load('packages/icon/assets/chrome.png');
      final Uint8List assetBytes = assetData.buffer.asUint8List();
      final img.Image? originalImage = img.decodePng(assetBytes);

      if (originalImage == null) {
        return 'Failed to load chrome.png from assets';
      }

      // 5. 先在原始图片上添加数字标识（保持原始分辨率1042*1042）
      final img.Image imageWithNumber = await _addNumberToImage(originalImage, number);

      // 6. 创建不同尺寸的图标（在添加文字后再缩放）
      final List<int> sizes = [16, 24, 32, 48, 64, 128, 256];
      final List<img.Image> iconImages = [];

      for (int size in sizes) {
        // 缩放已经添加了文字的图片，使用高质量的缩放算法
        final img.Image resizedImage = ImageResize.resizeLanczos3(
            imageWithNumber,
            size,
            size
        );
        iconImages.add(resizedImage);
      }

      // 7. 创建ICO格式数据
      final Uint8List icoData = _createIcoFile(iconImages);

      // 8. 确保目录存在并保存文件
      final Directory directory = Directory(icoDir);

      if (!await directory.exists()) {
        await directory.create(recursive: true);
      }

      await icoFile.writeAsBytes(icoData);
      print('ICO file generated successfully: $filePath');

      // 9. 调用平台代码设置图标
      final result = await IconPlatform.instance.setIcon(pid, filePath);
      return result;

    } catch (e) {
      return 'Error: $e';
    }
  }

  /// 在原始图片上添加数字标识（基于1042*1042的固定尺寸）
  Future<img.Image> _addNumberToImage(img.Image image, int number) async {
    final img.Image result = img.Image.from(image);
    final String numberText = number.toString();

    // 原图固定尺寸1042*1042
    const int textHeight = 417; // 40% of 1042 ≈ 417
    const int capsuleHeight = 450; // 胶囊稍大一些，给文字留点边距

    // 图标中心位置
    final int centerX = image.width ~/ 2; // 521
    final int centerY = image.height ~/ 2; // 521

    // 加载自定义字体（417像素）
    final img.BitmapFont font = await _loadCustomFont();

    // 计算文字的实际宽度
    // 使用417像素字体，每个字符大约宽度是高度的0.6倍
    final int estimatedTextWidth = (numberText.length * textHeight * 0.6).round();

    // 胶囊宽度：文字宽度 + 30%边距，但不能小于胶囊高度（保证圆形）
    int capsuleWidth = (estimatedTextWidth * 1.3).round();
    if (capsuleWidth < capsuleHeight) {
      capsuleWidth = capsuleHeight; // 确保至少是圆形
    }

    // 胶囊的位置（居中对齐）
    final int capsuleLeft = centerX - capsuleWidth ~/ 2;
    final int capsuleTop = centerY - capsuleHeight ~/ 2;
    final int capsuleRight = capsuleLeft + capsuleWidth;
    final int capsuleBottom = capsuleTop + capsuleHeight;

    // 绘制纯白色胶囊背景（圆角矩形）
    img.fillRect(result,
        x1: capsuleLeft,
        y1: capsuleTop,
        x2: capsuleRight,
        y2: capsuleBottom,
        color: img.ColorRgba8(255, 255, 255, 255), // 纯白色，无透明度
        radius: capsuleHeight / 2 // 胶囊形状的圆角半径
    );

    // 计算文字的居中位置
    // drawString的坐标是文字的左上角，所以需要计算偏移
    final int textX = centerX - estimatedTextWidth ~/ 2;
    final int textY = centerY - textHeight ~/ 2;

    // 绘制数字文字（居中对齐）
    img.drawString(result, numberText,
        font: font,
        x: textX,
        y: textY,
        color: img.ColorRgba8(12, 117, 248, 255) // 深蓝色
    );

    return result;
  }

  /// 创建ICO文件
  Uint8List _createIcoFile(List<img.Image> images) {
    // 计算总的数据大小
    int totalDataSize = 0;
    List<Uint8List> pngDataList = [];

    for (final img.Image image in images) {
      final Uint8List pngData = Uint8List.fromList(img.encodePng(image));
      pngDataList.add(pngData);
      totalDataSize += pngData.length;
    }

    final ByteData buffer = ByteData(6 + images.length * 16 + totalDataSize);
    int offset = 0;

    // ICO头部
    buffer.setUint16(offset, 0, Endian.little); // Reserved
    offset += 2;
    buffer.setUint16(offset, 1, Endian.little); // Type (1 = ICO)
    offset += 2;
    buffer.setUint16(offset, images.length, Endian.little); // Image count
    offset += 2;

    int dataOffset = 6 + images.length * 16;

    // 图像目录条目
    for (int i = 0; i < images.length; i++) {
      final img.Image image = images[i];
      final Uint8List pngData = pngDataList[i];

      buffer.setUint8(offset, image.width > 255 ? 0 : image.width); // Width
      offset++;
      buffer.setUint8(offset, image.height > 255 ? 0 : image.height); // Height
      offset++;
      buffer.setUint8(offset, 0); // Color palette
      offset++;
      buffer.setUint8(offset, 0); // Reserved
      offset++;
      buffer.setUint16(offset, 1, Endian.little); // Color planes
      offset += 2;
      buffer.setUint16(offset, 32, Endian.little); // Bits per pixel
      offset += 2;
      buffer.setUint32(offset, pngData.length, Endian.little); // Data size
      offset += 4;
      buffer.setUint32(offset, dataOffset, Endian.little); // Data offset
      offset += 4;

      dataOffset += pngData.length;
    }

    // 图像数据
    for (int i = 0; i < pngDataList.length; i++) {
      final Uint8List pngData = pngDataList[i];
      buffer.buffer.asUint8List().setRange(offset, offset + pngData.length, pngData);
      offset += pngData.length;
    }

    return buffer.buffer.asUint8List();
  }
}