import 'package:plugin_platform_interface/plugin_platform_interface.dart';

import 'icon_method_channel.dart';

abstract class IconPlatform extends PlatformInterface {
  /// Constructs a IconPlatform.
  IconPlatform() : super(token: _token);

  static final Object _token = Object();

  static IconPlatform _instance = MethodChannelIcon();

  /// The default instance of [IconPlatform] to use.
  ///
  /// Defaults to [MethodChannelIcon].
  static IconPlatform get instance => _instance;

  /// Platform-specific implementations should set this with their own
  /// platform-specific class that extends [IconPlatform] when
  /// they register themselves.
  static set instance(IconPlatform instance) {
    PlatformInterface.verifyToken(instance, _token);
    _instance = instance;
  }

  /// Sets the icon for a process with the given PID.
  ///
  /// [pid] - The process ID of the target process
  /// [iconPath] - The file path to the icon (.ico file)
  ///
  /// Returns "success" if the operation was successful, or an error message if it failed.
  Future<String?> setIcon(int pid, String iconPath) {
    throw UnimplementedError('setIcon() has not been implemented.');
  }
}