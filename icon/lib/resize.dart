import 'dart:math' as math;
import 'package:image/image.dart' as img;

/// 高质量图像缩放算法实现（修复透明通道问题）
class ImageResize {
  /// 使用Lanczos3算法缩放图像
  static img.Image resizeLanczos3(img.Image src, int width, int height) {
    return _resize(src, width, height, _lanczos3Kernel);
  }

  /// 使用Bicubic算法缩放图像
  static img.Image resizeBicubic(img.Image src, int width, int height) {
    return _resize(src, width, height, _bicubicKernel);
  }

  /// 通用缩放函数
  static img.Image _resize(
      img.Image src, int width, int height, double Function(double) kernel) {
    if (src.width == width && src.height == height) {
      return img.Image.from(src);
    }

    // 使用fromResized创建目标图像，保持原始图像格式
    final img.Image dst = img.Image.fromResized(src,
        width: width, height: height, noAnimation: true);

    // 计算缩放比例
    final double scaleX = src.width / width;
    final double scaleY = src.height / height;

    // 计算核心半径
    final double kernelRadius = _getKernelRadius(kernel);
    final double filterScaleX = math.max(1.0, scaleX);
    final double filterScaleY = math.max(1.0, scaleY);
    final double supportX = kernelRadius * filterScaleX;
    final double supportY = kernelRadius * filterScaleY;

    // 使用fromResized创建临时图像，保持原始图像格式
    final img.Image temp = img.Image.fromResized(src,
        width: width, height: src.height, noAnimation: true);

    // 先进行水平缩放
    for (int y = 0; y < src.height; y++) {
      for (int x = 0; x < width; x++) {
        final double centerX = (x + 0.5) * scaleX - 0.5;
        final int left = math.max(0, (centerX - supportX).floor());
        final int right = math.min(src.width - 1, (centerX + supportX).ceil());

        double totalWeight = 0.0;
        double r = 0.0, g = 0.0, b = 0.0, a = 0.0;

        for (int sx = left; sx <= right; sx++) {
          final double weight =
              kernel((sx - centerX) / filterScaleX) / filterScaleX;
          if (weight == 0.0) continue;

          final img.Pixel pixel = src.getPixel(sx, y);

          // 直接对各通道进行加权平均，就像官方实现那样
          r += pixel.r * weight;
          g += pixel.g * weight;
          b += pixel.b * weight;
          a += pixel.a * weight;
          totalWeight += weight;
        }

        if (totalWeight != 0.0) {
          r /= totalWeight;
          g /= totalWeight;
          b /= totalWeight;
          a /= totalWeight;
        } else {
          // 如果没有有效权重，保持透明
          r = g = b = a = 0;
        }

        temp.setPixelRgba(x, y, _clamp(r.round()), _clamp(g.round()),
            _clamp(b.round()), _clamp(a.round()));
      }
    }

    // 再进行垂直缩放
    for (int y = 0; y < height; y++) {
      for (int x = 0; x < width; x++) {
        final double centerY = (y + 0.5) * scaleY - 0.5;
        final int top = math.max(0, (centerY - supportY).floor());
        final int bottom =
        math.min(temp.height - 1, (centerY + supportY).ceil());

        double totalWeight = 0.0;
        double r = 0.0, g = 0.0, b = 0.0, a = 0.0;

        for (int sy = top; sy <= bottom; sy++) {
          final double weight =
              kernel((sy - centerY) / filterScaleY) / filterScaleY;
          if (weight == 0.0) continue;

          final img.Pixel pixel = temp.getPixel(x, sy);

          // 直接对各通道进行加权平均，就像官方实现那样
          r += pixel.r * weight;
          g += pixel.g * weight;
          b += pixel.b * weight;
          a += pixel.a * weight;
          totalWeight += weight;
        }

        if (totalWeight != 0.0) {
          r /= totalWeight;
          g /= totalWeight;
          b /= totalWeight;
          a /= totalWeight;
        } else {
          // 如果没有有效权重，保持透明
          r = g = b = a = 0;
        }

        dst.setPixelRgba(x, y, _clamp(r.round()), _clamp(g.round()),
            _clamp(b.round()), _clamp(a.round()));
      }
    }

    return dst;
  }

  /// Lanczos3核心函数
  static double _lanczos3Kernel(double x) {
    const double a = 3.0;
    x = x.abs();

    if (x == 0.0) return 1.0;
    if (x >= a) return 0.0;

    final double pix = math.pi * x;
    return a * math.sin(pix) * math.sin(pix / a) / (pix * pix);
  }

  /// Bicubic核心函数
  static double _bicubicKernel(double x) {
    x = x.abs();

    if (x <= 1.0) {
      return (1.5 * x - 2.5) * x * x + 1.0;
    } else if (x < 2.0) {
      return ((-0.5 * x + 2.5) * x - 4.0) * x + 2.0;
    }

    return 0.0;
  }

  /// 获取核心半径
  static double _getKernelRadius(double Function(double) kernel) {
    if (kernel == _lanczos3Kernel) return 3.0;
    if (kernel == _bicubicKernel) return 2.0;
    return 1.0;
  }

  /// 将值限制在0-255范围内
  static int _clamp(int value) {
    return math.max(0, math.min(255, value));
  }
}

/// 便捷的扩展方法
extension ImageResizeExtension on img.Image {
  /// 使用Lanczos3算法缩放
  img.Image resizeLanczos3(int width, int height) {
    return ImageResize.resizeLanczos3(this, width, height);
  }

  /// 使用Bicubic算法缩放
  img.Image resizeBicubic(int width, int height) {
    return ImageResize.resizeBicubic(this, width, height);
  }
}