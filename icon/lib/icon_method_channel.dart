import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

import 'icon_platform_interface.dart';

/// An implementation of [IconPlatform] that uses method channels.
class MethodChannelIcon extends IconPlatform {
  /// The method channel used to interact with the native platform.
  @visibleForTesting
  final methodChannel = const MethodChannel('icon');

  @override
  Future<String?> setIcon(int pid, String iconPath) async {
    final result = await methodChannel.invokeMethod<String>('setIcon', {
      'pid': pid,
      'iconPath': iconPath,
    });
    return result;
  }
}