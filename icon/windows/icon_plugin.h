#ifndef FLUTTER_PLUGIN_ICON_PLUGIN_H_
#define FLUTTER_PLUGIN_ICON_PLUGIN_H_

#include <flutter/method_channel.h>
#include <flutter/plugin_registrar_windows.h>

#include <memory>

namespace icon {

class IconPlugin : public flutter::Plugin {
 public:
  static void RegisterWithRegistrar(flutter::PluginRegistrarWindows *registrar);

  IconPlugin();

  virtual ~IconPlugin();

  // Disallow copy and assign.
  IconPlugin(const IconPlugin&) = delete;
  IconPlugin& operator=(const IconPlugin&) = delete;

  // Called when a method is called on this plugin's channel from Dart.
  void HandleMethodCall(
      const flutter::MethodCall<flutter::EncodableValue> &method_call,
      std::unique_ptr<flutter::MethodResult<flutter::EncodableValue>> result);
};

}  // namespace icon

#endif  // FLUTTER_PLUGIN_ICON_PLUGIN_H_
