#include "icon_plugin.h"

// This must be included before many other Windows headers.
#include <windows.h>

#include <flutter/method_channel.h>
#include <flutter/plugin_registrar_windows.h>
#include <flutter/standard_method_codec.h>

#include <memory>
#include <vector>

namespace icon {


struct WindowInfo {
    HWND hwnd;
    DWORD processId;
};


BOOL CALLBACK EnumWindowsProc(HWND hwnd, LPARAM lParam) {
    auto* windows = reinterpret_cast<std::vector<WindowInfo>*>(lParam);
    DWORD processId;
    GetWindowThreadProcessId(hwnd, &processId);

    if (IsWindowVisible(hwnd) && GetWindow(hwnd, GW_OWNER) == nullptr) {
        windows->push_back({hwnd, processId});
    }
    return TRUE;
}


std::vector<HWND> FindWindowsByPID(DWORD targetPID) {
    std::vector<WindowInfo> windows;
    std::vector<HWND> targetWindows;
    EnumWindows(EnumWindowsProc, reinterpret_cast<LPARAM>(&windows));
    for (const auto& window : windows) {
        if (window.processId == targetPID) {
            targetWindows.push_back(window.hwnd);
        }
    }
    return targetWindows;
}

// static
void IconPlugin::RegisterWithRegistrar(
    flutter::PluginRegistrarWindows *registrar) {
  auto channel =
      std::make_unique<flutter::MethodChannel<flutter::EncodableValue>>(
          registrar->messenger(), "icon",
          &flutter::StandardMethodCodec::GetInstance());

  auto plugin = std::make_unique<IconPlugin>();

  channel->SetMethodCallHandler(
      [plugin_pointer = plugin.get()](const auto &call, auto result) {
        plugin_pointer->HandleMethodCall(call, std::move(result));
      });

  registrar->AddPlugin(std::move(plugin));
}

IconPlugin::IconPlugin() {}

IconPlugin::~IconPlugin() {}

void IconPlugin::HandleMethodCall(
    const flutter::MethodCall<flutter::EncodableValue> &method_call,
    std::unique_ptr<flutter::MethodResult<flutter::EncodableValue>> result) {
  if (method_call.method_name().compare("setIcon") == 0) {

    const auto* arguments = std::get_if<flutter::EncodableMap>(method_call.arguments());
    if (!arguments) {
      result->Error("INVALID_ARGUMENT", "Arguments must be a map");
      return;
    }


    auto pid_it = arguments->find(flutter::EncodableValue("pid"));
    if (pid_it == arguments->end() || !std::holds_alternative<int32_t>(pid_it->second)) {
      result->Error("INVALID_ARGUMENT", "Missing or invalid pid");
      return;
    }
    DWORD targetPID = static_cast<DWORD>(std::get<int32_t>(pid_it->second));


    auto path_it = arguments->find(flutter::EncodableValue("iconPath"));
    if (path_it == arguments->end() || !std::holds_alternative<std::string>(path_it->second)) {
      result->Error("INVALID_ARGUMENT", "Missing or invalid iconPath");
      return;
    }
    std::string iconPath = std::get<std::string>(path_it->second);


    std::vector<HWND> windows = FindWindowsByPID(targetPID);
    if (windows.empty()) {
      result->Error("WINDOW_NOT_FOUND", "No visible windows found for the given PID");
      return;
    }


    HICON hIcon = (HICON)LoadImageA(
        nullptr,
        iconPath.c_str(),
        IMAGE_ICON,
        0, 0,
        LR_LOADFROMFILE | LR_SHARED | LR_DEFAULTSIZE
    );

    if (hIcon == nullptr) {
      result->Error("ICON_LOAD_FAILED", "Failed to load icon from the specified path");
      return;
    }


    for (HWND hwnd : windows) {
      SendMessage(hwnd, WM_SETICON, ICON_SMALL, (LPARAM)hIcon);
      SendMessage(hwnd, WM_SETICON, ICON_BIG, (LPARAM)hIcon);

      InvalidateRect(hwnd, nullptr, TRUE);
      UpdateWindow(hwnd);
    }

    result->Success(flutter::EncodableValue("success"));
  } else {
    result->NotImplemented();
  }
}

}  // namespace icon