import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:synchronizer/synchronizer_method_channel.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  MethodChannelSynchronizer platform = MethodChannelSynchronizer();
  const MethodChannel channel = MethodChannel('synchronizer');

  // 测试PID
  const int testSourcePID = 26604;
  const int testTargetPID = 32656;

  setUp(() {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
      channel,
          (MethodCall methodCall) async {
        // 根据不同的方法调用返回不同的模拟数据
        switch (methodCall.method) {
          case 'startSynchronization':
            final arguments = methodCall.arguments as Map<String, dynamic>;
            final sourcePID = arguments['sourcePID'] as int;
            final targetPIDs = arguments['targetPIDs'] as List<int>;

            // 验证参数
            if (sourcePID > 0 && targetPIDs.isNotEmpty) {
              return "success";
            } else {
              throw PlatformException(
                code: 'INVALID_ARGUMENT',
                message: 'Invalid parameters',
              );
            }

          case 'stopSynchronization':
            return "success";

          case 'isSynchronizationRunning':
            return false; // 默认未运行状态

          case 'arrangeWindow':
            final arguments = methodCall.arguments as Map<String, dynamic>;
            final pid = arguments['pid'] as int;
            final x = arguments['x'] as int;
            final y = arguments['y'] as int;
            final width = arguments['width'] as int;
            final height = arguments['height'] as int;

            // 验证参数
            if (pid > 0 && width > 0 && height > 0) {
              return "success";
            } else {
              throw PlatformException(
                code: 'INVALID_ARGUMENT',
                message: 'Invalid window parameters',
              );
            }

          case 'getScreenSize':
            return {
              'width': 1920,
              'height': 1080,
            };

          default:
            throw PlatformException(
              code: 'UNIMPLEMENTED',
              message: 'Method ${methodCall.method} not implemented',
            );
        }
      },
    );
  });

  tearDown(() {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(channel, null);
  });

  group('Synchronization Tests', () {
    test('startSynchronization with valid parameters', () async {
      final result = await platform.startSynchronization(
        sourcePID: testSourcePID,
        targetPIDs: [testTargetPID],
      );

      expect(result, "success");
    });

    test('startSynchronization with multiple target PIDs', () async {
      final result = await platform.startSynchronization(
        sourcePID: testSourcePID,
        targetPIDs: [testTargetPID, 12345, 67890],
      );

      expect(result, "success");
    });

    test('startSynchronization with invalid source PID', () async {
      expect(
            () => platform.startSynchronization(
          sourcePID: 0, // Invalid PID
          targetPIDs: [testTargetPID],
        ),
        throwsA(isA<Exception>()),
      );
    });

    test('startSynchronization with empty target PIDs', () async {
      expect(
            () => platform.startSynchronization(
          sourcePID: testSourcePID,
          targetPIDs: [], // Empty list
        ),
        throwsA(isA<Exception>()),
      );
    });

    test('stopSynchronization should return success', () async {
      final result = await platform.stopSynchronization();

      expect(result, "success");
    });

    test('isSynchronizationRunning should return boolean', () async {
      final result = await platform.isSynchronizationRunning();

      expect(result, isA<bool>());
      expect(result, false);
    });
  });

  group('Window Arrangement Tests', () {
    test('arrangeWindow with valid parameters', () async {
      final result = await platform.arrangeWindow(
        pid: testSourcePID,
        x: 0,
        y: 0,
        width: 800,
        height: 600,
      );

      expect(result, "success");
    });

    test('arrangeWindow with different positions', () async {
      // Test different window positions
      final positions = [
        {'x': 0, 'y': 0, 'width': 960, 'height': 1080}, // Left half
        {'x': 960, 'y': 0, 'width': 960, 'height': 1080}, // Right half
        {'x': 0, 'y': 0, 'width': 960, 'height': 540}, // Top left quarter
        {'x': 960, 'y': 0, 'width': 960, 'height': 540}, // Top right quarter
      ];

      for (final pos in positions) {
        final result = await platform.arrangeWindow(
          pid: testSourcePID,
          x: pos['x']! as int,
          y: pos['y']! as int,
          width: pos['width']! as int,
          height: pos['height']! as int,
        );

        expect(result, "success", reason: 'Failed for position: $pos');
      }
    });

    test('arrangeWindow with invalid PID', () async {
      expect(
            () => platform.arrangeWindow(
          pid: 0, // Invalid PID
          x: 0,
          y: 0,
          width: 800,
          height: 600,
        ),
        throwsA(isA<Exception>()),
      );
    });

    test('arrangeWindow with invalid dimensions', () async {
      expect(
            () => platform.arrangeWindow(
          pid: testSourcePID,
          x: 0,
          y: 0,
          width: 0, // Invalid width
          height: 600,
        ),
        throwsA(isA<Exception>()),
      );
    });

    test('arrangeWindow with negative dimensions', () async {
      expect(
            () => platform.arrangeWindow(
          pid: testSourcePID,
          x: 0,
          y: 0,
          width: 800,
          height: -100, // Invalid height
        ),
        throwsA(isA<Exception>()),
      );
    });

    test('getScreenSize should return valid dimensions', () async {
      final result = await platform.getScreenSize();

      expect(result['width'], isA<int>());
      expect(result['height'], isA<int>());
      expect(result['width'], 1920);
      expect(result['height'], 1080);
    });
  });

  group('Error Handling Tests', () {
    test('should handle platform exceptions', () async {
      // 临时设置一个会抛出异常的处理器
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
        channel,
            (MethodCall methodCall) async {
          throw PlatformException(
            code: 'TEST_ERROR',
            message: 'Test error message',
          );
        },
      );

      expect(
            () => platform.startSynchronization(sourcePID: testSourcePID, targetPIDs: [testTargetPID]),
        throwsA(isA<Exception>()),
      );
    });

    test('should handle null responses gracefully for operation methods', () async {
      // 临时设置一个返回null的处理器
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
        channel,
            (MethodCall methodCall) async {
          return null;
        },
      );

      // 操作方法应该返回空字符串
      final syncResult = await platform.startSynchronization(sourcePID: testSourcePID, targetPIDs: [testTargetPID]);
      expect(syncResult, '');

      final stopResult = await platform.stopSynchronization();
      expect(stopResult, '');

      final arrangeResult = await platform.arrangeWindow(
        pid: testSourcePID,
        x: 0,
        y: 0,
        width: 800,
        height: 600,
      );
      expect(arrangeResult, '');
    });

    test('should handle null responses gracefully for query methods', () async {
      // 临时设置一个返回null的处理器
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
        channel,
            (MethodCall methodCall) async {
          return null;
        },
      );

      // 查询方法应该返回默认值
      final isRunning = await platform.isSynchronizationRunning();
      expect(isRunning, false);

      final screenSize = await platform.getScreenSize();
      expect(screenSize, isA<Map<String, dynamic>>());
      expect(screenSize, <String, dynamic>{});
    });
  });

  group('Parameter Validation Tests', () {
    test('startSynchronization parameter types', () async {
      // 确保参数类型正确传递
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
        channel,
            (MethodCall methodCall) async {
          if (methodCall.method == 'startSynchronization') {
            final arguments = methodCall.arguments as Map<String, dynamic>;

            expect(arguments['sourcePID'], isA<int>());
            expect(arguments['targetPIDs'], isA<List<int>>());
            expect(arguments['sourcePID'], testSourcePID);
            expect(arguments['targetPIDs'], [testTargetPID]);

            return "success";
          }
          return null;
        },
      );

      final result = await platform.startSynchronization(
        sourcePID: testSourcePID,
        targetPIDs: [testTargetPID],
      );
      expect(result, "success");
    });

    test('arrangeWindow parameter types', () async {
      // 确保参数类型正确传递
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
        channel,
            (MethodCall methodCall) async {
          if (methodCall.method == 'arrangeWindow') {
            final arguments = methodCall.arguments as Map<String, dynamic>;

            expect(arguments['pid'], isA<int>());
            expect(arguments['x'], isA<int>());
            expect(arguments['y'], isA<int>());
            expect(arguments['width'], isA<int>());
            expect(arguments['height'], isA<int>());

            expect(arguments['pid'], testSourcePID);
            expect(arguments['x'], 100);
            expect(arguments['y'], 200);
            expect(arguments['width'], 800);
            expect(arguments['height'], 600);

            return "success";
          }
          return null;
        },
      );

      final result = await platform.arrangeWindow(
        pid: testSourcePID,
        x: 100,
        y: 200,
        width: 800,
        height: 600,
      );
      expect(result, "success");
    });
  });

  group('Real-world Scenario Tests', () {
    test('typical usage workflow', () async {
      // 模拟真实使用场景的工作流程

      // 1. 检查初始同步状态
      bool isRunning = await platform.isSynchronizationRunning();
      expect(isRunning, false);

      // 2. 启动同步
      String result = await platform.startSynchronization(
        sourcePID: testSourcePID,
        targetPIDs: [testTargetPID],
      );
      expect(result, "success");

      // 3. 获取屏幕尺寸
      final screenSize = await platform.getScreenSize();
      expect(screenSize['width'], greaterThan(0));
      expect(screenSize['height'], greaterThan(0));

      // 4. 排列窗口到左半屏
      result = await platform.arrangeWindow(
        pid: testSourcePID,
        x: 0,
        y: 0,
        width: screenSize['width']! ~/ 2,
        height: screenSize['height']!,
      );
      expect(result, "success");

      // 5. 排列另一个窗口到右半屏
      result = await platform.arrangeWindow(
        pid: testTargetPID,
        x: screenSize['width']! ~/ 2,
        y: 0,
        width: screenSize['width']! ~/ 2,
        height: screenSize['height']!,
      );
      expect(result, "success");

      // 6. 停止同步
      result = await platform.stopSynchronization();
      expect(result, "success");
    });

    test('error handling in workflow', () async {
      // 测试错误处理的工作流程

      // 尝试用无效参数启动同步
      expect(
            () => platform.startSynchronization(
          sourcePID: 0,
          targetPIDs: [testTargetPID],
        ),
        throwsA(isA<Exception>()),
      );

      // 尝试用无效参数排列窗口
      expect(
            () => platform.arrangeWindow(
          pid: testSourcePID,
          x: 0,
          y: 0,
          width: -100,
          height: 600,
        ),
        throwsA(isA<Exception>()),
      );

      // 正常操作应该仍然工作
      final screenSize = await platform.getScreenSize();
      expect(screenSize, isA<Map<String, dynamic>>());

      final isRunning = await platform.isSynchronizationRunning();
      expect(isRunning, isA<bool>());
    });
  });
}