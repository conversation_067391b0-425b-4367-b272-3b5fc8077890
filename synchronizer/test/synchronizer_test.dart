import 'package:flutter_test/flutter_test.dart';
import 'package:synchronizer/synchronizer.dart';
import 'package:synchronizer/synchronizer_platform_interface.dart';
import 'package:synchronizer/synchronizer_method_channel.dart';
import 'package:plugin_platform_interface/plugin_platform_interface.dart';

class MockSynchronizerPlatform
    with MockPlatformInterfaceMixin
    implements SynchronizerPlatform {

  bool _isRunning = false;
  bool _shouldThrowError = false;
  String _errorMessage = '';

  // 用于测试错误情况
  void setError(bool shouldThrow, [String message = 'Test error']) {
    _shouldThrowError = shouldThrow;
    _errorMessage = message;
  }

  @override
  Future<String> startSynchronization({
    required int sourcePID,
    required List<int> targetPIDs,
  }) async {
    if (_shouldThrowError) {
      throw Exception(_errorMessage);
    }

    // 模拟启动成功
    _isRunning = true;
    return "success";
  }

  @override
  Future<String> stopSynchronization() async {
    if (_shouldThrowError) {
      throw Exception(_errorMessage);
    }

    // 模拟停止成功
    _isRunning = false;
    return "success";
  }

  @override
  Future<bool> isSynchronizationRunning() async {
    if (_shouldThrowError) {
      throw Exception(_errorMessage);
    }

    return _isRunning;
  }

  @override
  Future<String> arrangeWindow({
    required int pid,
    required int x,
    required int y,
    required int width,
    required int height,
  }) async {
    if (_shouldThrowError) {
      throw Exception(_errorMessage);
    }

    // 模拟窗口排列成功
    return "success";
  }

  @override
  Future<Map<String, dynamic>> getScreenSize() async {
    if (_shouldThrowError) {
      throw Exception(_errorMessage);
    }

    // 模拟屏幕尺寸
    return {
      'width': 1920,
      'height': 1080,
    };
  }
}

void main() {
  final SynchronizerPlatform initialPlatform = SynchronizerPlatform.instance;

  // 测试PID
  const int testSourcePID = 26604;
  const int testTargetPID = 32656;

  test('$MethodChannelSynchronizer is the default instance', () {
    expect(initialPlatform, isInstanceOf<MethodChannelSynchronizer>());
  });

  group('SynchronizerManager Tests', () {
    late MockSynchronizerPlatform fakePlatform;

    setUp(() {
      fakePlatform = MockSynchronizerPlatform();
      SynchronizerPlatform.instance = fakePlatform;
    });

    tearDown(() {
      SynchronizerPlatform.instance = initialPlatform;
    });

    test('startSync should start synchronization successfully', () async {
      final result = await SynchronizerManager.startSync(
        sourcePID: testSourcePID,
        targetPIDs: [testTargetPID],
      );

      expect(result, true);
      expect(await SynchronizerManager.isRunning(), true);
    });

    test('startSync should handle errors gracefully', () async {
      fakePlatform.setError(true, 'Failed to start synchronization');

      final result = await SynchronizerManager.startSync(
        sourcePID: testSourcePID,
        targetPIDs: [testTargetPID],
      );

      expect(result, false);
    });

    test('stopSync should stop synchronization successfully', () async {
      // 先启动同步
      await SynchronizerManager.startSync(
        sourcePID: testSourcePID,
        targetPIDs: [testTargetPID],
      );

      // 然后停止同步
      final result = await SynchronizerManager.stopSync();

      expect(result, true);
      expect(await SynchronizerManager.isRunning(), false);
    });

    test('stopSync should handle errors gracefully', () async {
      fakePlatform.setError(true, 'Failed to stop synchronization');

      final result = await SynchronizerManager.stopSync();

      expect(result, false);
    });

    test('isRunning should return correct status', () async {
      // 初始状态应该是未运行
      expect(await SynchronizerManager.isRunning(), false);

      // 启动后应该是运行中
      await SynchronizerManager.startSync(
        sourcePID: testSourcePID,
        targetPIDs: [testTargetPID],
      );
      expect(await SynchronizerManager.isRunning(), true);

      // 停止后应该是未运行
      await SynchronizerManager.stopSync();
      expect(await SynchronizerManager.isRunning(), false);
    });

    test('isRunning should handle errors gracefully', () async {
      fakePlatform.setError(true, 'Failed to check status');

      final result = await SynchronizerManager.isRunning();

      expect(result, false);
    });
  });

  group('WindowArrangerManager Tests', () {
    late MockSynchronizerPlatform fakePlatform;

    setUp(() {
      fakePlatform = MockSynchronizerPlatform();
      SynchronizerPlatform.instance = fakePlatform;
    });

    tearDown(() {
      SynchronizerPlatform.instance = initialPlatform;
    });

    test('arrangeWindow should arrange window successfully', () async {
      final result = await WindowArrangerManager.arrangeWindow(
        pid: testSourcePID,
        x: 0,
        y: 0,
        width: 800,
        height: 600,
      );

      expect(result, true);
    });

    test('arrangeWindow should handle errors gracefully', () async {
      fakePlatform.setError(true, 'Failed to arrange window');

      final result = await WindowArrangerManager.arrangeWindow(
        pid: testSourcePID,
        x: 0,
        y: 0,
        width: 800,
        height: 600,
      );

      expect(result, false);
    });

    test('getScreenSize should return screen dimensions', () async {
      final screenSize = await WindowArrangerManager.getScreenSize();

      expect(screenSize.width, 1920);
      expect(screenSize.height, 1080);
    });

    test('getScreenSize should handle errors gracefully', () async {
      fakePlatform.setError(true, 'Failed to get screen size');

      final screenSize = await WindowArrangerManager.getScreenSize();

      // 应该返回默认值
      expect(screenSize.width, 1920);
      expect(screenSize.height, 1080);
    });

    test('arrangeToLeftHalf should arrange window to left half', () async {
      final result = await WindowArrangerManager.arrangeToLeftHalf(testSourcePID);
      expect(result, true);
    });

    test('arrangeToRightHalf should arrange window to right half', () async {
      final result = await WindowArrangerManager.arrangeToRightHalf(testSourcePID);
      expect(result, true);
    });

    test('arrangeToTopLeft should arrange window to top left quarter', () async {
      final result = await WindowArrangerManager.arrangeToTopLeft(testSourcePID);
      expect(result, true);
    });

    test('arrangeToTopRight should arrange window to top right quarter', () async {
      final result = await WindowArrangerManager.arrangeToTopRight(testSourcePID);
      expect(result, true);
    });

    test('arrangeToBottomLeft should arrange window to bottom left quarter', () async {
      final result = await WindowArrangerManager.arrangeToBottomLeft(testSourcePID);
      expect(result, true);
    });

    test('arrangeToBottomRight should arrange window to bottom right quarter', () async {
      final result = await WindowArrangerManager.arrangeToBottomRight(testSourcePID);
      expect(result, true);
    });
  });

  group('Synchronizer Class Tests', () {
    late MockSynchronizerPlatform fakePlatform;
    late Synchronizer synchronizer;

    setUp(() {
      fakePlatform = MockSynchronizerPlatform();
      SynchronizerPlatform.instance = fakePlatform;
      synchronizer = Synchronizer();
    });

    tearDown(() {
      SynchronizerPlatform.instance = initialPlatform;
    });

    test('startSynchronization should call platform method', () async {
      final result = await synchronizer.startSynchronization(
        sourcePID: testSourcePID,
        targetPIDs: [testTargetPID],
      );

      expect(result, "success");
    });

    test('startSynchronization should throw on error', () async {
      fakePlatform.setError(true, 'Start failed');

      expect(
            () => synchronizer.startSynchronization(
          sourcePID: testSourcePID,
          targetPIDs: [testTargetPID],
        ),
        throwsA(isA<Exception>()),
      );
    });

    test('stopSynchronization should call platform method', () async {
      final result = await synchronizer.stopSynchronization();

      expect(result, "success");
    });

    test('stopSynchronization should throw on error', () async {
      fakePlatform.setError(true, 'Stop failed');

      expect(
            () => synchronizer.stopSynchronization(),
        throwsA(isA<Exception>()),
      );
    });

    test('isSynchronizationRunning should call platform method', () async {
      final result = await synchronizer.isSynchronizationRunning();

      expect(result, false); // 初始状态
    });

    test('isSynchronizationRunning should throw on error', () async {
      fakePlatform.setError(true, 'Status check failed');

      expect(
            () => synchronizer.isSynchronizationRunning(),
        throwsA(isA<Exception>()),
      );
    });

    test('arrangeWindow should call platform method', () async {
      final result = await synchronizer.arrangeWindow(
        pid: testSourcePID,
        x: 100,
        y: 100,
        width: 800,
        height: 600,
      );

      expect(result, "success");
    });

    test('arrangeWindow should throw on error', () async {
      fakePlatform.setError(true, 'Arrange failed');

      expect(
            () => synchronizer.arrangeWindow(
          pid: testSourcePID,
          x: 100,
          y: 100,
          width: 800,
          height: 600,
        ),
        throwsA(isA<Exception>()),
      );
    });

    test('getScreenSize should call platform method', () async {
      final result = await synchronizer.getScreenSize();

      expect(result['width'], 1920);
      expect(result['height'], 1080);
    });

    test('getScreenSize should throw on error', () async {
      fakePlatform.setError(true, 'Get screen size failed');

      expect(
            () => synchronizer.getScreenSize(),
        throwsA(isA<Exception>()),
      );
    });
  });

  group('ScreenSize Model Tests', () {
    test('ScreenSize.fromMap should create instance from map', () {
      final map = {'width': 1920, 'height': 1080};
      final screenSize = ScreenSize.fromMap(map);

      expect(screenSize.width, 1920);
      expect(screenSize.height, 1080);
    });

    test('ScreenSize.fromMap should handle missing values', () {
      final map = <String, dynamic>{}; // 空map
      final screenSize = ScreenSize.fromMap(map);

      expect(screenSize.width, 0);
      expect(screenSize.height, 0);
    });

    test('ScreenSize.toMap should convert instance to map', () {
      final screenSize = ScreenSize(width: 1920, height: 1080);
      final map = screenSize.toMap();

      expect(map['width'], 1920);
      expect(map['height'], 1080);
    });

    test('ScreenSize.toString should return formatted string', () {
      final screenSize = ScreenSize(width: 1920, height: 1080);

      expect(screenSize.toString(), 'ScreenSize(1920x1080)');
    });
  });

  group('Integration Tests', () {
    late MockSynchronizerPlatform fakePlatform;

    setUp(() {
      fakePlatform = MockSynchronizerPlatform();
      SynchronizerPlatform.instance = fakePlatform;
    });

    tearDown(() {
      SynchronizerPlatform.instance = initialPlatform;
    });

    test('complete workflow: start sync, arrange window, stop sync', () async {
      // 1. 启动同步
      final startResult = await SynchronizerManager.startSync(
        sourcePID: testSourcePID,
        targetPIDs: [testTargetPID],
      );
      expect(startResult, true);
      expect(await SynchronizerManager.isRunning(), true);

      // 2. 排列窗口
      final arrangeResult = await WindowArrangerManager.arrangeToLeftHalf(testSourcePID);
      expect(arrangeResult, true);

      // 3. 获取屏幕尺寸
      final screenSize = await WindowArrangerManager.getScreenSize();
      expect(screenSize.width, 1920);
      expect(screenSize.height, 1080);

      // 4. 停止同步
      final stopResult = await SynchronizerManager.stopSync();
      expect(stopResult, true);
      expect(await SynchronizerManager.isRunning(), false);
    });

    test('multiple target PIDs should work', () async {
      final result = await SynchronizerManager.startSync(
        sourcePID: testSourcePID,
        targetPIDs: [testTargetPID, 12345, 67890], // 多个目标PID
      );

      expect(result, true);
      expect(await SynchronizerManager.isRunning(), true);
    });

    test('error recovery workflow', () async {
      // 模拟启动失败
      fakePlatform.setError(true, 'Failed to start');

      final startResult = await SynchronizerManager.startSync(
        sourcePID: testSourcePID,
        targetPIDs: [testTargetPID],
      );
      expect(startResult, false);

      // 恢复正常并重试
      fakePlatform.setError(false);

      final retryResult = await SynchronizerManager.startSync(
        sourcePID: testSourcePID,
        targetPIDs: [testTargetPID],
      );
      expect(retryResult, true);
      expect(await SynchronizerManager.isRunning(), true);
    });

    test('parallel operations should work', () async {
      // 测试并行操作
      final futures = [
        WindowArrangerManager.arrangeToTopLeft(testSourcePID),
        WindowArrangerManager.arrangeToTopRight(testTargetPID),
        WindowArrangerManager.getScreenSize(),
      ];

      final results = await Future.wait(futures);

      expect(results[0], true);  // arrangeToTopLeft
      expect(results[1], true);  // arrangeToTopRight
      expect((results[2] as ScreenSize).width, 1920);  // getScreenSize
    });
  });
}