import 'package:plugin_platform_interface/plugin_platform_interface.dart';

import 'synchronizer_method_channel.dart';

abstract class SynchronizerPlatform extends PlatformInterface {
  /// Constructs a SynchronizerPlatform.
  SynchronizerPlatform() : super(token: _token);

  static final Object _token = Object();

  static SynchronizerPlatform _instance = MethodChannelSynchronizer();

  /// The default instance of [SynchronizerPlatform] to use.
  ///
  /// Defaults to [MethodChannelSynchronizer].
  static SynchronizerPlatform get instance => _instance;

  /// Platform-specific implementations should set this with their own
  /// platform-specific class that extends [SynchronizerPlatform] when
  /// they register themselves.
  static set instance(SynchronizerPlatform instance) {
    PlatformInterface.verifyToken(instance, _token);
    _instance = instance;
  }

  // ==================== 同步器功能 ====================

  /// 启动输入同步
  /// [sourcePID] 主控窗口的进程ID
  /// [targetPIDs] 被控窗口的进程ID列表
  /// 返回 "success" 字符串表示成功
  Future<String> startSynchronization({
    required int sourcePID,
    required List<int> targetPIDs,
  }) {
    throw UnimplementedError('startSynchronization() has not been implemented.');
  }

  /// 停止输入同步
  /// 返回 "success" 字符串表示成功
  Future<String> stopSynchronization() {
    throw UnimplementedError('stopSynchronization() has not been implemented.');
  }

  /// 检查同步是否正在运行
  Future<bool> isSynchronizationRunning() {
    throw UnimplementedError('isSynchronizationRunning() has not been implemented.');
  }

  // ==================== 窗口排列功能 ====================

  /// 排列单个窗口
  /// [pid] 进程ID
  /// [x] X坐标
  /// [y] Y坐标
  /// [width] 宽度
  /// [height] 高度
  /// 返回 "success" 字符串表示成功
  Future<String> arrangeWindow({
    required int pid,
    required int x,
    required int y,
    required int width,
    required int height,
  }) {
    throw UnimplementedError('arrangeWindow() has not been implemented.');
  }

  /// 获取屏幕尺寸
  /// 返回包含 width 和 height 的 Map
  Future<Map<String, dynamic>> getScreenSize() {
    throw UnimplementedError('getScreenSize() has not been implemented.');
  }



  /// 聚焦指定PID的窗口
  /// [pid] 进程ID
  /// 返回 "success" 字符串表示成功
  Future<String> focusWindow({
    required int pid,
  }) {
    throw UnimplementedError('focusWindow() has not been implemented.');
  }
}
