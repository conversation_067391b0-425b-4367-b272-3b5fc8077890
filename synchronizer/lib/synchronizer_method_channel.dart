import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

import 'synchronizer_platform_interface.dart';

/// An implementation of [SynchronizerPlatform] that uses method channels.
class MethodChannelSynchronizer extends SynchronizerPlatform {
  /// The method channel used to interact with the native platform.
  @visibleForTesting
  final methodChannel = const MethodChannel('synchronizer');

  // ==================== 同步器功能 ====================

  @override
  Future<String> startSynchronization({
    required int sourcePID,
    required List<int> targetPIDs,
  }) async {
    try {
      final result = await methodChannel.invokeMethod<String>(
        'startSynchronization',
        {
          'sourcePID': sourcePID,
          'targetPIDs': targetPIDs,
        },
      );
      return result ?? '';
    } on PlatformException catch (e) {
      throw Exception('Failed to start synchronization: ${e.message}');
    }
  }

  @override
  Future<String> stopSynchronization() async {
    try {
      final result = await methodChannel.invokeMethod<String>(
        'stopSynchronization',
      );
      return result ?? '';
    } on PlatformException catch (e) {
      throw Exception('Failed to stop synchronization: ${e.message}');
    }
  }

  @override
  Future<bool> isSynchronizationRunning() async {
    try {
      final result = await methodChannel.invokeMethod<bool>(
        'isSynchronizationRunning',
      );
      return result ?? false;
    } on PlatformException catch (e) {
      throw Exception('Failed to check synchronization status: ${e.message}');
    }
    catch (e) {
      print('Failed to check synchronization status: $e');
      return false;
    }
  }

  // ==================== 窗口排列功能 ====================

  @override
  Future<String> arrangeWindow({
    required int pid,
    required int x,
    required int y,
    required int width,
    required int height,
  }) async {
    try {
      final result = await methodChannel.invokeMethod<String>(
        'arrangeWindow',
        {
          'pid': pid,
          'x': x,
          'y': y,
          'width': width,
          'height': height,
        },
      );
      return result ?? '';
    } on PlatformException catch (e) {
      throw Exception('Failed to arrange window: ${e.message}');
    }
  }

  @override
  Future<Map<String, dynamic>> getScreenSize() async {
    try {
      final result = await methodChannel.invokeMethod<Map<Object?, Object?>>(
        'getScreenSize',
      );
      return result != null ? Map<String, dynamic>.from(result) : <String, dynamic>{};
    } on PlatformException catch (e) {
      throw Exception('Failed to get screen size: ${e.message}');
    }
  }



  @override
  Future<String> focusWindow({
    required int pid,
  }) async {
    try {
      final result = await methodChannel.invokeMethod<String>(
        'focusWindow',
        {
          'pid': pid,
        },
      );
      return result ?? '';
    } on PlatformException catch (e) {
      throw Exception('Failed to focus window: ${e.message}');
    }
  }
}
