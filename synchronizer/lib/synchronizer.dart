import 'synchronizer_platform_interface.dart';

class Synchronizer {
  // ==================== 同步器功能 ====================

  /// 启动输入同步
  /// [sourcePID] 主控窗口的进程ID
  /// [targetPIDs] 被控窗口的进程ID列表
  /// 返回 "success" 字符串表示成功
  Future<String> startSynchronization({
    required int sourcePID,
    required List<int> targetPIDs,
  }) {
    return SynchronizerPlatform.instance.startSynchronization(
      sourcePID: sourcePID,
      targetPIDs: targetPIDs,
    );
  }

  /// 停止输入同步
  /// 返回 "success" 字符串表示成功
  Future<String> stopSynchronization() {
    return SynchronizerPlatform.instance.stopSynchronization();
  }

  /// 检查同步是否正在运行
  Future<bool> isSynchronizationRunning() {
    return SynchronizerPlatform.instance.isSynchronizationRunning();
  }

  // ==================== 窗口排列功能 ====================

  /// 排列单个窗口
  /// [pid] 进程ID
  /// [x] X坐标
  /// [y] Y坐标
  /// [width] 宽度
  /// [height] 高度
  /// 返回 "success" 字符串表示成功
  Future<String> arrangeWindow({
    required int pid,
    required int x,
    required int y,
    required int width,
    required int height,
  }) {
    print('arrangeWindow: $pid, $x, $y, $width, $height');
    return SynchronizerPlatform.instance.arrangeWindow(
      pid: pid,
      x: x,
      y: y,
      width: width,
      height: height,
    );
  }

  /// 获取屏幕尺寸
  Future<Map<String, dynamic>> getScreenSize() {
    return SynchronizerPlatform.instance.getScreenSize();
  }

  /// 聚焦指定PID的窗口
  /// [pid] 进程ID
  /// 返回 "success" 字符串表示成功
  Future<String> focusWindow({
    required int pid,
  }) {
    return SynchronizerPlatform.instance.focusWindow(
      pid: pid,
    );
  }
}

// ==================== 数据模型 ====================

/// 屏幕尺寸
class ScreenSize {
  final int width;
  final int height;

  ScreenSize({
    required this.width,
    required this.height,
  });

  factory ScreenSize.fromMap(Map<String, dynamic> map) {
    return ScreenSize(
      width: map['width'] ?? 0,
      height: map['height'] ?? 0,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'width': width,
      'height': height,
    };
  }

  @override
  String toString() {
    return 'ScreenSize(${width}x$height)';
  }
}

// ==================== 便捷管理器类 ====================

/// 同步器管理器
class SynchronizerManager {
  static final Synchronizer _synchronizer = Synchronizer();

  /// 启动同步
  static Future<bool> startSync({
    required int sourcePID,
    required List<int> targetPIDs,
  }) async {
    try {
      final result = await _synchronizer.startSynchronization(
        sourcePID: sourcePID,
        targetPIDs: targetPIDs,
      );
      return result == "success";
    } catch (e) {
      print('启动同步失败: $e');
      return false;
    }
  }

  /// 停止同步
  static Future<bool> stopSync() async {
    try {
      final result = await _synchronizer.stopSynchronization();
      return result == "success";
    } catch (e) {
      print('停止同步失败: $e');
      return false;
    }
  }

  /// 检查运行状态
  static Future<bool> isRunning() async {
    try {
      return await _synchronizer.isSynchronizationRunning();
    } catch (e) {
      print('检查同步状态失败: $e');
      return false;
    }
  }
}

/// 窗口排列管理器
class WindowArrangerManager {
  static final Synchronizer _synchronizer = Synchronizer();

  /// 排列单个窗口
  static Future<bool> arrangeWindow({
    required int pid,
    required int x,
    required int y,
    required int width,
    required int height,
  }) async {
    try {
      final result = await _synchronizer.arrangeWindow(
        pid: pid,
        x: x,
        y: y,
        width: width,
        height: height,
      );
      return result == "success";
    } catch (e) {
      print('排列窗口失败 (PID: $pid): $e');
      return false;
    }
  }

  /// 获取屏幕尺寸
  static Future<ScreenSize> getScreenSize() async {
    try {
      final result = await _synchronizer.getScreenSize();
      return ScreenSize.fromMap(result);
    } catch (e) {
      print('获取屏幕尺寸失败: $e');
      return ScreenSize(width: 1920, height: 1080); // 返回默认值
    }
  }

  /// 排列到左半屏
  static Future<bool> arrangeToLeftHalf(int pid) async {
    final screenSize = await getScreenSize();
    return await arrangeWindow(
      pid: pid,
      x: 0,
      y: 0,
      width: screenSize.width ~/ 2,
      height: screenSize.height,
    );
  }

  /// 排列到右半屏
  static Future<bool> arrangeToRightHalf(int pid) async {
    final screenSize = await getScreenSize();
    return await arrangeWindow(
      pid: pid,
      x: screenSize.width ~/ 2,
      y: 0,
      width: screenSize.width ~/ 2,
      height: screenSize.height,
    );
  }

  /// 排列到左上角1/4屏
  static Future<bool> arrangeToTopLeft(int pid) async {
    final screenSize = await getScreenSize();
    return await arrangeWindow(
      pid: pid,
      x: 0,
      y: 0,
      width: screenSize.width ~/ 2,
      height: screenSize.height ~/ 2,
    );
  }

  /// 排列到右上角1/4屏
  static Future<bool> arrangeToTopRight(int pid) async {
    final screenSize = await getScreenSize();
    return await arrangeWindow(
      pid: pid,
      x: screenSize.width ~/ 2,
      y: 0,
      width: screenSize.width ~/ 2,
      height: screenSize.height ~/ 2,
    );
  }

  /// 排列到左下角1/4屏
  static Future<bool> arrangeToBottomLeft(int pid) async {
    final screenSize = await getScreenSize();
    return await arrangeWindow(
      pid: pid,
      x: 0,
      y: screenSize.height ~/ 2,
      width: screenSize.width ~/ 2,
      height: screenSize.height ~/ 2,
    );
  }

  /// 排列到右下角1/4屏
  static Future<bool> arrangeToBottomRight(int pid) async {
    final screenSize = await getScreenSize();
    return await arrangeWindow(
      pid: pid,
      x: screenSize.width ~/ 2,
      y: screenSize.height ~/ 2,
      width: screenSize.width ~/ 2,
      height: screenSize.height ~/ 2,
    );
  }



  /// 聚焦指定PID的窗口
  static Future<bool> focusWindow(int pid) async {
    try {
      final result = await _synchronizer.focusWindow(pid: pid);
      return result == "success";
    } catch (e) {
      print('聚焦窗口失败 (PID: $pid): $e');
      return false;
    }
  }
}
