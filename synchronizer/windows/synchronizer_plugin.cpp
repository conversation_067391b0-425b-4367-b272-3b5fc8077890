#include "synchronizer_plugin.h"
// This must be included before many other Windows headers.
#include <windows.h>

#include <flutter/method_channel.h>
#include <flutter/plugin_registrar_windows.h>
#include <flutter/standard_method_codec.h>

#include <memory>
#include <vector>
#include <map>

// 引入同步器和窗口排列功能
#include "synchronizer.h"
#include "arrange.h"

namespace synchronizer {

// static
void SynchronizerPlugin::RegisterWithRegistrar(
    flutter::PluginRegistrarWindows *registrar) {
  auto channel =
      std::make_unique<flutter::MethodChannel<flutter::EncodableValue>>(
          registrar->messenger(), "synchronizer",
          &flutter::StandardMethodCodec::GetInstance());

  auto plugin = std::make_unique<SynchronizerPlugin>();

  channel->SetMethodCallHandler(
      [plugin_pointer = plugin.get()](const auto &call, auto result) {
        plugin_pointer->HandleMethodCall(call, std::move(result));
      });

  registrar->AddPlugin(std::move(plugin));
}

SynchronizerPlugin::SynchronizerPlugin() {
  // 构造函数，无需特殊初始化（使用单例模式）
}

SynchronizerPlugin::~SynchronizerPlugin() {
  // 确保清理资源
  auto& sync = Synchronizer::GetInstance();
  if (sync.IsRunning()) {
    sync.StopSync();
  }
}

void SynchronizerPlugin::HandleMethodCall(
    const flutter::MethodCall<flutter::EncodableValue> &method_call,
    std::unique_ptr<flutter::MethodResult<flutter::EncodableValue>> result) {

  const std::string& method = method_call.method_name();

  // ==================== 同步器功能 ====================
  if (method == "startSynchronization") {
    const auto* arguments = std::get_if<flutter::EncodableMap>(method_call.arguments());
    if (!arguments) {
      result->Error("INVALID_ARGUMENT", "Arguments must be a map");
      return;
    }

    // 获取源PID
    auto source_it = arguments->find(flutter::EncodableValue("sourcePID"));
    if (source_it == arguments->end()) {
      result->Error("MISSING_ARGUMENT", "sourcePID is required");
      return;
    }
    DWORD sourcePID = static_cast<DWORD>(std::get<int32_t>(source_it->second));

    // 获取目标PID列表
    auto targets_it = arguments->find(flutter::EncodableValue("targetPIDs"));
    if (targets_it == arguments->end()) {
      result->Error("MISSING_ARGUMENT", "targetPIDs is required");
      return;
    }

    const auto* target_list = std::get_if<flutter::EncodableList>(&targets_it->second);
    if (!target_list) {
      result->Error("INVALID_ARGUMENT", "targetPIDs must be a list");
      return;
    }

    std::vector<DWORD> targetPIDs;
    for (const auto& pid_value : *target_list) {
      if (std::holds_alternative<int32_t>(pid_value)) {
        targetPIDs.push_back(static_cast<DWORD>(std::get<int32_t>(pid_value)));
      }
    }

    if (targetPIDs.empty()) {
      result->Error("INVALID_ARGUMENT", "targetPIDs cannot be empty");
      return;
    }

    // 调用新的Synchronizer类
    auto& sync = Synchronizer::GetInstance();
    if (sync.StartSync(sourcePID, targetPIDs)) {
      result->Success(flutter::EncodableValue("success"));
    } else {
      result->Error("SYNC_FAILED", "Failed to start synchronization");
    }
  }

  else if (method == "stopSynchronization") {
    auto& sync = Synchronizer::GetInstance();
    sync.StopSync();
    result->Success(flutter::EncodableValue("success"));
  }

  else if (method == "isSynchronizationRunning") {
    auto& sync = Synchronizer::GetInstance();
    bool isRunning = sync.IsRunning();
    result->Success(flutter::EncodableValue(isRunning));
  }

  // ==================== 窗口排列功能 ====================
  else if (method == "arrangeWindow") {
    const auto* arguments = std::get_if<flutter::EncodableMap>(method_call.arguments());
    if (!arguments) {
      result->Error("INVALID_ARGUMENT", "Arguments must be a map");
      return;
    }

    // 解析参数
    auto pid_it = arguments->find(flutter::EncodableValue("pid"));
    auto x_it = arguments->find(flutter::EncodableValue("x"));
    auto y_it = arguments->find(flutter::EncodableValue("y"));
    auto width_it = arguments->find(flutter::EncodableValue("width"));
    auto height_it = arguments->find(flutter::EncodableValue("height"));

    if (pid_it == arguments->end() || x_it == arguments->end() ||
        y_it == arguments->end() || width_it == arguments->end() ||
        height_it == arguments->end()) {
      result->Error("MISSING_ARGUMENT", "pid, x, y, width, height are required");
      return;
    }

    DWORD pid = static_cast<DWORD>(std::get<int32_t>(pid_it->second));
    int x = std::get<int32_t>(x_it->second);
    int y = std::get<int32_t>(y_it->second);
    int width = std::get<int32_t>(width_it->second);
    int height = std::get<int32_t>(height_it->second);

    // 调用新的WindowArranger类（通过PID直接操作）
    if (WindowArranger::SetWindowPositionAndActivateByPID(pid, x, y, width, height)) {
      result->Success(flutter::EncodableValue("success"));
    } else {
      result->Error("ARRANGE_FAILED", "Failed to arrange window or window not found");
    }
  }

  else if (method == "getScreenSize") {
    // 调用新的WindowArranger类
    ScreenSize screenSize = WindowArranger::GetScreenSize();

    // 使用 map 返回屏幕尺寸
    std::map<flutter::EncodableValue, flutter::EncodableValue> sizeMap;
    sizeMap[flutter::EncodableValue("width")] = flutter::EncodableValue(screenSize.width);
    sizeMap[flutter::EncodableValue("height")] = flutter::EncodableValue(screenSize.height);

    result->Success(flutter::EncodableValue(sizeMap));
  }

  else if (method == "focusWindow") {
    const auto* arguments = std::get_if<flutter::EncodableMap>(method_call.arguments());
    if (!arguments) {
      result->Error("INVALID_ARGUMENT", "Arguments must be a map");
      return;
    }

    // 获取PID参数
    auto pid_it = arguments->find(flutter::EncodableValue("pid"));
    if (pid_it == arguments->end()) {
      result->Error("MISSING_ARGUMENT", "pid is required");
      return;
    }

    DWORD pid = static_cast<DWORD>(std::get<int32_t>(pid_it->second));

    // 调用WindowArranger的聚焦功能
    if (WindowArranger::FocusWindowByPID(pid)) {
      result->Success(flutter::EncodableValue("success"));
    } else {
      result->Error("FOCUS_FAILED", "Failed to focus window or window not found");
    }
  }

  // ==================== 错误处理 ====================
  else {
    result->NotImplemented();
  }
}

}  // namespace synchronizer
