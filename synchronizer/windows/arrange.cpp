#include "arrange.h"

ScreenSize WindowArranger::GetScreenSize() {
    int screenWidth = GetSystemMetrics(SM_CXSCREEN);
    int screenHeight = GetSystemMetrics(SM_CYSCREEN);
    return ScreenSize(screenWidth, screenHeight);
}

bool WindowArranger::SetWindowPositionAndActivate(HWND hwnd, int x, int y, int width, int height) {
    // 检查窗口句柄有效性
    if (!hwnd || !IsWindow(hwnd)) {
        return false;
    }

    // 检查窗口是否最小化，如果是则先恢复
    if (IsIconic(hwnd)) {
        if (!ShowWindow(hwnd, SW_RESTORE)) {
            return false;
        }
        // 等待窗口恢复完成
        Sleep(50);
    }

    // 设置窗口位置和尺寸
    if (!MoveWindow(hwnd, x, y, width, height, TRUE)) {
        return false;
    }

    // 激活窗口
    if (!SetForegroundWindow(hwnd)) {
        return false;
    }

    return true;
}

bool WindowArranger::SetWindowPositionAndActivateByPID(DWORD pid, int x, int y, int width, int height) {
    // 通过PID获取窗口句柄
    HWND hwnd = GetWindowHandleFromPID(pid);
    if (!hwnd) {
        return false;
    }

    // 调用窗口句柄版本的函数
    return SetWindowPositionAndActivate(hwnd, x, y, width, height);
}

bool WindowArranger::FocusWindow(HWND hwnd) {
    // 检查窗口句柄有效性
    if (!hwnd || !IsWindow(hwnd)) {
        return false;
    }

    // 检查窗口是否最小化，如果是则先恢复
    if (IsIconic(hwnd)) {
        if (!ShowWindow(hwnd, SW_RESTORE)) {
            return false;
        }
        // 等待窗口恢复完成
        Sleep(50);
    }

    // 如果窗口已经在前台，直接返回成功
    if (GetForegroundWindow() == hwnd) {
        return true;
    }

    // 获取当前前台窗口的线程ID
    DWORD currentThreadId = GetCurrentThreadId();
    DWORD foregroundThreadId = GetWindowThreadProcessId(GetForegroundWindow(), nullptr);
    DWORD targetThreadId = GetWindowThreadProcessId(hwnd, nullptr);

    // 尝试将输入附加到目标窗口的线程
    bool attached = false;
    if (foregroundThreadId != currentThreadId) {
        attached = AttachThreadInput(currentThreadId, foregroundThreadId, TRUE);
    }
    if (targetThreadId != currentThreadId && targetThreadId != foregroundThreadId) {
        AttachThreadInput(currentThreadId, targetThreadId, TRUE);
    }

    // 强制将窗口带到前台
    bool success = false;
    
    // 方法1: 使用BringWindowToTop
    if (BringWindowToTop(hwnd)) {
        success = true;
    }

    // 方法2: 使用SetForegroundWindow
    if (!success && SetForegroundWindow(hwnd)) {
        success = true;
    }

    // 方法3: 使用ShowWindow和SetActiveWindow组合
    if (!success) {
        if (ShowWindow(hwnd, SW_SHOW) && SetActiveWindow(hwnd)) {
            success = true;
        }
    }

    // 方法4: 如果以上方法都失败，尝试发送键盘事件后再试
    if (!success) {
        // 模拟Alt+Tab来激活窗口
        keybd_event(VK_MENU, 0, 0, 0);
        keybd_event(VK_TAB, 0, 0, 0);
        keybd_event(VK_TAB, 0, KEYEVENTF_KEYUP, 0);
        keybd_event(VK_MENU, 0, KEYEVENTF_KEYUP, 0);
        
        Sleep(100);
        
        if (SetForegroundWindow(hwnd)) {
            success = true;
        }
    }

    // 分离线程输入
    if (attached) {
        AttachThreadInput(currentThreadId, foregroundThreadId, FALSE);
    }
    if (targetThreadId != currentThreadId && targetThreadId != foregroundThreadId) {
        AttachThreadInput(currentThreadId, targetThreadId, FALSE);
    }

    // 最后确认窗口是否确实获得了焦点
    Sleep(100);  // 给系统一点时间处理
    return (GetForegroundWindow() == hwnd) || success;
}

bool WindowArranger::FocusWindowByPID(DWORD pid) {
    // 通过PID获取窗口句柄
    HWND hwnd = GetWindowHandleFromPID(pid);
    if (!hwnd) {
        return false;
    }

    // 调用窗口句柄版本的函数
    return FocusWindow(hwnd);
}

HWND WindowArranger::GetWindowHandleFromPID(DWORD targetPID) {
    HWND hwnd = GetTopWindow(nullptr);
    while (hwnd) {
        DWORD pid = 0;
        GetWindowThreadProcessId(hwnd, &pid);
        if (pid == targetPID && IsWindowVisible(hwnd)) {
            // 优先选择有标题的主窗口
            char windowTitle[256];
            if (GetWindowTextA(hwnd, windowTitle, sizeof(windowTitle)) > 0) {
                return hwnd;
            }
        }
        hwnd = GetNextWindow(hwnd, GW_HWNDNEXT);
    }

    // 如果没找到有标题的窗口，再次查找任何可见窗口
    hwnd = GetTopWindow(nullptr);
    while (hwnd) {
        DWORD pid = 0;
        GetWindowThreadProcessId(hwnd, &pid);
        if (pid == targetPID && IsWindowVisible(hwnd)) {
            return hwnd;
        }
        hwnd = GetNextWindow(hwnd, GW_HWNDNEXT);
    }

    return nullptr;
}
