#include "synchronizer.h"
#include <iostream>
#include <cassert>
#include <vector>

// 全局实例指针，用于静态回调函数访问
static Synchronizer* g_instance = nullptr;

Synchronizer::Synchronizer()
    : m_sourceHwnd(nullptr)
    , m_sourcePid(0)
    , m_keyboardHook(nullptr)
    , m_mouseHook(nullptr)
    , m_sourceThreadId(0)
    , m_isRunning(false)
    , m_shouldStop(false)
{
    // 设置控制台UTF-8输出支持中文
    SetConsoleOutputCP(65001);

    // 设置全局实例指针
    g_instance = this;
}

Synchronizer::~Synchronizer() {
    StopSync();
    g_instance = nullptr;
}

Synchronizer& Synchronizer::GetInstance() {
    static Synchronizer instance;
    return instance;
}

bool Synchronizer::StartSync(DWORD sourcePid, const std::vector<DWORD>& targetPids) {
    std::lock_guard<std::mutex> lock(m_stateMutex);

    // 检查是否已在运行
    if (m_isRunning.load()) {
        std::cout << "警告: 同步已在运行中" << std::endl;
        return false;
    }

    // 快速验证PID信息
    if (!ValidatePidInfo(sourcePid, targetPids)) {
        std::cout << "错误: PID信息验证失败" << std::endl;
        return false;
    }

    // 获取窗口句柄
    HWND sourceHwnd = GetWindowHandleFromPID(sourcePid);
    if (!IsWindowValid(sourceHwnd)) {
        std::cout << "错误: 源窗口句柄无效，进程 " << sourcePid << " 可能已关闭" << std::endl;
        return false;
    }

    std::vector<HWND> targetHwnds = GetWindowHandlesFromPIDs(targetPids);
    if (targetHwnds.empty()) {
        std::cout << "错误: 未找到有效的目标窗口" << std::endl;
        return false;
    }

    // 保存配置
    m_sourcePid = sourcePid;
    m_targetPids = targetPids;
    m_sourceHwnd = sourceHwnd;
    m_targetHwnds = targetHwnds;
    m_sourceThreadId = GetWindowThreadProcessId(sourceHwnd, nullptr);

    // 重置状态
    m_wheelEvent.Reset();
    m_mouseState.Reset();
    m_shouldStop = false;

    // 启动后台同步线程
    try {
        m_syncThread = std::make_unique<std::thread>(&Synchronizer::SyncThreadWorker, this);
        std::cout << "输入同步启动中... (源PID: " << sourcePid
                  << " -> " << targetPids.size() << "个目标)" << std::endl;
        return true;
    } catch (const std::exception& e) {
        std::cout << "错误: 创建同步线程失败 - " << e.what() << std::endl;
        return false;
    }
}

void Synchronizer::StopSync() {
    // 设置停止标志
    m_shouldStop = true;

    // 等待同步线程结束并回收资源
    if (m_syncThread && m_syncThread->joinable()) {
        m_syncThread->join();
        m_syncThread.reset();
    }

    // 确保运行状态已停止
    m_isRunning = false;

    std::cout << "输入同步已停止，线程资源已回收" << std::endl;
}

bool Synchronizer::IsRunning() const {
    return m_isRunning.load();
}

LRESULT CALLBACK Synchronizer::LowLevelKeyboardProc(int nCode, WPARAM wParam, LPARAM lParam) {
    if (nCode >= 0 && g_instance && g_instance->m_isRunning.load()) {
        KBDLLHOOKSTRUCT* pKeyStruct = reinterpret_cast<KBDLLHOOKSTRUCT*>(lParam);

        // 检查源窗口是否有焦点
        HWND foregroundWnd = GetForegroundWindow();
        if (foregroundWnd == g_instance->m_sourceHwnd) {
            int vkCode = pKeyStruct->vkCode;

            // 安全的类型转换
            UINT msgType = static_cast<UINT>(wParam);
            WPARAM virtualKey = static_cast<WPARAM>(vkCode);

            // 向所有目标窗口发送键盘事件
            for (HWND targetHwnd : g_instance->m_targetHwnds) {
                if (IsWindow(targetHwnd)) {
                    if (g_instance->IsCharacterKey(vkCode)) {
                        // 字符键：只转发按下事件，避免重复输入
                        if (wParam == WM_KEYDOWN || wParam == WM_SYSKEYDOWN) {
                            PostMessage(targetHwnd, msgType, virtualKey, pKeyStruct->scanCode);
                        }
                    } else {
                        // 功能键：转发完整的按下/释放序列
                        PostMessage(targetHwnd, msgType, virtualKey, pKeyStruct->scanCode);
                    }
                }
            }
        }
    }

    return CallNextHookEx(nullptr, nCode, wParam, lParam);
}

LRESULT CALLBACK Synchronizer::LowLevelMouseProc(int nCode, WPARAM wParam, LPARAM lParam) {
    if (nCode >= 0 && g_instance && g_instance->m_isRunning.load()) {
        if (wParam == WM_MOUSEWHEEL) {
            MSLLHOOKSTRUCT* pMouseStruct = reinterpret_cast<MSLLHOOKSTRUCT*>(lParam);

            // 转换为窗口相对坐标
            POINT pt = pMouseStruct->pt;
            if (ScreenToClient(g_instance->m_sourceHwnd, &pt)) {
                RECT clientRect;
                if (GetClientRect(g_instance->m_sourceHwnd, &clientRect)) {
                    // 检查是否在窗口范围内
                    bool inBounds = (pt.x >= 0 && pt.y >= 0 &&
                                   pt.x < clientRect.right && pt.y < clientRect.bottom);

                    // 检查窗口焦点
                    HWND foregroundWnd = GetForegroundWindow();
                    bool hasFocus = (foregroundWnd == g_instance->m_sourceHwnd);

                    if (hasFocus && inBounds) {
                        // 提取滚轮增量值
                        int delta = static_cast<short>(HIWORD(pMouseStruct->mouseData));

                        // 原子操作更新滚轮事件
                        g_instance->m_wheelEvent.delta = delta;
                        g_instance->m_wheelEvent.mousePos = pt;
                        g_instance->m_wheelEvent.processed = false;
                    }
                }
            }
        }
    }

    return CallNextHookEx(nullptr, nCode, wParam, lParam);
}

void Synchronizer::ProcessWheelEvent() {
    // 原子检查是否有待处理的滚轮事件
    if (m_wheelEvent.processed.load()) {
        return;
    }

    try {
        int wheelDelta = m_wheelEvent.delta;
        POINT mousePos = m_wheelEvent.mousePos;
        bool ctrlPressed = (GetAsyncKeyState(VK_CONTROL) & 0x8000) != 0;

        // 向所有目标窗口发送滚轮事件
        for (HWND targetHwnd : m_targetHwnds) {
            if (!IsWindow(targetHwnd)) continue;

            if (ctrlPressed) {
                // Ctrl + 滚轮 = 缩放操作
                WPARAM ctrlKey = static_cast<WPARAM>(VK_CONTROL);
                WPARAM actionKey = static_cast<WPARAM>(wheelDelta > 0 ? VK_OEM_PLUS : VK_OEM_MINUS);

                PostMessage(targetHwnd, WM_KEYDOWN, ctrlKey, 0);
                PostMessage(targetHwnd, WM_KEYDOWN, actionKey, 0);
                PostMessage(targetHwnd, WM_KEYUP, actionKey, 0);
                PostMessage(targetHwnd, WM_KEYUP, ctrlKey, 0);
            } else {
                // 普通滚轮操作 - 坐标转换
                POINT clientOffset = {0, 0};
                if (ClientToScreen(targetHwnd, &clientOffset)) {
                    // 计算屏幕坐标
                    int screenX = mousePos.x + clientOffset.x;
                    int screenY = mousePos.y + clientOffset.y;

                    // 限制坐标范围
                    screenX = max(0, min(screenX, MAX_COORDINATE));
                    screenY = max(0, min(screenY, MAX_COORDINATE));

                    // 构造消息参数
                    WORD keyState = 0;
                    if (GetAsyncKeyState(VK_CONTROL) & 0x8000) keyState |= MK_CONTROL;
                    if (GetAsyncKeyState(VK_SHIFT) & 0x8000) keyState |= MK_SHIFT;
                    if (GetAsyncKeyState(VK_LBUTTON) & 0x8000) keyState |= MK_LBUTTON;
                    if (GetAsyncKeyState(VK_RBUTTON) & 0x8000) keyState |= MK_RBUTTON;
                    if (GetAsyncKeyState(VK_MBUTTON) & 0x8000) keyState |= MK_MBUTTON;

                    WPARAM wParam = MAKEWPARAM(keyState, wheelDelta);
                    LPARAM lParam = MAKELPARAM(screenX, screenY);

                    // 发送滚轮消息
                    if (!PostMessage(targetHwnd, WM_MOUSEWHEEL, wParam, lParam)) {
                        // PostMessage失败时尝试SendMessage
                        SendMessage(targetHwnd, WM_MOUSEWHEEL, wParam, lParam);
                    }
                }
            }
        }

        // 标记事件为已处理
        m_wheelEvent.processed = true;

    } catch (...) {
        // 异常情况下也要标记为已处理
        m_wheelEvent.processed = true;
    }
}

void Synchronizer::ProcessMouseEvents() {
    HWND foregroundWnd = GetForegroundWindow();
    bool hasFocus = (foregroundWnd == m_sourceHwnd);

    // 获取鼠标位置和窗口区域
    POINT cursorPos;
    if (!GetCursorPos(&cursorPos) || !ScreenToClient(m_sourceHwnd, &cursorPos)) {
        return;
    }

    RECT clientRect;
    if (!GetClientRect(m_sourceHwnd, &clientRect)) {
        return;
    }

    bool inBounds = (cursorPos.x >= 0 && cursorPos.y >= 0 &&
                    cursorPos.x < clientRect.right && cursorPos.y < clientRect.bottom);

    // 获取鼠标按键状态
    bool leftPressed = (GetAsyncKeyState(VK_LBUTTON) & 0x8000) != 0;
    bool rightPressed = (GetAsyncKeyState(VK_RBUTTON) & 0x8000) != 0;
    bool middlePressed = (GetAsyncKeyState(VK_MBUTTON) & 0x8000) != 0;

    // 拖拽状态管理
    if (!m_mouseState.isDragging && leftPressed && inBounds) {
        m_mouseState.isDragging = true;
    } else if (m_mouseState.isDragging && !leftPressed) {
        m_mouseState.isDragging = false;
    }

    // 决定是否发送鼠标事件
    bool shouldSendEvents = (hasFocus && inBounds) || m_mouseState.isDragging;

    if (shouldSendEvents) {
        // 限制坐标范围
        cursorPos.x = max(0, min(cursorPos.x, MAX_COORDINATE));
        cursorPos.y = max(0, min(cursorPos.y, MAX_COORDINATE));

        LPARAM lParam = MAKELPARAM(cursorPos.x, cursorPos.y);

        // 向所有目标窗口发送鼠标事件
        for (HWND targetHwnd : m_targetHwnds) {
            if (!IsWindow(targetHwnd)) continue;

            // 鼠标移动事件
            if (cursorPos.x != m_mouseState.lastPos.x || cursorPos.y != m_mouseState.lastPos.y) {
                WPARAM moveWParam = MK_LBUTTON;
                if (rightPressed) moveWParam |= MK_RBUTTON;
                if (middlePressed) moveWParam |= MK_MBUTTON;

                PostMessage(targetHwnd, WM_MOUSEMOVE, moveWParam, lParam);
            }

            // 左键事件 - 使用显式的WPARAM转换
            if (leftPressed && !m_mouseState.leftButton.wasPressed) {
                PostMessage(targetHwnd, WM_LBUTTONDOWN, static_cast<WPARAM>(MK_LBUTTON), lParam);
            } else if (!leftPressed && m_mouseState.leftButton.wasPressed) {
                PostMessage(targetHwnd, WM_LBUTTONUP, static_cast<WPARAM>(0), lParam);
            }

            // 右键事件
            if (rightPressed && !m_mouseState.rightButton.wasPressed) {
                PostMessage(targetHwnd, WM_RBUTTONDOWN, static_cast<WPARAM>(MK_RBUTTON), lParam);
            } else if (!rightPressed && m_mouseState.rightButton.wasPressed) {
                PostMessage(targetHwnd, WM_RBUTTONUP, static_cast<WPARAM>(0), lParam);
            }

            // 中键事件
            if (middlePressed && !m_mouseState.middleButton.wasPressed) {
                PostMessage(targetHwnd, WM_MBUTTONDOWN, static_cast<WPARAM>(MK_MBUTTON), lParam);
            } else if (!middlePressed && m_mouseState.middleButton.wasPressed) {
                PostMessage(targetHwnd, WM_MBUTTONUP, static_cast<WPARAM>(0), lParam);
            }
        }

        // 更新最后位置（只需要更新一次）
        m_mouseState.lastPos = cursorPos;
    }

    // 更新按键状态
    m_mouseState.leftButton.wasPressed = leftPressed;
    m_mouseState.rightButton.wasPressed = rightPressed;
    m_mouseState.middleButton.wasPressed = middlePressed;
}

void Synchronizer::SyncThreadWorker() {
    // 在线程中初始化同步环境
    if (!InitializeSyncInThread()) {
        std::cout << "错误: 线程中初始化同步环境失败" << std::endl;
        m_isRunning = false;
        return;
    }

    std::cout << "输入同步已启动 (按ESC或调用StopSync停止)" << std::endl;
    m_isRunning = true;

    // 运行消息循环
    MessageLoop();

    // 清理线程资源
    {
        std::lock_guard<std::mutex> lock(m_stateMutex);
        CleanupResources();
    }

    m_isRunning = false;
}

bool Synchronizer::InitializeSyncInThread() {
    // 绑定输入队列
    DWORD currentThreadId = GetCurrentThreadId();
    if (m_sourceThreadId != currentThreadId) {
        if (!AttachThreadInput(currentThreadId, m_sourceThreadId, TRUE)) {
            std::cout << "错误: 绑定输入队列失败 (错误码: " << GetLastError() << ")" << std::endl;
            return false;
        }
    }

    // 安装钩子
    if (!InstallHooks()) {
        // 清理已绑定的输入队列
        if (m_sourceThreadId != currentThreadId) {
            AttachThreadInput(currentThreadId, m_sourceThreadId, FALSE);
        }
        return false;
    }

    return true;
}

void Synchronizer::MessageLoop() {
    MSG msg;

    while (!m_shouldStop.load()) {
        // 处理Windows消息队列
        while (PeekMessage(&msg, nullptr, 0, 0, PM_REMOVE)) {
            TranslateMessage(&msg);
            DispatchMessage(&msg);
        }

        // 处理自定义事件
        ProcessMouseEvents();
        ProcessWheelEvent();

        // 检查ESC退出
        if (GetAsyncKeyState(VK_ESCAPE) & 0x8000) {
            m_shouldStop = true;
            break;
        }

        // 短暂休眠以降低CPU使用率
        Sleep(SLEEP_INTERVAL);
    }

    m_isRunning = false;
}

HWND Synchronizer::GetWindowHandleFromPID(DWORD targetPID) const {
    HWND hwnd = GetTopWindow(nullptr);
    while (hwnd) {
        DWORD pid = 0;
        GetWindowThreadProcessId(hwnd, &pid);
        if (pid == targetPID && IsWindowVisible(hwnd)) {
            // 优先选择有标题的主窗口
            char windowTitle[256];
            if (GetWindowTextA(hwnd, windowTitle, sizeof(windowTitle)) > 0) {
                return hwnd;
            }
        }
        hwnd = GetNextWindow(hwnd, GW_HWNDNEXT);
    }

    // 如果没找到有标题的窗口，再次查找任何可见窗口
    hwnd = GetTopWindow(nullptr);
    while (hwnd) {
        DWORD pid = 0;
        GetWindowThreadProcessId(hwnd, &pid);
        if (pid == targetPID && IsWindowVisible(hwnd)) {
            return hwnd;
        }
        hwnd = GetNextWindow(hwnd, GW_HWNDNEXT);
    }

    return nullptr;
}

std::vector<HWND> Synchronizer::GetWindowHandlesFromPIDs(const std::vector<DWORD>& pids) const {
    std::vector<HWND> result;

    for (DWORD pid : pids) {
        HWND hwnd = GetWindowHandleFromPID(pid);
        if (IsWindowValid(hwnd)) {
            result.push_back(hwnd);
        } else {
            std::cout << "警告: PID " << pid << " 对应的窗口无效，已跳过" << std::endl;
        }
    }

    return result;
}

bool Synchronizer::ValidatePidInfo(DWORD sourcePid, const std::vector<DWORD>& targetPids) const {
    // 检查源PID有效性
    if (sourcePid == 0) {
        std::cout << "错误: 源PID不能为0" << std::endl;
        return false;
    }

    // 检查目标PID列表
    if (targetPids.empty()) {
        std::cout << "错误: 目标PID列表不能为空" << std::endl;
        return false;
    }

    // 检查是否有重复PID
    for (DWORD targetPid : targetPids) {
        if (targetPid == 0) {
            std::cout << "错误: 目标PID不能为0" << std::endl;
            return false;
        }

        if (targetPid == sourcePid) {
            std::cout << "错误: 目标PID " << targetPid << " 不能与源PID相同" << std::endl;
            return false;
        }
    }

    // 检查源进程是否存在
    HANDLE sourceProcess = OpenProcess(PROCESS_QUERY_INFORMATION, FALSE, sourcePid);
    if (!sourceProcess) {
        std::cout << "错误: 源进程 (PID: " << sourcePid << ") 不存在或无法访问" << std::endl;
        return false;
    }
    CloseHandle(sourceProcess);

    // 检查目标进程是否存在
    for (DWORD targetPid : targetPids) {
        HANDLE targetProcess = OpenProcess(PROCESS_QUERY_INFORMATION, FALSE, targetPid);
        if (!targetProcess) {
            std::cout << "错误: 目标进程 (PID: " << targetPid << ") 不存在或无法访问" << std::endl;
            return false;
        }
        CloseHandle(targetProcess);
    }

    return true;
}

bool Synchronizer::IsCharacterKey(int vkCode) const {
    return (vkCode >= 'A' && vkCode <= 'Z') ||
           (vkCode >= '0' && vkCode <= '9') ||
           (vkCode >= VK_NUMPAD0 && vkCode <= VK_NUMPAD9) ||
           (vkCode >= VK_OEM_1 && vkCode <= VK_OEM_7) ||
           vkCode == VK_SPACE;
}

bool Synchronizer::IsWindowValid(HWND hwnd) const {
    return hwnd != nullptr && IsWindow(hwnd) && IsWindowVisible(hwnd);
}

bool Synchronizer::InstallHooks() {
    // 安装键盘钩子
    m_keyboardHook = SetWindowsHookEx(WH_KEYBOARD_LL, LowLevelKeyboardProc,
                                    GetModuleHandle(nullptr), 0);
    if (!m_keyboardHook) {
        std::cout << "错误: 安装键盘钩子失败 (错误码: " << GetLastError() << ")" << std::endl;
        return false;
    }

    // 安装鼠标钩子
    m_mouseHook = SetWindowsHookEx(WH_MOUSE_LL, LowLevelMouseProc,
                                 GetModuleHandle(nullptr), 0);
    if (!m_mouseHook) {
        std::cout << "错误: 安装鼠标钩子失败 (错误码: " << GetLastError() << ")" << std::endl;
        UnhookWindowsHookEx(m_keyboardHook);
        m_keyboardHook = nullptr;
        return false;
    }

    return true;
}

void Synchronizer::UninstallHooks() {
    if (m_keyboardHook) {
        UnhookWindowsHookEx(m_keyboardHook);
        m_keyboardHook = nullptr;
    }

    if (m_mouseHook) {
        UnhookWindowsHookEx(m_mouseHook);
        m_mouseHook = nullptr;
    }
}

void Synchronizer::CleanupResources() {
    // 卸载钩子
    UninstallHooks();

    // 解绑输入队列
    DWORD currentThreadId = GetCurrentThreadId();
    if (m_sourceThreadId != 0 && m_sourceThreadId != currentThreadId) {
        AttachThreadInput(currentThreadId, m_sourceThreadId, FALSE);
    }

    // 重置状态（不重置PID，以便重新启动）
    m_sourceThreadId = 0;
}