#include "include/synchronizer/synchronizer_plugin_c_api.h"

#include <flutter/plugin_registrar_windows.h>

#include "synchronizer_plugin.h"

void SynchronizerPluginCApiRegisterWithRegistrar(
    FlutterDesktopPluginRegistrarRef registrar) {
  synchronizer::SynchronizerPlugin::RegisterWithRegistrar(
      flutter::PluginRegistrarManager::GetInstance()
          ->GetRegistrar<flutter::PluginRegistrarWindows>(registrar));
}
