#pragma once

#include <windows.h>
#include <memory>
#include <thread>
#include <mutex>
#include <atomic>
#include <vector>

/**
 * @brief 窗口输入同步器 - 单实例类
 *
 * 该类用于在两个窗口之间同步键盘、鼠标输入事件
 * 支持键盘输入、鼠标移动、点击、滚轮等所有输入事件的转发
 */
class Synchronizer {
public:
    /**
     * @brief 获取单实例
     * @return Synchronizer的唯一实例引用
     */
    static Synchronizer& GetInstance();

    // 禁用拷贝构造和赋值操作
    Synchronizer(const Synchronizer&) = delete;
    Synchronizer& operator=(const Synchronizer&) = delete;
    Synchronizer(Synchronizer&&) = delete;
    Synchronizer& operator=(Synchronizer&&) = delete;

    /**
     * @brief 启动输入同步（非阻塞）
     * 快速验证PID信息，如果正常则启动后台线程并立即返回成功
     * @param sourcePid 源进程ID（输入捕获进程）
     * @param targetPids 目标进程ID列表（输入转发进程）
     * @return 启动成功返回true，失败返回false
     */
    bool StartSync(DWORD sourcePid, const std::vector<DWORD>& targetPids);

    /**
     * @brief 停止输入同步
     */
    void StopSync();

    /**
     * @brief 获取同步状态
     * @return 正在同步返回true，否则返回false
     */
    bool IsRunning() const;

    /**
     * @brief 获取源窗口句柄
     */
    HWND GetSourceWindow() const { return m_sourceHwnd; }

    /**
     * @brief 获取目标窗口句柄列表
     */
    const std::vector<HWND>& GetTargetWindows() const { return m_targetHwnds; }

private:
    // 私有构造函数和析构函数
    Synchronizer();
    ~Synchronizer();

    // 滚轮事件数据结构
    struct WheelEvent {
        int delta;
        POINT mousePos;
        std::atomic<bool> processed;

        WheelEvent() : delta(0), mousePos({0, 0}), processed(true) {}

        void Reset() {
            delta = 0;
            mousePos = {0, 0};
            processed = true;
        }
    };

    // 按键状态结构
    struct KeyState {
        bool wasPressed;
        KeyState() : wasPressed(false) {}
        void Reset() { wasPressed = false; }
    };

    // 鼠标状态结构
    struct MouseState {
        bool isDragging;
        POINT lastPos;
        KeyState leftButton;
        KeyState rightButton;
        KeyState middleButton;

        MouseState() : isDragging(false), lastPos({-1, -1}) {}

        void Reset() {
            isDragging = false;
            lastPos = {-1, -1};
            leftButton.Reset();
            rightButton.Reset();
            middleButton.Reset();
        }
    };

    // 静态钩子回调函数
    static LRESULT CALLBACK LowLevelKeyboardProc(int nCode, WPARAM wParam, LPARAM lParam);
    static LRESULT CALLBACK LowLevelMouseProc(int nCode, WPARAM wParam, LPARAM lParam);

    // 内部处理函数
    void ProcessWheelEvent();
    void ProcessMouseEvents();
    void MessageLoop();

    // 工具函数
    bool IsCharacterKey(int vkCode) const;
    bool IsWindowValid(HWND hwnd) const;
    bool InstallHooks();
    void UninstallHooks();
    void CleanupResources();

    // PID相关函数
    HWND GetWindowHandleFromPID(DWORD targetPID) const;
    bool ValidatePidInfo(DWORD sourcePid, const std::vector<DWORD>& targetPids) const;
    std::vector<HWND> GetWindowHandlesFromPIDs(const std::vector<DWORD>& pids) const;

    // 线程工作函数
    void SyncThreadWorker();
    bool InitializeSyncInThread();

    // 窗口相关
    HWND m_sourceHwnd;
    std::vector<HWND> m_targetHwnds;
    DWORD m_sourcePid;
    std::vector<DWORD> m_targetPids;

    // 钩子相关
    HHOOK m_keyboardHook;
    HHOOK m_mouseHook;
    DWORD m_sourceThreadId;

    // 状态管理
    std::atomic<bool> m_isRunning;
    std::atomic<bool> m_shouldStop;

    // 事件数据
    WheelEvent m_wheelEvent;
    MouseState m_mouseState;

    // 线程管理
    std::unique_ptr<std::thread> m_syncThread;
    mutable std::mutex m_stateMutex;

    // 性能优化
    static constexpr DWORD SLEEP_INTERVAL = 1;
    static constexpr int MAX_COORDINATE = 32767;
};