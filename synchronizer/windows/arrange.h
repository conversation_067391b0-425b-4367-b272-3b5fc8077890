#pragma once

#include <windows.h>

/**
 * @brief 屏幕尺寸结构
 */
struct ScreenSize {
    int width;
    int height;

    ScreenSize() : width(0), height(0) {}
    ScreenSize(int w, int h) : width(w), height(h) {}
};

/**
 * @brief 窗口排列管理类
 */
class WindowArranger {
public:
    /**
     * @brief 获取屏幕尺寸
     * @return 屏幕的宽度和高度
     */
    static ScreenSize GetScreenSize();

    /**
     * @brief 设置窗口尺寸和位置，并激活窗口
     * @param hwnd 窗口句柄
     * @param x 窗口左上角X坐标
     * @param y 窗口左上角Y坐标
     * @param width 窗口宽度
     * @param height 窗口高度
     * @return 操作成功返回true，失败返回false
     */
    static bool SetWindowPositionAndActivate(HWND hwnd, int x, int y, int width, int height);

    /**
     * @brief 通过PID设置窗口尺寸和位置，并激活窗口
     * @param pid 进程ID
     * @param x 窗口左上角X坐标
     * @param y 窗口左上角Y坐标
     * @param width 窗口宽度
     * @param height 窗口高度
     * @return 操作成功返回true，失败返回false
     */
    static bool SetWindowPositionAndActivateByPID(DWORD pid, int x, int y, int width, int height);

    /**
     * @brief 根据窗口句柄聚焦窗口
     * @param hwnd 窗口句柄
     * @return 聚焦成功返回true，失败返回false
     */
    static bool FocusWindow(HWND hwnd);

    /**
     * @brief 根据PID聚焦窗口
     * @param pid 进程ID
     * @return 聚焦成功返回true，失败返回false
     */
    static bool FocusWindowByPID(DWORD pid);

private:
    /**
     * @brief 通过PID获取窗口句柄
     * @param targetPID 目标进程ID
     * @return 窗口句柄，失败返回nullptr
     */
    static HWND GetWindowHandleFromPID(DWORD targetPID);
};
