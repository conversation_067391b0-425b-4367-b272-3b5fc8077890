#ifndef FLUTTER_PLUGIN_SYNCHRONIZER_PLUGIN_H_
#define FLUTTER_PLUGIN_SYNCHRONIZER_PLUGIN_H_

#include <flutter/method_channel.h>
#include <flutter/plugin_registrar_windows.h>

#include <memory>

namespace synchronizer {

class SynchronizerPlugin : public flutter::Plugin {
 public:
  static void RegisterWithRegistrar(flutter::PluginRegistrarWindows *registrar);

  SynchronizerPlugin();

  virtual ~SynchronizerPlugin();

  // Disallow copy and assign.
  SynchronizerPlugin(const SynchronizerPlugin&) = delete;
  SynchronizerPlugin& operator=(const SynchronizerPlugin&) = delete;

  // Called when a method is called on this plugin's channel from Dart.
  void HandleMethodCall(
      const flutter::MethodCall<flutter::EncodableValue> &method_call,
      std::unique_ptr<flutter::MethodResult<flutter::EncodableValue>> result);
};

}  // namespace synchronizer

#endif  // FLUTTER_PLUGIN_SYNCHRONIZER_PLUGIN_H_
